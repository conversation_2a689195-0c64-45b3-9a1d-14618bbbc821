.block-gravityforms {
  box-shadow: 0 0 13px -5px rgba(0,0,0,.4);
  display: flex;
  justify-content: space-between;
  padding: 2.27rem 6.67rem;
  @include breakpoint($sub-l) {
    flex-direction: column;
    padding: 2rem 2rem;
  }
  .form_header {
    text-align: left;
    display: flex;
    width: 30%;
    padding-right: 2rem;
    flex-direction: column;
    justify-content: space-around;
    h2 {
      font-weight: 400;
    }
    @include breakpoint($sub-l) {
      width: 100%;
      text-align: left;
      h2 {
        font-weight: 400;
      }
    }
    img {
      max-width: 15rem;
      @include breakpoint($sub-l) {
        display: none;
      }
    }
  }
  .form_form {
    width: 70%;
    @include breakpoint($sub-l) {
      width: 100%;
    }
  }
}
body .gform_wrapper ul li.gfield {
  margin-top: 0px !important;
}

.gform_wrapper .gform_select {
  border: 2px solid $light-blue !important;
}
.gform_wrapper .gform_select option {
  color: $primary !important;
}

.gform_wrapper input:not([type='radio']):not([type='checkbox']):not([type='submit']):not([type='button']):not([type='image']):not([type='file']),
.gform_wrapper textarea { // Remove if doesnt work
  min-height: 48px;
  padding: 10px 10px;
  border: 2px solid $light-blue;
  font-weight: $ws-regular;
  letter-spacing: 0.2px;
  &:focus {
      outline: 2px solid $primary;
      outline-offset: -2px;
      border-radius: 0%;
  }
  &::placeholder {
      letter-spacing: 0.2px;
      font-weight: $ws-regular;
      color: $gray-text;
      font-size: 16px !important;
  }
  font-size: inherit;
  font-family: inherit;
  padding: 10px 7px !important;
  letter-spacing: normal;
  // border-radius: 5px !important;
  border-radius: 0 !important;
  // border: 1px solid $grey-dark;
  &::placeholder {
    font-size: 16px !important;
    font-weight: 400 !important;
  }
}

.gform_wrapper .top_label li.gfield.gf_left_half .ginput_container:not(.gfield_time_hour):not(.gfield_time_minute):not(.gfield_time_ampm):not(.ginput_container_date):not(.ginput_quantity):not(.datepicker), .gform_wrapper .top_label li.gfield.gf_right_half .ginput_container:not(.gfield_time_hour):not(.gfield_time_minute):not(.gfield_time_ampm):not(.ginput_container_date):not(.ginput_quantity):not(.datepicker) {
  width: 100% !important;
  margin: 0px 0 0 0 !important;
  padding-left: 0;
  padding-right: 0;
}

.gform_wrapper legend.gfield_label, .gform_wrapper .top_label .gfield_label {
  display: inline-block;
  line-height: 1.3;
  clear: both;
  margin-top: 1rem !important;
  margin-left: 2px !important;
}

.gform_wrapper li.hidden_label input {
  margin-top: 3px !important;
}

.gform_wrapper textarea.medium {
  height: 100px !important;
}

#field_1_11 > div {
  // margin-top: -5px !important;
}

#field_1_12 > div {
  // margin-top: 15px !important;
}

.gform_wrapper.gf_browser_chrome .gfield_checkbox li input[type=checkbox], .gform_wrapper.gf_browser_chrome .gfield_radio li input[type=radio], .gform_wrapper.gf_browser_chrome .gfield_checkbox li input {
  margin-top: 0px !important;
  margin-right: 3px;
}

.gform_legacy_markup_wrapper legend.gfield_label, .gform_legacy_markup_wrapper label.gfield_label {
  font-weight: 500 !important;
  font-size: 14px !important;
  text-transform: uppercase !important;
  letter-spacing: 0.2px !important;
}

@media only screen and (min-width: 641px) {
  .gform_wrapper .top_label li.gfield.gf_left_half, .gform_wrapper .top_label li.gfield.gf_right_half {
    width: calc(50% - 1rem) !important;
  }
}

body .gform_legacy_markup_wrapper .top_label div.ginput_container {
  margin-top: 0 !important;
}

div.ginput_container .ginput_container_fileupload {
  border: 1px dotted $primary !important;
}

.gform_button {
  @include button;
  line-height: normal !important;
}