.block-trainer-list {
  display: flex;
  overflow: hidden;
  padding: 0 1.5rem;
  .slick-prev {
    left: 0px;
  }
  .slick-next {
    right: 0px;
  }

  .loop_item {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
    padding: 2rem;
    min-height: 377px;
    display: flex;
    align-items: flex-end;
    flex-wrap: wrap;
    position: relative;
    transition: all .2s ease-out;
    margin-left: 1rem;
    &__content {
      z-index: 10;
    }
    .name {
      color: white;
      width: 100%;
      display: block;
      text-transform: uppercase;
      font-size: 0.75rem;
    }
    .term {
      color: white;
      width: 100%;
      display: block;
      text-transform: uppercase;
      font-size: 0.75rem;
      font-weight: 700;
    }
    .excerpt {
      padding-top: 1.25rem;
      color: white;
      font-size: 1.5rem;
      display: block;
      line-height: 1.88rem;
    }

    a {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 11;
    }
    &:hover {
      //transform:scale(1.005);
      box-shadow: 0 7px 13px -10px #000;
    }
  }
}
