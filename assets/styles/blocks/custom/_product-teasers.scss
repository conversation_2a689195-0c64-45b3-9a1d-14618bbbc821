.block-product-teasers {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  & + .block-product-teasers {
    margin-top: -3rem;
  }
  .tabs {
    width: 100%;
  }
  .tabs__list {
    justify-content: space-between;
    flex-wrap: wrap;
  }

  &__item {
    width: 100%;
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 1rem;
    box-shadow: 0 0 13px -5px rgba(0,0,0,.4);
    transition: all .1s linear;
    @include breakpoint($l) {
      width: calc(50% - .5rem);
    }
    .title {
      margin-top: 0;
      font-weight: 600;
      display: block;
      font-size: 1.5rem;
      margin-bottom: 1.5rem;
    }
    img {
      min-width: 225px;
      max-width: 225px;
      height: 100%;
      max-height: 300px;
      min-height: 225px;
      object-fit: cover;
      @include breakpoint($sub-l) {
        order: 1;
        min-width: 100%;
        max-width: 100%;
      }
    }
    &:hover, &:focus, &:active, &.on {
      transform: scale(1.025);
      box-shadow: 2px 0 13px -5px rgba(0,0,0,.5);
    }
  }

  &__content {
    padding: 1.5rem;
    flex: 1;
    @include breakpoint($sub-l) {
      order: 2;
    }
  }
  .tabs__btn {
    cursor: pointer;
    position: relative;
    &.on {
      &::after {
        content: " ";
        width: 0;
        height: 0;
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        border-bottom: 10px solid $grey;
        position: absolute;
        bottom: -15px;
        left:50%;
      }
    }
  }
  .tabs__content {
    display: block;
    position: relative;
    height: 0;
    overflow: hidden;
    background-color: $grey;
    .tabs__wrap {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      padding-top: 7.25rem;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }

    .content {
      width: 60%;

      h4 {
        margin-top: 0;
      }
      @include breakpoint($sub-l) {
        width: 100%;

      }
    }
    .content + img {
      width: 30%;
      max-width: 200px;
      height: auto;
      align-self: center;
    }
    &--close {
      position: absolute;
      right: 1.25rem;
      top: 1.25rem;
      cursor: pointer;
      svg {
        width: 2rem;
        height: 2rem;
        color: $grey-darker;
      }
    }
    .product__wrap {
      width: 100%;
    }
  }

  a {
    @include button;
    margin-top: 1.5rem;
    display: block;
    width: auto;
    min-width: 150px;
    text-align: center;
    border: 1px solid $brand;
    color: $brand;
    background: transparent;
    width: fit-content;
    &:hover {
      color: white;
    }
    &.external {
      background-color: #002663;
      border: 1px solid #002663;
      color: white;
      padding: 0.75rem 4.5rem 0.75rem 3rem;
      &:after {
        position: absolute;
        background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23ffffff' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-position: center;
        background-size: 84%;
        width: 1.1rem;
        height: 1rem;
        display: inline-block;
        content: " ";
        margin-left: 8px;
        color: transparent;
        background-repeat: no-repeat;
      }
      &:hover {
        background-color: white;
        color: #002663;
        &:after {
          background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23002663' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
      }
    }
    &:hover {
      background-color: $primary;
      border-color: $primary;
      color: white;
    }
  }
}
