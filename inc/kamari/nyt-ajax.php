<?php

add_action('wp_ajax_nopriv_kauppakamari_filter_nyt', 'kauppakamari_filter_nyt');
add_action('wp_ajax_kauppakamari_filter_nyt', 'kauppakamari_filter_nyt');

function kauppakamari_filter_nyt() {

  $tax_query = array('relation' => 'AND');

	if (isset( $_POST['category']) && !empty( $_POST['category'])) {
    $cats = explode(',', $_POST['category']);
    $tax_query[] = array(
        'taxonomy' => 'article_type',
        'field' => 'slug',
        'terms' => $cats
    );
  }

  if (isset( $_POST['theme']) && !empty($_POST['theme'])) {
    $tax_query[] = array(
        'taxonomy' => 'category',
        'field' => 'slug',
        'terms' => $_POST['theme']
    );
  }

  $args = array(
    'post_type'              => array( 'post', 'release' ),
    'posts_per_page'         => '5',
    'tax_query'              => $tax_query,
    'post_status'            => 'publish',
    'orderby'                => 'date',
    'order'                  => 'desc',
    'lang'                   => 'fi',
  );

  if (isset($_POST['theme']) && !empty($_POST['theme'])) {
    if ($_POST['theme'] === 'international-topics') {
      $args['lang'] = 'en';
    } /* elseif ($_POST['theme'] === 'tiedote') {
      $args['lang'] = 'fi';
    } else {
      $args['lang'] = 'fi';
    } */
  }
  if (isset($_POST['category']) && !empty($_POST['category'])) {
    if (in_array('tiedotteet', $_POST['category'])) {
      $args['lang'] = 'fi';
    }
  }

  if (isset($_POST['writer']) && !empty($_POST['writer'])) {
    $args['meta_key'] = 'writer_object';
    $args['meta_value'] = $_POST['writer'];
    $args['order'] = 'DESC';
  }

  if ( isset( $_POST['current_page'] ) ) {
    $next_page = $_POST['current_page'] + 1;
    $args['paged'] = $next_page;
    $i = $next_page > 1 ? 5 : 0;
  } else {
    $i = 0;
  }

  $query = new WP_Query( $args );

  if ( $query->have_posts() ) {

    ob_start();

    //var_dump($args);

    while ( $query->have_posts() ) {
      $query->the_post();
      if ($i===0) {
        get_template_part('partials/content/teaser', 'large');
      } elseif ($i===1 || $i===2) {
        get_template_part('partials/content/teaser', 'medium');
      } elseif ($i >= 3 ) {
        echo '<div class="clearfix"></div>';
        get_template_part('partials/content/teaser');
      }
      $i++;
    } 

  } else {}

  $return = array(
    'articles' => ob_get_clean(),
    'pageCount' => $query->max_num_pages
  );

  wp_send_json_success($return);

  wp_die();
}
