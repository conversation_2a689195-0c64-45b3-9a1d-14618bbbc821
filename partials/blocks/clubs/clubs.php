 <?php

/**
 * clubs
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-clubs-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-clubs';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}
?>

<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">

  <?php
    $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
    $args = array(
      'post_type'       => 'club',
      'posts_per_page'  => 5,
      'meta_key'        => 'date',
      'orderby'         => 'meta_value',
      'order'           => 'asc'
      //'paged'           => $paged
    );

    $today = new \DateTime();

    $args['meta_query'] = [
      [
          //'key' => 'txtbox_start_date',
          'key' => 'date',
          'type' => 'DATETIME',
          'compare' => '>=',
          'value' => $today->format('Y-m-d h:i:s'),
      ]
    ];
    $clubs = new WP_Query($args);
  ?>

  <div class="block-clubs__wrap"
    data-page="<?php echo get_query_var('paged') ? get_query_var('paged') : 1; ?>"
    data-max="<?php echo $clubs->max_num_pages; ?>">

    <div class="block-clubs__title">
      <h5>seuraavat klubit ja klubitapahtumat</h5>
      <div class="nav">
        <a href="#" class="clubs_prev_page"><?php echo kauppakamari_get_svg('chevron_left'); ?></a>
        <span class="clubs_current_page"><?php echo 1 . '</span> / ' . $clubs->max_num_pages; ?>
        <a href="#" class="clubs_next_page"><?php echo kauppakamari_get_svg('chevron_right'); ?></a>
      </div>
    </div>

    <div class="block-clubs__ajax">

    <?php if ( $clubs->have_posts() ) : ?>

        <?php while ( $clubs->have_posts() ) : $clubs->the_post();
            global $post; ?>
            <div class="block-clubs__item">
              <div class="block-clubs__item--content <?php echo get_field('content', $post->ID) ? 'accordion-toggle' : ''; ?>">
                <h5><?php the_title(); ?></h5>
                <span class="date"><?php echo date_i18n( 'l j.m.Y', strtotime( get_field('date', $post->ID) ) ); ?></span>
                <span class="time"><?php echo get_field('time', $post->ID) ? "<span class='separator'>I</span>" . get_field('time', $post->ID) : ""; ?></span>
                <span class="location"><?php echo get_field('location', $post->ID) ? "<span class='separator'>I</span>" . get_field('location', $post->ID) : ""; ?></span>
                <span class="icon-wrap"><?php echo get_field('content', $post->ID) ? kauppakamari_get_svg('chevron_right') . ' Lisää tietoa': ''; ?></span>
              </div>
              <?php if(get_field('link', $post->ID)) : ?>
                <a href="<?php echo get_field('link', $post->ID); ?>">Ilmoittaudu mukaan</a>
              <?php endif; ?>

              <?php if(get_field('content', $post->ID)) : ?>
                <div class="block-clubs__item--hidden">
                  <?php echo get_field('content', $post->ID); ?>
                </div>
              <?php endif; ?>

            </div>

        <?php endwhile; ?>

        <?php wp_reset_postdata(); ?>

    <?php else : ?>
        <p><?php _e( 'Sorry, no posts matched your criteria.' ); ?></p>
    <?php endif; ?>
    </div>
  </div>
  <img src="<?php echo get_template_directory_uri() . '/dist/images/kuvitus_aikatauluttaja.png'; ?>" >
</div>
