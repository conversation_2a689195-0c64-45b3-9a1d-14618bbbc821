/* ==========================================================================
 # Teaser people search result
========================================================================== */

.search-results--people {
  width: 100%;
  margin-top: 20px;
  margin-bottom: 20px;
  padding-left: 1.5rem;

  p.contact-info-text {
    font-size: 15px;
    margin-top: -14px;
  }

  p.cat {
    font-size: 14px;
    font-weight: $ws-semibold;
    color: #002662;
    margin-top: 0px;
    margin-bottom: 20px;
    text-transform: uppercase;
  }

  .show-more-people {
    text-align: center;
  }
}

.search-results--people--container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  column-gap: 20px;
  @media screen and (max-width: 1124px) {
    grid-template-columns: 1fr 1fr;
  }
  @media screen and (max-width: $menu-visible) {
    grid-template-columns: 1fr;
  }

  .search-result--people--item.teaser--people {
    display: block;
    min-height: 70px;
    margin-bottom: 20px;
    //box-shadow: 0 0px 2.2px rgba(0, 0, 0, 0.057), 0 3.7px 5.3px rgba(0, 0, 0, 0.065);

    &.teaser--people-simple {

    }
    &.teaser--people-full {
      margin-bottom: 70px;
    }

    .block-people-select__img {
      width: 143px;
      height: 143px;
      border-radius: 50%;
      background-position: 50%;
      background-size: cover;
      margin: 0 auto;
      margin-bottom: 2.5rem;
      margin-left: auto;
      margin-right: auto;

    }

    .contact__item__cat {
      min-height: 45px;
    }
    .contact__item__name {
      font-size: 16px;
      font-weight: 700;
      border-bottom: 1px solid #d3d3d3;
      padding: 0 0 0.5rem;
    }

    .contact__item__info:empty {
      border-bottom: none;
    }

    .person-title, .person-puh {

    }

  }

}


#show-more-people-link,
.search-result--people--item.teaser--people .search_by_writer {
  display: block;
  margin-top: 11px;
  font-size: 16px;
  font-weight: 700;
  color: #002662;
  line-height: 20px;
  text-decoration: underline;
}
