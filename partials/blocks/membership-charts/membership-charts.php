<?php
/**
 * Membership charts
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-membership-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-membership';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$item = get_fields();
$chart_data = get_field('chart_data');
foreach($chart_data as $element);

?>

<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">
    <?php if($item['title'] && $item['subtitle']) {?>
        <div class="block-membership__title-wrapper">
            <h2 class="block-membership__title">
                <?php echo $item['title']; ?>
            </h2>
            <span class="block-membership__subtitle">
                <?php echo $item['subtitle']; ?>
    </span>
        </div>
    <?php } ?>
    <div class="block-membership__charts">

        <div class="block-membership__chart">
            <canvas data-chart-id="canvas1" id="canvas1" role="img" class="is-chart"
                data-textupper="<?php echo $element['group']['upper-text']; ?>"
                data-textlower="<?php echo $element['group']['lower-text']; ?>"
                data-textvalue="<?php echo $element['group']['percentage']; ?>">
                </canvas>
            <div class="block-membership__chart-text">
                <?php if($element['group']['upper-text']) { ?>
                    <p><?php echo $element['group']['upper-text']; ?></p>
                <?php } ?>
                <span class="chart-value has-percentage"><?php echo $element['group']['percentage']; ?><span>%</span></span>
                <?php if($element['group']['lower-text']) {?>
                    <p><?php echo $element['group']['lower-text']; ?></p>
                <?php } ?>
            </div>
        </div>

        <div class="mobile-separator"></div>

        <div class="block-membership__chart map-chart">
            <div data-map-id="map" class="block-membership__map <?php 
                if($element['group_2']['map_fill'] == 'less-20') echo 'less-20';
                elseif($element['group_2']['map_fill'] == 'less-40') echo 'less-40';
                elseif($element['group_2']['map_fill'] == 'less-60') echo 'less-60';
                elseif($element['group_2']['map_fill'] == 'less-80') echo 'less-80';
                elseif($element['group_2']['map_fill'] == 'less-100') echo 'less-100';
                elseif($element['group_2']['map_fill'] == 'fill-helsinki') echo 'fill-helsinki';
                else echo 'is-100';?>">
                <img decoding="async" src="<?php echo get_template_directory_uri() . '/dist/images/fin_map_2.svg';?>" alt="">
            </div>
            <div class="block-membership__chart-text">
                <?php if($element['group_2']['upper-text']) { ?>
                    <p><?php echo $element['group_2']['upper-text']; ?></p>
                <?php } ?>
                <span class="chart-value"><?php echo $element['group_2']['percentage']; ?></span>
                <?php if($element['group_2']['lower-text']) {?>
                    <p><?php echo $element['group_2']['lower-text']; ?></p>
                <?php } ?>
            </div>
        </div>

        <div class="mobile-separator"></div>

        <div class="block-membership__chart">
            <canvas data-chart-id="canvas3" id="canvas3" role="img" class="is-chart" 
                data-textupper="<?php echo $element['group_3']['upper-text']; ?>"
                data-textlower="<?php echo $element['group_3']['lower-text']; ?>"
                data-textvalue="<?php echo $element['group_3']['percentage']; ?>">
                </canvas>
            <div class="block-membership__chart-text">
                <?php if($element['group_3']['upper-text']) { ?>
                    <p><?php echo $element['group']['upper-text']; ?></p>
                <?php } ?>

                <span class="chart-value has-percentage"><?php echo $element['group_3']['percentage']; ?><span>%</span></span>
                
                <?php if($element['group_3']['lower-text']) { ?>
                    <p><?php echo $element['group_3']['lower-text']; ?></p>
                <?php } ?>
            </div>
        </div>

    </div>
    
    <div class="block-membership__link-wrapper">
        <?php if($item['link']) {?>
            <a class="block-membership__link" href="<?php echo $item['link']['url']?>"><?php echo $item['link']['title']; ?></a>
        <?php } ?>
    </div>
</div>

