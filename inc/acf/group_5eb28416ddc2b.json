{"key": "group_5eb28416ddc2b", "title": "Tapahtumien esitykset", "fields": [{"key": "field_5eb2a31822022", "label": "Tapahtuman päivämäärä", "name": "date", "type": "date_picker", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "display_format": "d/m/Y", "return_format": "d.m.Y", "first_day": 1}, {"key": "field_5eb28460de3a5", "label": "<PERSON><PERSON><PERSON>", "name": "recording_link", "type": "url", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": ""}, {"key": "field_5eb2848cde3a6", "label": "Tall<PERSON><PERSON>", "name": "recording_title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "<PERSON><PERSON> tall<PERSON>", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5eb285549cf9a", "label": "<PERSON><PERSON>", "name": "list_title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Tapahtuman esitykset pdf-muodossa:", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5eb285709cf9b", "label": "Esitykset", "name": "presentations", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "", "sub_fields": [{"key": "field_5eb285869cf9c", "label": "<PERSON><PERSON><PERSON><PERSON>mi", "name": "presentation_name", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5eb2859f9cf9d", "label": "Esittäjä", "name": "presenter_name", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5eb285dc9cf9e", "label": "Esittäjän kuvaus", "name": "presenter_title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5eb285f29cf9f", "label": "Tiedosto", "name": "file", "type": "file", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "url", "library": "all", "min_size": "", "max_size": "", "mime_types": ""}, {"key": "field_5eb286139cfa0", "label": "<PERSON><PERSON>", "name": "file_link", "type": "url", "instructions": "<PERSON><PERSON> on liian suuri <PERSON>, voit viitata siihen tässä ulkopuolisella linkillä.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": ""}]}], "location": [[{"param": "post_type", "operator": "==", "value": "event-presentation"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": 1, "description": "", "modified": 1588765619}