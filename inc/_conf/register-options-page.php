<?php

/**
 * ACF Options Page
 */

if( function_exists('acf_add_options_page') ) {

  acf_add_options_page(array(
    'page_title' 	=> 'General Settings',
    'menu_title'	=> 'Settings',
    'menu_slug' 	=> 'general-settings',
    'capability'	=> 'edit_posts',
    'redirect'		=> false
  ));

  acf_add_options_sub_page(array(
    'page_title' 	=> 'Header Settings',
    'menu_title'	=> 'Header',
    'parent_slug'	=> 'general-settings',
  ));

  acf_add_options_sub_page(array(
    'page_title' 	=> 'Footer Settings',
    'menu_title'	=> 'Footer',
    'parent_slug'	=> 'general-settings',
  ));


  acf_add_options_page(array(
    'page_title' 	=> 'Uutiskirjeiden tekstit',
    'menu_title'	=> 'Uutiskirjeiden tekstit',
    'menu_slug' 	=> 'newsletter-settings',
    'capability'	=> 'edit_posts',
    'redirect'		=> 'Tavallinen'
  ));

  acf_add_options_sub_page(array(
    'page_title' 	=> 'Globaali uutiskirje',
    'menu_title'	=> 'Tavallinen',
    'parent_slug'	=> 'newsletter-settings',
  ));

  acf_add_options_sub_page(array(
    'page_title' 	=> 'Ajankohtaista -uutiskirje',
    'menu_title'	=> 'Ajankohtaista',
    'parent_slug'	=> 'newsletter-settings',
  ));

}

//
// Language Specific Options
// Translatable options specific languages. e.g., social profiles links
//

/*   $languages = array( 'it', 'en' );

foreach ( $languages as $lang ) {
  acf_add_options_sub_page( array(
    'page_title' => 'Options (' . strtoupper( $lang ) . ')',
    'menu_title' => __('Options (' . strtoupper( $lang ) . ')', 'text-domain'),
    'menu_slug'  => "options-${lang}",
    'post_id'    => $lang,
    'parent'     => $parent['menu_slug']
  ) );
} */
