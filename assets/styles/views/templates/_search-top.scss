/* ==========================================================================
 # Search-top 2022 - top accordion styles - also text input in search page (in header)
========================================================================== */

/* top site search toolbar : */
@media screen and (min-width: 783px) {
  #primary-navigation .primary-navigation__items {
    // float: left;
  }
}
.search-menu-button {
  font-size: 16px;
  display: flex;
  align-items: center;
  text-decoration: none;
  color: $white;
  text-transform: uppercase;
  position: relative;
  font-weight: $ws-semibold;
  padding: 0 4rem;
  @include breakpoint($sub-m) {
    padding: 0 1.5rem;
  }
}


.search-top-container {
  float: right;
  display: flex;
  align-items: center;
}

/*hide in search results page */
body.search .search-top-container {
  display: none;
  margin: 0;
}

.search-top-container:not(.search-open) {
  //padding-right: 10px;
  .search-menu-button {
    //margin-top: 10px;
  }
  .search-menu-button:after {
    content: "\f002";
    font-family: "Font Awesome 6 Sharp";
    font-size: 18px;
    margin-left: 10px;
    margin-top: -4px;
    color: #fff;
    display: inline-block;
   //  position: absolute;
  }
  .search-top-container--inner {
    display: none;
    margin: 0;
  }
  .search-top-container-form {
    display: none;
  }
}

.search-top-container.search-open {
  padding-right: 0px;
  position: initial;
  .search-menu-button:after {
    content: "\f00d";
    font-family: "Font Awesome 6 Sharp";
    font-size: 22px;
    margin-left: 10px;
    color: #fff;
    display: inline-block;
   //  position: absolute;
  }
  .search-top-container--inner {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    right: 0;
    z-index: 20;
    // height: 267px;
    height: max-content;
    background-color: $light-blue-25;
    margin-top: 44px;
    color: #fff;
    padding: 1.25rem 0rem 0rem 0rem;
    border-bottom: 2px solid $light-blue;
    
    // animated dropdown
    animation: growDown 250ms ease-in-out forwards;
    transform-origin: top center;
  }

  .search-top-container--inner--container {
    position: relative;
    display: block;
    &--form-container {
      width: 450px;
      display: block;
      margin-left: auto;
      margin-right: auto;
      .helper-links {
        display: flex;
        flex-direction: column;
        margin: 1.5rem 0;
        align-items: center;
        .helper-link {
          @include textlink;
        }
      }
      @include breakpoint($sub-s) {
        width: 100%;
        margin-left: auto;
        margin-right: auto;
      }
    }
  }
}

/* top site search toolbar end */



/* top search form */

.search-top-title {
  display: block;
  font-size: 28px;
  font-weight: $ws-bold;
  margin-bottom: 24px;
  text-align: center;
}


.search-top-container--inner {
  .search-top-title {
    color: $primary;
  }
  &--container {
    display: block;
    width: 100%;
    margin: 20px auto;
    padding: 0 $gap;
  }
}

.search-top-container-form {
  fieldset {
    //margin: auto !important;
    width: 100%;
    position: relative;
  }
  input[type="text"],
  button {
    height: 50px;
    // line-height: 50px;
    line-height: normal;
    border: none;
    font-size: 15px;
  }

  input[type="text"] {
    @include textinput;
    width: 100%;
    color: #000;
    padding-right: 50px;
  }

  button {
    @include transition;
    width: 100%;
    color: #fff;
    text-align: center;
    outline: none;
    font-weight: $ws-semibold;
    text-transform: uppercase;
    margin-top: 1rem;
    background: $primary;
    &:hover {
      background: $link-use;
    }
  }
  button:focus {
    outline: 2px dotted $black;
  }
  .main-search-input-clear, .top-search-input-clear {
    display: block;
    text-align: center;
    width: 30px;
    position: absolute;
    right: 1rem;
    top: 4px;
    z-index: 8;
    overflow: visible;
    background-color: transparent;
    color: $gray-text;
    &:after {
      content: "\f00d";
      font-family: 'Font Awesome 6 Sharp';
      font-size: 26px
    }
    cursor: pointer;
    &:hover {
      filter: opacity(1);
    }
  }


  @media screen and (max-width: 480px) {
    .main-search-input::placeholder {
        /*
        background: -webkit-linear-gradient(left, red , yellow);
        background: -o-linear-gradient(right, red, yellow);
        background: -moz-linear-gradient(right, red, yellow);
        background: linear-gradient(to right, red , yellow);
        */
        background: rgb(143,152,161);
        background: -moz-linear-gradient(90deg, rgba(143,152,161,1) 0%, rgba(143,152,161,1) 72%, rgba(255,255,255,1) 91%);
        background: -webkit-linear-gradient(90deg, rgba(143,152,161,1) 0%, rgba(143,152,161,1) 72%, rgba(255,255,255,1) 91%);
        background: linear-gradient(90deg, rgba(143,152,161,1) 0%, rgba(143,152,161,1) 72%, rgba(255,255,255,1) 91%);


       -webkit-background-clip: text;
       -webkit-text-fill-color: transparent;
    }
  }
}


/* top search form */


/* responsive */
@media screen and (min-width: $menu-visible) {
  .search-top-container-form {
    width: inherit;
    margin-left: auto;
    margin-right: auto;
  }
}

/*@media screen and (min-width: $l) and (max-width: 1349px) {
  .search-top-container:not(.search-open) .search-menu-button,
  .search-top-container.search-open .search-menu-button {
      //overflow: hidden;
      width: 42px;
      color: transparent;
      font-size: 0;
  }
  .search-top-container:not(.search-open) .search-menu-button:after {
    margin-left: -2px;
  }
  .search-top-container.search-open .search-menu-button:after {
    margin-left: -7px;
  }
}*/


@media screen and (max-width: 1189px) {
  .search-top-container:not(.search-open) .search-menu-button {
      // display: none;
  }
}

@media screen and (max-width: $xl) {
  .search-top-container-form {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    .search-top-container-form {
      input[type="text"] {
        width: 100%;
      }
    }
  }
}
