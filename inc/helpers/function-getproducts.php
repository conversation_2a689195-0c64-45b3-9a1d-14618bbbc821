<?php

/**
 * Get products
 *
 * @return object
 */
if (! function_exists('kauppakamari_get_products')) {
    function kauppakamari_get_products()
    {
        return; //not working anymore
        $response = wp_remote_get('https://kauppakamarikauppa.fi/api/rest/productsets');

        $json_path = ABSPATH . "/wp-content/products.json";
        file_put_contents($json_path, $response['body']);

        return $response['body'];
    }
}

/* Automated Import
----------------------------------------------- */

function kauppakamari_cron_schedules($schedules){
  if(!isset($schedules["5min"])){
      $schedules["5min"] = array(
          'interval' => 5*60,
          'display' => __('Once every 5 minutes'));
  }
  if(!isset($schedules["30min"])){
      $schedules["30min"] = array(
          'interval' => 30*60,
          'display' => __('Once every 30 minutes'));
  }
  if(!isset($schedules["yearly"])){
    $schedules["yearly"] = array(
        'interval' => 60 * 60 * 24 * 365,
        'display' => __('Once every year'));
}
  return $schedules;
}
add_filter('cron_schedules','kauppakamari_cron_schedules');

// Run import once a hour
/*
if (! wp_next_scheduled('get_products_cron')) {
  wp_schedule_event(time(), '5min', 'get_products_cron');
}
*/

//add_action('get_products_cron', 'kauppakamari_get_products');
//add_action('init', 'kauppakamari_get_products');


