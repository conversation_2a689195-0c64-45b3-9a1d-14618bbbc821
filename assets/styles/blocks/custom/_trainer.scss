.block-trainer {
  box-shadow: 0 0 13px -5px rgba(0,0,0,.4);
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  &__content {
    width: 100%
  }
  &__image {
    width: 100%
  }
  &__list {
    width: 100%;
  }
  @include breakpoint($m) {
    &__content {
      width: 35%
    }
    &__image {
      width: 40%
    }
    &__list {
      width: 25%;
    }
  }
  &__content {
    padding: 2.06rem;
    &--name {
      color: $grey-darker;
      display: block;
      text-transform: uppercase;
    }
    &--description {
      display: block;
      font-size: 2.13rem;
      margin-top: 2.06rem;
      font-weight: 700;
      line-height: 2.38rem;
    }
    &--excerpt {
      display: block;
      font-size: 1rem;
      line-height: 1.56rem;
      margin: 2.06rem 0;
    }
    a {
      @include button;
      width: 100%;
      text-align: center;
      border: 1px solid $brand;
      color: $brand;
      background: transparent;
      &:hover {
        color: white;
      }
      &.external {
        background-color: $green;
        border: 1px solid $green;
        color: white;
        &:hover {
          background-color: white;
          color: $green;
        }
      }
      &:hover {
        background-color: white;
        color: $primary;
      }
    }
  }
  &__image {
    img {
      height: 100%;
      object-fit: cover;
    }
  }
  &__list {
    padding: 2.06rem;
    background-color: $grey-lighter;
    &--title {
      color: $grey-darker;
      text-transform: uppercase;
      width: 100%;
      display: block;
      margin-bottom: .875rem;
      padding-bottom: .875rem;
      border-bottom: 1px solid $grey-dark;
    }
  }
}
