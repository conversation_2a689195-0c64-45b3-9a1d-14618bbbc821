<?php

$labels = array(
    'name'                  => _x('Työpaikkailmoitukset', 'Post Type General Name', 'kauppakamari'),
    'singular_name'         => _x('Työpaikkailmoitus', 'Post Type Singular Name', 'kauppakamari'),
    'menu_name'             => __('Työpaikkailmoitukset', 'kauppakamari'),
    'name_admin_bar'        => __('Työpaikkailmoitukset', 'kauppakamari'),
    'archives'              => __('Item Archives', 'kauppakamari'),
    'attributes'            => __('Item Attributes', 'kauppakamari'),
    'parent_item_colon'     => __('Parent Item:', 'kauppakamari'),
    'all_items'             => __('Kaikki', 'kauppakamari'),
    'add_new_item'          => __('Uusi', 'kauppakamari'),
    'add_new'               => __('Uusi', 'kauppakamari'),
    'new_item'              => __('Uusi', 'kauppakamari'),
    'edit_item'             => __('Mu<PERSON>kaa', 'kauppakamari'),
    'update_item'           => __('Päivitä', 'kauppakamari'),
    'view_item'             => __('Katso', 'kauppakamari'),
    'view_items'            => __('Katso', 'kauppakamari'),
    'search_items'          => __('Etsi', 'kauppakamari'),
    'not_found'             => __('Ei löytynyt', 'kauppakamari'),
    'not_found_in_trash'    => __('Not found in Trash', 'kauppakamari'),
    'featured_image'        => __('Featured Image', 'kauppakamari'),
    'set_featured_image'    => __('Set featured image', 'kauppakamari'),
    'remove_featured_image' => __('Remove featured image', 'kauppakamari'),
    'use_featured_image'    => __('Use as featured image', 'kauppakamari'),
    'insert_into_item'      => __('Insert into item', 'kauppakamari'),
    'uploaded_to_this_item' => __('Uploaded to this item', 'kauppakamari'),
    'items_list'            => __('Items list', 'kauppakamari'),
    'items_list_navigation' => __('Items list navigation', 'kauppakamari'),
    'filter_items_list'     => __('Filter items list', 'kauppakamari'),
);

$rewrite = array(
    'slug'                  => 'avoimet-tyopaikat',
    'with_front'            => false,
    'pages'                 => true,
    'feeds'                 => true,
);

$args = array(
    'label'                 => __('Työpaikkailmoitus', 'kauppakamari'),
    'description'           => __('Post Type Description', 'kauppakamari'),
    'labels'                => $labels,
    'supports'              => array( 'title', 'editor', 'thumbnail', 'page-attributes' ),
    'taxonomies'            => array( ),
    'hierarchical'          => true,
    'public'                => true,
    'show_ui'               => true,
    'show_in_menu'          => true,
    'menu_position'         => 5,
    'menu_icon'             => 'dashicons-megaphone',
    'show_in_admin_bar'     => true,
    'show_in_nav_menus'     => true,
    'can_export'            => true,
    'has_archive'           => false,
    'exclude_from_search'   => true,
    'publicly_queryable'    => true,
    'rewrite'               => $rewrite,
    'capability_type'       => 'page',
    'show_in_rest'          => true,
);

register_post_type('job', $args);

// CUSTOM TAXONOMY

//create a custom taxonomy name it "type" for your posts
function create_job_taxonomy() {
    //Events categories
    $labels = array(
        'name' => _x( 'Työilmoituskategoriat', 'taxonomy general name' ),
        'singular_name' => _x( 'Työilmoituskategoria', 'taxonomy singular name' ),
        'search_items' =>  __( 'Etsi Työilmoituskategorioista' ),
        'all_items' => __( 'Kaikki Työilmoituskategoriat' ),
        'parent_item' => __( 'Isäntä' ),
        'parent_item_colon' => __( 'Isäntä:' ),
        'edit_item' => __( 'Muokkaa' ), 
        'update_item' => __( 'Päivitä' ),
        'add_new_item' => __( 'Lisää uusi' ),
        'new_item_name' => __( 'Uuden nimi' ),
        'menu_name' => __( 'Työilmoituskategoriat' ),
    );    

    register_taxonomy('job_category', 'job', array(
        'hierarchical' => true,
        'labels' => $labels,
        'show_ui' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'show_in_rest' => true,
        'rewrite' => array( 'slug' => 'job-category' ),
    ));

    flush_rewrite_rules( false ); // Fixes custom post type pages randomly throwing 404
}

add_action( 'init', 'create_job_taxonomy', 0 );

function validateJobArray($candidate) {
    return is_array($candidate) && count($candidate) > 0;
}
/*
function after_job_loop_data($data, $wp_query, $args) {
    // Getting meta fields

    $id = $wp_query->post->ID;
    
    $meta = array();

    foreach($args['meta_query'] as $query) {
        if (!is_array($query) || !array_key_exists('key', $query)) continue;
        break;
    }

    $start = get_post_meta($id, 'starting_date');
    $meta['starting_date'] = validateJobArray($start) ? $start[0] : null;

    $end = get_post_meta($id, 'ends_date');
    $meta['ends_date'] = validateJobArray($end) ? $end[0] : null;

    // Store meta to own folder in $data (response data)
    $data['meta'][$id] = $meta;

    return $data;
}
add_filter( 'wp_query_route_to_rest_api_after_loop_data', 'after_job_loop_data', 10, 3 );
*/

// Increase max post size to unlimited
function my_max_posts_per_page2($max) {
    return -1; // Default 50
}
// add_filter( 'wp_query_route_to_rest_api_max_posts_per_page', 'my_max_posts_per_page2' );

add_action('rest_api_init', 'init_job_posts');
function init_job_posts()
{
    register_rest_route('api/v1', '/cron/jobs_posts_data/', array(
        'methods' => 'GET',
        'callback' => 'cron_job_jobposts_callback',
        'permission_callback' => '__return_true'
    ));

    register_rest_route('api/v1', '/cron/jobs_posts_data_json/', array(
        'methods' => 'GET',
        'callback' => 'cron_job_jobs_posts_data_json_callback',
        'permission_callback' => '__return_true'
    ));

}

function cron_job_jobposts_callback() {
    // Require post functionality
    require_once ABSPATH . '/wp-admin/includes/post.php';

    // Jobs api url (Laura)
    $url = 'https://kauppakamari.rekrytointi.com/paikat/?o=A_RSS&jgid=1';

    $arrContextOptions = array(
		"ssl" => array(
			"verify_peer"=>false,
			"verify_peer_name"=>false,
		),
	);	
	$response = file_get_contents($url, false, stream_context_create($arrContextOptions));
    $response = str_replace("laura:", "laura", $response);

    $jobs = new SimpleXMLElement($response);
    $jobs = simplexml_load_string($response, 'SimpleXMLElement', LIBXML_NOCDATA);
    
    $jobs = $jobs->channel; // Singular job items are under channel->item

    //remove old events
    $removablePosts = get_posts(array(
        'numberposts'   => -1,
        'post_type'     => 'job',
        'post_status'   => 'any',
    ));

    foreach ($removablePosts as $removable) {
        // Don't delete posts with no_override = true
        $no_override = get_field('no_override', $removable); 

        if(!$no_override) wp_delete_post($removable->ID, true);
    }

    //Old events removed, add new ones
    foreach ($jobs->item as $item) {
        //echo print_r($item); // For troubleshooting / testing purposes

        // Don't add posts with existing title
        if(post_exists($item->title, '', '', 'job')) continue;
        
        $categories = [];

        // Create categories
        $lauracats = [];
        $lauracats[] = $item->lauracommon_category;

        foreach($lauracats as $cat) {
            $cat = str_replace("&", "ja", $cat); // WP doesn't support special characters on term names
            $termInsert = wp_insert_term($cat, "job_category");

            $termID = null;

            if (is_a($termInsert, "WP_Error") && isset($termInsert->error_data['term_exists'])) {
                $termID = $termInsert->error_data['term_exists'];
            }
            // Term doesn't exists, let's create it 
            else if (is_array($termInsert) && isset($termInsert['term_id'])) {
                $termID = $termInsert['term_id'];
            } 
            if (!empty($termID)) $categories[] = $termID;
        }

        // Metadata for the created post
        $postLink = strval($item->lauraform);
        $postDescription = strval($item->description);
        $postBody = strval($item->lauradescription);
        $postWorktime = strval($item->lauracommon_worktime);
        $postJobtype = strval($item->lauracommon_type);

        // all about locations
        $locationData = array(
            'city' => isset($item->lauracommon_job_city) ? strval($item->lauracommon_job_city) : null,
            'address' => isset($item->lauracommon_job_address) ? strval($item->lauracommon_job_address) : null,
            'postal_code' => isset($item->lauracommon_job_postal_code) ? strval($item->lauracommon_job_postal_code) : null,
            'region' => isset($item->lauracommon_job_region) ? strval($item->lauracommon_job_region) : null,
        );

        if( $item->pubDate ) {
            $publishDate_raw = new DateTime($item->pubDate);
               $publishDate = $publishDate_raw->format('d.m.Y');
        } else {
            $publishDate = '-';
        }

        if( $item->laurastartdate) {
            $startDate_raw = new DateTime($item->laurastartdate);
                $startDate = $startDate_raw->format('d.m.Y');
        } else {
            $startDate = '-';
        }

        if( $item->lauraenddate ) {
            $endDate_raw = new DateTime($item->lauraenddate);
                $endDate = $endDate_raw->format('d.m.Y');
        } else {
            $endDate = '-';
        }

        $data = array(
            'post_title' => $item->title,
            'post_type' => 'job',
            'post_status' => 'publish',
            'meta_input' => array(
                'dates' => array(
                    'start_date' => isset($startDate) ? $startDate : '-',
                    'end_date' => isset($endDate) ? $endDate : '-',
                    'publish_date' => isset($publishDate) ? $publishDate : '-',
                ),
                'locations' => $locationData,
                'description' => $postDescription,
                'origin_link' => $postLink,
                'the_content' => $postBody,
                'worktime' => $postWorktime,
                'type' => isset($postJobtype) ? $postJobtype : '-',
                'categories' => $categories,
            ),
        );

        $new = wp_insert_post($data);

        // Categories only manually for now!
        wp_set_post_terms($new, $categories, "job_category");

        // ACF fields
        if (get_post_status($new) == 'publish') {
            $post = get_post($new);
            update_field('job_body', $post->the_content, $post);    // Main content into acf wysiwyg field
            update_field('hide_newsletter', true, $post);           // Hide newsletter
            update_field('job_time', $post->worktime, $post);       // Working time
        }
    }
}

function cron_job_jobs_posts_data_json_callback() {
    global $wpdb;

    $jobCategories = get_terms(array(
        'taxonomy'  => 'job_category',
        'hide_empty' => false,
    ));

    $json = [];
    $json['job_categories'] = $jobCategories;

    return $json;
}

if (class_exists("WP_CLI")) {
    WP_CLI::add_command( "update_jobs_posts", "cron_job_jobposts_callback" );
    WP_CLI::add_command( "update_job_posts_json", "cron_job_jobs_posts_data_json_callback" );
}


add_action( 'kamari_rekry_update', 'cron_job_jobposts_callback' );

/**
 * WP Cron config
 * https://seravo.com/docs/configuration/wordpress-cron/
 */

add_action('kamari_cron_hook', 'kamari_cron_exec');

// Cron function
function kamari_cron_exec() {
    do_action('kamari_rekry_update'); // Update courses from Moodle API
}
 
// Init cron event if not present
if (!wp_next_scheduled('kamari_cron_hook')) {
    $timestamp = strtotime('today midnight');
    // Schedule cron event at midnight, then recurring each day.
    wp_schedule_event($timestamp, 'hourly', 'kamari_cron_hook');
}
 
// (Not in use) Remove scheduled cron event
// function remove_kamari_cron_job() {
//     $timestamp = wp_next_scheduled( 'kamari_cron_hook' );
//     wp_unschedule_event( $timestamp, 'kamari_cron_hook' );
// }
 