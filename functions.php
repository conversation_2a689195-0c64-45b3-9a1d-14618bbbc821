<?php

/**
 * Include PHP files, nothing else.
 *
 * All the functions, hooks and setup should be on their own
 * filer organized at /inc/. The names of files should describe
 * what the file does as following:
 *
 * `register-`  configures new settings and assets
 * `setup-`     configures existing settings and assets
 * `function-`  adds functions to be used in templates
 *
 * @package kauppakamari
 */


/**
 * Configuration
 */
require_once 'inc/_conf/register-assets.php';
require_once 'inc/_conf/register-blocks.php';
require_once 'inc/_conf/register-image-sizes.php';
require_once 'inc/_conf/register-localization.php';
require_once 'inc/_conf/register-menus.php';
require_once 'inc/_conf/register-options-page.php';
require_once 'inc/_conf/register-acf-json.php';
require_once 'inc/_conf/admin-sidebar.php';

/**
 * Blocks
 */

require_once 'inc/blocks/blocks.php';

/**
 * Editor
 */
require_once 'inc/editor/setup-classic-editor.php';
require_once 'inc/editor/setup-gutenberg.php';
require_once 'inc/editor/setup-theme-support.php';

/**
 * Forms
 */
require_once 'inc/forms/function-search-form.php';

/**
 * Helpers
 */
require_once 'inc/helpers/function-dates.php';
require_once 'inc/helpers/function-entry-footer.php';
require_once 'inc/helpers/function-hardcoded-ids.php';
require_once 'inc/helpers/function-html-attributes.php';
require_once 'inc/helpers/function-last-edited.php';
require_once 'inc/helpers/function-titles.php';
require_once 'inc/helpers/function-globals.php';
require_once 'inc/helpers/setup-fallbacks.php';
require_once 'inc/helpers/function-featured-loop.php';
require_once 'inc/helpers/function-entry-meta.php';
require_once 'inc/helpers/function-excerpt.php';


/**
 * Media
 */
require_once 'inc/media/function-image.php';
require_once 'inc/media/function-svg.php';
require_once 'inc/media/function-sidebar-news.php';

/**
 * Navigation
 */
require_once 'inc/navigation/function-menu-toggle.php';
require_once 'inc/navigation/function-numeric-posts-nav.php';
require_once 'inc/navigation/function-share-buttons.php';
require_once 'inc/navigation/function-sub-pages-nav.php';
require_once 'inc/navigation/setup-menu-hooks.php';


/**
 * Check if site is kauppakamarinkoulutus
 * true : koulutus
 * false : mainsite
 */

function is_koulutus()
{
    if (get_current_blog_id() === kauppakamari_get_hardcoded_id('koulutus')) {
        return true;
    } else {
        return false;
    }
}

require_once 'inc/helpers/function-getproducts.php';
require_once 'inc/helpers/function-getverkkoproducts.php';
require_once 'inc/helpers/function-showproducts.php';
require_once 'inc/kamari/contact-ajax.php';

/**
 * KOULUTUS
 */

if (is_koulutus()) {
    require_once 'inc/koulutus/trainer.php';
    require_once 'inc/koulutus/experiences.php';
    require_once 'inc/koulutus/training-type.php';
    require_once 'inc/koulutus/clubs.php';
    require_once 'inc/koulutus/clubs-ajax.php';
} else {

    /**
     * KAMARI
     */

    require_once 'inc/kamari/article-type.php';
    require_once 'inc/kamari/category-rename.php';
    require_once 'inc/kamari/nyt-ajax.php';
    require_once 'inc/kamari/nyt-writer-ajax.php';
    require_once 'inc/kamari/people.php';
    //require_once 'inc/kamari/content-pages.php';
    require_once 'inc/kamari/people-cat.php';
    require_once 'inc/kamari/cpt-releases.php';
    require_once 'inc/helpers/function-showverkkoproducts.php';
    require_once 'inc/kamari/event-presentations.php';
    require_once 'inc/kamari/archive-functions.php';
    require_once 'inc/kamari/search-functions.php';
    //Shopify
    require_once 'inc/kamari/shopify-events.php';
    //Job posts
    require_once 'inc/kamari/job-posts.php';
}


/* function kauppakamari_template_redirect() {
  if(!is_user_logged_in()) {
    if(strpos($_SERVER['REQUEST_URI'], '/en/') !== false) {
      $url = site_url( '/coming-soon' );
      wp_safe_redirect( $url, 301 );
      exit();
    }
  }
}

add_action( 'template_redirect', 'kauppakamari_template_redirect' ); */

//CF7 enum fix
remove_action('wpcf7_swv_create_schema', 'wpcf7_swv_add_select_enum_rules', 20, 2);
remove_action('wpcf7_swv_create_schema', 'wpcf7_swv_add_radio_enum_rules', 20, 2);
remove_action('wpcf7_swv_create_schema', 'wpcf7_swv_add_checkbox_enum_rules', 20, 2);

/**
 * Init Twitter
 */
add_filter('dude-twitter-feed/oauth_consumer_key', function () {
    return '*************************';
});
add_filter('dude-twitter-feed/oauth_consumer_secret', function () {
    return 'a8xCwa8Jd4zO8qGhzLxB10081oVxjjckmd2k1vGyMjRQQTWv14';
});
add_filter('dude-twitter-feed/oauth_access_token', function () {
    return '814720729-3XWPlW8W86Jqp27764lVGyYdsuHui8RsSySVmV7b';
});
add_filter('dude-twitter-feed/oauth_access_token_secret', function () {
    return 'uei55HdIdyw8SONY7Ma85xra7u5kra2iK4xyBmUkDwtH1';
});

/**
 * Tweet settings
 */
add_filter('dude-twitter-feed/user_tweets_parameters', function ($args) {
    $args['count'] = 5;

    return $args;
});

/**
 * Adds a date filter to the search query.
 *
 * @param WP_Query $query The query object.
 *
 * @return WP_Query The modified query object.
 */
function kauppakamari_date_filter($query)
{
    //error_log('relevanssi_modify_wp_query date filter added');
    if (is_search() && !empty($_GET['search_date'])) {
        $query->set('orderby', 'date');
        $query->set('order', esc_attr(@$_GET['search_date']));
        return $query;
    } else {
        return $query;
    }
}

//need to be disabled 2022 for new search site. Still enabled in EN site
if (isset($_GET['search_date']) && !empty($_GET['search_date'])) {
    add_filter('relevanssi_modify_wp_query', 'kauppakamari_date_filter');
}


/* 202109 - disable annoying welcome tips in edit new page! this might be irrelevant in future wp versions */

add_action('admin_head', 'dwat_hide_popovers');

function dwat_hide_popovers()
{
?>
    <style>
        .wp-admin .components-popover.nux-dot-tip {
            display: none !important;
        }
    </style>

    <script>
        if (jQuery) {
            jQuery(window).load(function() {
                wp.data && wp.data.select('core/edit-post').isFeatureActive('welcomeGuide') && wp.data.dispatch('core/edit-post').toggleFeature('welcomeGuide');
                wp.data && wp.data.select('core/edit-post').isFeatureActive('fullscreenMode') && wp.data.dispatch('core/edit-post').toggleFeature('fullscreenMode');
            });
        }
    </script>
    <?php
}

/**
 * 2023 Allow svg files to be uploaded into media
 */
add_filter('wp_check_filetype_and_ext', function ($data, $file, $filename, $mimes) {

    global $wp_version;
    if ($wp_version !== '4.7.1') {
        return $data;
    }

    $filetype = wp_check_filetype($filename, $mimes);

    return [
        'ext'             => $filetype['ext'],
        'type'            => $filetype['type'],
        'proper_filename' => $data['proper_filename']
    ];
}, 10, 4);

function cc_mime_types($mimes)
{
    $mimes['svg'] = 'image/svg+xml';
    return $mimes;
}
add_filter('upload_mimes', 'cc_mime_types');

function fix_svg()
{
    echo '<style type="text/css">
        .attachment-266x266, .thumbnail img {
             width: 100% !important;
             height: auto !important;
        }
        </style>';
}
add_action('admin_head', 'fix_svg');
//


/*
  Override Yoast SEO Breadcrumb Trail
*/

add_filter('wpseo_breadcrumb_links', 'chamber_override_yoast_breadcrumb_trail');
function chamber_override_yoast_breadcrumb_trail($links)
{
    global $post;

    if (is_single()) {
        if ($post->post_type == 'job') {
            $breadcrumb[] = array(
                'url' => get_site_url(),
                'text' => 'Etusivu',
            );

            $breadcrumb[] = array(
                'url' => get_site_url() . '/tietoa-meista/',
                'text' => 'Tietoa meistä',
            );

            $breadcrumb[] = array(
                'url' => get_site_url() . '/meille-toihin/',
                'text' => 'Meille töihin',
            );

            $breadcrumb[] = array(
                'url' => '/avoimet-tyopaikat',
                'text' => 'Avoimet työpaikat',
            );
        } else {
            $breadcrumb[] = array(
                'url' => get_site_url(),
                'text' => 'Etusivu',
            );

            $breadcrumb[] = array(
                'url' => get_site_url() . '/ajankohtaista/',
                'text' => 'Ajankohtaista',
            );

            if ($post->post_type == 'release') {
                $breadcrumb[] = array(
                    'url' => '/artikkelityyppi/tiedotteet/',
                    'text' => 'Tiedotteet',
                );
            } else {
                //error_log(print_r($post, true));
                //get post taxonomy url:
                $article_type_term = get_article_type_term_by_post_id($post->ID);
                $slug = isset($article_type_term->slug) ? $article_type_term->slug : "kaikki";
                $term_name = isset($article_type_term->name) ? $article_type_term->name : "Kaikki ajankohtaiset";
                $breadcrumb[] = array(
                    'url' => '/artikkelityyppi/' . $slug,
                    'text' => $term_name,
                );
            }
        }

        array_splice($links, 0, -1, $breadcrumb);
    } else {
        global $wp;
        $current_url = home_url(add_query_arg(array(), $wp->request));
        $contains = str_contains($current_url, "artikkelityyppi/kaikki");

        if (is_archive() || $contains) {

            $breadcrumb[] = array(
                'url' => get_site_url(),
                'text' => 'Etusivu',
            );

            $breadcrumb[] = array(
                'url' => get_site_url() . '/ajankohtaista/',
                'text' => 'Ajankohtaista',
            );


            if ($contains) {
                $breadcrumb[] = array(
                    'url' => '/artikkelityyppi/uutiset/',
                    'text' => "Kaikki ajankohtaiset",
                );
                return $breadcrumb;
            }

            //@@todo, get article_type if given category

            /*
          //not needed
          $article_type = sanitize_text_field(get_query_var('article_type'));
          if($article_type) {
            $breadcrumb[] = array(
              'url' => '/artikkelityyppi/uutiset/',
              'text' => $article_type,
            );
          }
          */
            array_splice($links, 0, -1, $breadcrumb);
        }
    }

    return $links;
}

add_filter('single_template', function ($orig) {
    global $post;
    $post_type = $post->post_type;
    $base_name = 'single-' . $post_type . '.php';
    $template = locate_template($base_name);
    if ($template && !empty($template)) return $template;
    return $orig;
});

add_action('restrict_manage_posts', function () {
    global $typenow;
    if ($typenow === 'page') {
        $depth = isset($_GET['page_depth']) ? intval($_GET['page_depth']) : '';
    ?>
        <select name="page_depth">
            <option value="">Hierarkian tasot</option>
            <option value="1" <?php selected($depth, 1); ?>>Päätaso (1)</option>
            <option value="2" <?php selected($depth, 2); ?>>Tasot 1 & 2</option>
            <option value="3" <?php selected($depth, 3); ?>>Tasot 1, 2 & 3</option>
        </select>
<?php
    }
});

add_action('pre_get_posts', function ($query) {
    if (is_admin() && $query->is_main_query() && $query->get('post_type') === 'page' && isset($_GET['page_depth']) && $_GET['page_depth'] !== '') {
        global $wpdb;
        $depth = intval($_GET['page_depth']);
        if ($depth < 1) return;

        // Get IDs of pages up to the selected depth
        $page_ids = [];
        if ($depth >= 1) {
            // Level 1: parent = 0
            $level1_ids = $wpdb->get_col("SELECT ID FROM {$wpdb->posts} WHERE post_type='page' AND post_parent=0 AND post_status IN ('publish', 'draft', 'private')");
            $page_ids = array_merge($page_ids, $level1_ids);
        }
        if ($depth >= 2 && !empty($level1_ids)) {
            // Level 2: children of level 1
            $level1_ids_csv = implode(',', array_map('intval', $level1_ids));
            $level2_ids = $wpdb->get_col("SELECT ID FROM {$wpdb->posts} WHERE post_type='page' AND post_parent IN ($level1_ids_csv) AND post_status IN ('publish', 'draft', 'private')");
            $page_ids = array_merge($page_ids, $level2_ids);
        }
        if ($depth >= 3 && !empty($level2_ids)) {
            // Level 3: children of level 2
            $level2_ids_csv = implode(',', array_map('intval', $level2_ids));
            $level3_ids = $wpdb->get_col("SELECT ID FROM {$wpdb->posts} WHERE post_type='page' AND post_parent IN ($level2_ids_csv) AND post_status IN ('publish', 'draft', 'private')");
            $page_ids = array_merge($page_ids, $level3_ids);
        }

        if (!empty($page_ids)) {
            $query->set('post__in', $page_ids);
        } else {
            $query->set('post__in', array(0));
        }
    }
});

/**
 * Enqueue a custom script in the WordPress admin to show a confirmation dialog 
 * before moving a post/page to trash from the list table.
 *
 * This ensures users cannot accidentally delete content without an extra step.
 */
function wp_confirm_trash_admin_script($hook) {
    // Define which admin screens (hooks) should load this script.
    // Each entry corresponds to an "Edit" list table screen for a specific post type.
    $allowed_hooks = array(
        'edit.php',
        'edit.php?post_type=page',
        'edit.php?post_type=release',
        'edit.php?post_type=event',
        'edit.php?post_type=event-presentation',
        'edit.php?post_type=people',
        'edit.php?post_type=k3_person_gallery',
        'edit.php?post_type=job',
    );

    // Bail out early if the current admin screen is not in the allowed list
    if ( ! in_array( $hook, $allowed_hooks, true ) ) {
        return;
    }

    // Enqueue the confirmation script.
    // - Depends on jQuery
    // - Loaded in the footer (true as the last parameter)
    wp_enqueue_script(
        'wp-confirm-trash',
        get_stylesheet_directory_uri() . '/assets/scripts/components/wp-confirm-trash.js',
        array('jquery'),
        '1.0',
        true
    );
}
// Hook into admin_enqueue_scripts so the script is available only in admin context
add_action('admin_enqueue_scripts', 'wp_confirm_trash_admin_script');
