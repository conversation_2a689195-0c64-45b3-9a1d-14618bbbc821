/* ==========================================================================
 # Table block
========================================================================== */

/* Front-end and back-end
----------------------------------------------- */

.editor-styles-wrapper .wp-block-table,
.wysiwyg .wp-block-table {
  border: 1px solid $grey;
  font-size: .875rem;
  @include breakpoint($s) {
    font-size: 1rem;
  }
  margin: $gap auto;
  width: 100%;

  &--responsive {
    display: block;
    overflow-x: auto;
    // scroll indicator, @see https://www.smashingmagazine.com/2019/01/table-design-patterns-web/#style-the-scroll
    background:
        linear-gradient(to right, $white 30%, rgba(255,255,255,0)),
        linear-gradient(to right, rgba(255,255,255,0), $white 70%) 0 100%,
        radial-gradient(farthest-side at 0% 50%, rgba(0,0,0,.2), rgba(0,0,0,0)),
        radial-gradient(farthest-side at 100% 50%, rgba(0,0,0,.2), rgba(0,0,0,0)) 0 100%;
    background-repeat: no-repeat;
    background-color: $white;
    background-size: 2.5rem 100%, 2.5rem 100%, .875rem 100%, .875rem 100%;
    background-position: 0 0, 100%, 0 0, 100%;
    background-attachment: local, local, scroll, scroll;
    table {
      min-width: 100%;
    }
  }

  &.alignfull {
    margin-left: auto;
    margin-right: auto;
    td {
      @include breakpoint($l) {
        padding: $gap;
      }
    }
  }

  &.alignleft,
  &.alignright {
    @include breakpoint($content-fits) {
      max-width: $content-default-width-half;
    }
  }

  thead {
    background: $primary;
    color: $white;
  }

  td {
    border: 0;
    border-left: 1px solid $grey;
    min-width: 8rem;  // for responsive tables
    padding: $gap-small $gap-small;
  }

  tr {
    border-bottom: 1px solid $grey;
    &:last-of-type {
      border-bottom: 0;
    }
  }

  &.has-fixed-layout {
    &, table {
      table-layout: fixed;
    }
  }

  &.is-style-stripes {
    tr:nth-child(even) {
      background-color: $grey-light;
    }
    th, td {
      border-bottom: 0;
    }
  }

}

/* Only front-end
----------------------------------------------- */

.wysiwyg .wp-block-table {

}

/* Only back-end
----------------------------------------------- */

.editor-styles-wrapper .wp-block-table {

}
