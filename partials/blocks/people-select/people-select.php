<?php

/**
 * People
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-people-select-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-people-select';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$items = get_field('people-select_rows');

?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">

  <?php 
    $title_size = get_field('title-size');
    switch ($title_size) {
      case 'size-h2':
        echo '<h2>';
        break;
      case 'size-h3':
        echo '<h3>';
        break;
      default:
        echo '<h2>';
        break;
    }

    echo get_field('title') ?: "Otsikko";

    switch ($title_size) {
      case 'size-h2':
        echo '</h2>';
        break;
      case 'size-h3':
        echo '</h3>';
        break;
      default:
        echo '</h2>';
        break;
    }
  ?>
  
  <?php
    global $post;
    foreach($items as $post) {

      setup_postdata($post); ?>

      <div class="block-people-select__item">

        <?php if (get_the_post_thumbnail_url(get_the_ID(),'medium')) : ?>
          <div class="block-people-select__img" style="background-image: url(<?php echo get_the_post_thumbnail_url(get_the_ID(),'medium'); ?>)"></div>
        <?php else : ?>
          <div class="block-people-select__img" style="background-image: url(<?php echo get_template_directory_uri() . '/dist/images/person-placeholder.png'; ?>)"></div>
        <?php endif; ?>

        <div class="block-people-select__content">
          <span class="name">
            <?php the_title(); ?>
          </span>
          <span class="title">
          <?php if(get_locale() == 'fi') {
            echo get_field('title', $post->ID);
          } else {
            echo get_field('title_eng', $post->ID);
          } ?>
          </span>
          <span class="content">
            <?php echo get_field('puh', $post->ID); ?> <br>
            <?php if(get_locale() == 'fi') {
              echo get_field('info', $post->ID);
            } else {
              echo get_field('info_eng', $post->ID);
            } ?>
          <span>
        </div>
      </div>
  <?php  }
    wp_reset_postdata();
  ?>

</div>
