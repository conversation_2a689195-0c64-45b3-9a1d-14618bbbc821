<?php

/**
 * Product teasers blocks
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-product-teasers-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-product-teasers';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$items = get_field('product-teasers');

?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?> scrollto">

  <div class="tabs">

    <div class="tabs__list">
    <?php
      foreach($items as $row) { ?>
          <div class="block-product-teasers__item tabs__btn">
            <div class="block-product-teasers__content">
              <span class="title">
                <?php echo $row['title'] ?: "Otsikko"; ; ?>
              </span>
              <span class="content">
                <?php echo $row['content'] ?: "Sisältö"; ?>
              <span>
            </div>

            <?php
            if($icon = $row['img']) {
              echo '<img class="block-product-teasers__image" src="' . $icon['sizes']['medium_large'] . '" alt="' . $row['title'] . '" />';
            }
            ?>
          </div>
      <?php }
    ?>
    </div>
    <?php
    foreach($items as $row) {
      if($row['product_group']['id']) { ?>
      <div class="tabs__content">

        <?php //vd($row); ?>

        <div class="tabs__wrap">
          <div class="content">
          <h4><?php echo $row['product_group']['title']; ?></h4>
          <?php echo $row['product_group']['content']; ?>
          </div>
          <?php
            if($icon = $row['product_group']['icon']) {
              echo '<img src="' . $icon['sizes']['medium'] . '" alt="' . $row['title'] . '" />';
            }
          ?>
          <span class="tabs__content--close"><?php echo kauppakamari_get_svg('close'); ?></span>
          <?php kauppakamari_showproducts((string)$row['product_group']['id']); ?>

          <?php if($row['product_group']['link']) {
              echo '<a href="' . $row['product_group']['link']['url'] . '" >' . $row['product_group']['link']['title'] . '</a>';
            }
          ?>
        </div>
      </div>
    <?php }
    } ?>

  </div>

</div>
