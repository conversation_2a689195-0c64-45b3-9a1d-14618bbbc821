<?php
function kauppakamari_get_writers() {
  $args = [
    'posts_per_page' => -1,
    'post_type' => 'post',
  ];
  $posts = get_posts($args);

  $writers = [];
  foreach ($posts as $post) {
    $writer = get_field('writer_object', $post);
    if ($writer) {
      $writers[] = $writer;
    }

  }

  $writers = array_unique($writers);

  //$writers = kauppakamari_array_unique($writers);

  /*   usort($writers, function($a, $b) {
    return strcmp($a->post_title, $b->post_title);
  }); */

  return $writers;

}

function kauppakamari_array_unique($array, $keep_key_assoc = false){
  $duplicate_keys = array();
  $tmp = array();

  foreach ($array as $key => $val) {
      if (is_object($val)) {
        $val = (array)$val;
      }

      if (!in_array($val, $tmp)) {
        $tmp[] = $val;
      }
      else {
        $duplicate_keys[] = $key;
      }

  }

  foreach ($duplicate_keys as $key) {
    unset($array[$key]);
  }

  return $keep_key_assoc ? $array : array_values($array);
}

