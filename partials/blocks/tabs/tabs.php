<?php

/**
 * Tabs
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-tabs-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-tabs';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$content = get_field('tab_rows');

?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">
 <div class="tabs active_tabs">

  <div class="tabs__list">
  <?php
    foreach($content as $row) { ?>
        <div class="tabs__btn"><?php echo $row['title']; ?></div>
    <?php }
  ?>
  </div>
  <?php
  foreach($content as $row) { ?>
    <div class="tabs__content">
      <div class="tabs__wrap">
      <?php $item_row = $row['item'];
      foreach($item_row as $item) { ?>
        <div class="tabs__column">
          <h3 class="h3"><?php echo $item['title']; ?></h3>
          <?php echo $item['content']; ?>
          <?php if($link = $item['link']) { ?>
            <a href="<?php echo $link['url']; ?>"><?php echo $link['title']; ?></a>
          <?php } ?>
        </div>
        <?php } ?>
       </div>
    </div>
  <?php }
  ?>
  </div>
</div>

