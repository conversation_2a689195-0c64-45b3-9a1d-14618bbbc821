<?php
/**
 * Template part: Teaser-3-cols, single column
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package kauppakamari
 */

?>

<div id="teaser-<?php the_ID(); ?>" <?php post_class('block-news-lift-3__item teaser-new--' . get_post_type()); ?>>

  <?php if (has_post_thumbnail()) : ?>
    <div class="block-news-lift-3__item--teaser__thumbnail">
      <a href="<?php the_permalink(); ?>" tabindex="-1">
        <?php echo kauppakamari_get_image(get_post_thumbnail_id(), 'article'); ?>
      </a>
    </div>
  <?php else : ?>
    <div class="block-news-lift-3__item--teaser__thumbnail">
      <a href="<?php the_permalink(); ?>" tabindex="-1">
        <?php echo kauppakamari_get_image(kauppakamari_get_hardcoded_id('img_placeholder'), 'article'); ?>
      </a>
    </div>
  <?php endif; ?>

  <div class="block-news-lift-3__item--content">

    <?php echo kauppakamari_get_post_meta_new(); ?>
      <?php //the_title(sprintf('<h3 class="teaser__header__title teaser__header__title-new"><a href="%s" rel="bookmark">', esc_url(get_permalink())), '</a></h3>');
      ?>
      <a href="<?php echo get_the_permalink(); ?>">
        <?php
        $col_title = the_title('', '', false);
        ?>
        <h3 class="title"><?php echo $col_title; ?></h3>
      </a>

    <div class="block-news-lift-3__item--summary">
      <?php
      if(strlen($col_title) < 60) {
        $col_ingress = get_field('ingress', get_the_ID());
        if ($col_ingress && strlen($col_ingress) < 200) {
          echo $col_ingress;
        } else {
          echo excerpt(18);
        }
      }
      else if(strlen($col_title) < 90) {
        $col_ingress = get_field('ingress', get_the_ID());
        if ($col_ingress && strlen($col_ingress) < 200) {
          echo $col_ingress;
        } else {
          echo excerpt(16);
        }
      }
      else {
        echo excerpt(12);
      }
      ?>
    </div>

  </div>

</div>
