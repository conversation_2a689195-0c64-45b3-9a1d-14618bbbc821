<?php
/**
 * Template part: Hero
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package kauppakamari
 */

// extra classes
$class = array();

// background
$image = '';
if (is_singular() && has_post_thumbnail()) {
  $image = kauppakamari_get_image(get_post_thumbnail_id(), 'hero', ['lazyload' => 'animated']);
}

if (!empty($image)) {
  $class[] = 'hero--has-background';
} else {
  $class[] = 'hero--no-background';
}

?>

<div class="hero page <?php echo !is_front_page() ?: "page-front "; ?> <?php echo esc_attr(implode(' ', $class)); ?>">

  <?php if (!empty($image)) : ?>
    <div class="hero__background">
      <div class="hero__background__image">
        <?php echo $image; ?>
      </div>
      <div class="hero__background__dimming"></div>
    </div>
  <?php endif; ?>

  <div class="hero__container">

    <div class="hero__container--wrap">
      <?php if($title = get_field('title')) : ?>
        <h1 class="hero__title"><?php echo $title; ?></h1>
      <?php else : ?>
        <h1 class="hero__title"><?php echo get_the_title(); ?></h1>
      <?php endif; ?>

      <?php if ($content = get_field('content')) : ?>
        <p class="hero__description"><?php echo $content; ?></p>
      <?php endif; ?>

      <?php if ($link = get_field('link')) : ?>
        <a href="<?php echo $link['url']; ?>"><?php echo $link['title']; ?></a>
      <?php endif; ?>
    </div>

    <?php if (!empty($image)) : ?>
      <div class="dimming dimming__left"></div>
    <?php endif;?>


  </div>

</div>
