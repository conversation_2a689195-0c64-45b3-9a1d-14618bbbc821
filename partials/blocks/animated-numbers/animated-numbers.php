<?php
/**
 * Animated numbers
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-animated-numbers-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-animated-numbers';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$title = get_field('title');
$subtitle = get_field('subtitle');
$alignment = get_field('title_align');
$link = get_field('link');
$background = get_field('background');
$numbers = get_field('block_rows');


?>

<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?> <?php if($background) echo 'has-bg'; ?>">

    <?php if(!empty($title)) {?>
        <div class="block-animated-numbers__title-wrapper" style="<?php if($alignment) echo 'text-align:'. $alignment .';';?>">
            <h2 class="block-animated-numbers__title">
                <?php echo $title; ?>
            </h2>
            <?php if( !empty($subtitle)) { ?>
                <span class="block-animated-numbers__subtitle">
                    <?php echo $subtitle; ?>
                </span>
            <?php } ?>
        </div>
    <?php } ?>

    <div class="block-animated-numbers__items">
        <?php if($numbers) : ?>

            <?php foreach($numbers as $key => $row) : ?> <!-- very poorly built acf field *cough cough* -->

                <?php foreach($row as $key => $element) : ?>
                    <?php // echo $key; ?>
                    <div class="block-animated-numbers__item"> <!-- Single item -->
                        <div class="item__body">
                            <div class="img-wrapper">
                                <?php if($element['image']) { ?>
                                    <img src="<?php echo $element['image']['url']; ?>" alt="Jäsenyyttä kuvaava ikoni">
                                <?php } else {?>
                                    <img src="<?php echo get_template_directory_uri() . '/dist/images/ko-ratas.svg'; ?>" alt="Kauppakamari ratas-ikoni">
                                <?php } ?>
                            </div>
                            <div class="contents">
                                <?php if($element['upper-text']) { ?>
                                    <p class="upper-text"><?php echo $element['upper-text']; ?></p>
                                <?php } ?>
                                <div class="number-wrapper">
                                    <span class="animated-number" data-destination="<?php echo $element['value']; ?>"><?php echo $element['value']; ?></span>
                                </div>
                                <?php if($element['lower-text']) {?>
                                    <p class="lower-text"><?php echo $element['lower-text']; ?></p>
                                <?php } ?>
                            </div>
                        </div>
                    </div>

                <?php endforeach; ?> <!-- endforeach // fields under repeater-->

            <?php endforeach; ?> <!-- endforeach // repeater -->

        <?php endif; ?>
    </div>
    <?php if(!empty($link)) {?>
        <div class="block-animated-numbers__link-wrapper">
            <a class="block-animated-numbers__link" href="<?php echo $link['url'];?>"><?php echo $link['title']; ?></a>
        </div>
    <?php } ?>
</div>

