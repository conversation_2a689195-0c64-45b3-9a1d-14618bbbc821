<?php

/**
 * Icon - Text
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'price-text-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'price-text';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$items = get_field('block_rows');

?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">

  <?php
    foreach($items as $item) { ?>
      <?php if(!empty($item['group']['title']) && !empty($item['group']['content'])) { ?>
      <div class="price-text__item">
        <div class="price-text__price">
          <?php
          if($icon = $item['group']['icon']) { ?>
            <div class="price-text__img">
            <?php echo '<img src="' . $icon['sizes']['medium_large'] . '" alt="' . $item['group']['title'] . '" />'; ?>
            </div>
          <?php } ?>
          <div class="info-wrapper">
            <span class="price-text__type"><?php echo $item['group']['price_title']; ?></span>
            <span class="price-text__number"><?php echo $item['group']['price']; ?></span>
            <?php if($item['group']['price_link']) : ?>
              <a class="price-text__link" href="<?php echo $item['group']['price_link']['url']; ?>"><?php echo $item['group']['price_link']['title']; ?></a>
            <?php endif; ?>
          </div>
        </div>

        <div class="price-text__content">
          <span class="title">
            <?php echo $item['group']['title'] ?: "Otsikko"; ; ?></span>
          <span class="content">
            <?php echo $item['group']['content'] ?: "Sisältö"; ?>
          <span>
        </div>
      </div>
      <?php } ?>

      <?php if(!empty($item['group_2']['title']) && !empty($item['group_2']['content'])) : ?>
        <div class="price-text__item">
        <div class="price-text__price">
          <?php
          if($icon = $item['group_2']['icon']) { ?>
            <div class="price-text__img">
            <?php echo '<img src="' . $icon['sizes']['medium_large'] . '" alt="' . $item['group_2']['title'] . '" />'; ?>
            </div>
          <?php } ?>
          <div class="info-wrapper">
            <span class="price-text__type"><?php echo $item['group_2']['price_title']; ?></span>
            <span class="price-text__number"><?php echo $item['group_2']['price']; ?></span>
            <?php if($item['group_2']['price_link']) : ?>
              <a class="price-text__link" href="<?php echo $item['group_2']['price_link']['url']; ?>"><?php echo $item['group_2']['price_link']['title']; ?></a>
            <?php endif; ?>
          </div>
        </div>

        <div class="price-text__content">
          <span class="title">
            <?php echo $item['group_2']['title'] ?: "Otsikko"; ; ?></span>
          <span class="content">
            <?php echo $item['group_2']['content'] ?: "Sisältö"; ?>
          <span>
        </div>
      </div>
      <?php endif; ?>
    <?php
    }
  ?>

</div>
