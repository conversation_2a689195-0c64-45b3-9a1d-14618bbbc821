<?php

//wp redirects to 404 if archive params cannot find any posts, fix this:

function wdp_archive_template($template = '')
{
    global $wp_query;
    if (
        isset($wp_query->query['tax_query']) ||
        isset($wp_query->query['post_type']) ||
        isset($_GET['category'])
    ) {
        $template = locate_template('archive.php', false);
        if (isset($wp_query->query['post_type'])) {
            $located = locate_template('archive-' . $wp_query->query['post_type'] . '.php', false);
            $template = $located !== '' ? $located : locate_template('archive.php', false);
        }
    }
    return $template;
}
add_filter('404_template', 'wdp_archive_template');



add_action('pre_get_posts', 'archive_add_taxonomy');

function kk_is_blog2()
{
    global $post;
    if (!isset($post)) return false;
    $post_type = get_post_type($post);
    return ($post_type === 'post') && (is_home() || is_archive() || is_single());
}

function archive_add_taxonomy($query)
{
    global $typenow;
    if (in_array($typenow, array('post', 'release')) && is_admin()) {
        return;
    }

    if (is_archive() && (is_category() || is_tag()) && ($query->is_main_query()) ||  is_home() || kk_is_blog2() &&  $query->is_main_query()) {
        $query->set('post_type', array('post', 'release'));
    }

    if (!isset($_GET["category"])) {
        $_GET["category"] = "";
    }
    $pre_cats = sanitize_text_field($_GET['category']);

    /*
  $taxonomies = get_taxonomies( array( 'name' => 'developer_tag', 'public' => true, '_builtin' => false ), 'objects' );
  https://www.codeable.io/blog/get-your-custom-taxonomy-urls-in-order/

  */



    if (!empty($pre_cats)) {
        $taxquery = array(
            array(
                'taxonomy' => 'category',
                'field' => 'slug',
                'terms' => array($pre_cats),
                'operator' => 'IN'
            )
        );

        $query->set('tax_query', $taxquery);
    }
}
