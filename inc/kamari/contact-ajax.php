<?php

add_action('wp_ajax_nopriv_kauppakamari_filter_contact', 'kauppakamari_filter_contact');
add_action('wp_ajax_kauppakamari_filter_contact', 'kauppakamari_filter_contact');

function kauppakamari_filter_contact() {

  $tax_query = array('relation' => 'AND');

	if (isset( $_POST['peoplecat']) && !empty( $_POST['peoplecat'])) {
    $cats = $_POST['peoplecat'];
    $tax_query[] = array(
        'taxonomy' => 'people-cat',
        'field' => 'slug',
        'terms' => $cats
    );
  }

  $args = array(
    'post_type'              => array( 'people' ),
    'posts_per_page'         => '-1',
    'order'                  => 'ASC',
    'orderby'                => 'title',
    'tax_query'              => $tax_query,
    'lang'                   => '', /* deactivates polylang filter, does not work, https://polylang.pro/doc/developpers-how-to/ */
    //'lang'                   => 'fi', /* bugfix, does not find anything if browser language is english */
    'post_status'            => 'publish',
    //'suppress_filters'       => true /* does not work */
  );

  $query = new WP_Query( $args );

  if ( $query->have_posts() ) {
    ob_start();

    while ( $query->have_posts() ) {
      $query->the_post(); ?>
        <div class="contact__item">
        <div class="contact__item__img" style="background-image: url(<?php echo get_the_post_thumbnail_url(get_the_ID(), 'medium'); ?>)"></div>
          <div class="contact__item__cat">
            <?php
              $peoplecat = get_the_terms(get_the_ID(), 'people-cat');
              $i = 0;
              foreach ($peoplecat as $people) {
                if ($i > 0) {
                  echo ', ';
                }
                echo $people->name;
                $i++;
              }
            ?>
          </div>
          <div class="contact__item__name">
            <?php the_title(); ?>
          </div>
          <?php if (!empty(get_field('title'))) : ?>
            <div class="contact__item__info">
              <?php echo get_field('title'); ?>
            </div>
          <?php endif; ?>
          <?php if (!empty(get_field('puh'))) : ?>
            <div class="contact__item__info">
              <?php echo get_field('puh'); ?>
            </div>
          <?php endif; ?>
          <?php if (!empty(get_field('info'))) : ?>
            <div class="contact__item__info">
              <?php echo get_field('info'); ?>
            </div>
          <?php endif; ?>
        </div>
      <?php
    }
    echo '<div class="contact__item filler">';
  } else {
    echo '<div class="contact__item">Haku ei palauttanut tuloksia</div>';
  }

  wp_send_json_success(ob_get_clean());

  wp_die();
}
