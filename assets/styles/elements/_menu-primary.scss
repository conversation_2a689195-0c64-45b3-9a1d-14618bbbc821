/* ==========================================================================
 # Primary menu (very messy code)
========================================================================== */
.main-body:has(.primary-navigation.active) {
  overflow: hidden; // prevents page from scrolling while menu is open
}

.page-template-template-landingpage-blank {
  #primary-navigation {
    display: none;
  }
}
#primary-navigation {
  background: transparent;
  width: 100%;
  display: block;
  position: relative;
  opacity: 1;
  @include breakpoint($menu-hidden) {
    @include breakpoint(max-width $m) {
      background: $primary;
    }
    &.active {
      @include breakpoint(min-width $m) {
        border-top: none;
        border-right: none;
        position: fixed;
        padding: 0;
        height: calc(100vh - 140px);
        & .primary-navigation__items {
          padding: 0 !important;
          > li:not(.header-join-now) {
            margin: 0 1rem;
          }
        }
      }
      overflow-y: scroll;
      overflow-x: hidden;
      height: calc(100vh - 110px);
      border-top: 1px solid $light-blue-50;
      & .primary-navigation__items {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
        padding-top: 1.25rem;
      }
    }
  }
  @include breakpoint($menu-visible) {
    width: fit-content; // fit-content? %?
    height: 4rem;
    padding: 1.25rem 0;
  }
}

.primary-navigation__items {
  /* Both navigations
  ----------------------------------------------- */

  a {
    font-weight: $ws-medium;
    color: $link-use !important;
    letter-spacing: normal;
    &:hover {
      color: $link-base !important;
    }
    &.header-site .site {
      color: $white;
    }
    &.external {
      @include external-link;
      &:after {
        margin-left: 6px !important;
      }
    }
  }

  &,
  ul {
    @include list-reset;
  }

  li {
    // call to action
    &.cta {
      & > span > a {
        @include button;
        // font-size: .875rem;
        font-weight: $bold;
      }
    }
  }

  /* Mobile / Tablet navigation 
  ----------------------------------------------- */
  @include breakpoint($menu-hidden) {
    display: none;
    opacity: 0;
    a {
      font-size: 16px !important; // mobile menu font size always same
    }
    @include breakpoint($xl) {
      padding: 1rem 0;
      & .header_sites {
        display: none !important;
      }
    }
    // animated dropdown
    animation: growDown 0.6s ease;
    transform-origin: top;

    // Bigger than mobile / Smaller than desktop menu
    @include breakpoint($m) {
      .active & {
        margin-bottom: 2rem;
      }
      // float: right !important;
      width: 100%;
      & .menu-item {
        padding: 0.75rem 2.5rem;
      }
    }
    @include breakpoint(min-width 1000px) {
      float: right !important;
      width: 50%;
      box-shadow: 1px 0 20px -1px rgb(0 0 0 / 40%);
    }
    .active & {
      display: flex;
      flex-direction: column;
      background-color: white;
      z-index: 20;
      opacity: 1;
      .contact-hide {
        display: none;
      }

      // Top bar links inside smaller menus
      .header_sites {
        display: flex;
        flex-direction: column;
        justify-content: left;
        @include breakpoint($sub-m) {
          margin: 2rem -2rem 0 -2rem;
          .header-site {
            padding-left: 3.25rem;
            padding-right: 3.25rem;
          }
        }
        .header-site {
          padding: 0.5rem 2.5rem;
          border: none;
          justify-content: flex-start;
          &:first-of-type {
            padding-top: 1.5rem;
          }
          &:last-of-type {
            padding-bottom: 1.5rem;
          }
        }
      }
    }

    // all <li>
    li {
      // Liity jäseneksi nappi mobile
      &.header-join-now {
        // custom class
        &.current-menu-item {
          color: $primary;
        }
        @include newbutton;
        height: auto !important;
        width: 100%;
        text-align: center;
        order: -1;
        line-height: 1.5;
        padding-top: $gap-small;
        padding-bottom: $gap-small;
        margin-bottom: 0.8rem;
        a {
          font-weight: $ws-semibold;
          color: $primary !important;
        }
        @include breakpoint($m) {
          display: none;
        }
      }

      background-color: white;
      padding: 0 $gap 0;
      // all <a>
      a {
        flex-grow: 1;
        text-decoration: none;
      }

      & > span {
        align-items: center;
        display: flex;
      }

      // call to action
      &.cta {
        border-top: 0;
        & > span > a {
          display: block;
          text-align: center;
          svg {
            margin-right: 0.25rem;
            position: relative;
            top: -0.125rem;
          }
        }
      }

      &.menu-item-has-children {
        position: relative;
        & > span {
          align-items: center;
          display: flex;
          width: 100%;
          & .menu-item__link__caret {
            @include transition;
          }
        }
        &.current-menu-item {
          > .menu-item__link {
            > a {
              color: $link-base !important;
            }
          }
        }
        &.active {
          & > .menu-item__link {
            .menu-item__link__caret {
              transform: rotate(180deg); // flip caret to point up
            }
          }
        }
      }
    }

    // all carets
    .menu-item__link__caret {
      @include button-reset;
      padding: 0.5rem 0.5rem;
    }
    // all .sub-menu
    .sub-menu-wrap {
    }
    .sub-menu-wrap {
      height: 0;
      overflow: hidden;
      &.open {
        padding: 0 0 0 $gap;
        overflow: visible;
        height: auto;
        // animated dropdown
        //animation: growDown 400ms ease-in-out forwards;
        //transform-origin: top center;
      }

      ul {
        width: 100%;
      }
      .current-menu-item.active {
        > .menu-item__link {
          > a {
            color: $link-base !important;
          }
        }
      }
      // all lower <li>
      li {
        padding: 0.25rem 0;
        &:last-of-type {
          padding-bottom: 0;
        }
      }
    }

    // 1st level <li>
    & > li {
      padding: 0.5rem 1.25rem;
      border-top: 1px solid $light-blue-50;
      &:first-of-type {
        border-top: 0;
      }

      // 1st level <a>
      & > span > a {
        color: $link-use !important;
      }

      // 2nd level <ul class="sub-menu"> [closed]
      & > .sub-menu-wrap {
        max-height: 0;
        background: $white;
        opacity: 0;

        // 2nd level <ul class="sub-menu"> [open]
        &.open {
          opacity: 1;
          max-height: 999px;
        }

        // 2nd level <li>
        & > li {
          // 2nd level <a>
          & > span > a {
          }

          // 3rd level <ul class="sub-menu"> [closed]
          & > .sub-menu-wrap {
            max-height: 0;
            opacity: 0;

            // 3rd level <ul class="sub-menu"> [open]
            &.open {
              max-height: 999px;
              opacity: 1;
            }
          }
        }
      }
    }
  }

  /* Desktop navigation
  ----------------------------------------------- */

  @include breakpoint($menu-visible) {
    .contact-hide,
    .header_sites {
      display: none;
    }
    display: flex;
    flex-wrap: wrap;
    align-items: center;

    // all <li>
    li {
      & > span {
        width: 100%;
        display: flex;
        align-items: center;
      }
    }

    // all <a>
    a {
      color: $link-use;
      text-decoration: none;
      flex-grow: 1;
      font-size: 15px !important;
      @include breakpoint(min-width 1750px) {
        font-size: 16px !important;
      }
    }

    // color if active page
    .current-menu-item {
      &.active > span > a {
        color: $link-base !important;
      }
    }

    // all carets
    .menu-item__link__caret {
      @include button-reset;
      flex-shrink: 0;
    }

    // all <svg>
    .icon {
      width: 1em;
      height: 1em;
      top: -0.125rem;
      position: relative;
      &.icon-from-class {
        margin-right: 0.5rem;
        width: 1.25em;
        height: 1.25em;
      }
    }

    // 1st level <li>
    & > li {
      margin-right: 1.25rem;
      @include breakpoint(min-width 1750px) {
        margin-right: 1.5rem;
      }

      &:not(.header-join-now) {
        &:after {
          // custom underline for all li but last
          content: "";
          display: block;
          position: static;
          height: 3px;
          margin-top: 6px;
          width: 0;
          background-color: $coral;
          transition: width 0.3s ease-in-out;
        }
      }

      // position:relative;
      border-bottom: 3px solid transparent;
      padding-bottom: 0.5rem;
      &.current-menu-item,
      &.current-menu-ancestor {
        // padding-bottom: 0.5rem;
        &:after {
          width: 100%;
        }
      }
      .menu-item__link {
        // gap: 0.5rem;
      }
      .menu-item__link__caret {
        transition: transform 0.4s ease-in-out;
      }
      > .sub-menu-wrap > .sub-menu {
        // first level submenu padding y
        padding: 0.75rem 0;
      }
      &:hover {
        &:after {
          // underline slide in
          width: 100%;
        }
        .sub-menu-wrap {
          // animated dropdown
          animation: growDown 0.3s ease-in-out forwards;
          transform-origin: top center;
        }
        .menu-item__link__caret {
          transform: rotate(180deg);
        }
      }

      &.header-join-now {
        // Liity jäseneksi -'nappi'
        &.current-menu-item a {
          color: $primary !important;
        }
        @include newbutton;
        a {
          font-size: 16px;
          font-weight: $ws-semibold;
          color: $primary !important;
        }
        height: 42px !important;
        margin-top: -19px;
        margin-right: 0;
        margin-left: 0.5rem;
        padding: 0.25rem 1.5rem !important;
      }

      // 1st level <a>
      &.menu-item-has-children > span > a {
        padding-right: 0.5rem; // caret and link gap
      }

      .sub-menu-wrap a,
      .sub-menu-wrap .sub-menu-wrap a {
        transition: all 0.15s ease-in-out;
        padding-left: 15px;
        border-left: 0px solid black;
        border-left: 3px solid white;
      }
      &:nth-of-type() {
        &:hover,
        &.active {
          border-bottom: 3px solid black;
        }
        &.current-menu-ancestor {
          border-bottom: 3px solid black;
        }
        .sub-menu-wrap a:hover,
        .sub-menu-wrap .sub-menu-wrap a:hover {
          background: rgba(0, 0, 0, 0.1);
          border-left: 3px solid rgba(0, 0, 0, 1);
          padding-left: 14px;
        }
      }

      // all dropdown <li>
      li {
        background: white;
        & > span > a {
          color: $menu-dropdown-color;
          padding: 0.5rem 0.75rem;
          // font-size: .875rem;
          font-size: 16px;
          display: block;
        }
        .icon {
          color: $menu-dropdown-color;
          transform: rotate(-90deg);
        }
      }

      // 2nd level <ul class="sub-menu"> [open]
      &:hover > .sub-menu-wrap,
      & > .sub-menu-wrap.open {
        opacity: 1;
        height: auto;
        align-items: stretch;

        box-shadow: 4px 4px 0px rgba(0, 38, 99, 0.3);
        -webkit-box-shadow: 4px 4px 0px rgba(0, 38, 99, 0.3);
        -moz-box-shadow: 4px 4px 0px rgba(0, 38, 99, 0.3);
        // margin-left: -$gap;
        .menu-item-has-children a {
          padding-bottom: 0;
        }
      }
      & > .sub-menu-wrap {
        position: absolute;
        background: $white;
        width: auto;
        margin-top: 0.7rem;
        opacity: 0;
        overflow: hidden;
        height: 0;
        transition: opacity 0.2s ease-out;
      }
      // 2nd level <ul class="sub-menu"> [closed]
      & > .sub-menu-wrap > ul {
        flex-wrap: wrap;
        border-bottom: 1px solid $grey-light;
        z-index: 99;
        flex: 5;
        // 2nd level <li>
        & > li {
          &.menu-item-has-children {
            position: relative;
          }
          // 2nd level <a>
          & > span a {
            display: block;
            padding: 0.5rem 1rem;
            font-weight: $ws-semibold;
            min-width: 100%;
          }
          .sub-menu-wrap {
            width: 100%;
            background-color: transparent;
            ul,
            .sub-menu {
              padding-bottom: 0.75rem;
              width: 100%;
              background-color: transparent;
              li {
                padding: 0;
                background-color: transparent;
              }
              a {
                font-weight: 500;
                background-color: transparent;
                padding: 0rem 2rem;
                font-size: 1rem;
              }
            }
          }

          // carets 2nd level and below
          .menu-item__link__caret {
            padding-left: 0.75rem;
            padding-right: 0.75rem;
          }

          // 3rd level <ul class="sub-menu"> [open]
          &:hover > .sub-menu,
          & > .sub-menu.open {
            display: none;
            opacity: 1;
            left: $menu-dropdown-width;
            top: 0;
          }

          // 3rd level <ul class="sub-menu"> [closed]
          & > ul {
            width: $menu-dropdown-width;
            display: none;
            background: $white;
            position: absolute;
            left: -999em;
            z-index: 99;
            box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.15);
            opacity: 0;
            transition: opacity 0.2s ease-out;
          }
        }
      }
    }
  }
}

.sub-menu-featured {
  border-left: 1px solid $grey-light;
  padding: 1.6rem;
  min-height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 2;
  padding-right: 15%;
  .title {
    margin-bottom: 1rem;
  }
  .content {
    font-size: 0.8rem;
    margin-bottom: 1rem;
  }
  img {
    margin-bottom: 1rem;
  }
  a {
    color: $brand;
  }
}
