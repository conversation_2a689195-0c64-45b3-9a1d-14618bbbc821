<?php

/**
 * News list
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-news-list' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-news-list';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$post_object = get_field('featured_article');

?>


<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">

<div class="title-wrapper">
  <span class="h2 block-news-list__top-title">Ajankohtaista</span>
</div>


<?php
    $args = array(
      'post_type'       => array('post', 'release'),
      'article_type'    => 'uutiset',
      'posts_per_page'  => 1,
      'orderby'         => 'date',
      'order'           => 'desc'
      //'paged'           => $paged
    );

    $latest = new WP_Query($args);
  ?>

  <?php if(!empty($post_object)) { ?>
    <div class="block-news-list__wrap">
      <?php
      global $post;
      $post = $post_object;
      setup_postdata( $post ); ?>

      <?php get_template_part('partials/content/teaser', 'front'); ?>
      <?php wp_reset_postdata(); ?>
    </div>
  <?php } else { ?>
    <div class="block-news-list__wrap">
      <?php if ( $latest->have_posts() ) : ?>

          <?php while ( $latest->have_posts() ) : $latest->the_post();
              global $post; ?>
            <?php get_template_part('partials/content/teaser', 'front'); ?>
            <?php break; ?>
          <?php endwhile; ?>

          <?php wp_reset_postdata();
          wp_reset_query(); ?>

      <?php else : ?>
          <p><?php _e( 'Sorry, no posts matched your criteria.' ); ?></p>
      <?php endif; ?>
    </div>
  <?php } ?>


  <div class="block-news-list__wrap">

    <?php
      $args = array(
        'post_type'       => 'post',
        'article_type'    => 'neuvonnan-artikkelit',
        'posts_per_page'  => 1,
        'orderby'         => 'date',
        'order'           => 'desc'
        //'paged'           => $paged
      );

      $news = new WP_Query($args);
    ?>


    <div class="block-news-list__ajax">

    <?php if ( $news->have_posts() ) : ?>

        <?php while ( $news->have_posts() ) : $news->the_post();
            global $post; ?>
            <div class="block-news-list__item">
              <?php get_template_part('partials/content/teaser', 'tiny'); ?>
            </div>

        <?php endwhile; ?>

        <?php wp_reset_postdata(); ?>

    <?php else : ?>
        <p><?php _e( 'Sorry, no posts matched your criteria.' ); ?></p>
    <?php endif; ?>
    </div>

    <?php
      $args = array(
        'post_type'       => 'release',
        'article_type'    => 'tiedotteet',
        'posts_per_page'  => 1,
        'orderby'         => 'date',
        'order'           => 'desc',
      );

      $news = new WP_Query($args);
    ?>

    <div class="block-news-list__ajax">

    <?php if ( $news->have_posts() ) : ?>

    <?php while ( $news->have_posts() ) : $news->the_post();
        global $post; ?>
        <div class="block-news-list__item">
          <?php get_template_part('partials/content/teaser', 'tiny'); ?>
        </div>

    <?php endwhile; ?>

    <?php wp_reset_postdata(); ?>

    <?php else : ?>
    <p><?php _e( 'Sorry, no posts matched your criteria.' ); ?></p>
    <?php endif; ?>
    </div>

    <?php
      $args = array(
        'post_type'       => 'post',
        'article_type'    => 'kolumnit-ja-tarinat',
        'posts_per_page'  => 1,
        'orderby'         => 'date',
        'order'           => 'desc',
      );

      $news = new WP_Query($args);
    ?>

    <div class="block-news-list__ajax">

    <?php if ( $news->have_posts() ) : ?>

    <?php while ( $news->have_posts() ) : $news->the_post();
        global $post; ?>
        <div class="block-news-list__item">
          <?php get_template_part('partials/content/teaser', 'tiny'); ?>
        </div>

    <?php endwhile; ?>

    <?php wp_reset_postdata(); ?>

    <?php else : ?>
    <p><?php _e( 'Sorry, no posts matched your criteria.' ); ?></p>
    <?php endif; ?>
    </div>

    <?php
      $args = array(
        'post_type'       => 'post',
        'article_type'    => 'lausunnot',
        'posts_per_page'  => 1,
        'orderby'         => 'date',
        'order'           => 'desc',
      );

      $news = new WP_Query($args);
    ?>

    <div class="block-news-list__ajax">

    <?php if ( $news->have_posts() ) : ?>

    <?php while ( $news->have_posts() ) : $news->the_post();
        global $post; ?>
        <div class="block-news-list__item">
          <?php get_template_part('partials/content/teaser', 'tiny'); ?>
        </div>

    <?php endwhile; ?>

    <?php wp_reset_postdata(); ?>

    <?php else : ?>
    <p><?php _e( 'Sorry, no posts matched your criteria.' ); ?></p>
    <?php endif; ?>
    </div>

  </div>

  <div class="links-wrapper">
    <a class="news-list__link" href="/ajankohtaista/">Kaikki ajankohtaiset</a>
    <a class="news-list__link" href="artikkelityyppi/tiedotteet">Lue tiedotteet</a>
  </div>

</div>

