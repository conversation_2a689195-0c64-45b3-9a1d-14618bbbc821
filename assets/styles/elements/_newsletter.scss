/* <PERSON><PERSON><PERSON>htaista oma newsletter (.subscribe)
--------------------------------------------------*/
.subscribe {
  background-color: $light-blue-25;
  color: $primary;
  padding: 2rem 0;
  &__wrap {
    @include wrap();
  }
  h2 {
    text-align:center;
    max-width: 600px; // 100px more than the form itself
    margin: 2rem auto;
    font-size: 1.6rem;
    font-weight: $ws-medium;
    @include breakpoint($m) {
      font-size: 1.6rem;
      width: 75%;
      margin: 2rem auto 1rem;
    }
  }
  .accompany {
    width: 75%;
    max-width: 600px;
    margin: 1.5rem auto 2rem;
    font-weight: $ws-medium;
    text-align: center;
  }
  .emaillist {
    max-width: 650px;
    width: 100%;
    margin: 2rem auto;
    @media screen and (max-width: 600px) {
      width: 100%;
    }
    form {
      display: flex;
      flex-wrap: wrap;
      // justify-content: space-evenly;
      justify-content: space-between;
      align-items: center;
      .es-field-wrap {
        width: calc(50% - .75rem);
        font-weight: $ws-medium;
        @media screen and (max-width: 600px) {
          width: 100%;
        }
        input {
          @include textinput;
          width: 100%;
          //border-radius: 5px;
          //padding: 10px 10px;
          //letter-spacing: normal;
          //border: 1px solid #d3d3d3;
        }
        br {
          display: none;
        }
      }
      >div:not(.es-field-wrap) { // List selection (icegram form)
        order: -1;
        width: 100%;
        text-align: start;
        margin-bottom: 1.5rem;

        >p {
          display: none;
        }

        table {
          display: flex;

          label {
            display: flex;
            text-align: left;
            align-items: center;
            position: relative;
            cursor: pointer;

            > span {
              line-height: 1.25;
            }
            input {
              @include checkbox;
              margin: auto 0;
            }
          }

          tbody {
            width: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 1rem;

            tr {
              width: 100%;

              td {
                text-transform: uppercase;
                font-weight: $ws-semibold;
                font-size: 16px;

                span {
                  margin-left: .75rem;
                }
                .description {
                  text-transform: none;
                  display: block;
                  text-align: start;
                  margin: 0;
                  margin-top: 0.5rem;
                  > p {
                    margin: 0;
                    margin-bottom: 0.25rem;
                    font-weight: 400;
                    font-size: 14px !important;
                  }
                }
              }
            }

            @include breakpoint($m) {
              flex-direction: row;
              align-items: start;
              gap: 1rem;
              margin: 1.25rem 0;
            }
          }
        }

        @include breakpoint($m) {
          text-align: center;
        }
      }
      // 
      p {
        margin: 0 1.75rem 1.75rem;
        font-size: 16px !important;
      }
      a {
        color: $link-use;
        font-weight: $ws-medium;
      }
      .es_spinner_image {
        width: 0px;
        height: 0px;
      }
      .es_subscription_message.success {
        color: $primary;
        text-align: center;
      }
    }
    input[name="es_gdpr_consent"] {
      @include checkbox;
      top: 5px; // aligning
    }
    input[type="submit"] {
      align-self: flex-end;
      @include newbutton;
      padding-left: 3rem;
      padding-right: 3rem;
      margin: 1.5rem auto 0;
      text-align: center;
      cursor: pointer;
    }
  }
}
/* /.subscribe */





.newsletter {
  background-color: $light-blue-25;
  color: $primary;
  padding: 2rem 0;
  &__shrunk-info {
    display: none;
  }
  h2 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    margin: 1rem auto;
    font-size: 24px;
    font-weight: $ws-semibold;
    @include breakpoint($m) {
      font-size: 32px;
    }
  }
  &-icon {
    font-size: 36px;
    @include breakpoint($sub-s) {
     // display: none;

    }
  }
  .newsletter__subtitle {
    width: 100%;
    display: block;
    clear: both;
    line-height: 1.5;
    margin-top: 10px;
    margin-bottom: 2.5rem;
    text-align: center;
    font-weight: $ws-semibold;
    font-size: 18px;
  }
  &-item {
    text-align: left;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    margin: 1rem 0;
    padding: 0 1.5rem;
    
    & .ch_title {
      margin-left: $gap-small;
      font-weight: $ws-semibold;
      font-size: 16px;
    }
    @include breakpoint(max-width 1370px) {
      text-align: center;
      padding: 0;
      & label {
        justify-content: center;
      }
    }
    &:not(:first-child) {
      @include breakpoint(min-width 1370px) {
        border-left: 1px solid $light-blue;
      }
    }
    & .checkbox-info {
      max-width: 375px;
      font-weight: $ws-medium;
      margin-top: $gap-small;
      font-size: 14px;
    }
    & .regularity {
      display: block;
      margin-top: $gap-small;
    }
  }

  .newsletter__checkboxes {
    label {
      display: flex;
      text-align: left;
      align-items: center;
      min-width: 100%;
      font-weight: $ws-medium;
      text-transform: uppercase;
      transition: all .1s ease-in-out;
      cursor: pointer;
      &:focus, &.focus {
        outline: 2px solid white;
        outline-offset: 1px;
      }
    }

    // hide default checkbox
    input {
      position: absolute;
      opacity: 0;
      cursor: pointer;
      height: 0;
      width: 0;
    }

    // custom checkbox
    .newsletter-checkbox {
      top: 0;
      left: 0;
      height: 20px;
      width: 20px;
      background-color: #fff;
      border: 2px solid $light-blue;
    }
    input:checked ~ .newsletter-checkbox:after{
      display: block;
      // custom 'checked' icon
      content:"\f00c";
      font-family: 'Font Awesome 6 Sharp';
      font-weight: 900;
      text-align: center;
      line-height: 1.25;
    }
    input:focus ~ .newsletter-checkbox {
      outline: 1px solid $light-blue;
      //outline-offset: 0.5px;
    }
    // --- //


    input:focus label {
      outline: 1px solid $light-blue;
      outline-offset: 1px;
    }
    &--wrap {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      @include breakpoint(max-width 920px) {
        flex-direction: column;
        align-items: center;
      }
    }

  }
  .newsletter__wrap {
    @include wrap();
  }
  .newletter__info_text {
    text-align: center;
  }
  .newsletter__error {
    display: none;
    text-align: center;
    font-weight: $ws-semibold;
    font-size: 18px;
    padding-top: $gap;
    padding-bottom: $gap;
  }
  .newsletter__info {
    display: none;
    max-width: 500px;
    margin: 0 auto;
    label {
      width: calc(50% - .5rem);
      margin-bottom: 1rem;
      display: flex;
      flex-direction: column;
      @include breakpoint($sub-s) {
        width: 100%;
      }
    }
    input[type="text"], input[type="email"] {
      @include textinput;
    }
    .info-label {
      font-weight: $ws-medium;
      font-size: 14px;
      text-transform: uppercase;
      margin-left: 2px;
      letter-spacing: 0.2px;
    }
    &--wrap {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-bottom: 2rem;
    }
    .mail, .consent {
      width: 100%;
    }
    // Consent checkbox
    .consent-container {
      display: flex;
      align-items: start;
      .consent-input {
        cursor: pointer;
        flex-direction: row;
        justify-content: start;
        position: relative;
        &-info {
          margin-left: 1rem;
        }
        // hide default checkbox
        input {
          position: absolute;
          opacity: 0;
          cursor: pointer;
          height: 0;
          width: 0;
        }
        // custom checkbox
        .consent-checkbox {
          top: 0;
          left: 0;
          height: 20px;
          width: 20px;
          background-color: #fff;
          border: 2px solid $light-blue;
        }
        input:checked ~ .consent-checkbox:after{
          display: block;
          // custom 'checked' icon
          content:"\f00c";
          font-family: 'Font Awesome 6 Sharp';
          font-weight: 900;
          text-align: center;
          line-height: 1.25;
        }
        input:focus ~ .consent-checkbox {
          outline: 1px solid $light-blue;
          //outline-offset: 0.5px;
        }
      }
    }
  }

  .newsletter__success {
    display: none;
    text-align: center;
    color: $primary;
  }

  .hidden {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
  }

  .newsletter__buttons--wrap {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    grid-template-columns: 30% auto;

    &>div>a, &>button {
      margin: 20px 0;
    }
    &>div {
      width: fit-content;
    }

  }
  a:not(.exclude), button {
    align-self: flex-end;
    @include newbutton;
    margin: 2rem auto 1rem;
    width: 100%;
    @include breakpoint($m) {
      width: fit-content;
      padding-left: 2rem;
      padding-right: 2rem;
      margin: 3rem auto 2rem auto;
    }
  }
  a.exclude {
    color: $link-use;
    font-weight: 600;
    &:hover {
      color: $link-base;
    }
  }
  a.btn_checkbox {
    cursor: pointer;
  }
}
