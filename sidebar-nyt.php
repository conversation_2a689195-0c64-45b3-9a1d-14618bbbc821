<aside>
  <?php if(get_locale() === 'fi') : ?>
  <div class="sidebar-widget">
    <div class="sidebar-widget__header">
      <span class="sidebar-widget__header--title"><?php ask_e('<PERSON><PERSON>i: Uusimmat'); ?></span>
      <a href="<?php ask_e('Kamari: kauppakamarinyt'); ?>"><?php ask_e('Kamari: Näytä lisää'); ?></a>
    </div>

    <?php
    $args = array(
      'post_type'              => array( 'post', 'release' ),
      'posts_per_page'         => '5',
    );

    // The Query
    $sidequery = new WP_Query( $args );

    // The Loop
    if ( $sidequery->have_posts() ) { ?>
    <div class="recent-posts">
      <?php while ( $sidequery->have_posts() ) {
        $sidequery->the_post(); ?>
        <a href="<?php echo get_permalink(); ?>" class="recent-posts-item">
          <span class="date"><?php echo kauppakamari_get_posted_on(); ?></span>
          <h5><?php the_title(); ?> </h5>
        </a>
      <?php } ?>
      </div>
    <?php } else {
      // no posts found
    }

    // Restore original Post Data
    wp_reset_postdata(); ?>
  </div>
    <a href="https://kauppakamarilehti.fi" class="aside__nyt-link">
      <img src="<?php echo get_template_directory_uri() . '/dist/images/kauppakamarilehti-banneri.jpg'; ?>" alt="Kauppakamarilehti" />
    </a>
    <div class="sidebar-widget">
      <div class="sidebar-widget__header">
        <span class="sidebar-widget__header--title"><?php ask_e('Kamari: Mediatiedotteet'); ?></span>
      </div>


      <!-- $url = 'https://www.sttinfo.fi/json/v2/releases?publisher=26487429'; -->

      <?php
      $args = array(
        'post_type'       => 'release',
        'posts_per_page'  => 5,
        'orderby'         => 'date',
        'order'           => 'desc',
      );

      $news = new WP_Query($args);
      ?>

      <div class="block-news-list__ajax">

      <?php if ( $news->have_posts() ) : ?>

      <?php while ( $news->have_posts() ) : $news->the_post();
        global $post; ?>
        <div class="block-news-list__item">
          <a class="block-news-list__item--content" href="<?php echo get_the_permalink(); ?>" target="_blank">
            <?php echo kauppakamari_get_post_meta(); ?>
            <h5><?php the_title(); ?></h5>
          </a>
        </div>

      <?php endwhile; ?>

      <?php wp_reset_postdata(); ?>

      <?php else : ?>
      <p><?php _e( 'Sorry, no posts matched your criteria.' ); ?></p>
      <?php endif; ?>

      <a href="/kauppakamarinyt/?kategoria=tiedotteet" class="block-news-list__all"><?php ask_e('Kamari: Kaikki tiedotteet'); ?></a>

      </div>
    </div>
  <?php endif; ?>
  <div class="sidebar-widget">
    <div class="sidebar-widget__header">
      <span class="sidebar-widget__header--title"><?php echo kauppakamari_get_svg('x-twitter'); ?> @K2HEL</span>
      <a href="https://x.com/K2HEL" target="_blank">x</a>
    </div>

  <?php
  if ( function_exists('dude_twitter_feed') ) :
  $tweets = dude_twitter_feed()->get_user_tweets( 'k2hel' );
  if ($tweets) : ?>
    <div class="feed twitter-feed">
      <?php foreach ( $tweets as $tweet ) : ?>
        <?php
          $date = new DateTime($tweet->created_at);
          $date->setTimezone(new DateTimeZone('Europe/Helsinki'));
          $formatted_date = $date->format('H:i, M d');
        ?>
        <div class="twitter-feed--item">
          <a target="_blank" href="https://x.com/mashable/status/<?php echo $tweet->id; ?>">
            <h5><?php ask_e('Kamari: HSK'); ?></h5>
            <div class="twitter-feed--meta">
              <span class="date"><?php echo date('j.n.', strtotime($tweet->created_at)); ?> </span> | <span class="time"><?php echo date('h:i', strtotime($formatted_date)); ?> </span>
            </div>
            <?php echo $tweet->text; ?>
          </a>
        </div><!-- .item -->
      <?php endforeach; ?>
    </div><!-- .feed -->
  <?php endif;
  endif; ?>

  </div>


</aside>
