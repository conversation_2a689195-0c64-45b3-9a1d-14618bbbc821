.wysiwyg .alignwide.block-news-list{

}
.block-news-list {
  display: flex;
  justify-content: space-between;
  background-color: $light-blue-50;
  position: relative;
  margin-bottom: 5rem !important;
  padding: 8rem 0 8rem;
  @include breakpoint($m) {
    padding: 12rem 0 6rem;
  }
  &-logo {
    display: block !important;
    margin-top: -4rem;
    margin-bottom: 2rem;
    @include breakpoint($l) {
      display: none !important;
    }
  }

  .title-wrapper {
    position: absolute;
    top: 4.5rem;
    @include breakpoint($sub-m) {
      top: 2rem;
      margin: 0 auto;
      left: 1rem;
    }
  }
  .links-wrapper {
    display: flex;
    flex-direction: column;
    position: absolute;
    bottom: 0;
    margin-bottom: 1rem;
    padding: 0.5rem 0;
    gap: 1rem;
    a {
      @include bigtextlink;
      // font-size: 20px;
      //text-transform: uppercase;
      //border-bottom: 2px solid $light-blue-75;
      width: fit-content;
    }
    @include breakpoint($sub-m) {
      // margin: 0 auto;
    }
  }

  .teaser {
    margin-top: 0;
    padding-top: 0;
    &__header {
      margin: 0;
      a {
        color: $link-base;
        text-decoration: none;
        &:hover, &:visited {
          color: $link-use;
        }
      }
      &__title {
        font-size: 22px;
        @include breakpoint($m) {
          font-size: 28px;
        }
        &:hover {
          text-decoration: underline;
        }
        &:focus {
          border: 1px dotted $primary;
        }
      }
    }
    &__summary {
      font-size: 16px;
      font-weight: $ws-medium;
      color: $black-text;
      line-height: 24px;
    }
  }
  &:before {
    content: " ";
    position: absolute;
    z-index: -1; /* behind parent */
    top: 0;
    bottom: 0;
    /* subtract h2 padding (1.5rem) */
    left: -100%;
    right: -100%;
    background: $light-blue-50;
  }

  @include breakpoint($sub-l) {
    display: block;
    img {
      display: none;
    }
  }
  img {
  }
  &__wrap {
    .block-news-list__ajax {
      margin-bottom: 2rem;
      &:not(:last-of-type) {
        border-bottom: 2px solid $white;
      }
    }
    width: calc(50% - 1.5rem);
    min-height: 420px;
    .teaser--large {
      border-bottom: 0;
      margin-bottom: 2rem;
      img {
        margin-bottom: 1.75rem;
      }
    }
    @include breakpoint($sub-l) {
      width: 100%;
      .teaser--large {
        border-bottom: 2px solid $white;
        padding-bottom: 4rem;
      }
    }
  }
  &__top-title {
    margin-top: 0px;
    margin-bottom: 8px;
  }
  &__title {
    border-bottom: 2px solid $white;
    letter-spacing: 1px;
    display: flex;
    align-content: center;
    justify-content: space-between;
    padding: .5rem 0;
  }
  &__item {
    display: flex;
    align-content: center;
    flex-wrap: wrap;
    justify-content: space-between;
    h5 {
      margin-top: .5rem;
      a {
        font-size: 16px;
        @include breakpoint($m) {
          font-size: 18px;
        }
      }
    }
    .date {
      margin: .5rem 0rem;
      display: inline-block;
      color: $grey-darker;
      font-weight: 600;
      text-transform: capitalize;

    }
    &--hidden {
      padding: 1em;
      overflow: hidden;
      display: none;
      width: 100%;
      background-color: $grey-light;
      &.show {
        /*display: block;*/
      }
    }
    .icon-wrap {
      display: flex;
      align-items: center;
      margin-bottom: .5rem;
      color: $primary;
      font-weight: 600;
    }
    .icon {
      color: white;
      background-color: $primary;
      transition: all .3s ease;
      margin-right: .5rem;
      width: 1.5rem;
      height: 1.5rem;
    }
    .accordion-toggle {
      cursor: pointer;
    }
    .active .icon {
      transform: rotate(90deg);
    }
  }
  &-header {
    display: flex;
    justify-content: space-between;
    img {
      height: 2.25rem;
      width: auto;
    }

  }
  
}

.block-news-list .block-news-list__wrap {
  // All news-list entry meta
  .entry-meta {
    text-transform: uppercase;
    display: block;
    display: flex;
    max-width: 100%;
    min-width: fit-content;
    text-align: left;
    gap: 1rem;
    &__article_type {
      min-width: fit-content;
      a {
        float: left;
        text-decoration: none;
        color: $primary;
        font-weight: $ws-semibold;
      }
    }
    &__date {
      margin: 0;
    }
    a+a {
      display: none;
    }
  }
}

/* frontpage block news-list entry meta rows */
.block-news-list .block-news-list__wrap .block-news-list__item {
  flex-wrap: nowrap;
  justify-content: space-between;

  // small mini teaser styles
  .teaser {
    border-top: none;
    &__thumbnail {
      max-width: 35%;
      margin-right: 2.5rem;
      @include breakpoint($sub-l) {
        max-width: 30%;
      }
    }
    &__header__title {
      margin-top: 0.5rem;
      font-size: 18px !important;
      color: $primary !important;
      font-weight: $ws-semibold !important;
      line-height: 1.5 !important;
      &:hover, &:focus {
        text-decoration: underline !important;
      }
    }
    @media (max-width: 800px) {
      &__thumbnail {
        display: none;
      }
      margin-bottom: $gap-tiny;
    }
  }
}