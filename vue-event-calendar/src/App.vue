<template>
  <div id="event-calendar-wp-app" v-if="baseData != null" @click="offClick">
    <header class="event-type-selector">
      <button
        @click="switchEventType('event')"
        class="main-link"
        :class="eventType == 'event' ? 'main-link--is-selected' : ''"
      >
        Tapahtumat
      </button>
      <button
        @click="switchEventType('workshop')"
        class="main-link"
        :class="eventType == 'workshop' ? 'main-link--is-selected' : ''"
      >
        Koulutukset
      </button>
    </header>

    <div v-if="isWorkshop" class="filter-buttons">
      <button
        @click="updateCategoryFilter([])"
        :class="filters.length ? '' : 'active'"
        :data-value="[]"
      >
        <span>Kaikki</span>
      </button>
      <button
        v-for="button in filteredButtons"
        :key="button.slug"
        :data-value="[button.slug]"
        @click="updateCategoryFilter([button.slug])"
        :class="filters.includes(button.slug) ? 'active' : ''"
      >
        <span>{{ button.name }}</span>
      </button>
    </div>

    <div class="sort-tools">
      <div v-if="isWorkshop">
        <label for="select-method-filter">Toteutusmuoto</label>
        <div class="select checkboxes-filter-wrapper month-filter">
          <select
            id="select-method-filter"
            class="month-filter btn-filter-toggle btn-normal"
            v-bind:value="methodFilter"
            @change="(event) => updateMethodFilter(event.target?.value)"
          >
            <option class="checkbox capitalize-me" value="">
              <span>Näytä kaikki</span>
            </option>
            <option
              v-for="method in methods"
              :key="method"
              :value="method"
              class="checkbox capitalize-me"
            >
              <span>
                {{ method }}
              </span>
            </option>
          </select>
        </div>
      </div>
      <div>
        <label for="select-month-filter">Ajankohta</label>
        <div class="select checkboxes-filter-wrapper month-filter">
          <select
            id="select-month-filter"
            class="month-filter btn-filter-toggle btn-normal"
            v-bind:value="monthFilters"
            @change="(event) => updateMonthFilter([event.target?.value])"
          >
            <option class="checkbox capitalize-me" value="">
              <span>Näytä kaikki</span>
            </option>
            <option
              v-for="month in monthFilterItems"
              :key="month.title"
              class="checkbox capitalize-me"
              :value="month.title"
            >
              <span>
                {{ month.title }}
              </span>
            </option>
          </select>
        </div>
      </div>
      <div>
        <button @click="resetExtraFilters" class="btn-normal btn-link">
          Tyhjennä rajaukset
          <span class="icon-wrapper cancel-icon">
            <IconCancel />
          </span>
        </button>
      </div>
    </div>
    <Paging
      v-if="totalEvents > postsPerPage"
      :totalPages="totalPages"
      :currentPage="currentPage"
      :gotoPage="gotoPage"
      class="paging--top"
    />
    <main>
      <EventContainer
        :rawData="rawData"
        :isWorkshop="isWorkshop"
        v-if="!loading"
      />
      <div v-else class="event-list-filler">
        <div v-for="item in '12345678'" class="event-filler" role="status">
          ladataan...
        </div>
      </div>
    </main>
    <Paging
      v-if="totalEvents > postsPerPage"
      :totalPages="totalPages"
      :currentPage="currentPage"
      :gotoPage="gotoPage"
    />
  </div>
</template>

<script setup>
//@ts-check
import "./assets/custom-icons.css";
import EventContainer from "./components/EventContainer.vue";
import Paging from "./components/Paging.vue";
import IconCancel from "./components/icons/IconCancel.vue";
import IconCaretUp from "./components/icons/IconCaretUp.vue";
import IconCaretDown from "./components/icons/IconCaretDown.vue";

// import TheWelcome from './components/TheWelcome.vue'
</script>
<script>
import { addQueryArgs } from "@wordpress/url";
import { dates } from "./helpers";

// Define location for localhost
const hrefDefinition =
  location.hostname === "127.0.0.1" ? "https://helsinkichamberfi.local" : "";

export default {
  data() {
    return {
      filters: [],
      filterOpen: null,
      methodFilter: "",
      monthFilters: [],
      baseData: null,
      eventType: "event",
      queryResult: null,
      loading: false,
      currentPage: 1,
      totalPages: 1,
      totalEvents: 0,
      rawData: {},
      postsPerPage: this.getPostsPerPage(),
    };
  },

  async mounted() {
    try {
      await this.getData(); // Wait for filtering data
      this.runFilter(); // Then run initial filtering
    } catch (error) {
      console.log("Kurssitietoja ei voitu ladata...");
    }
  },

  computed: {
    isWorkshop() {
      return this.eventType === "workshop";
    },

    methods() {
      let methods =
        this.eventType === "workshop"
          ? this.baseData.workshop_methods
          : this.baseData.event_methods;
      return methods.filter((m) => m != "Yrityskohtainen koulutus");
    },

    monthFilterItems() {
      console.log("DATES", dates);
      return this.baseData.workshop_months.filter(
        (month) =>
          month.timestamp <= dates.timeStampLimit &&
          month.timestamp >= dates.timeStampStart
      );
    },

    filterButtons() {
      if (!this.baseData || !this.baseData.event_categories) return [];

      if (this.eventType == "workshop")
        return this.baseData.workshop_categories.map((item) => {
          return { name: item.name, slug: item.slug };
        });
      else
        return this.baseData.private_or_public.map((item) => {
          return { name: item, slug: item };
        });
    },

    selectedMethods() {
      if (this.methodFilter.length === 0) return "Kaikki";
      return this.methodFilter;
    },
    selectedMonths() {
      if (this.monthFilters.length === 0) return "Kaikki";
      const addition = this.monthFilters.length > 1 ? " +" : "";

      return this.monthFilters.slice(0, 1).join(", ") + addition;
    },

    allCategories() {
      // Filtering filters (removing certain categories that client don't want to show)

      const filtered = this.filterBlacklistedCategories();
      // return all category slugs
      return filtered.map((filter) => filter.slug.toLowerCase());
    },
    filteredButtons() {
      return this.filterBlacklistedCategories();
    },
  },

  methods: {
    getPostsPerPage() {
      // Change the posts per page amount on frontpage
      if (this.isFrontPage() == true) {
        let amount = document.querySelector(".event-calendar").dataset.amount;
        return amount;
      } else {
        return 8;
      }
    },

    isFrontPage() {
      // Checking is page is frontpage
      let isFrontpage = document.querySelector(".is-front-page");
      if (isFrontpage) {
        return true;
      } else {
        return false;
      }
    },

    filterBlacklistedCategories() {
      const blacklist = [
        "digitalisaatio-ja-kasvu",
        "maankaytto-ja-liikenne",
        "osaaminen-ja-tyovoima",
        "vastuullisuus-ja-yritysturvallisuus",
        "verkostoituminen",
      ];

      // Getting filterable categies list and applying blacklist
      const filtered = this.filterButtons.filter(
        (filter) => !blacklist.includes(filter.slug)
      );
      return filtered;
    },

    offClick(e) {
      this.filterOpen = null;
    },
    /**
     * @param {number} num
     */
    gotoPage(num) {
      this.currentPage = num;
      this.runFilter();
    },
    /**
     * @param {any[]} categories
     */
    updateCategoryFilter(categories) {
      if (categories) this.filters = categories;

      this.currentPage = 1;
      this.runFilter();
    },
    /**
     * @param {string} method
     */
    updateMethodFilter(method) {
      if (this.methodFilter === method) return;
      this.currentPage = 1;
      this.methodFilter = method;
      this.filterOpen = null;
      this.runFilter();
    },
    /**
     * @param {any[]} month
     */
    updateMonthFilter(month) {
      this.currentPage = 1;
      if (month) this.monthFilters = month;
      this.filterOpen = null;
      this.runFilter();
    },
    resetExtraFilters() {
      this.monthFilters = [];
      this.methodFilter = "";
      this.currentPage = 1;
      this.runFilter();
    },
    runFilter() {
      if (this.isWorkshop) this.getWorkshops();
      else this.getEvents();
    },
    getWorkshops() {
      const categoryFilters = this.filters.length
        ? this.filters
        : this.allCategories;

      // Build taxonomy query
      const taxQuery = [
        {
          taxonomy: this.eventType + "_category",
          field: "slug",
          terms: categoryFilters,
        },
      ];

      let today = new Date();
      const dd = String(today.getDate()).padStart(2, "0");
      const mm = String(today.getMonth() + 1).padStart(2, "0"); //January is 0!
      const yyyy = today.getFullYear();

      today = yyyy + "-" + mm + "-" + dd + " 23:59:59";

      // Init meta query with start_date, which is needed for ordering
      const metaQuery = [
        {
          key: "start_date",
          value: dates.limit,
          compare: "<=",
          type: "DATE",
        },
        {
          key: "start_date",
          value: today,
          compare: ">=",
          type: "DATE",
        },
        {
          key: "event_type",
          value: "workshop",
          compare: "==",
          type: "STRING",
        },
      ];

      metaQuery["relation"] = "AND";

      // Add method filtering to meta query (if selected)
      if (this.methodFilter) {
        metaQuery.push({
          key: "workshop_method",
          value: this.methodFilter,
          compare: "LIKE",
        });
      }

      // Add month filtering to meta query (if selected)
      if (this.monthFilters.length) {
        const subQuery = [];
        subQuery["relation"] = "OR";

        this.monthFilters.forEach((month) => {
          subQuery.push({
            key: "workshop_month",
            value: month,
            compare: "LIKE",
          });
        });

        metaQuery.push(subQuery);
      }
      // console.log('query', metaQuery)

      // Wrap up wp query args
      const args = {
        post_type: "event",
        posts_per_page: this.postsPerPage,
        meta_query: metaQuery,
        meta_type: "DATE",
        orderby: { start_date: "ASC", title: "ASC" },
        paged: this.currentPage,
        tax_query: taxQuery,
      };

      // console.log(args)
      this.fetchEvents(args);
    },

    getEvents() {
      var today = new Date();
      var dd = String(today.getDate()).padStart(2, "0");
      var mm = String(today.getMonth() + 1).padStart(2, "0"); //January is 0!
      var yyyy = today.getFullYear();

      today = yyyy + "-" + mm + "-" + dd + " 23:59:59";
      
      const metaQuery = [
        {
          key: "start_date",
          value: dates.limit,
          compare: "<=",
          type: "DATE",
        },
        {
          key: "start_date",
          value: today,
          compare: ">=",
          type: "DATE",
        },
        {
          key: "event_type",
          value: "event",
          compare: "==",
          type: "STRING",
        },
      ];

      metaQuery["relation"] = "AND";

      // Add month filtering to meta query (if selected)
      if (this.monthFilters.length) {
        const subQuery = [];
        subQuery["relation"] = "OR";

        this.monthFilters.forEach((month) => {
          subQuery.push({
            key: "event_month",
            value: month,
            compare: "LIKE",
          });
        });

        metaQuery.push(subQuery);
      }

      // Wrap up wp query args
      const args = {
        post_type: "event",
        posts_per_page: this.postsPerPage,
        meta_query: metaQuery,
        //'meta_type': 'DATE',
        orderby: { start_date: "ASC", title: "ASC" },
        paged: this.currentPage,
      };

      this.fetchEvents(args);
    },

    fetchEvents(args) {
      this.loading = true;
      fetch(addQueryArgs(hrefDefinition + "/wp-json/wp_query/args/", args))
        .then((response) => {
          if (!response.ok) Promise.reject(response);
          if (response.headers.has("x-wp-total"))
            this.totalEvents = parseInt(response.headers.get("x-wp-total"));
          if (response.headers.has("x-wp-totalpages"))
            this.totalPages = parseInt(response.headers.get("x-wp-totalpages"));
          return response.json();
        })
        .then((data) => {
          // console.log('response data', data)
          this.rawData = data;
          this.loading = false;
        })
        .catch((err) => {
          // There was an error.
          console.warn("Something went wrong.", err);
          this.loading = false;
        });
    },

    getData() {
      return fetch(
        hrefDefinition + "/wp-json/api/v1/cron/shopify_eventdata_json/"
      )
        .then((response) => response.json())
        .then((data) => (this.baseData = data));
    },
    /**
     * @param {string} type
     */
    switchEventType(type) {
      this.resetFilters();
      this.eventType = type;
      this.runFilter(true);
    },

    resetFilters() {
      this.filters = [];
      this.methodFilter = "";
      this.monthFilters = [];
      this.currentPage = 1;
    },
  },
};
</script>
<style lang="scss">
$darkBlue: #002663;
$liteBlue: #d8e3f2;
$darkGrey: #333;
$grey: #ebebeb;
$greyChamber: #dbdbdb;
$mediumGrey: #6d6d6d;
$tablet: 768px;
$font-text: "Work Sans", "myriad-pro", "Helvetica Neue", Helvetica, "Roboto",
  Arial, sans-serif;

#event-calendar-wp-app,
#event-calendar-wp-app button {
  font-family: $font-text;
}
.icon-wrapper {
  display: inline-block;
  svg {
    height: 14px;
    width: 14px;
  }
  &.cancel-icon {
    position: relative;
    top: 2px;
  }
}
.event-type-selector {
  margin-bottom: 2rem;
}
button.main-link {
  background: none;
  border: none;
  outline: 0;
  text-transform: uppercase;
  display: inline-block;
  // background-color:red;
  padding: 1px 20px;
  color: $mediumGrey;
  font-size: 19px;
  font-weight: 600;
  border-bottom: 3px solid #e1e1e1;
  cursor: pointer;
  &:focus {
    border: 1px dotted $darkBlue;
  }
  &--is-selected {
    color: $darkBlue;
    border-color: $darkBlue;
  }
}

.filter-buttons {
  display: flex;
  gap: 18px 10px;
  flex-wrap: wrap;
  margin-bottom: 2rem;
  button {
    background-color: transparent;
    border: none;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    span {
      display: inline-block;
      border-radius: 30px;
      padding: 0.75rem 1.5rem;
      background-color: $liteBlue;
      transition: all 450ms;
      line-height: 1.2;
      font-size: 15px;
      color: $darkBlue;
      // font-weight: 600;
      &:hover {
        background: darken($liteBlue, 10%);
      }
    }
    &.active span {
      background: $darkBlue;
      color: white;
    }
    input[type="checkbox"] {
      position: absolute;
      left: -9999rem;
      &:is(:checked) + span {
        background-color: $darkBlue;
        color: #fff;
      }
    }
  }
}

.sort-tools {
  margin-bottom: 1rem;
  @media (min-width: $tablet) {
    display: flex;
    gap: 1rem;
    align-items: flex-end;
  }
  label {
    color: $darkGrey;
    display: block;
    margin-bottom: 5px;
  }
}
.checkboxes-filter-wrapper {
  position: relative;
  .btn-filter-toggle {
    width: 180px;
    text-align: left;
    display: flex;
    text-transform: capitalize;
    .icon {
      margin-left: auto;
    }
  }
  .filter-content {
    position: absolute;
    width: calc(100vw - 2rem);
    display: grid;
    grid-template-columns: 1fr;
    justify-content: space-between;
    gap: 6px;
    z-index: 2;
    background: white;
    padding: 1rem;
    border: 1px solid $grey;
    box-shadow: 0px 3px 4px -2px rgb(0 0 0 / 10%);
    @media (min-width: $tablet) {
      width: 200px;
    }
    option.checkbox {
      cursor: pointer;
      &:hover {
        color: $darkBlue;
      }
      // text-align: center;
    }
  }
}

.event-filler {
  height: 120px;
  color: $grey;
  border: 1px dotted $grey;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-normal {
  border: 1px solid $greyChamber;
  border-radius: 2px;
  background: white;
  padding: 10px 14px;
  font-size: 15px;
  transition: all 100ms;
  cursor: pointer;
  &.btn-filter {
    border-radius: 200px;
  }
  &:hover {
    border-color: darken($grey, 20%);
  }
}
.btn-link {
  border-color: transparent !important;
  color: $darkBlue;
  &:hover {
    color: lighten($darkBlue, 20%);
  }
}
.select-method-wrapper {
  position: relative;
  .icon {
    position: absolute;
    top: 0;
    left: auto;
    bottom: 0;
    right: 0.5rem;
    margin: auto;
    display: block;
    height: 16px;
    width: 16px;
    pointer-events: none;
  }
}
.select-method {
  display: block;
  -moz-appearance: none; /* Firefox */
  -webkit-appearance: none; /* Safari and Chrome */
  appearance: none;
}
.btn-filter {
  background: $darkBlue;
  border-color: $darkBlue !important;
  color: white;
  min-width: 200px;
  &:hover {
    background: lighten($darkBlue, 10%);
  }
}
.capitalize-me {
  text-transform: capitalize;
}

.paging--top {
  padding-top: 0;
  padding-bottom: 0;
}
</style>
