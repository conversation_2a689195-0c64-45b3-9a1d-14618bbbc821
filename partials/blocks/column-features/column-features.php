<?php

/**
 * Column features
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-column-features' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-column-features';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$content = get_field('column-features');

?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">

  <div class="block-column-features__wrap">

    <span class="block-column-features__subtitle">
      <?php echo $content['small_title']; ?>
    </span>

    <?php
    if($content['title']) {
      echo '<h3 class="block-column-features__title">' . $content['title'] .'</h3>';
    }
    ?>

    <div class="block-column-features__content_wrap">
      <?php foreach ($content['columns'] as $column) { ?>
        <div class="block-column-features__item">
          <span class="block-column-features__item--title"><?php echo $column['title']; ?></span>
          <span class="block-column-features__item--content"><?php echo $column['kuvaus']; ?></span>
          <?php if(isset($column) && isset($column['link']) && isset($column['link']['url']) && $column['link']['url']) {
            echo '<a class="block-column-features__item--link" href="' . $column['link']['url'] . '" >' . $column['link']['title'] . '</a>';
          }
          if(isset($column['img']) && isset($column['img']['url'])) {
          ?>
          <img src="<?php echo $column['img']['url']; ?>" alt="<?php echo $column['title']; ?>">
          <?php
          }
          ?>
        </div>
      <?php } ?>
    </div>

  </div>

</div>
