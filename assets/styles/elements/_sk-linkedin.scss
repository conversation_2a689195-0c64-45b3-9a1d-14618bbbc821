.sk-posts-masonry {
  display: grid !important;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 3rem 2.25rem;
}

.sk-posts-header,
.sk-post-username,
.sk-post-footer,
.sk-posts-footer,
.sk_branding {
  display: none !important;
}

.sk-post-userpic {
  border-radius: 0px !important;
}

body .sk-ww-linkedin-page-post .sk-posts-grid {
  gap: 15px !important;
}

.sk-post-item {
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  border: 1px solid #bfbfbf !important;
  border-radius: 11px !important;
}

.sk-post-dateposted {
  font-family: $font-text !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #636363 !important;
}

.sk-post-body-full {
  font-family: $font-text !important;
  color: #212121 !important;
}

.sk-post-body-full a {
  color: #002663 !important;
}

.sk-post-body {
  margin-bottom: 0 !important;
}

.sk-post-shared {
  margin-top: 1em;
}

.sk-shared-username {
  font-family: $font-text !important;
}

.sk-shared-userpic {
  min-width: 42px;
}
