{"version": 3, "file": "rss-feed/index.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAmC;AAAA;AAEpB,SAASG,IAAIA,CAAA,EAAG;EAC9B,oBAAOD,sDAAA,CAACF,mDAAO,IAAE,CAAC;AACnB;;;;;;;;;;;;;;;;ACJ4C;AACT;;AAEnC;AAAA;AACA,SAASK,eAAeA,CAAA,EAAG;EAC1B,oBAAOH,sDAAA,CAACF,mDAAO,IAAE,CAAC;AACnB;;AAEA;AACAM,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;EACnDD,QAAQ,CAACE,gBAAgB,CAAC,gBAAgB,CAAC,CAACC,OAAO,CAAEC,EAAE,IAAK;IAC3D,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,EAAE,CAACI,OAAO,CAACC,UAAU,IAAI,IAAI,CAAC;IACvDX,0DAAM,cAACF,sDAAA,CAACG,eAAe;MAACU,UAAU,EAAEJ;IAAM,CAAE,CAAC,EAAED,EAAE,CAAC;EACnD,CAAC,CAAC;AACH,CAAC,CAAC;;;;;;;;;;;;;;;;;;ACdF;AACA;AACA;AACA;AACA;AACsD;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACsB;;AAEtB;AACA;AACA;AAC0B;AACA;AACU;;AAEpC;AACA;AACA;AACoB;;AAEpB;AACA;AACA;AACA;AACA;AACAM,oEAAiB,CAACE,6CAAa,EAAE;EAChC;AACD;AACA;EACCE,IAAI,EAAEjB,6CAAI;EAEV;AACD;AACA;EACCc,IAAIA,+CAAAA;AACL,CAAC,CAAC;;;;;;;;;;;;;;;;AC3CF;AACA;AACA;AACA;AACA;AACA;AACwD;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,IAAIA,CAAA,EAAG;EAC9B,OAAO,IAAI,CAAC,CAAC;AACd;;;;;;;;;;;ACnBA;;;;;;;;;;;;;;;;;;;ACAyD;;AAEzD;AAAA;AACA,SAASjB,OAAOA,CAAA,EAAG;EAClB,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAGL,4DAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAGP,4DAAQ,CAAC,IAAI,CAAC;EAE5CC,6DAAS,CAAC,MAAM;IACf;IACA,MAAMO,aAAa,GAAG,MAAAA,CAAA,KAAY;MACjCD,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACH;QACA,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAC3B,qFACD,CAAC;QACD,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACE,IAAI,CAAC,CAAC;QAClC,MAAMC,MAAM,GAAG,IAAIC,SAAS,CAAC,CAAC;QAC9B,MAAMC,MAAM,GAAGF,MAAM,CAACG,eAAe,CAACJ,IAAI,EAAE,iBAAiB,CAAC;;QAE9D;QACA,MAAMK,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAAC5B,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACzD,MAAMiC,MAAM,GAAGH,KAAK,CAClBI,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;UACtBC,EAAE,EAAED,KAAK;UACTE,KAAK,EAAEH,IAAI,CAACI,aAAa,CAAC,OAAO,CAAC,EAAEC,WAAW,IAAI,aAAa;UAChEC,IAAI,EAAE,IAAIC,IAAI,CACbP,IAAI,CAACI,aAAa,CAAC,SAAS,CAAC,EAAEC,WAChC,CAAC,CAACG,kBAAkB,CAAC,OAAO,CAAC;UAC7BC,OAAO,EACNT,IAAI,CAACU,oBAAoB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAEL,WAAW,IACvD,YAAY;UACbM,IAAI,EAAEX,IAAI,CAACI,aAAa,CAAC,MAAM,CAAC,EAAEC;QACnC,CAAC,CAAC;QACF;QAAA,CACCO,MAAM,CACLC,OAAO,IACP,CAAC,CAAC,UAAU,EAAE,oBAAoB,CAAC,CAACC,QAAQ,CAACD,OAAO,CAACJ,OAAO,CAC9D,CAAC;QAEFzB,WAAW,CAACc,MAAM,CAAC;MACpB,CAAC,CAAC,OAAOiB,GAAG,EAAE;QACb;QACAC,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAAEF,GAAG,CAAC;MACrD,CAAC,SAAS;QACT7B,UAAU,CAAC,KAAK,CAAC;MAClB;IACD,CAAC;IAEDC,aAAa,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACC5B,sDAAA;IAAA2D,QAAA,EACEjC,OAAO;IAAA;IACP;IACA1B,sDAAA;MAAA2D,QAAA,EAAG;IAAwB,CAAG,CAAC;IAAA;IAE/B;IACA3D,sDAAA;MAAK4D,KAAK,EAAC,aAAa;MAAAD,QAAA,EACtBnC,QAAQ,CAACqC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACrB,GAAG,CAAEc,OAAO,iBACjC/B,uDAAA;QAAsBqC,KAAK,EAAC,iBAAiB;QAAAD,QAAA,gBAC5C3D,sDAAA;UAAI4D,KAAK,EAAC;QAA2B,CAAK,CAAC,eAC3CrC,uDAAA;UAAGqC,KAAK,EAAC,MAAM;UAAAD,QAAA,GACbL,OAAO,CAACP,IAAI,EAAC,GAAC,eAAA/C,sDAAA;YAAM4D,KAAK,EAAC,WAAW;YAAAD,QAAA,EAAC;UAAC,CAAM,CAAC,EAAC,GAAG,EAClDL,OAAO,CAACJ,OAAO;QAAA,CACd,CAAC,eACJlD,sDAAA;UAAA2D,QAAA,eACC3D,sDAAA;YACC8D,IAAI,EAAER,OAAO,CAACF,IAAK;YACnBW,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YAAAL,QAAA,EAExBL,OAAO,CAACV;UAAK,CACZ;QAAC,CACD,CAAC;MAAA,GAdIU,OAAO,CAACX,EAeb,CACL;IAAC,CACE;EACL,CACG,CAAC;AAER;AAEA,iEAAe7C,OAAO;;;;;;;;;;ACpFtB;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;UAEA;UACA;;;;;WCzBA;WACA;WACA;WACA;WACA,+BAA+B,wCAAwC;WACvE;WACA;WACA;WACA;WACA,iBAAiB,qBAAqB;WACtC;WACA;WACA,kBAAkB,qBAAqB;WACvC;WACA;WACA,KAAK;WACL;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;;;;WC3BA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;WCNA;;WAEA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,MAAM,qBAAqB;WAC3B;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;WACA;WACA;;;;;UEjDA;UACA;UACA;UACA;UACA", "sources": ["webpack://rss-feed/./src/rss-feed/edit.js", "webpack://rss-feed/./src/rss-feed/frontend.js", "webpack://rss-feed/./src/rss-feed/index.js", "webpack://rss-feed/./src/rss-feed/save.js", "webpack://rss-feed/./src/rss-feed/style.scss?40b3", "webpack://rss-feed/./src/rss-feed/useRSSFeed.js", "webpack://rss-feed/external window [\"wp\",\"blockEditor\"]", "webpack://rss-feed/external window [\"wp\",\"blocks\"]", "webpack://rss-feed/external window [\"wp\",\"element\"]", "webpack://rss-feed/external window \"ReactJSXRuntime\"", "webpack://rss-feed/webpack/bootstrap", "webpack://rss-feed/webpack/runtime/chunk loaded", "webpack://rss-feed/webpack/runtime/compat get default export", "webpack://rss-feed/webpack/runtime/define property getters", "webpack://rss-feed/webpack/runtime/hasOwnProperty shorthand", "webpack://rss-feed/webpack/runtime/make namespace object", "webpack://rss-feed/webpack/runtime/jsonp chunk loading", "webpack://rss-feed/webpack/before-startup", "webpack://rss-feed/webpack/startup", "webpack://rss-feed/webpack/after-startup"], "sourcesContent": ["import RSSFeed from \"./useRSSFeed\";\n\nexport default function Edit() {\n\treturn <RSSFeed />;\n}\n", "import { render } from \"@wordpress/element\";\nimport RSSFeed from \"./useRSSFeed\";\n\n// Front-end component for rendering RSS feed\nfunction RSSFeedFrontend() {\n\treturn <RSSFeed />;\n}\n\n// Mount the React component to all RSS feed block roots on the page\ndocument.addEventListener(\"DOMContentLoaded\", () => {\n\tdocument.querySelectorAll(\"#rss-feed-root\").forEach((el) => {\n\t\tconst attrs = JSON.parse(el.dataset.attributes || \"{}\");\n\t\trender(<RSSFeedFrontend attributes={attrs} />, el);\n\t});\n});\n", "/**\n * Registers a new block provided a unique name and an object defining its behavior.\n *\n * @see https://developer.wordpress.org/block-editor/reference-guides/block-api/block-registration/\n */\nimport { registerBlockType } from \"@wordpress/blocks\";\n\n/**\n * Lets webpack process CSS, SASS or SCSS files referenced in JavaScript files.\n * All files containing `style` keyword are bundled together. The code used\n * gets applied both to the front of your site and to the editor.\n *\n * @see https://www.npmjs.com/package/@wordpress/scripts#using-css\n */\nimport \"./style.scss\";\n\n/**\n * Internal dependencies\n */\nimport Edit from \"./edit\";\nimport save from \"./save\";\nimport metadata from \"./block.json\";\n\n/**\n * Frontend script\n */\nimport \"./frontend\";\n\n/**\n * Every block starts by registering a new block type definition.\n *\n * @see https://developer.wordpress.org/block-editor/reference-guides/block-api/block-registration/\n */\nregisterBlockType(metadata.name, {\n\t/**\n\t * @see ./edit.js\n\t */\n\tedit: Edit,\n\n\t/**\n\t * @see ./save.js\n\t */\n\tsave,\n});\n", "/**\n * React hook that is used to mark the block wrapper element.\n * It provides all the necessary props like the class name.\n *\n * @see https://developer.wordpress.org/block-editor/reference-guides/packages/packages-block-editor/#useblockprops\n */\nimport { useBlockProps } from \"@wordpress/block-editor\";\n\n/**\n * The save function defines the way in which the different attributes should\n * be combined into the final markup, which is then serialized by the block\n * editor into `post_content`.\n *\n * @see https://developer.wordpress.org/block-editor/reference-guides/block-api/block-edit-save/#save\n *\n * @return {Element} Element to render.\n */\nexport default function save() {\n\treturn null; // Return nothing because PHP handles the rendering\n}\n", "// extracted by mini-css-extract-plugin\nexport {};", "import { useState, useEffect } from \"@wordpress/element\";\n\n// Front-end component for rendering RSS feed\nfunction RSSFeed() {\n\tconst [articles, setArticles] = useState([]);\n\tconst [loading, setLoading] = useState(true);\n\n\tuseEffect(() => {\n\t\t// Fetch RSS articles on initial component mount\n\t\tconst fetchArticles = async () => {\n\t\t\tsetLoading(true);\n\t\t\ttry {\n\t\t\t\t// Retrieve and parse the RSS feed XML\n\t\t\t\tconst response = await fetch(\n\t\t\t\t\t\"https://www.retriever-info.com/feed/2011171/helsingin-seudun-kauppakamari/index.xml\",\n\t\t\t\t);\n\t\t\t\tconst text = await response.text();\n\t\t\t\tconst parser = new DOMParser();\n\t\t\t\tconst xmlDoc = parser.parseFromString(text, \"application/xml\");\n\n\t\t\t\t// Extract article data from XML items\n\t\t\t\tconst items = Array.from(xmlDoc.querySelectorAll(\"item\"));\n\t\t\t\tconst parsed = items\n\t\t\t\t\t.map((item, index) => ({\n\t\t\t\t\t\tid: index,\n\t\t\t\t\t\ttitle: item.querySelector(\"title\")?.textContent || \"Ei otsikkoa\",\n\t\t\t\t\t\tdate: new Date(\n\t\t\t\t\t\t\titem.querySelector(\"pubDate\")?.textContent,\n\t\t\t\t\t\t).toLocaleDateString(\"fi-FI\"),\n\t\t\t\t\t\tcreator:\n\t\t\t\t\t\t\titem.getElementsByTagName(\"dc:creator\")[0]?.textContent ||\n\t\t\t\t\t\t\t\"Tuntematon\",\n\t\t\t\t\t\tlink: item.querySelector(\"link\")?.textContent,\n\t\t\t\t\t}))\n\t\t\t\t\t// Filter out items with creator as \"STT Info\" or \"Kauppalehti KL Nyt\"\n\t\t\t\t\t.filter(\n\t\t\t\t\t\t(article) =>\n\t\t\t\t\t\t\t![\"STT Info\", \"Kauppalehti KL Nyt\"].includes(article.creator),\n\t\t\t\t\t);\n\n\t\t\t\tsetArticles(parsed);\n\t\t\t} catch (err) {\n\t\t\t\t// Log any errors during fetch/parsing\n\t\t\t\tconsole.error(\"Virhe haettaessa RSS-syötettä:\", err);\n\t\t\t} finally {\n\t\t\t\tsetLoading(false);\n\t\t\t}\n\t\t};\n\n\t\tfetchArticles();\n\t}, []);\n\n\treturn (\n\t\t<div>\n\t\t\t{loading ? (\n\t\t\t\t// Show loading message while fetching articles\n\t\t\t\t<p>Ladataan RSS-syötettä...</p>\n\t\t\t) : (\n\t\t\t\t// Render the parsed articles\n\t\t\t\t<div class=\"rss-wrapper\">\n\t\t\t\t\t{articles.slice(0, 6).map((article) => (\n\t\t\t\t\t\t<div key={article.id} class=\"wp-block-column\">\n\t\t\t\t\t\t\t<hr class=\"wp-block-separator rss-hr\"></hr>\n\t\t\t\t\t\t\t<p class=\"meta\">\n\t\t\t\t\t\t\t\t{article.date} <span class=\"separator\">|</span>{\" \"}\n\t\t\t\t\t\t\t\t{article.creator}\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t<h3>\n\t\t\t\t\t\t\t\t<a\n\t\t\t\t\t\t\t\t\thref={article.link}\n\t\t\t\t\t\t\t\t\ttarget=\"_blank\"\n\t\t\t\t\t\t\t\t\trel=\"noopener noreferrer\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{article.title}\n\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t</h3>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t))}\n\t\t\t\t</div>\n\t\t\t)}\n\t\t</div>\n\t);\n}\n\nexport default RSSFeed;\n", "module.exports = window[\"wp\"][\"blockEditor\"];", "module.exports = window[\"wp\"][\"blocks\"];", "module.exports = window[\"wp\"][\"element\"];", "module.exports = window[\"ReactJSXRuntime\"];", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = (result, chunkIds, fn, priority) => {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar [chunkIds, fn, priority] = deferred[i];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t\"rss-feed/index\": 0,\n\t\"rss-feed/style-index\": 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = globalThis[\"webpackChunkrss_feed\"] = globalThis[\"webpackChunkrss_feed\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [\"rss-feed/style-index\"], () => (__webpack_require__(\"./src/rss-feed/index.js\")))\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n", ""], "names": ["RSSFeed", "jsx", "_jsx", "Edit", "render", "RSSFeedFrontend", "document", "addEventListener", "querySelectorAll", "for<PERSON>ach", "el", "attrs", "JSON", "parse", "dataset", "attributes", "registerBlockType", "save", "metadata", "name", "edit", "useBlockProps", "useState", "useEffect", "jsxs", "_jsxs", "articles", "setArticles", "loading", "setLoading", "fetchArticles", "response", "fetch", "text", "parser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xmlDoc", "parseFromString", "items", "Array", "from", "parsed", "map", "item", "index", "id", "title", "querySelector", "textContent", "date", "Date", "toLocaleDateString", "creator", "getElementsByTagName", "link", "filter", "article", "includes", "err", "console", "error", "children", "class", "slice", "href", "target", "rel"], "sourceRoot": ""}