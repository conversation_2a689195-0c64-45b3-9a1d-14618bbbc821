<template>
  <div class="event-list-wrapper">
    <p v-if="getEventsLength === 0" class="events-not-found">
      Ei näytettäviä tapahtumia. Kokeile muuttaa hakutermejä.
    </p>
    <div v-for="month in sortedMonthKeys">
      <div class="month-header">
        {{ eventsByMonth[month].name }}
      </div>
      <EventRow
        :event="event"
        v-for="event in eventsByMonth[month].events"
        :isWorkshop="isWorkshop"
      />
    </div>
  </div>
</template>

<script>
import EventRow from "./EventRow.vue";
import { monthIndex } from "../helpers";

export default {
  props: ["rawData", "isWorkshop"],
  components: { EventRow },
  computed: {
    events() {
      if (Object.keys(this.rawData).length === 0) return [];
      const events = [];
      const postCount = Object.keys(this.rawData).length - 1;
      for (let i = 0; i < postCount; i++) {
        const event = this.rawData[i];
        const meta = this.rawData.meta ? this.rawData.meta[event.id] : {};
        events.push({ ...event, ...meta });
      }
      return events;
    },

    eventsByMonth() {
      if (this.events.length === 0) return {};
      const groupedMonths = this.events.reduce((prev, item) => {
        const monthArr = item.month
          ? item.month.split(" ")
          : ["Ei aikataulutettu", ""];

        const monthKey = monthArr[1] + "_" + monthIndex[monthArr[0]];
        if (!prev[monthKey])
          prev[monthKey] = { name: monthArr.join(" "), events: [] };
        prev[monthKey].events.push(item);
        return prev;
      }, {});

      return groupedMonths;
    },

    getEventsLength() {
      const arr = Object.keys(this.eventsByMonth);
      return arr.length;
    },

    sortedMonthKeys() {
      const keys = Object.keys(this.eventsByMonth);
      return keys.sort();
    },
  },
};
</script>
<style lang="scss">
$darkBlue: #002663;
$separatorblue: #e4eff8;
$grey: #ebebeb;

.month-header {
  font-size: 18px;
  margin-top: 2rem;
  border-bottom: 2px solid $separatorblue;
  padding: 4px 0;
  font-weight: 600;
  text-transform: uppercase;
  color: $darkBlue;
}

button.main-link {
  background: none;
  border: none;
  outline: 0;
  text-transform: uppercase;
  display: inline-block;
  // background-color:red;
  padding: 5px 20px;
  color: #d8d8d8;
  border-bottom: 2px solid #d8d8d8;
  &--is-selected {
    color: $darkBlue;
    border-color: $darkBlue;
  }
}

.filter-buttons {
  label {
    display: inline-block;
    position: relative;
    overflow: hidden;
    span {
      display: inline-block;
      border-radius: 15px;
      padding: 4px 20px;
      background-color: #d8e3f2;
      margin: 5px;
      transition: all 450ms;
    }
    &.active span {
      background: $darkBlue;
      color: white;
    }
    input[type="checkbox"] {
      position: absolute;
      left: -9999rem;
      &:is(:checked) + span {
        background-color: $darkBlue;
        color: #fff;
      }
    }
  }
}

.events-not-found {
  margin-top: 1rem;
  font-weight: 500;
}
</style>
