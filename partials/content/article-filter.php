<?php

/**
 * Template part: Article filter
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package kauppakamari
 */


/*
[term_id] => 2736
[name] => Artikkelin Alateema1
[slug] => artikkelin-alateema1
[term_group] => 0
[term_taxonomy_id] => 2736
[taxonomy] => category
[description] =>
[parent] => 2734
[count] => 1
[filter] => raw
[term_order] => 1
*/



/**
 * takes child slug, returns object
 */
function get_article_type_slug_by_child_category_slug($child_category_slug)
{
    $category_term = get_term_by('slug', $child_category_slug, 'category');
    $parent_category_term = get_term_by('id', $category_term->parent, 'category');
    $parent_category_term_slug = $parent_category_term->slug;
    //use slug to get the same article_type:
    return get_term_by('slug', $parent_category_term_slug, 'article_type');
}


/**
 * $article_type_slug is category_taxomy or article_type_taxonomy value, does not matter which one we know
 */
function get_categories_by_article_type_slug($article_type_slug)
{
    $parent_category_term = get_term_by('slug', $article_type_slug, 'category');
    if (!$parent_category_term) return array();
    $parent_category_term_id = $parent_category_term->term_id;
    $category_terms = get_terms(array(
        'taxonomy' => 'category',
        'hide_empty' => false,
    ));

    if ($parent_category_term_id > 0) {
        $category_terms = array_filter($category_terms, function ($k) use ($parent_category_term_id) {
            return $k->parent == $parent_category_term_id;
        });
    } else {
        return array();
    }

    return $category_terms;
}


$article_type_terms = get_terms(array(
    'taxonomy' => 'article_type',
    'hide_empty' => false,
    'meta_key'  => 'show_article_type_in_filters',
    'meta_value' => true,
));


//$category_terms = get_terms( array(
//    'taxonomy' => 'category',
//    'hide_empty' => false,
//) );
//GET:
$term = get_queried_object();

//CATEGORY GIVEN:
if (!isset($term)) {
    /* we are at main level, only type selected*/
    $article_type_parameter = 'kaikki'; // blogit - article_type
    $category_name = 'kaikki';
    $category_slug = 'kaikki';
    $article_category_parameter = 'kaikki';
} else if ($term->taxonomy == 'category') {
    /* we are at category sub-level, find out the type */
    $parent_category_id = $term->parent; //this id is for cat, not for type
    $category_name = $term->name;
    $category_slug = $term->slug;
    $article_category_parameter = $category_slug;

    $type_term = get_article_type_slug_by_child_category_slug($category_slug);
    $article_type_parameter = $type_term->slug;
    $filter_title_type = $type_term->name;
    $filter_title = $type_term->name;
} else if ($term->taxonomy == 'article_type') {
    /* we are at main level, only type selected*/
    $article_type_parameter = $term->slug; // blogit - article_type
    $category_name = '';
    $category_slug = '';
    $article_category_parameter = '';
    $filter_title_type = $term->name;
    $filter_title = $term->name;
}

if ($category_slug == "kaikki" || strtolower($filter_title) == "kaikki") {
    $filter_title_type = 'Kaikki ajankohtaiset';
    $filter_title = 'Kaikki ajankohtaiset';
}

if (strlen($filter_title) > 1 && strlen($category_name) > 1 && $article_type_parameter != "kaikki") {
    $filter_title .= ', ' . $category_name;
} else {
    //??
}

$category_terms = get_categories_by_article_type_slug($article_type_parameter);


//year url: 2021/?article_type=lausunnot
$query_year = get_query_var('year');
$years_arr = array();


if ($query_year || $article_type_parameter == 'lausunnot') {
    for ($i = 2021; $i <= date('Y'); $i++) {
        $years_arr[] = $i;
    }
} else {
    $query_year = false;
}




//echo '<div style="display: none;">';
//echo '<p style="font-size: 10px; line-height: 3px;">Annettu term slug: '.$term->slug.'</p>'; //slug, taxonomy, name
//echo '<p style="font-size: 10px; line-height: 3px;">Annettu term taxonomy: '.$term->taxonomy.'</p>'; //slug, taxonomy, name
//echo '<p style="font-size: 10px; line-height: 3px;"></p>';
//echo '<p style="font-size: 10px; line-height: 3px;">TYPE: '.$article_type_parameter.'</p>';
//echo '<p style="font-size: 10px; line-height: 3px;">CAT: '.$article_category_parameter.'</p>';
//echo '</div>';


if (empty($filter_title_type)) $filter_title_type = 'Artikkelit';

if (!is_404()) {
    echo '<h1 class="archive-title">' . $filter_title_type . '</h1>';
}

?>


<div class="article-filter-container">
    <div class="article-filter">
        <div class="article-filter--post-types post-type-<?php echo $article_type_parameter; ?>">
            <!--<p aria-hidden="true" class="filter-label">Valitse sisältötyyppi</p>-->
            <label for="article-filter--tabs--select" class="filter-label" aria-label="Valitse sisältötyyppi"><?php pll_e("Valitse sisältötyyppi") ?></label>
            <?php

            //echo '<label for="article-filter--tabs--select" class="mobile-only filter-label" aria-label="Valitse sisältötyyppi">Valitse sisältötyyppi</label>';

            echo '<ul class="article-filter--tabs">';
            foreach ($article_type_terms as $article_type_term) {
                $active = ($article_type_term->slug == $article_type_parameter ? 'active' : '');

                $term_link = get_term_link($article_type_term->term_id);
                if ($active) {
                    $category_all_link = $term_link;
                }

                echo '<li class="' . $active . '">';
                echo '<a href="' . $term_link . '" class="post-type-term-tab">' . $article_type_term->name . '</a>';
                echo '</li>';
            }
            echo '</ul>';

            //FOR MOBILE:
            echo '<select class="mobile-only article-filter--select id="article-filter--tabs--select" aria-label="<?php pll_e("Valitse sisältötyyppi") ?>">';
            foreach ($article_type_terms as $article_type_term) {
                $active = ($article_type_term->slug == $article_type_parameter ? 'selected="selected"' : '');

                $term_link = get_term_link($article_type_term->term_id);
                echo '<option ' . $active;
                echo ' value="' . $term_link . '">' . $article_type_term->name;
                echo '</option>';
            }
            echo '</select>';


            ?>
        </div>

        <div class="article-filter--category post-type-<?php echo $article_type_parameter; ?> article-category-<?php echo $article_category_parameter; ?>">
            <?php
            if ($article_type_parameter != "kaikki") {
                $choose_content_text = ($query_year || sizeof($years_arr) > 1) ? 'vuosi' : 'teema';

                echo '<label for="article-filter--select--pills" class="filter-label" aria-label="Valitse ' . $choose_content_text . '"><?php pll_e("Valitse") ?> ' . $choose_content_text . '</label>'; //TEEMA


                echo '<ul class="article-filter--pills">';

                if (empty($article_category_parameter) && $query_year < 1) $active = 'active';
                echo '<li class="' . $active . '">';
                echo '<a href="' . esc_url( $category_all_link ) . '" class="post-type-term-tab">' . pll__( 'Kaikki' ) . '</a>';
                echo '</li>';

                /*expection, year:*/
                if (sizeof($years_arr) > 1) {
                    foreach ($years_arr as $year_term) {
                        $active = ($year_term == $query_year ? 'active' : '');

                        $term_link = '/' . $year_term . '/?article_type=lausunnot';

                        echo '<li class="' . $active . '">';
                        echo '<a href="' . $term_link . '" class="post-type-term-tab">' . $year_term . '</a>';
                        echo '</li>';
                    }
                } else {
                    foreach ($category_terms as $category_term) {
                        $active = ($category_term->slug == $article_category_parameter ? 'active' : '');

                        $term_link = get_term_link($category_term->term_id);

                        echo '<li class="' . $active . '">';
                        echo '<a href="' . $term_link . '" class="post-type-term-tab">' . $category_term->name . '</a>';
                        echo '</li>';
                    }
                }

                echo '</ul>';

                /* FOR MOBILE: */

                echo '<select class="mobile-only article-filter--select post-type-' . $article_type_parameter . ' article-category-' . $article_category_parameter . '" id="article-filter--select--pills" aria-label="<?php pll_e("Valitse") ?> ' . $choose_content_text . '">';


                echo '<option class="">';
                echo '<a href="' . $category_all_link . '" class="post-type-term-tab">Kaikki</a>';
                echo '</option>';

                /*expection, year 2 mobile :*/
                if (sizeof($years_arr) > 1) {
                    reset($years_arr);
                    foreach ($years_arr as $year_term) {
                        $active = ($year_term == $query_year ? 'selected="selected"' : '');

                        $term_link = '/' . $year_term . '/?article_type=lausunnot';
                        echo '<option ' . $active;
                        echo ' value="' . $term_link . '">' . $year_term;
                        echo '</option>';
                    }
                } else {
                    foreach ($category_terms as $category_term) {
                        $active = ($category_term->slug == $article_category_parameter ? 'selected="selected"' : '');

                        $term_link = get_term_link($category_term->term_id);
                        echo '<option ' . $active;
                        echo ' value="' . $term_link . '">' . $category_term->name;
                        echo '</option>';
                    }
                }



                echo '</select>';
            }

            // If show all categories
            if ($article_type_parameter == "kaikki") {
                // Remake the query to get all posts
                $args = array(
                    'post_type' => 'post',
                    "post_status" => "publish",
                    'posts_per_page' => get_query_var("posts_per_page"),
                    'paged' => get_query_var("paged"),
                );
                $wp_query = new WP_Query($args);
            }
            ?>
        </div>


    </div>
</div>