<?php
/**
 * Register: Assets
 *
 * Enqueue scripts and stylesheets for theme.
 * Append content into <head> or footer.
 * Include favicons.
 *
 * @package kauppakamari
 */

/**
 * Enqueue scripts and styles
 */
add_action('wp_enqueue_scripts', function() {

  // main css
  wp_enqueue_style(
    'kauppakamari-style',
    get_template_directory_uri() . '/dist/styles/main.css',
    [],
    kauppakamari_last_edited('css')
  );

  // critical js
  wp_enqueue_script(
    'kauppakamari-critical-js',
    get_template_directory_uri() . '/dist/scripts/critical.js',
    [],
    kauppakamari_last_edited('js'),
    false
  );

  // main js
  wp_enqueue_script(
    'kauppakamari-js',
    get_template_directory_uri() . '/dist/scripts/main.js',
    [],
    kauppakamari_last_edited('js'),
    true
  );
  wp_localize_script( 'kauppakamari-js', 'frontendajax', array( 'ajaxurl' => admin_url( 'admin-ajax.php' )));

  // remove gutenberg default stylesheets
  //wp_deregister_style('wp-block-library-theme');
  //wp_deregister_style('wp-block-library');

  // comments
  if (is_singular() && comments_open() && get_option('thread_comments')) {
    wp_enqueue_script('comment-reply');
  }

});

/**
 * Add data-attribute to main.js to avoid Cookiebot conflicts
 */
function fix_cookiebot_mainjs($tag, $handle, $src) {
  if ('kauppakamari-js' === $handle) {
    $tag = str_replace('src=', 'data-cookieconsent="ignore" src=', $tag);
  } else if ('kauppakamari-critical-js' === $handle) {
    $tag = str_replace('src=', 'data-cookieconsent="ignore" src=', $tag);
  }
  return $tag;
}
add_filter('script_loader_tag', 'fix_cookiebot_mainjs', 10, 3);

/**
 * Enqueue styles for Gutenberg Editor
 */
add_action('enqueue_block_editor_assets', function() {

  // editor styles
  wp_enqueue_style(
    'kauppakamari-editor-gutenberg-style',
    get_stylesheet_directory_uri() . '/dist/styles/editor-gutenberg.css',
    [],
    kauppakamari_last_edited('css')
  );

  // editor scripts
  wp_enqueue_script(
    'kauppakamari-editor-gutenberg-scripts',
    get_stylesheet_directory_uri() . '/dist/scripts/editor-gutenberg.js',
    ['wp-i18n', 'wp-blocks', 'wp-dom-ready'],
    kauppakamari_last_edited('js'),
    true
  );

  // overwrite Core block styles with empty styles
  //wp_deregister_style('wp-block-library' );
  //wp_register_style('wp-block-library', '' );

  // overwrite Core theme styles with empty styles
  //wp_deregister_style('wp-block-library-theme');
  //wp_register_style('wp-block-library-theme', '');

}, 10);

/**
 * Enqueue scripts and styles for admin
 */
add_action('admin_enqueue_scripts', function() {

  // admin.css
  wp_enqueue_style(
    'kauppakamari-admin-css',
    get_template_directory_uri() . '/dist/styles/admin.css',
    [],
    kauppakamari_last_edited('css')
  );

});

/**
 * Assets for login screen
 */
add_action('login_enqueue_scripts', function() {

  // wp-login.css
  wp_enqueue_style(
    'kauppakamari-login-styles',
    get_stylesheet_directory_uri() . '/dist/styles/wp-login.css',
    [],
    kauppakamari_last_edited('css')
  );

});

/**
 * Enqueue styles for Classic Editor
 */
add_action('admin_init', function() {

  add_editor_style('dist/styles/editor-classic.css');

});

/**
 * Append to <head>
 */
add_action('wp_head', function() {

  // replace class no-js with js in html tag
  echo "<script>(function(d){d.className = d.className.replace(/\bno-js\b/,'js')})(document.documentElement);</script>\n";

});

/**
 * Append to footer
 */
add_action('wp_footer', function() {

});

/**
 * Favicons
 *
 * Add favicons' <link> and <meta> tags here
 */
function kauppakamari_favicons() { ?>
  <link rel="apple-touch-icon" sizes="57x57" href="<?php echo get_stylesheet_directory_uri(); ?>/dist/favicon/apple-icon-57x57.png">
  <link rel="apple-touch-icon" sizes="60x60" href="<?php echo get_stylesheet_directory_uri(); ?>/dist/favicon/apple-icon-60x60.png">
  <link rel="apple-touch-icon" sizes="72x72" href="<?php echo get_stylesheet_directory_uri(); ?>/dist/favicon/apple-icon-72x72.png">
  <link rel="apple-touch-icon" sizes="76x76" href="<?php echo get_stylesheet_directory_uri(); ?>/dist/favicon/apple-icon-76x76.png">
  <link rel="apple-touch-icon" sizes="114x114" href="<?php echo get_stylesheet_directory_uri(); ?>/dist/favicon/apple-icon-114x114.png">
  <link rel="apple-touch-icon" sizes="120x120" href="<?php echo get_stylesheet_directory_uri(); ?>/dist/favicon/apple-icon-120x120.png">
  <link rel="apple-touch-icon" sizes="144x144" href="<?php echo get_stylesheet_directory_uri(); ?>/dist/favicon/apple-icon-144x144.png">
  <link rel="apple-touch-icon" sizes="152x152" href="<?php echo get_stylesheet_directory_uri(); ?>/dist/favicon/apple-icon-152x152.png">
  <link rel="apple-touch-icon" sizes="180x180" href="<?php echo get_stylesheet_directory_uri(); ?>/dist/favicon/apple-icon-180x180.png">
  <link rel="icon" type="image/png" sizes="192x192"  href="<?php echo get_stylesheet_directory_uri(); ?>/dist/favicon/android-icon-192x192.png">
  <link rel="icon" type="image/png" sizes="32x32" href="<?php echo get_stylesheet_directory_uri(); ?>/dist/favicon/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="96x96" href="<?php echo get_stylesheet_directory_uri(); ?>/dist/favicon/favicon-96x96.png">
  <link rel="icon" type="image/png" sizes="16x16" href="<?php echo get_stylesheet_directory_uri(); ?>/dist/favicon/favicon-16x16.png">
  <link rel="manifest" href="<?php echo get_stylesheet_directory_uri(); ?>/dist/favicon/manifest.json">
  <meta name="msapplication-TileColor" content="#ffffff">
  <meta name="msapplication-TileImage" content="<?php echo get_stylesheet_directory_uri(); ?>/dist/favicon/ms-icon-144x144.png">
  <meta name="theme-color" content="#ffffff">
<?php }
add_action('wp_head',    'kauppakamari_favicons');
add_action('admin_head', 'kauppakamari_favicons');
add_action('login_head', 'kauppakamari_favicons');

/**
 * If blog is kauppakamari main site, use different single.php
 */

add_filter('single_template', function($the_template) {
 if(!is_koulutus()) {
  return STYLESHEETPATH . "/single-kamari.php";
 } else {
   return $the_template;
 }
});
