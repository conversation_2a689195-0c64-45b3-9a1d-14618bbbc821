.icon-text {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  &__link-wrapper {
    width: 100%;
    text-align: center;
  }
  &__link {
      @include newbutton;
      margin: 3rem auto 2rem;
      padding-left: 4rem;
      padding-right: 4rem;
  }

  &__item {
    width: 100%;
    align-items: center;
    margin-bottom: 2rem;
    text-align: center;
    p {
      font-size: 16px!important;
    }
    @include breakpoint($l) {
      display: flex;
      text-align: left;
      width: calc(50% - 1.5rem);
    }
    .title {
      font-weight: $ws-semibold;
      display: block;
      font-size: 18px;
      letter-spacing: 0.5px;
      margin-top: 1.5rem;
      margin-bottom: 1.5rem;
      @include breakpoint($l) {
        margin-top: 0;
      }
    }
  }
  img {
    width: 120px;
    max-width: 120px;
    height: 120px;
    max-height: 100%;
  }
  &__img {

    max-height: 120px;
    min-height: 120px;
    height: 100%;

    @include breakpoint($l) {
     /*max-height: 143px;
      min-height: 143px;
      max-width: 30%;
      min-width: 30%;*/
      align-items: center;
      padding-right: 3rem;
      display: flex;
    }
  }
  &__content {
    @include breakpoint($l) {
      padding-right: 6rem;
    }
  }
}
