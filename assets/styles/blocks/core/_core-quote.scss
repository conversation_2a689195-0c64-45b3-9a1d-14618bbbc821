/* ==========================================================================
 # Quote block
========================================================================== */

/* Front-end and back-end
----------------------------------------------- */

.editor-styles-wrapper .wp-block-quote,
.wysiwyg .wp-block-quote,
.wysiwyg blockquote {

  .page-template-template-landingpage & {
    @include breakpoint($m) {padding: 2rem 10%;}

    padding: 1.5rem 0;
  
    p {
      @include breakpoint($m) {padding: 1.5rem 3rem;}
    }
  }

  text-align: center;
  margin: 2.5rem auto;
  padding: 1.5rem 3rem;
  // quotes: '“' '”';
  // border-left: $primary 5px solid;
  

  p {
    border-top: 3px solid $separatorblue;
  border-bottom: 3px solid $separatorblue;
  padding: 1.5rem 0;
    // font-size: 1.25rem;
    font-size: 20px;
    line-height: 30px;
    font-weight: $ws-semibold;
    @include breakpoint($s) {
      //font-size: 1.425rem;
    }
    @include breakpoint($sub-m) {
      //font-size: 1.65rem;
      font-size: 18px;
      line-height: 28px;
    }
    margin: 0;
  }

  p:before { 
    // content: '“'; 
    // content: '”';
    // content: open-quote;
    // content: close-quote;
    font-family: "Font Awesome 6 Sharp";
    content: "\f10e";
    font-size: 40px;
    line-height: initial;
    padding-bottom: 1rem;
    display: block;
    text-align: center;
    color: $coral;
  }
  // p:after {content: '”'; content: close-quote;}

  cite,
  .wp-block-quote__citation {
    display: block;
    font-style: normal;
    margin-top: $gap-small;
    color: $gray-text;
    font-size: .875rem;
    @include breakpoint($s) {
      font-size: 1rem;
    }
  }

}



/* Only front-end
----------------------------------------------- */

.wysiwyg {

  .wp-block-quote, blockquote {

  }

}

/* Only back-end
----------------------------------------------- */

.editor-styles-wrapper .wp-block-quote {

}
