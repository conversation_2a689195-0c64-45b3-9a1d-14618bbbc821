<?php

/**
 * Register: Blocks
 *
 * @package kauppakamari
 */

/**
 * Set explicitly allowed blocks (all others are disallowed)
 *
 * Notice that you need to manually add any custom block or plugins'
 * blocks here to appear on Gutenberg. This is to keep the control on what
 * is or is not allowed.
 *
 * @param bool|array $allowed_block_types list of block names or true for all
 * @param WP_Post $post the current post object
 *
 * @return array $allowed_block_types list of block names
 */
function kauppakamari_gutenberg_allowed_blocks($allowed_block_types, $post)
{

    $blocks = [];

    /**
     * Common blocks
     */
    $blocks[] = 'core/paragraph';
    $blocks[] = 'core/image';
    $blocks[] = 'core/heading';
    $blocks[] = 'core/gallery';
    $blocks[] = 'core/list';
    $blocks[] = 'core/quote';
    $blocks[] = 'core/file';
    $blocks[] = 'core/html';
    $blocks[] = 'core/cover';

    /**
     * Formatting
     */
    $blocks[] = 'core/table';
    $blocks[] = 'core/freeform'; // classic editor

    /**
     * Layout
     */
    $blocks[] = 'core/button';
    $blocks[] = 'core/buttons';
    $blocks[] = 'core/media-text';
    $blocks[] = 'core/columns';
    $blocks[] = 'core/group';
    $blocks[] = 'core/separator';
    $blocks[] = 'core/textColumns';

    /**
     * Widgets
     */
    $blocks[] = 'core/shortcode';

    /**
     * Embeds
     */
    $blocks[] = 'core/spacer';
    $blocks[] = 'core/embed';
    $blocks[] = 'core-embed/twitter';
    $blocks[] = 'core-embed/youtube';
    $blocks[] = 'core-embed/facebook';
    $blocks[] = 'core-embed/instagram';
    $blocks[] = 'core-embed/soundcloud';
    $blocks[] = 'core-embed/spotify';
    $blocks[] = 'core-embed/flickr';
    $blocks[] = 'core-embed/vimeo';
    $blocks[] = 'core-embed/issuu';
    $blocks[] = 'core-embed/slideshare';

    /**
     * Reusable blocks
     */
    $blocks[] = 'core/block';

    /**
     * Plugins
     */
    $blocks[] = 'hskk/hero-carousel';
    $blocks[] = 'hskk/hero-carousel-slide';
    /**
     * Custom
     */

    $blocks[] = 'acf/icon-text';
    $blocks[] = 'acf/tabs';
    $blocks[] = 'acf/text-image';
    //2023
    $blocks[] = 'acf/highlight-1';
    $blocks[] = 'acf/small-lift-2';
    $blocks[] = 'acf/membership-charts';
    $blocks[] = 'acf/event-calendar-fp';
    $blocks[] = 'acf/accordion';
    $blocks[] = 'acf/slider';
    $blocks[] = 'acf/animated-numbers';
    $blocks[] = 'acf/job-listing';

    $blocks[] = 'acf/teaser';
    $blocks[] = 'acf/gravityforms';
    $blocks[] = 'acf/people';
    $blocks[] = 'acf/acf';
    $blocks[] = 'acf/hubspot';
    $blocks[] = 'acf/article-teaser';
    $blocks[] = 'acf/shopify-scroller';
    $blocks[] = 'acf/event-calendar-vue';
    $blocks[] = 'acf/event-calendar';

    if (is_koulutus()) {
        $blocks[] = 'acf/clubs';
        $blocks[] = 'acf/color-teasers';
        $blocks[] = 'acf/product-teasers';
        $blocks[] = 'acf/trainer';
        $blocks[] = 'acf/trainer-list';
    } else {
        $blocks[] = 'acf/new-members';
        $blocks[] = 'acf/news-list';
        $blocks[] = 'acf/post-columns';
        $blocks[] = 'acf/video-modal';
        $blocks[] = 'acf/events-external';
        $blocks[] = 'acf/column-features';
        $blocks[] = 'acf/memberstories';
        $blocks[] = 'acf/content-background';
        $blocks[] = 'acf/price-text';
        $blocks[] = 'acf/people-select';
        $blocks[] = 'acf/presentations';
        //news site 2022 new blocks:
        $blocks[] = 'acf/news-lift-1-2';
        $blocks[] = 'acf/news-lift-3';
        $blocks[] = 'acf/read-more-3';
    }

    //2025 new blocks:
    $blocks[] = 'hskk/news-lift-wrapper';
    $blocks[] = 'hskk/news-lift-item';
    $blocks[] = 'hskk/rss-feed';
    $blocks[] = 'hskk/hero-carousel';
    $blocks[] = 'hskk/hero-carousel-slide';

    return $blocks;
}
add_filter('allowed_block_types', 'kauppakamari_gutenberg_allowed_blocks', 10, 2);
