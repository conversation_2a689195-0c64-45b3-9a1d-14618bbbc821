<?php

/**
 * Product teasers blocks
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-color-teasers-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-color-teasers';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$items = get_field('color-teasers');

?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?> scrollto">

  <div class="tabs">

    <div class="tabs__list">
    <?php
      foreach($items as $row) { ?>
          <div class="block-color-teasers__item tabs__btn <?php echo $row['color']; ?>">
            <?php
            if($icon = $row['img']) {
              echo '<img class="block-color-teasers__image" src="' . $icon['sizes']['medium'] . '" alt="' . $row['title'] . '" />';
            }
            ?>
            <div class="block-color-teasers__content">
              <?php if($row['title']) { ?>
                <span class="title">
                  <?php echo $row['title'] ?: ""; ?>
                </span>
              <?php } ?>
              <span class="name">
                <?php echo $row['name'] ?: "Nimi"; ?>
              </span>
              <span class="content">
                <?php echo $row['content'] ?: "Sisältö"; ?>
              <span>
            </div>
            <div class="block-color-teasers__cta">
              Tutustu tarkemmin
            </div>
          </div>
      <?php }
    ?>
    </div>
    <?php
    foreach($items as $row) { ?>
      <div class="tabs__content">

        <?php //vd($row); ?>

        <div class="tabs__wrap">
          <div class="content">
          <h4><?php echo $row['product_group']['title']; ?></h4>
          <?php echo $row['product_group']['content']; ?>
          </div>
          <?php
            if($icon = $row['product_group']['icon']) {
              echo '<img src="' . $icon['sizes']['medium'] . '" alt="' . $row['title'] . '" />';
            }
          ?>
          <span class="tabs__content--close"><?php echo kauppakamari_get_svg('close'); ?></span>
          <?php if($row['product_group']['id']) : ?>
            <?php kauppakamari_showproducts((string)$row['product_group']['id']); ?>
          <?php endif; ?>
          <div class="tabs__content--linkwrap">
          <?php if($row['product_group']['link']) {
              echo '<a href="' . $row['product_group']['link']['url'] . '" >' . $row['product_group']['link']['title'] . '</a>';
            }
          ?>
          </div>
        </div>
      </div>
    <?php } ?>

  </div>

</div>
