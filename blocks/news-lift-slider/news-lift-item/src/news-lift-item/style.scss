.block-editor-block-list__block.news-lift-slider-wrapper {
	padding-bottom: 80px !important;
}

.wp-block-hskk-news-lift-item {
	min-width: 420px;
	background-color: #fff;
	border-radius: 14px;
}

.news-lift-slider-article {
	background-color: #fff;
	border-radius: 14px;
	width: 420px !important;
	padding: 20px;
	flex: 0 0 auto;
	scroll-snap-align: start;
	box-sizing: border-box;
}

.news-lift-slider-article input,
.news-lift-slider-article textarea {
	max-width: 380px !important;
	color: #212121;
	font-size: 16px !important;
}

.news-lift-slider-img {
	width: 380px;
	height: 250px;
	overflow: hidden;
	margin-top: 0 !important;
}

.block-editor-block-list__block .news-lift-slider-img {
	margin-bottom: 1.5rem !important;
}

.news-lift-slider-img img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	display: block;
	border-radius: 8px;
}

.news-lift-slider-body {
	padding: 0 15px;
}

.news-lift-slider-meta {
	gap: 30px;
	display: flex;
	align-items: baseline;
}

.news-lift-slider-meta p {
	margin: 0 !important;
}

.meta-data-date {
	color: #636363 !important;
	font-size: 14px !important;
	font-weight: 600 !important;
}

.meta-data-category {
	color: #002662;
	font-size: 14px !important;
	font-weight: 600 !important;
}

.news-lift-slider-heading {
	font-size: 20px !important;
	color: #002662;
	font-weight: 600;
	line-height: 28px !important;
	margin-top: 1.2rem;
	margin-bottom: 0.8rem !important;
}

.news-lift-slider-text {
	font-size: 16px !important;
	color: #212121;
	line-height: 20px;
}

.news-lift-slider-link {
	color: #002662;
	font-size: 16px !important;
	font-weight: 600;
	position: relative;
}

.news-lift-slider-link::before {
	content: "";
	position: absolute;
	top: 7px;
	right: -12px;
	width: 6px;
	height: 10px;
	background-image: url("../../../assets/arrow.svg");
	background-size: contain;
	background-repeat: no-repeat;
}

.block-editor-block-list__block.news-lift-slider-wrapper
	.news-lift-slider-link::before {
	display: none;
}

.news-lift-slider-body a.external:after {
	top: 0px !important;
	margin-left: 4px !important;
}

.news-lift-slider-body a.external {
	padding-right: 20px !important;
}

.wp-block-create-block-news-lift-item {
	background-color: #fff;
	color: #002662;
	padding: 2px;
}

@media (max-width: 570px) {
	.news-lift-slider-article {
		width: 80% !important;
	}

	.news-lift-slider-img {
		width: 100% !important;
	}

	.news-lift-slider-heading {
		font-size: 16px !important;
		line-height: 25px !important;
	}
}
