<?php
/**
 * Function for post meta data
 *
 * @package kauppakamari
 */

/**
 * Posted on
 */
function kauppakamari_get_posted_on($post_id = '') {

  if (!$post_id) {
    $post_id = get_the_ID();
  }

  if (get_post_type($post_id) == 'page') {
    $date_tmp = esc_attr(get_the_date('j.n.Y', $post_id));
    return '<span class="posted-on hide"><time class="entry-date published updated" datetime="'.$date_tmp.'"></time></span>'; //no post date on pages
  }

  $time_string_format = '<time class="entry-date published updated" datetime="%1$s">%2$s</time>';
  $time_string = sprintf($time_string_format, esc_attr(get_the_date('j.n.Y', $post_id)), esc_html(get_the_date('j.n.Y', $post_id)));


  return '<span class="posted-on">' . $time_string . '</span>';

}
