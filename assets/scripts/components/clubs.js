var checkarrow = document.querySelectorAll(".clubs_next_page");

function nyt_change_page(next_page, max_pages) {
  let data = {
    action: "kauppakamari_filter_clubs",
    next_page: next_page,
  };

  jQuery(".block-clubs__ajax").fadeOut(500);

  setTimeout(function () {
    jQuery.ajax({
      type: "POST", // *GET, POST, PUT, DELETE, etc.
      credentials: "same-origin", // include, *same-origin, omit
      url: frontendajax.ajaxurl,
      data: data,
      beforeSend: function (xhr) {
        let posts_list = document.querySelector(".block-clubs__ajax");
        //posts_list.innerHTML = "...";
      },
      success: function (response) {
        console.log(response);
        if (response) {
          let posts_list = document.querySelector(".block-clubs__ajax");
          document.querySelector(".block-clubs__wrap").dataset.page = next_page;
          document.querySelector(".clubs_current_page").innerHTML = next_page;
          posts_list.innerHTML = response.data;
          jQuery(".block-clubs__ajax").fadeIn(500);
          if (next_page === max_pages) {
            button_next[0].style.display = "none";
          } else {
            button_next[0].style.display = "initial";
          }
          if (next_page === 1) {
            button_prev[0].style.display = "none";
          } else if (next_page > 1) {
            button_prev[0].style.display = "initial";
          }
        } else {
          console.log(response);
        }
      },
    });
  }, 500);
}

if (typeof checkarrow[0] !== "undefined" && checkarrow[0] != null) {
  const button_next = document.querySelectorAll(".clubs_next_page");
  button_next[0].addEventListener("click", (e) => {
    e.preventDefault();
    let current_page =
      document.querySelector(".block-clubs__wrap").dataset.page;
    let max_pages = document.querySelector(".block-clubs__wrap").dataset.max;
    nyt_change_page(parseInt(current_page) + 1, parseInt(max_pages));
  });

  const button_prev = document.querySelectorAll(".clubs_prev_page");
  button_prev[0].addEventListener("click", (e) => {
    e.preventDefault();
    let current_page =
      document.querySelector(".block-clubs__wrap").dataset.page;
    let max_pages = document.querySelector(".block-clubs__wrap").dataset.max;
    nyt_change_page(parseInt(current_page) - 1, parseInt(max_pages));
  });

  button_prev[0].style.display = "none";
}
