<?php

/**
 * Post columns
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-post-columns' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-post-columns columns is-8';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
 $content = get_field('featured_column_item');

?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">

<?php
$i = 0;
foreach ((array)$content as $item) { ?>
  <?php if($i > 0) : ?>
    <div class="block-post-columns__separator"></div>
    <div class="block-post-columns__separator-mobile"></div>
  <?php endif; ?>
  <div class="block-post-columns__item">
    <img src="<?php echo $item['img']['sizes']['people']; ?>" alt="<?php echo $item['subtitle']; ?>">
    <div class="block-post-columns__content">
      <?php
      if($item['title']) { ?>
      <span class="title"><?php echo $item['title']; ?></span>
      <?php }
      if($item['subtitle']) { ?>
      <span class="subtitle <?php if ($item['subtitle-variation'] == "Sitaatti") echo 'is-quote';?>"><?php echo $item['subtitle']; ?></span>
      <?php }
      if($item['link']) {
        echo 
        '<a href="' . $item['link']['url'] . '" ><span>' . $item['link']['title'] . '</span></a>';
      } ?>
    </div>
  </div>
  <?php $i++; ?>
<?php } ?>

</div>
