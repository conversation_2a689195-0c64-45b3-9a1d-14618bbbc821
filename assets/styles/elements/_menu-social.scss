/* ==========================================================================
 # Social menu
========================================================================== */

.social-navigation {
  &__items {
    @include list-reset;

    li {
      display: inline-block;
      margin-right: $gap-small;
      &:last-of-type {
        margin-right: 0;
      }
    }

    a {
      // @include transition;
      //color: $grey-font-color;
      &:hover,
      &:focus,
      &:active {
        //color: $primary;
      }
    }

    .social-navigation__icon {
      height: 2.25rem;
      width: 2.25rem;
      padding: 8px;
      background-color: $coral;
      border-radius: 50%;
      display: inline-block;
      background-repeat: no-repeat;
      background-size: auto 20px;
      background-position: 50% 50%;
      &-bluesky {
        background-size: auto 16px;
      }
    }

    //.social-navigation__item__label {
    //  @include visuallyhidden;
    //}
  }
}

/* new css */

.site-footer__social2 .social-navigation {
  &__items {
    display: block;
    //flex-wrap: wrap;

    margin-block-start: 0;
    margin-block-end: 0;
    padding-inline-start: 0;

    li {
      display: block;
      width: 100%;
      margin-right: 0;
      margin-bottom: 0.75rem;

      &:last-of-type {
        margin-right: 0;
      }

      .menu-item__link {
        width: 100%;
        display: block;
        position: relative;
      }
    }

    a {
      width: 100%;
      font-weight: $ws-medium;
      margin-bottom: 0;
      // padding-bottom: .5rem;
      &:hover {
        span {
          @include elementhover;
          &:after {
            text-decoration: none;
          }
        }
      }
      &:focus {
        border: 1px dotted $white;
      }
      .icon {
        display: inline-block;
      }
      svg {
        color: $link-use !important;
      }
      span:not(.social-navigation__icon) {
        color: $white;
        text-transform: capitalize;
        margin-left: 0px;
        text-indent: 2rem;
        width: calc(100% - 60px);
        width: fit-content;
        display: inline-block;
        font-weight: $ws-medium;
        position: relative;
        top: -12px;
      }
    }
  }
  .social-navigation__item__label:after {
    content: "\f35d";
    font-family: "Font Awesome 6 Sharp";
    position: absolute;
    margin-left: -1.5rem;
  }
}
