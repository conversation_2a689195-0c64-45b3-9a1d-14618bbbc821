<?php

/**
 * Trainer
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-trainer-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-trainer';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$content = get_field('kouluttaja');

?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">

  <div class="block-trainer__content">
    <span class="block-trainer__content--name"><?php echo get_the_title($content['trainer']); ?></span>
    <span class="block-trainer__content--description"><?php echo get_field('description', $content['trainer']->ID); ?></span>
    <span class="block-trainer__content--excerpt"><?php echo substr(get_the_excerpt($content['trainer']), 0, 200) ?></span>
    <a class="btn" href="<?php echo get_permalink($content['trainer']); ?>">Lue koko juttu</a>
  </div>
  <div class="block-trainer__image">
    <?php echo get_the_post_thumbnail( $content['trainer'], 'large'); ?>
  </div>
  <div class="block-trainer__list">
    <span class="block-trainer__list--title"><?php echo $content['list_title'] ?: 'List title'; ?></span>
    <ul>
      <?php foreach($content['list'] as $item) {
        echo '<li>' . $item['subject'] . '</li>';
      } ?>
    </ul>
  </div>

</div>
