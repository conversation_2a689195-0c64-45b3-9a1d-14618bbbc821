<?php

/**
 * Get verkkoproducts
 *
 * @return object
 */

function kauppakamari_find_property_index($products, $field, $value) {
   foreach($products as $key => $product) {
      if ( $product[$field] === $value )
         return $key;
   }
   return false;
}

if (! function_exists('kauppakamari_get_verkkoproducts')) {
  function kauppakamari_get_verkkoproducts()
  {
    //error_log('kauppakamari_get_verkkoproducts started');
    return array();



    $headers = array(
      'Content-type' => 'application/json',
      'X-Auth-Token' => 'DZA7bgYwCIPvkrkDEAKht+Ep_XPHqndvFi+2LH_+XZ_1vVr3HbmbBOGAj44jhdAOPcos62v9vBhb161Nu_YmFohTEpryZA9Uumfga6o2oxgetOvpBWmsUhvaPYAywm3wbFSD6vBqz76DV0EEw_NOs8n1FlYjuPW80AISaxh2c6Bre9tFcElpwCwBb8I6YpKAfY6_YKOwF0YYEzYHv33RSl1yCDO2q0+hFEA+5KG6OZDzoPWW48ESDnx30Mj6+wc='
    );

    $args = array(
      'headers' => $headers,
      'timeout' => '50',
      'sslverify' => false
    );

    $product_array = array();
    $limit = 100;
    $offset = 0;
    $loop = 0;
    $restcounter = 0;

    while($loop < 3) {

      $url = 'https://www.kauppakamariverkosto.fi/api/v3/products?offset=' . $offset . '&limit=' . $limit;

      $response = wp_remote_get($url, $args);
      //error_log('$response1:' . var_export($response, true));
      $response = json_decode( wp_remote_retrieve_body( $response ), true );
      error_log('$response:' . var_export($response, true));

      $items = $response['items'];

      if(empty($items)){
        $loop = 5;
        break;
      }

      if(!empty($items)) {
        foreach($items as $item) {
          $chamber_index = 0;
          $type_index = 0;
          $location_index = 0;
          $location = '';
          $type = '';
          $chamber = '';
          $chamber_index = array_search(49, array_column($item['properties'],'property_id'));
          $type_index = array_search(50, array_column($item['properties'],'property_id'));
          $location_index = array_search(51, array_column($item['properties'],'property_id'));
          $location = ($location_index != '') ? $item['properties'][$location_index]['value'] : null;
          $type = ($type_index != '') ? $item['properties'][$type_index]['value'] : null;
          $chamber = ($chamber_index != '') ? $item['properties'][$chamber_index]['value'] : null;
          $product_array[] = array (
            'name' => $item['translations'],
            'links' => $item['links'],
            'location' => $location,
            'type'  => $type,
            'chamber'  => $chamber,
          );
        }
      }
      echo $offset;
      unset($items);
      unset($response);
      $restcounter++;
      $offset = $offset + 100;
      $limit = $limit;
      if($restcounter == 6) {
        sleep(2);
        $restcounter = 0;
      }
    }

    if(!empty($product_array)) {
      $json_path = ABSPATH . "/wp-content/verkko_products.json";
      file_put_contents($json_path, json_encode($product_array));
    }

    return $product_array;
  }
}

/* Automated Import
----------------------------------------------- */

// Run import every 5min
//if (! wp_next_scheduled('get_verkkoproducts_cron')) {
//  wp_schedule_event(time(), 'yearly', 'get_verkkoproducts_cron');
//}

//add_action('get_verkkoproducts_cron', 'kauppakamari_get_verkkoproducts');
//add_action('init', 'kauppakamari_get_verkkoproducts');



