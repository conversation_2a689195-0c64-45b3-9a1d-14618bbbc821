<?php

/**
 * Tabs
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-gravityforms-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-gravityforms';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$form = get_field('gravityforms_form');

?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">

  <div class="form_header">
    <?php
      if($form['title']) {
        echo "<h2>" . $form['title'] . "</h2>";
      }
      if($form['image']) {
        echo '<img src="' .  $form['image']['sizes']['medium'] . '" alt="' . $form['title'] . '" />';
      }
    ?>
  </div>
  <div class="form_form">
  <?php
    gravity_form(
      $form['form_id'],
      $display_title = false,
      $display_description = false,
      $display_inactive = false,
      $field_values = null,
      $ajax = true,
      $echo = true
    );
  ?>
  </div>

</div>

