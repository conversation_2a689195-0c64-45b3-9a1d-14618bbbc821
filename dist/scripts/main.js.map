{"version": 3, "sources": ["../node_modules/svgxuse/svgxuse.js", "../node_modules/objectFitPolyfill/dist/objectFitPolyfill.min.js", "../node_modules/fitvids/dist/fitvids.js", "../assets/scripts/components/youtube_api.js", "../assets/scripts/components/navigation.js", "../assets/scripts/components/markup-enhancements.js", "../assets/scripts/components/menu-toggle.js", "../assets/scripts/components/tabs.js", "../assets/scripts/components/slick.min.js", "../assets/scripts/components/slick.js", "../assets/scripts/components/accordion.js", "../assets/scripts/components/contact.js", "../assets/scripts/components/clubs.js", "../assets/scripts/components/video-modal.js", "../assets/scripts/main.js", "../assets/scripts/components/skip-to-content.js"], "names": ["cache", "tid", "deboun<PERSON><PERSON><PERSON><PERSON>", "unobserveChanges", "xlinkNS", "checkUseElems", "winLoad", "window", "addEventListener", "Object", "create", "clearTimeout", "setTimeout", "base", "bcr", "hash", "href", "i", "isHidden", "url", "uses", "xhr", "Request", "origin", "origin2", "inProgressCount", "observeIfDone", "observer", "MutationObserver", "observe", "document", "documentElement", "childList", "subtree", "attributes", "disconnect", "removeEventListener", "ignore", "attrUpdateFunc", "spec", "useEl", "setAttributeNS", "hasAttribute", "setAttribute", "onErrorTimeout", "onerror", "ontimeout", "getElementsByTagName", "length", "getBoundingClientRect", "getAttribute", "getAttributeNS", "split", "left", "right", "top", "bottom", "width", "height", "undefined", "XMLHttpRequest", "<PERSON><PERSON><PERSON><PERSON>", "location", "withCredentials", "XDomainRequest", "onload", "svg", "body", "x", "createElement", "innerHTML", "responseText", "style", "position", "overflow", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "onloadFunc", "open", "send", "abort", "loc", "a", "protocol", "replace", "host", "readyState", "t", "e", "n", "o", "l", "navigator", "userAgent", "match", "parseInt", "d", "clientWidth", "clientHeight", "indexOf", "dataset", "objectFit", "objectPosition", "parentNode", "getComputedStyle", "getPropertyValue", "display", "className", "max-width", "max-height", "min-width", "min-height", "margin-top", "margin-right", "margin-bottom", "margin-left", "marginTop", "marginLeft", "Event", "querySelectorAll", "nodeName", "_typeof", "toLowerCase", "complete", "this", "objectFitPolyfill", "f", "exports", "module", "define", "amd", "global", "self", "<PERSON><PERSON><PERSON>", "r", "s", "u", "require", "Error", "code", "call", "1", "selectors", "queryAll", "el", "selector", "Array", "prototype", "slice", "toSelectorArray", "input", "map", "trim", "filter", "<PERSON><PERSON><PERSON><PERSON>", "toString", "concat", "apply", "str", "parentSelector", "opts", "players", "div", "custom", "ignored", "ignoredSelector", "containers", "getElementById", "head", "append<PERSON><PERSON><PERSON>", "childNodes", "join", "for<PERSON>ach", "container", "video", "matches", "test", "widthAttr", "heightAttr", "isNaN", "aspect", "removeAttribute", "wrapper", "paddingTop", "wrap", "YT", "YTConfig", "scriptUrl", "ttPolicy", "trustedTypes", "createPolicy", "createScriptURL", "loading", "loaded", "ready", "push", "onYTReady", "e$0", "setConfig", "c", "k", "hasOwnProperty", "type", "id", "src", "async", "currentScript", "nonce", "b", "aucor_navigation", "menu", "options", "hover_timer", "settings", "defaults", "prop", "extended", "extend", "desktop_min_width", "menu_toggle", "querySelector", "is_desktop_menu", "Math", "max", "innerWidth", "open_sub_menu", "this_tree_submenus", "current_parent", "parentElement", "isEqualNode", "classList", "contains", "current_descendants", "all_open_sub_menus", "j", "remove", "add", "close_sub_menu", "parent", "open_submenu_with_click", "li", "target", "toggle", "stopPropagation", "current_sub_menu", "current_menu_item", "items_with_children", "item", "caret", "on_link_focus", "submenu_below", "on_link_blur", "links", "link", "dispatchEvent", "findAndRemoveClass", "elements", "maybeCloseMenuFn", "elem", "isInsideMenu", "nodeType", "Node", "ELEMENT_NODE", "targetInsideMenu", "touchStartFn", "current_list_item", "preventDefault", "this_parents_li", "all_tapped", "this_parents_submenu", "all_open_submenus", "parent_links", "p", "responsive_tables_in_content", "tables", "wrap_old_images_with_caption", "figures", "wrap_old_aligned_images", "aligned_parent", "aligned", "<PERSON><PERSON><PERSON><PERSON>", "alignment", "figure", "offCanvas", "menuToggle", "main", "closingElements", "toggleElements", "openElements", "_len", "arguments", "_key", "elementToOpen", "closeElements", "_len2", "_key2", "elementToClose", "stopImmediatePropagation", "closingElem", "cloned_menu", "cloneNode", "lastScrollTop", "displayCurrentTab", "current", "tabsContents", "tabsBtns", "scrollIntoView", "block", "behavior", "st", "pageYOffset", "scrollTop", "tabs", "_loop", "close", "event", "j<PERSON><PERSON><PERSON>", "trigger", "Slick", "accessibility", "adaptiveHeight", "appendArrows", "appendDots", "arrows", "asNavFor", "prevArrow", "nextArrow", "autoplay", "autoplaySpeed", "centerMode", "centerPadding", "cssEase", "customPaging", "text", "dots", "dotsClass", "draggable", "easing", "edgeFriction", "fade", "focusOnSelect", "focusOnChange", "infinite", "initialSlide", "lazyLoad", "mobileFirst", "pauseOnHover", "pauseOnFocus", "pauseOnDotsHover", "respondTo", "responsive", "rows", "rtl", "slide", "slidesPerRow", "slidesToShow", "slidesToScroll", "speed", "swipe", "swipeToSlide", "touchMove", "touchThreshold", "useCSS", "useTransform", "variableWidth", "vertical", "verticalSwiping", "waitForAnimate", "zIndex", "initials", "animating", "dragging", "autoPlayTimer", "currentDirection", "currentLeft", "currentSlide", "direction", "$dots", "listWidth", "listHeight", "loadIndex", "$nextArrow", "$prevArrow", "scrolling", "slideCount", "slideWidth", "$slideTrack", "$slides", "sliding", "slideOffset", "swipeLeft", "swiping", "$list", "touchObject", "transformsEnabled", "unslicked", "activeBreakpoint", "animType", "animProp", "breakpoints", "breakpointSettings", "cssTransitions", "focussed", "interrupted", "hidden", "paused", "positionProp", "rowCount", "shouldClick", "$slider", "$slidesCache", "transformType", "transitionType", "visibilityChange", "windowWidth", "windowTimer", "data", "originalSettings", "mozHidden", "webkitHidden", "autoPlay", "proxy", "autoPlayClear", "autoPlayIterator", "changeSlide", "clickHandler", "<PERSON><PERSON><PERSON><PERSON>", "setPosition", "swi<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "instanceUid", "htmlExpr", "registerBreakpoints", "init", "activateADA", "find", "attr", "aria-hidden", "tabindex", "addSlide", "<PERSON><PERSON><PERSON>", "unload", "appendTo", "eq", "insertAfter", "prependTo", "children", "detach", "append", "each", "reinit", "animateHeight", "outerHeight", "animate", "animateSlide", "animStart", "duration", "step", "ceil", "css", "applyTransition", "disableTransition", "getNavTarget", "not", "slick", "<PERSON><PERSON><PERSON><PERSON>", "setInterval", "clearInterval", "buildArrows", "addClass", "removeClass", "removeAttr", "aria-disabled", "buildDots", "getDotCount", "first", "buildOut", "wrapAll", "setupInfinite", "updateDots", "setSlideClasses", "buildRows", "createDocumentFragment", "get", "empty", "checkResponsive", "min", "unslick", "refresh", "currentTarget", "is", "closest", "message", "index", "checkNavigable", "getNavigableIndexes", "cleanUpEvents", "off", "interrupt", "visibility", "cleanUpSlideEvents", "orientationChange", "resize", "cleanUpRows", "destroy", "fadeSlide", "opacity", "fadeSlideOut", "filterSlides", "slickFilter", "focusHandler", "on", "get<PERSON>urrent", "slickCurrentSlide", "getLeft", "floor", "offsetLeft", "outerWidth", "getOption", "slickGetOption", "getSlick", "getSlideCount", "abs", "goTo", "slickGoTo", "hasClass", "setProps", "startLoad", "loadSlider", "initializeEvents", "updateArrows", "initADA", "role", "aria-<PERSON><PERSON>", "aria-controls", "aria-label", "aria-selected", "end", "initArrowEvents", "initDotEvents", "initSlideEvents", "action", "initUI", "show", "tagName", "keyCode", "progressiveLazyLoad", "next", "slickNext", "pause", "slickPause", "play", "slickPlay", "postSlide", "focus", "prev", "slick<PERSON>rev", "breakpoint", "splice", "sort", "windowDelay", "removeSlide", "slickRemove", "setCSS", "setDimensions", "padding", "setFade", "setHeight", "setOption", "slickSetOption", "WebkitTransition", "MozTransition", "msTransition", "OTransform", "perspectiveProperty", "webkitPerspective", "MozTransform", "MozPerspective", "webkitTransform", "msTransform", "transform", "clone", "parents", "hide", "swipeDirection", "startX", "curX", "startY", "curY", "atan2", "round", "PI", "swipeEnd", "swipe<PERSON><PERSON><PERSON>", "edgeHit", "minSwipe", "fingerCount", "originalEvent", "touches", "swipeStart", "swipeMove", "pageX", "clientX", "pageY", "clientY", "sqrt", "pow", "unfilterSlides", "slickUnfilter", "fn", "$", "click", "$this", "toggleClass", "slideUp", "slideToggle", "checkboxes", "setGetParam", "key", "value", "params", "newUrl", "history", "pushState", "URLSearchParams", "search", "set", "pathname", "path", "nyt_filter", "peoplecat", "console", "log", "ajaxSetup", "ajax", "headers", "cache-control", "credentials", "frontendajax", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beforeSend", "success", "response", "boxes", "URL", "cats", "catindex", "checked", "button_next", "button_prev", "checkarrow", "nyt_change_page", "next_page", "max_pages", "fadeOut", "posts_list", "page", "fadeIn", "current_page", "close_video_modal", "keyup", "search_url", "paramsString", "searchParams", "searchTimeout", "hashChangeTimeout", "getQueryStringParam", "paramName", "getQueryStringArrayParam", "_step", "post_category_array", "_iterator", "_createForOfIteratorHelper", "entries", "done", "pair", "err", "setQueryStringParam", "val", "setQueryStringArrayParam", "set<PERSON>ueryString<PERSON>y", "paramArray", "_step2", "_iterator2", "newVal", "getHashQueryStringParam", "substr", "emptyResults", "html", "searchInit", "s_php", "s_js", "people_id", "s_form_hidden", "s_form_input", "people_id_form_input", "post_type", "post_category", "isArray", "_step3", "_iterator3", "post_category_slug", "triggerSearch", "noSearchMessage", "getSearchResults", "search_key", "dataLayer", "searchTerm", "form_data", "serialize", "pageNum", "res", "scrollToTop", "createCharts", "mapobserver", "IntersectionObserver", "entry", "isIntersecting", "charts", "chartValue", "chartLeftover", "chartRotation", "chartId", "chartContainer", "chartConfig", "chart", "textvalue", "datasets", "backgroundColor", "tooltips", "enabled", "hover", "mode", "events", "rotation", "borderWidth", "animation", "delay", "chartInstances", "has", "Chart", "WeakMap", "scrollingNumbers", "destination", "element", "startNum", "digits", "digit", "fresh", "possibleNumbers", "insertAdjacentHTML", "firstDigit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addDigit", "tick", "currNum", "setup", "checkExistence", "small__lift2", "from", "highlight1", "text__image", "array", "offset", "Waypoint", "handler", "animationDelay", "scrollFunction", "scrollToTopButton", "y", "scrollY", "requestAnimationFrame", "scrollTo", "onclick", "hostname", "searchText", "lang", "closeText", "<PERSON><PERSON><PERSON>", "minthree2", "icegramListSelection", "$inputs", "handleMouseDown", "handleFirstTab", "alert", "hr", "urlPaged", "post_category_arr", "$form", "handleFormSubmit", "handleRequestSuccess", "prepend", "descToAppend", "ct", "createTextNode", "ce", "cookieblockSrc", "sp", "textContent"], "mappings": "gfAQC,WAEG,IACQA,EAEAC,EACAC,EAIAC,EA2DAC,EACJC,EA2IAC,EA/MkB,oBAAXC,QAA0BA,OAAOC,mBACpCR,EAAQS,OAAOC,OAAO,MAGtBR,EAAiB,WACjBS,aAAaV,GACbA,EAAMW,WAAWP,EAAe,MAEhCF,EAAmB,aA2DnBC,EAAU,+BACdC,EAAgB,WACZ,IAAIQ,EACAC,EAEAC,EACAC,EACAC,EAEAC,EAEAC,EACAC,EACAC,EA1CsBF,EActBG,EACAC,EACAC,EAqBAC,EAAkB,EAMtB,SAASC,IAtEQ,IACbC,EAwEwB,MADxBF,IAEItB,IAxERI,OAAOC,iBAAiB,SAAUN,GAAgB,GAClDK,OAAOC,iBAAiB,oBAAqBN,GAAgB,GAQzDC,EAPAI,OAAOqB,mBACPD,EAAW,IAAIC,iBAAiB1B,IACvB2B,QAAQC,SAASC,gBAAiB,CACvCC,WAAW,EACXC,SAAS,EACTC,YAAY,IAEG,WACf,IACIP,EAASQ,aACT5B,OAAO6B,oBAAoB,SAAUlC,GAAgB,GACrDK,OAAO6B,oBAAoB,oBAAqBlC,GAAgB,GAClE,MAAOmC,QAGbP,SAASC,gBAAgBvB,iBAAiB,qBAAsBN,GAAgB,GAC7D,WACf4B,SAASC,gBAAgBK,oBAAoB,qBAAsBlC,GAAgB,GACnFK,OAAO6B,oBAAoB,SAAUlC,GAAgB,GACrDK,OAAO6B,oBAAoB,oBAAqBlC,GAAgB,MAuDxE,SAASoC,EAAeC,GACpB,OAAO,YACsB,IAArBvC,EAAMuC,EAAK1B,QACX0B,EAAKC,MAAMC,eAAerC,EAAS,aAAc,IAAMmC,EAAKxB,MACxDwB,EAAKC,MAAME,aAAa,SACxBH,EAAKC,MAAMG,aAAa,OAAQ,IAAMJ,EAAKxB,QAwB3D,SAAS6B,EAAevB,GACpB,OAAO,WACHA,EAAIwB,QAAU,KACdxB,EAAIyB,UAAY,KAChBpB,KAMR,IAHAvB,IAEAiB,EAAOU,SAASiB,qBAAqB,OAChC9B,EAAI,EAAGA,EAAIG,EAAK4B,OAAQ/B,GAAK,EAAG,CACjC,IACIH,EAAMM,EAAKH,GAAGgC,wBAChB,MAAOZ,GAELvB,GAAM,EAUVD,GAJIM,GAJJH,EAAOI,EAAKH,GAAGiC,aAAa,SACjB9B,EAAKH,GAAGkC,eAAe/C,EAAS,SAChCgB,EAAKH,GAAGiC,aAAa,gBACpBlC,EAAKoC,MACPpC,EAAKoC,MAAM,KAEX,CAAC,GAAI,KAEJ,GACXrC,EAAOI,EAAI,GACXD,EAAWJ,GAAoB,IAAbA,EAAIuC,MAA4B,IAAdvC,EAAIwC,OAA2B,IAAZxC,EAAIyC,KAA4B,IAAfzC,EAAI0C,OACxE1C,GAAqB,IAAdA,EAAI2C,OAA8B,IAAf3C,EAAI4C,SAAiBxC,GAO3CE,EAAKH,GAAGyB,aAAa,SACrBtB,EAAKH,GAAGwB,eAAerC,EAAS,aAAcY,GAE9CH,EAAKmC,UAGO,KADZ3B,EAAMrB,EAAMa,KAGRD,WAAW0B,EAAe,CACtBE,MAAOpB,EAAKH,GACZJ,KAAMA,EACNE,KAAMA,IACN,QAEI4C,IAARtC,IAjIUF,EAkIcN,EAlHpCW,EADAD,EADAD,OAAAA,EAGAf,OAAOqD,iBACPtC,EAAU,IAAIsC,eACdrC,EAASsC,EAAUC,UACnBtC,EAAUqC,EAAU1C,GAEhBG,OAD4BqC,IAA5BrC,EAAQyC,iBAA6C,KAAZvC,GAAkBA,IAAYD,EAC7DyC,qBAAkBL,EAElBC,qBA2GcD,IAxGzBrC,IAyGaD,EAAM,IAzGnBC,GA0GatB,EAAMa,GAAQQ,GACV4C,OAzExB,SAAoB5C,GAChB,OAAO,WACH,IAEI6C,EAFAC,EAAOrC,SAASqC,KAChBC,EAAItC,SAASuC,cAAc,KAE/BhD,EAAI4C,OAAS,KACbG,EAAEE,UAAYjD,EAAIkD,cAClBL,EAAME,EAAErB,qBAAqB,OAAO,MAEhCmB,EAAIvB,aAAa,cAAe,QAChCuB,EAAIM,MAAMC,SAAW,WACrBP,EAAIM,MAAMf,MAAQ,EAClBS,EAAIM,MAAMd,OAAS,EACnBQ,EAAIM,MAAME,SAAW,SACrBP,EAAKQ,aAAaT,EAAKC,EAAKS,aAEhClD,KAyDyBmD,CAAWxD,GACxBA,EAAIwB,QAAUD,EAAevB,GAC7BA,EAAIyB,UAAYF,EAAevB,GAC/BA,EAAIyD,KAAK,MAAOjE,GAChBQ,EAAI0D,OACJtD,GAAmB,MAK1BP,EAWML,EAAKmC,QAAUhD,EAAMa,IAC5BD,WAAW0B,EAAe,CACtBE,MAAOpB,EAAKH,GACZJ,KAAMA,EACNE,KAAMA,IACN,QAfgB4C,IAAhB3D,EAAMa,GAENb,EAAMa,IAAQ,EACPb,EAAMa,GAAMoD,SAGnBjE,EAAMa,GAAMmE,eACLhF,EAAMa,GAAMoD,OACnBjE,EAAMa,IAAQ,GArJ9B,SAASgD,EAAUoB,GACf,IAAIC,EAOJ,YANqBvB,IAAjBsB,EAAIE,SACJD,EAAID,GAEJC,EAAIpD,SAASuC,cAAc,MACzBrD,KAAOiE,EAENC,EAAEC,SAASC,QAAQ,KAAM,IAAMF,EAAEG,KAwJ5CjE,EAAO,GACPK,GAAmB,EACnBC,KAGJpB,EAAU,WACNC,OAAO6B,oBAAoB,OAAQ9B,GAAS,GAC5CL,EAAMW,WAAWP,EAAe,IAER,aAAxByB,SAASwD,WAET/E,OAAOC,iBAAiB,OAAQF,GAAS,GAGzCA,KA1NX,GCRA,WAAwB,IAAmCiF,EAAsDC,EAA2BC,EAAqFC,EAA0hBC,EAAo/C1E,EAAptE,oBAAoBV,SAAkEiF,GAAtDD,EAAEhF,OAAOqF,UAAUC,UAAUC,MAAM,oBAAuBC,SAASR,EAAE,GAAG,IAAI,KAAKE,IAAID,GAAI,IAAIA,GAAGA,GAAG,GAAS,cAAc1D,SAASC,gBAAgByC,OAAO,GAAKiB,GAAOC,EAAE,SAASH,EAAEC,EAAEvE,GAAG,IAAIwE,EAAEC,EAAEC,EAAET,EAAEc,EAAE,IAAI/E,EAAEA,EAAEmC,MAAM,MAAMJ,OAAO,IAAI/B,EAAE,GAAGA,EAAE,IAAI,MAAMsE,EAAEE,EAAExE,EAAE,GAAGyE,EAAEzE,EAAE,GAAG0E,EAAE,OAAOT,EAAE,QAAQc,EAAER,EAAES,gBAAgB,CAAC,GAAG,MAAMV,EAAE,OAAOE,EAAExE,EAAE,GAAGyE,EAAEzE,EAAE,GAAG0E,EAAE,MAAMT,EAAE,SAASc,EAAER,EAAEU,aAAa,GAAGT,IAAIE,GAAGD,IAAIC,EAAE,CAAC,GAAGF,IAAIP,GAAGQ,IAAIR,EAAE,MAAM,WAAWO,GAAG,QAAQA,GAAGD,EAAEhB,MAAMmB,GAAG,WAAWH,EAAEhB,MAAM,UAAUmB,GAAGK,GAAG,EAAE,YAAY,GAAGP,EAAEU,QAAQ,MAAMV,EAAEM,SAASN,EAAE,KAAK,IAAID,EAAEhB,MAAMmB,GAAGF,EAAE,IAAID,EAAEhB,MAAM,UAAUmB,GAAGK,GAAGP,GAAG,KAAK,OAAOA,EAAE,IAAIA,EAAED,EAAEhB,MAAMU,GAAGO,EAAE,IAAID,EAAEhB,MAAM,UAAUU,GAAGc,GAAGP,GAAG,KAAK,MAAMD,EAAEhB,MAAMmB,GAAGF,GAAGD,EAAEhB,MAAMU,GAAG,SAASM,EAAEhB,MAAMmB,GAAG,KAAKA,EAAE,SAASJ,GAAG,IAAiNA,EAAOC,EAAkCvE,EAAiCwE,EAAiCC,EAA1KF,GAA9IA,EAAED,EAAEa,QAAQb,EAAEa,QAAQC,UAAUd,EAAErC,aAAa,qBAAoG,QAAQjC,GAAzFA,EAAEsE,EAAEa,QAAQb,EAAEa,QAAQE,eAAef,EAAErC,aAAa,0BAA0C,UAAcuC,EAAEF,EAAEgB,WAAW,OAAgBhB,EAA8ZE,EAAvZD,EAAEjF,OAAOiG,iBAAiBjB,EAAE,MAAMtE,EAAEuE,EAAEiB,iBAAiB,YAAYhB,EAAED,EAAEiB,iBAAiB,YAAYf,EAAEF,EAAEiB,iBAAiB,WAAWxF,GAAG,WAAWA,IAAIsE,EAAEf,MAAMC,SAAS,YAAY,WAAWgB,IAAIF,EAAEf,MAAME,SAAS,UAAUgB,GAAG,WAAWA,IAAIH,EAAEf,MAAMkC,QAAQ,SAAS,IAAInB,EAAEW,eAAeX,EAAEf,MAAMd,OAAO,SAAS,IAAI6B,EAAEoB,UAAUR,QAAQ,yBAAyBZ,EAAEoB,UAAUpB,EAAEoB,UAAU,wBAA4B,SAASpB,GAAG,IAAkQE,EAA9PD,EAAEjF,OAAOiG,iBAAiBjB,EAAE,MAAMtE,EAAE,CAAC2F,YAAY,OAAOC,aAAa,OAAOC,YAAY,MAAMC,aAAa,MAAMxD,IAAI,OAAOD,MAAM,OAAOE,OAAO,OAAOH,KAAK,OAAO2D,aAAa,MAAMC,eAAe,MAAMC,gBAAgB,MAAMC,cAAc,OAAO,IAAQ1B,KAAKxE,EAAEuE,EAAEiB,iBAAiBhB,KAAKxE,EAAEwE,KAAKF,EAAEf,MAAMiB,GAAGxE,EAAEwE,IAAjU,CAAsUF,GAAGA,EAAEf,MAAMC,SAAS,WAAWc,EAAEf,MAAMf,MAAM,OAAO8B,EAAEf,MAAMd,OAAO,OAAO,eAAe8B,IAAIA,EAAED,EAAEU,YAAYR,EAAEQ,aAAaV,EAAEW,aAAaT,EAAES,aAAa,OAAO,WAAW,SAASV,GAAGE,EAAE,IAAIH,EAAEtE,QAAQyE,EAAE,IAAIH,EAAEtE,IAAI,SAASuE,GAAGD,EAAEf,MAAMf,MAAM,OAAO8B,EAAEf,MAAMd,OAAO,OAAOgC,EAAE,IAAIH,EAAEtE,QAAQyE,EAAE,IAAIH,EAAEtE,KAAKsE,EAAEf,MAAMd,OAAO,YAAY,UAAU8B,GAAGD,EAAEU,YAAYR,EAAEQ,aAAa,YAAYT,GAAGD,EAAEU,YAAYR,EAAEQ,aAAaV,EAAEf,MAAMjB,IAAI,IAAIgC,EAAEf,MAAM4C,UAAU,IAAI1B,EAAE,IAAIH,EAAEtE,KAAKsE,EAAEf,MAAMf,MAAM,OAAO8B,EAAEf,MAAMd,OAAO,OAAO6B,EAAEf,MAAMnB,KAAK,IAAIkC,EAAEf,MAAM6C,WAAW,IAAI3B,EAAE,IAAIH,EAAEtE,OAAOA,EAAE,SAASsE,GAAG,QAAG,IAASA,GAAGA,aAAa+B,MAAM/B,EAAEzD,SAASyF,iBAAiB,0BAA0B,GAAGhC,GAAGA,EAAEiC,SAASjC,EAAE,CAACA,QAAQ,GAAG,UAAQkC,QAASlC,KAAIA,EAAEvC,SAASuC,EAAE,GAAGiC,SAAS,OAAM,EAAO,IAAI,IAAIhC,EAAE,EAAEA,EAAED,EAAEvC,OAAOwC,IAAI,GAAGD,EAAEC,GAAGgC,SAAS,CAAC,IAAIvG,EAAEsE,EAAEC,GAAGgC,SAASE,cAAc,GAAG,QAAQzG,EAAE,CAAC,GAAGwE,EAAE,SAASF,EAAEC,GAAGmC,SAAShC,EAAEJ,EAAEC,IAAID,EAAEC,GAAGhF,iBAAiB,OAAO,WAAWmF,EAAEiC,YAAY,UAAU3G,GAAE,EAAEsE,EAAEC,GAAGF,WAA+EK,EAAEJ,EAAEC,IAAhED,EAAEC,GAAGhF,iBAAiB,iBAAiB,WAAWmF,EAAEiC,QAAgB,OAAM,GAAI,YAAY9F,SAASwD,WAAWxD,SAAStB,iBAAiB,mBAAmBS,GAAGA,IAAIV,OAAOC,iBAAiB,SAASS,GAAGV,OAAOsH,kBAAkB5G,GAAOV,OAAOsH,kBAAkB,WAAW,OAAM,IAA55F,GCAD,SAAUC,GAAuB,YAAH,oBAAPC,QAAO,YAAAN,QAAPM,WAAoC,oBAATC,OAAsBA,OAAOD,QAAQD,IAA4B,mBAATG,QAAqBA,OAAOC,IAAKD,OAAO,GAAGH,IAAiC,oBAATvH,OAAwBA,OAA+B,oBAAT4H,OAAwBA,OAA6B,oBAAPC,KAAsBA,KAAYR,MAAOS,QAAUP,IAA5T,CAAmU,WAAqC,OAAQ,SAAStC,EAAED,EAAEE,EAAE6C,GAAG,SAASC,EAAE7C,EAAE8C,GAAG,IAAI/C,EAAEC,GAAG,CAAC,IAAIH,EAAEG,GAAG,CAAC,IAAIR,EAAkB,mBAATuD,SAAqBA,QAAQ,IAAID,GAAGtD,EAAE,OAAOA,EAAEQ,GAAE,GAAI,GAAGzE,EAAE,OAAOA,EAAEyE,GAAE,GAAI,IAAIoC,EAAE,IAAIY,MAAM,uBAAuBhD,EAAE,KAAK,MAAMoC,EAAEa,KAAK,mBAAmBb,EAAE,IAAInC,EAAEF,EAAEC,GAAG,CAACqC,QAAQ,IAAIxC,EAAEG,GAAG,GAAGkD,KAAKjD,EAAEoC,QAAQ,SAASvC,GAAG,IAAIC,EAAEF,EAAEG,GAAG,GAAGF,GAAG,OAAO+C,EAAE9C,GAAID,IAAIG,EAAEA,EAAEoC,QAAQvC,EAAED,EAAEE,EAAE6C,GAAG,OAAO7C,EAAEC,GAAGqC,QAAkD,IAA1C,IAAI9G,EAAkB,mBAATwH,SAAqBA,QAAgB/C,EAAE,EAAEA,EAAE4C,EAAEtF,OAAO0C,IAAI6C,EAAED,EAAE5C,IAAI,OAAO6C,EAAtb,CAA0b,CAACM,EAAE,CAAC,SAASJ,EAAQT,EAAOD,GACt0B,IAAIe,EAAY,CACd,kCACA,6BACA,sCACA,oDACA,UA+CF,SAASC,EAASC,EAAIC,GAKpB,MAJkB,iBAAPD,IACTC,EAAWD,EACXA,EAAKlH,UAEAoH,MAAMC,UAAUC,MAAMR,KAAKI,EAAGzB,iBAAiB0B,IAGxD,SAASI,EAAgBC,GACvB,MAAqB,iBAAVA,EACFA,EACJlG,MAAM,KACNmG,IAAIC,GACJC,OAAOC,IAmDGJ,EAlDIA,EAmD8B,mBAA1C7I,OAAO0I,UAAUQ,SAASf,KAAKU,IATvBA,EAzCEA,EAAMC,IAAIF,GAAiBI,OAAOC,GA0C5C,GAAGE,OAAOC,MAAM,GAAIP,IAxCpBA,GAAS,IAuClB,IAAiBA,EAQAA,EAhBjB,SAASI,EAAUJ,GACjB,OAAsB,EAAfA,EAAMtG,OAGf,SAASwG,EAAKM,GACZ,OAAOA,EAAI1E,QAAQ,aAAc,IA9FnC4C,EAAOD,QAAU,SAASgC,EAAgBC,GAqG1C,IAAkBV,EAnGhBU,EAAOA,GAAQ,GAmGCV,EApGhBS,EAAiBA,GAAkB,OAqGc,oBAA1CtJ,OAAO0I,UAAUQ,SAASf,KAAKU,KAjGpCU,EAAOD,EACPA,EAAiB,QAGnBC,EAAK3H,OAAS2H,EAAK3H,QAAU,GAC7B2H,EAAKC,QAAUD,EAAKC,SAAW,GAE/B,IAwEIC,EAhEAC,EACAC,EACAC,EACApB,EAXAqB,EAAavB,EAASgB,GACrBL,EAAUY,KAEVxI,SAASyI,eAAe,oBAChBzI,SAAS0I,MAAQ1I,SAASiB,qBAAqB,QAAQ,IAC7D0H,cAmEHP,EAAMpI,SAASuC,cAAc,QAC7BC,UAAY,8QACT4F,EAAIQ,WAAW,KAlElBP,EAASd,EAAgBW,EAAKC,SAC9BG,EAAUf,EAAgBW,EAAK3H,QAC/BgI,EAAmC,EAAjBD,EAAQpH,OAAaoH,EAAQO,OAAS,KAGvDjB,EAFDT,EAAWH,EAAUc,OAAOO,GAAQQ,SAMxCL,EAAWM,QAAQ,SAASC,GACb9B,EAAS8B,EAAW5B,GAE1B2B,QAAQ,SAASE,GAClBT,GAAmBS,EAAMC,QAAQV,IA4B3C,SAAcrB,GACZ,GAAI,4BAA4BgC,KAAKhC,EAAGzC,WAAWI,WACjD,OAGF,IAAIsE,EAAYlF,SAASiD,EAAG9F,aAAa,SAAU,IAC/CgI,EAAanF,SAASiD,EAAG9F,aAAa,UAAW,IAEjDO,EAAS0H,MAAMF,GAAyBjC,EAAG/C,YAAfgF,EAE5BG,GADUD,MAAMD,GAA2BlC,EAAG9C,aAAhBgF,GACZzH,EAEtBuF,EAAGqC,gBAAgB,SACnBrC,EAAGqC,gBAAgB,UAEnB,IAAIC,EAAUxJ,SAASuC,cAAc,OACrC2E,EAAGzC,WAAW5B,aAAa2G,EAAStC,GACpCsC,EAAQ3E,UAAY,4BACpB2E,EAAQ9G,MAAM+G,WAAsB,IAATH,EAAe,IAC1CE,EAAQb,YAAYzB,GA5ChBwC,CAAKV,UAyET,KAAK,GAAG,CAAC,GAzHqW,CAyHjW,KCzHf,IAAqRW,GAAgDC,SAAjUC,UAAY,kFAAyF,IAAI,IAAIC,SAASrL,OAAOsL,aAAaC,aAAa,qBAAqB,CAACC,gBAAgB,SAAS3H,GAAG,OAAOA,KAAKuH,UAAUC,SAASG,gBAAgBJ,WAAW,MAAMnG,IAAejF,OAAW,KAAEkL,GAAG,CAACO,QAAQ,EAAEC,OAAO,IAAoB1L,OAAiB,WAAEmL,SAAS,CAACrG,KAAO,4BAClXoG,GAAGO,UAASP,GAAGO,QAAQ,EAAE,WAAY,IAAIrG,EAAE,GAAG8F,GAAGS,MAAM,SAASpE,GAAM2D,GAAGQ,OAAOnE,IAASnC,EAAEwG,KAAKrE,IAAIvH,OAAO6L,UAAU,WAAWX,GAAGQ,OAAO,EAAE,IAAI,IAAIhL,EAAE,EAAEA,EAAE0E,EAAE3C,OAAO/B,IAAI,IAAI0E,EAAE1E,KAAK,MAAMoL,MAAQZ,GAAGa,UAAU,SAASC,GAAG,IAAI,IAAIC,KAAKD,EAAKA,EAAEE,eAAeD,KAAGd,SAASc,GAAGD,EAAEC,KAAI,IAAItH,EAAEpD,SAASuC,cAAc,UAAUa,EAAEwH,KAAK,kBAAkBxH,EAAEyH,GAAG,uBAAuBzH,EAAE0H,IAAIjB,UAAUzG,EAAE2H,OAAM,EAAK,IAAuCpH,EAAnC8G,EAAEzK,SAASgL,eAAiBP,IAAO9G,EAAE8G,EAAEQ,OAAOR,EAAErJ,aAAa,WAAcgC,EAAEvC,aAAa,QAAQ8C,GAAG,IAAIuH,EAC3flL,SAASiB,qBAAqB,UAAU,GAAGiK,EAAEzG,WAAW5B,aAAaO,EAAE8H,GAD1C,ICY7B,IAAIC,iBAAmB,SAAUC,EAAMC,GACrC,IA0BEC,EAJEC,EAtBS,SAAUC,EAAUH,GAC/B,IACII,EADAC,EAAW,GAEf,IAAKD,KAAQD,EACP7M,OAAO0I,UAAUsD,eAAe7D,KAAK0E,EAAUC,KACjDC,EAASD,GAAQD,EAASC,IAG9B,IAAKA,KAAQJ,EACP1M,OAAO0I,UAAUsD,eAAe7D,KAAKuE,EAASI,KAChDC,EAASD,GAAQJ,EAAQI,IAG7B,OAAOC,EASMC,CALA,CACbC,kBAAmB,IACnBC,YAAa,gBAGiBR,GAC9BO,EAAoB,KACpBC,EAAc7L,SAAS8L,cAAcP,EAASM,aAYhD,SAASE,IAKP,QAJWC,KAAKC,IACdjM,SAASC,gBAAgBkE,YACzB1F,OAAOyN,YAAc,GAERN,GAWG,SAAhBO,IACF,GAAIJ,IAAmB,CAErBlN,aAAayM,GAQb,IAJA,IAAIc,EAAqB,GAGrBC,EAAiBvG,KAAKwG,eAClBD,EAAeE,YAAYnB,IAC7BiB,EAAeG,UAAUC,SAAS,kBACpCL,EAAmB/B,KAAKgC,GAE1BA,EAAiBA,EAAeC,cAKlC,IADA,IAAII,EAAsB5G,KAAKL,iBAAiB,kBACvCvB,EAAI,EAAGA,EAAIwI,EAAoBxL,OAAQgD,IAC9CkI,EAAmB/B,KAAKqC,EAAoBxI,IAK9C,IADA,IAAIyI,EAAqBvB,EAAK3F,iBAAiB,SACtCmH,EAAI,EAAGA,EAAID,EAAmBzL,OAAQ0L,KAEc,IAAvDR,EAAmB/H,QAAQsI,EAAmBC,KAChDD,EAAmBC,GAAGJ,UAAUK,OAAO,QAKvC/G,KAAKgG,cAAc,mBACrBhG,KAAKgG,cAAc,kBAAkBU,UAAUM,IAAI,SAUpC,SAAjBC,IACF,IAAItJ,EAAIqC,KAEJiG,MACFT,EAAcxM,WAAW,WAEvB,IADA,IAAIkO,EAASvJ,EAAE6I,eACPU,EAAOT,YAAYnB,IACzB4B,EAAOR,UAAUK,OAAO,QACxBG,EAASA,EAAOV,cAEd7I,EAAEqI,cAAc,UAClBrI,EAAEqI,cAAc,SAASU,UAAUK,OAAO,SAE3C,MAIuB,SAA1BI,EAAoCvJ,GAGtC,IAFA,IAAIwJ,EAAK,KACLF,EAAStJ,EAAEyJ,OAAOb,eACdU,EAAOT,YAAYnB,IAAO,CAChC,GAAI4B,EAAOR,UAAUC,SAAS,aAAc,CAC1CS,EAAKF,EACL,MAEFA,EAASA,EAAOV,cAIlBY,EAAGpB,cAAc,kBAAkBU,UAAUY,OAAO,QAG/CrB,KACHmB,EAAGV,UAAUY,OAAO,UAItB1J,EAAE2J,kBAlFJ,IA+FQC,EATJC,EAAoBnC,EAAKU,cAAc,uBAGxCC,KACDwB,GACAA,EAAkBf,UAAUC,SAAS,WAEjCc,EAAkBf,UAAUC,SAAS,4BACvCc,EAAkBf,UAAUM,IAAI,WAC5BQ,EAAmBC,EAAkBzB,cAAc,oBAErDwB,EAAiBd,UAAUM,IAAI,SAMrC,IADA,IAAIU,EAAsBpC,EAAK3F,iBAAiB,2BACvCtG,EAAI,EAAGA,EAAIqO,EAAoBtM,OAAQ/B,IAAK,CACnD,IAAIsO,EAAOD,EAAoBrO,GAC/BsO,EAAK/O,iBAAiB,YAAayN,GACnCsB,EAAK/O,iBAAiB,aAAcqO,GAEpC,IAAIW,EAAQD,EAAK3B,cAAc,kBAC3B4B,GAGFA,EAAMhP,iBAAiB,QAASuO,GAyCpC,IAnCA,IAAIU,EAAgB,SAAUjK,GAE5B,IAAIkK,EAAgBlK,EAAEyJ,OAAOb,cAAcR,cAAc,kBACrD8B,GACFA,EAAcpB,UAAUM,IAAI,QAK9B,IADA,IAAIE,EAAStJ,EAAEyJ,OAAOb,eACdU,EAAOT,YAAYnB,IACrB4B,EAAOR,UAAUC,SAAS,kBAC5BO,EAAOR,UAAUM,IAAI,QAEvBE,EAASA,EAAOV,eAIhBuB,EAAe,SAAUnK,GAE3B,IAAIkK,EAAgBlK,EAAEyJ,OAAOb,cAAcR,cAAc,kBACrD8B,GACFA,EAAcpB,UAAUK,OAAO,QAKjC,IADA,IAAIG,EAAStJ,EAAEyJ,OAAOb,eACdU,EAAOT,YAAYnB,IACrB4B,EAAOR,UAAUC,SAAS,kBAC5BO,EAAOR,UAAUK,OAAO,QAE1BG,EAASA,EAAOV,eAIhBwB,EAAQ1C,EAAK3F,iBAAiB,KACzBiF,EAAI,EAAGA,EAAIoD,EAAM5M,OAAQwJ,IAAK,CACrC,IAAIqD,EAAOD,EAAMpD,GAEjBqD,EAAKrP,iBAAiB,QAASiP,GAC/BI,EAAKrP,iBAAiB,OAAQmP,GA8ChC,GAxCAhC,EAAYnN,iBAAiB,QAAS,WAChCmN,EAAYW,UAAUC,SAAS,wBAEjCZ,EAAYW,UAAUK,OAAO,uBAC7BhB,EAAYhL,aAAa,gBAAiB,SAG1CuK,EAAKoB,UAAUK,OAAO,UAGtBhB,EAAYmC,cAAc,IAAIxI,MAAM,YAGpCqG,EAAYW,UAAUM,IAAI,uBAC1BjB,EAAYhL,aAAa,gBAAiB,QAG1CuK,EAAKoB,UAAUM,IAAI,aAuBnB,iBAAkBrO,OAsHpB,IArHA,IAAIwP,EAAqB,SAAUlF,EAAWlE,GAE5C,IADA,IAAIqJ,EAAWnF,EAAUtD,iBAAiB,IAAMZ,GACvCnB,EAAI,EAAGA,EAAIwK,EAAShN,OAAQwC,IACnCwK,EAASxK,GAAG8I,UAAUK,OAAOhI,IAkBjCsJ,EAAmB,SAAUzK,GAGzB0H,IAAS1H,EAAEyJ,SAjBQ,SAAUiB,GAE/B,IADA,IAAIC,GAAe,EACoB,QAA/BD,EAAOA,EAAK9B,gBACd8B,EAAKE,WAAaC,KAAKC,cAGvBJ,EAAK7B,YAAYnB,KACnBiD,GAAe,GAGnB,OAAOA,EAQJI,CAAiB/K,EAAEyJ,SACpBpB,MAGAkC,EAAmB7C,EAAM,QACzB6C,EAAmB7C,EAAM,UACzB6C,EAAmB7C,EAAM,WAI3BpL,SAASM,oBAAoB,eAAgB6N,GAAkB,IAGjEO,EAAe,SAAUhL,GAEvB,IAAKqI,IACH,OAAO,EAGT,IAAI4C,EAAoB7I,KAAKwG,cAG7B,GAAKqC,EAAkBnC,UAAUC,SAAS,UA6DxCkC,EAAkBnC,UAAUK,OAAO,UAGnCoB,EAAmB7C,EAAM,YAhE0B,CAEnD1H,EAAEkL,iBAKF,IAFA,IAAIC,EAAkB,GACtBxC,EAAiBsC,GACTtC,EAAeE,YAAYnB,IAC7BiB,EAAeG,UAAUC,SAAS,WACpCoC,EAAgBxE,KAAKgC,GAEvBA,EAAiBA,EAAeC,cAGlC,IADA,IAAIwC,EAAa1D,EAAK3F,iBAAiB,WAC9BmH,EAAI,EAAGA,EAAIkC,EAAW5N,OAAQ0L,KAEW,IAA5CiC,EAAgBxK,QAAQyK,EAAWlC,KACrCkC,EAAWlC,GAAGJ,UAAUK,OAAO,UAKnC8B,EAAkBnC,UAAUM,IAAI,UAGhC,IAAIiC,EAAuB,GAE3B,IADA1C,EAAiBsC,GACTtC,EAAeE,YAAYnB,IAC7BiB,EAAeG,UAAUC,SAAS,SACpCsC,EAAqB1E,KAAKgC,GAE5BA,EAAiBA,EAAeC,cAGlC,IADA,IAAI0C,EAAoB5D,EAAK3F,iBAAiB,SACrChC,EAAI,EAAGA,EAAIuL,EAAkB9N,OAAQuC,KAEgB,IAAxDsL,EAAqB1K,QAAQ2K,EAAkBvL,KACjDuL,EAAkBvL,GAAG+I,UAAUK,OAAO,QAW1C,IANI8B,EAAkB7C,cAAc,cAClC6C,EAAkB7C,cAAc,aAAaU,UAAUM,IAAI,QAI7DT,EAAiBvG,KAAKwG,eACdD,EAAeE,YAAYnB,IAC7BiB,EAAeG,UAAUC,SAAS,aACpCJ,EAAeG,UAAUM,IAAI,QAE/BT,EAAiBA,EAAeC,cAIlCtM,SAAStB,iBAAiB,aAAcyP,GAAkB,KAa1Dc,EAAe7D,EAAK3F,iBAAiB,+BAChCyJ,EAAI,EAAGA,EAAID,EAAa/N,OAAQgO,IACvCD,EAAaC,GAAGxQ,iBAAiB,aAAcgQ,GAAc,GAKjE,OAAO5I,MClYLqJ,6BAA+B,WACjC,IAAIC,EAASpP,SAASyF,iBAAiB,4BACvC,GAAI2J,EACF,IAAK,IAAIjQ,EAAI,EAAGA,EAAIiQ,EAAOlO,OAAQ/B,IAAK,CAEtCiQ,EAAOjQ,GAAGqN,UAAUM,IAAI,8BAGxB,IAAItD,EAAUxJ,SAASuC,cAAc,OAGrCiH,EAAQ3I,aAAa,QAASuO,EAAOjQ,GAAGiC,aAAa,UAGrDgO,EAAOjQ,GAAGoK,gBAAgB,SAG1B6F,EAAOjQ,GAAGsF,WAAW5B,aAAa2G,EAAS4F,EAAOjQ,IAClDqK,EAAQb,YAAYyG,EAAOjQ,MAIjCgQ,+BAQA,IAAIE,6BAA+B,WACjC,IAIU7F,EAJN8F,EAAUtP,SAASyF,iBAAiB,wBACxC,GAAI6J,EAAQpO,OACV,IAAK/B,EAAI,EAAGA,EAAImQ,EAAQpO,OAAQ/B,IAAK,CAC9BmQ,EAAQnQ,GAAGsF,WAAW+H,UAAUC,SAAS,qBACxCjD,EAAUxJ,SAASuC,cAAc,QAC7B1B,aAAa,QAAS,kBAC9ByO,EAAQnQ,GAAGsF,WAAW5B,aAAa2G,EAAS8F,EAAQnQ,IACpDqK,EAAQb,YAAY2G,EAAQnQ,OAKpCkQ,+BAQA,IAAIE,wBAA0B,WAC5B,IAAIC,EACAC,EAAUzP,SAASyF,iBACrB,mDAEF,GAAIgK,EAAQvO,OACV,IAAK/B,EAAI,EAAGA,EAAIsQ,EAAQvO,OAAQ/B,IAAK,CAKH,OAHhCqQ,EAAiBC,EAAQtQ,GAAGsF,YAGTiB,WACjB8J,EAAe/K,WAAW5B,aAAa4M,EAAQtQ,GAAIqQ,GAEV,IAArCA,EAAe5G,WAAW1H,QAC5BsO,EAAe/K,WAAWiL,YAAYF,IAK1C,IAAIG,EAAYF,EAAQtQ,GAAGqN,UAAUC,SAAS,aAC1C,YACA,aACJgD,EAAQtQ,GAAGqN,UAAUK,OAAO8C,GAG5B,IAAIC,EAAS5P,SAASuC,cAAc,UACpCqN,EAAO/O,aAAa,QAAS8O,GAC7BF,EAAQtQ,GAAGsF,WAAW5B,aAAa+M,EAAQH,EAAQtQ,IACnDyQ,EAAOjH,YAAY8G,EAAQtQ,IAG3B,IAAIiJ,EAAMpI,SAASuC,cAAc,OACjC6F,EAAIvH,aAAa,QAAS,kBAC1B+O,EAAOnL,WAAW5B,aAAauF,EAAKwH,GACpCxH,EAAIO,YAAYiH,KAItBL,0BC7FA,IAAMM,UAAY7P,SAAS8L,cAAc,iBACvCgE,WAAa9P,SAAS8L,cAAc,mBACpCiE,KAAO/P,SAAS8L,cAAc,cAE1BkE,gBAAkB,CAACF,WAAYC,MAC/BE,eAAiB,CAACJ,UAAWE,MAEnC,SAASG,eAA0B,IAAA,IAAAC,EAAAC,UAAAlP,OAAVgN,EAAQ,IAAA9G,MAAA+I,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAARnC,EAAQmC,GAAAD,UAAAC,GACX,GAAAvI,OAAOoG,GACZpF,QAAQ,SAACwH,GAAa,OACnCA,EAAc9D,UAAUY,OAAO,UAInC,SAASmD,gBAA2B,IAAA,IAAAC,EAAAJ,UAAAlP,OAAVgN,EAAQ,IAAA9G,MAAAoJ,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAARvC,EAAQuC,GAAAL,UAAAK,GACX,GAAA3I,OAAOoG,GACZpF,QAAQ,SAAC4H,GAAc,OACrCA,EAAelE,UAAUK,OAAO,UAIpCiD,WAAWpR,iBAAiB,QAAS,SAACgF,GACpCA,EAAEkL,iBACFlL,EAAEiN,2BACFT,aAAYnI,WAAA,EAAIkI,kBAGlBD,gBAAgBlH,QAAQ,SAAC8H,GACvBA,EAAYlS,iBAAiB,QAAS,SAACgF,GAGnCqM,KAAKvD,UAAUC,SAAS,UACvB/I,EAAEyJ,OAAOX,UAAUC,SAAS,gBAE7B8D,cAAaxI,WAAA,EAAIkI,oBAavB,IAAM7E,KAAOpL,SAAS8L,cAAc,iBAC9B+E,YAAczF,KAAK0F,WAAU,QAC0BjP,IAAzD7B,SAASyI,eAAe,8BAC1BzI,SAASyI,eAAe,6BAA6BE,YAAYkI,aAOnE,IAAIE,cAAgB,GC7DpB,SAASC,kBAAkBC,EAASC,EAAcC,GAChD,IAAK,IAAIhS,EAAI,EAAGA,EAAI+R,EAAahQ,OAAQ/B,IACnC8R,IAAY9R,GACd+R,EAAa/R,GAAGuD,MAAMd,OAAS,OAE7BsP,EAAa/R,GAAGsF,WAAWA,WAAW+H,UAAUC,SAAS,aAEzDyE,EAAa/R,GAAGiS,eAAe,CAAEC,MAAO,QAASC,SAAU,YAG7DJ,EAAa/R,GAAGuD,MAAMd,OAAS,IAGnC,IAAK,IAAIzC,EAAI,EAAGA,EAAIgS,EAASjQ,OAAQ/B,IAC/B8R,IAAY9R,EACdgS,EAAShS,GAAGqN,UAAUM,IAAI,MAE1BqE,EAAShS,GAAGqN,UAAUK,OAAO,MAKnC,GDyCApO,OAAOC,iBACL,SACA,WACE,IAAI6S,EAAK9S,OAAO+S,aAAexR,SAASC,gBAAgBwR,UAC/CV,cAALQ,GAA2B,IAALA,EACxBvR,SAASyI,eAAe,sBAAsB+D,UAAUM,IAAI,QAE5D9M,SAASyI,eAAe,sBAAsB+D,UAAUK,OAAO,QAEjEkE,cAAgBQ,IAElB,GCpDEvR,SAAS8L,cAAc,SAGzB,IAFA,IAAM4F,KAAO1R,SAASyF,iBAAiB,SAASkM,MAAA,WAG9C,IAAMR,EAAWO,KAAKvS,GAAGsG,iBAAiB,cACpCyL,EAAeQ,KAAKvS,GAAGsG,iBAAiB,kBACxCmM,EAAQF,KAAKvS,GAAGsG,iBAAiB,yBAEnCzF,SAAS8L,cAAc,iBACzBkF,kBAAkB,EAAGE,EAAcC,GAGrC,IAAK,IAAIhS,EAAI,EAAGA,EAAIyS,EAAM1Q,OAAQ/B,IAChCyS,EAAMzS,GAAGT,iBAAiB,QAAS,SAACmT,GAClC,IAAK,IAAI1S,EAAI,EAAGA,EAAI+R,EAAahQ,OAAQ/B,IACvC+R,EAAa/R,GAAGuD,MAAMd,OAAS,MAKrC8P,KAAKvS,GAAGT,iBAAiB,QAAS,SAACmT,GAGjC,GAFAC,OAAO,kBAAkBC,QAAQ,UACjCD,OAAO,kBAAkBC,QAAQ,UAE/BF,EAAM1E,OAAOX,UAAUC,SAAS,cAChCoF,EAAM1E,OAAO1I,WAAW+H,UAAUC,SAAS,cAC3CoF,EAAM1E,OAAO1I,WAAWA,WAAW+H,UAAUC,SAAS,aAEtD,IAAK,IAAItN,EAAI,EAAGA,EAAIgS,EAASjQ,OAAQ/B,IACnC,GACE0S,EAAM1E,SAAWgE,EAAShS,IAC1B0S,EAAM1E,OAAO1I,aAAe0M,EAAShS,IACrC0S,EAAM1E,OAAO1I,WAAWA,aAAe0M,EAAShS,GAChD,CACA6R,kBAAkB7R,EAAG+R,EAAcC,GACnC,UAhCDhS,EAAI,EAAGA,EAAIuS,KAAKxQ,OAAQ/B,IAAGwS,Q,kPCzBrC,SAASxS,GAAgB,mBAAmBgH,QAAQA,OAAOC,IAAID,OAAO,CAAC,UAAUhH,GAAG,oBAAoB8G,QAAQC,OAAOD,QAAQ9G,EAAEwH,QAAQ,WAAWxH,EAAE2S,QAAtJ,CAA+J,SAAS3S,GAAgB,IAAyCuE,EAArCA,EAAEjF,OAAOuT,OAAO,GAAqBtO,EAAE,GAAnBA,EAA4B,SAASD,EAAEG,GAAG,IAAI6C,EAAE9C,EAAEmC,KAAKnC,EAAE6H,SAAS,CAACyG,eAAc,EAAGC,gBAAe,EAAGC,aAAahT,EAAEsE,GAAG2O,WAAWjT,EAAEsE,GAAG4O,QAAO,EAAGC,SAAS,KAAKC,UAAU,mFAAmFC,UAAU,2EAA2EC,UAAS,EAAGC,cAAc,IAAIC,YAAW,EAAGC,cAAc,OAAOC,QAAQ,OAAOC,aAAa,SAASpP,EAAED,GAAG,OAAOtE,EAAE,4BAA4B4T,KAAKtP,EAAE,IAAIuP,MAAK,EAAGC,UAAU,aAAaC,WAAU,EAAGC,OAAO,SAASC,aAAa,IAAIC,MAAK,EAAGC,eAAc,EAAGC,eAAc,EAAGC,UAAS,EAAGC,aAAa,EAAEC,SAAS,WAAWC,aAAY,EAAGC,cAAa,EAAGC,cAAa,EAAGC,kBAAiB,EAAGC,UAAU,SAASC,WAAW,KAAKC,KAAK,EAAEC,KAAI,EAAGC,MAAM,GAAGC,aAAa,EAAEC,aAAa,EAAEC,eAAe,EAAEC,MAAM,IAAIC,OAAM,EAAGC,cAAa,EAAGC,WAAU,EAAGC,eAAe,EAAEC,QAAO,EAAGC,cAAa,EAAGC,eAAc,EAAGC,UAAS,EAAGC,iBAAgB,EAAGC,gBAAe,EAAGC,OAAO,KAAKvR,EAAEwR,SAAS,CAACC,WAAU,EAAGC,UAAS,EAAGC,cAAc,KAAKC,iBAAiB,EAAEC,YAAY,KAAKC,aAAa,EAAEC,UAAU,EAAEC,MAAM,KAAKC,UAAU,KAAKC,WAAW,KAAKC,UAAU,EAAEC,WAAW,KAAKC,WAAW,KAAKC,WAAU,EAAGC,WAAW,KAAKC,WAAW,KAAKC,YAAY,KAAKC,QAAQ,KAAKC,SAAQ,EAAGC,YAAY,EAAEC,UAAU,KAAKC,SAAQ,EAAGC,MAAM,KAAKC,YAAY,GAAGC,mBAAkB,EAAGC,WAAU,GAAI1X,EAAEwM,OAAOhI,EAAEA,EAAEwR,UAAUxR,EAAEmT,iBAAiB,KAAKnT,EAAEoT,SAAS,KAAKpT,EAAEqT,SAAS,KAAKrT,EAAEsT,YAAY,GAAGtT,EAAEuT,mBAAmB,GAAGvT,EAAEwT,gBAAe,EAAGxT,EAAEyT,UAAS,EAAGzT,EAAE0T,aAAY,EAAG1T,EAAE2T,OAAO,SAAS3T,EAAE4T,QAAO,EAAG5T,EAAE6T,aAAa,KAAK7T,EAAEoQ,UAAU,KAAKpQ,EAAE8T,SAAS,EAAE9T,EAAE+T,aAAY,EAAG/T,EAAEgU,QAAQxY,EAAEsE,GAAGE,EAAEiU,aAAa,KAAKjU,EAAEkU,cAAc,KAAKlU,EAAEmU,eAAe,KAAKnU,EAAEoU,iBAAiB,mBAAmBpU,EAAEqU,YAAY,EAAErU,EAAEsU,YAAY,KAAKxR,EAAEtH,EAAEsE,GAAGyU,KAAK,UAAU,GAAGvU,EAAE0H,QAAQlM,EAAEwM,OAAO,GAAGhI,EAAE6H,SAAS5H,EAAE6C,GAAG9C,EAAE8R,aAAa9R,EAAE0H,QAAQoI,aAAa9P,EAAEwU,iBAAiBxU,EAAE0H,aAAQ,IAASrL,SAASoY,WAAWzU,EAAE2T,OAAO,YAAY3T,EAAEoU,iBAAiB,4BAAuB,IAAS/X,SAASqY,eAAe1U,EAAE2T,OAAO,eAAe3T,EAAEoU,iBAAiB,0BAA0BpU,EAAE2U,SAASnZ,EAAEoZ,MAAM5U,EAAE2U,SAAS3U,GAAGA,EAAE6U,cAAcrZ,EAAEoZ,MAAM5U,EAAE6U,cAAc7U,GAAGA,EAAE8U,iBAAiBtZ,EAAEoZ,MAAM5U,EAAE8U,iBAAiB9U,GAAGA,EAAE+U,YAAYvZ,EAAEoZ,MAAM5U,EAAE+U,YAAY/U,GAAGA,EAAEgV,aAAaxZ,EAAEoZ,MAAM5U,EAAEgV,aAAahV,GAAGA,EAAEiV,cAAczZ,EAAEoZ,MAAM5U,EAAEiV,cAAcjV,GAAGA,EAAEkV,YAAY1Z,EAAEoZ,MAAM5U,EAAEkV,YAAYlV,GAAGA,EAAEmV,aAAa3Z,EAAEoZ,MAAM5U,EAAEmV,aAAanV,GAAGA,EAAEoV,YAAY5Z,EAAEoZ,MAAM5U,EAAEoV,YAAYpV,GAAGA,EAAEqV,WAAW7Z,EAAEoZ,MAAM5U,EAAEqV,WAAWrV,GAAGA,EAAEsV,YAAYvV,IAAIC,EAAEuV,SAAS,4BAA4BvV,EAAEwV,sBAAsBxV,EAAEyV,MAAK,KAAS/R,UAAUgS,YAAY,WAAWvT,KAAKsQ,YAAYkD,KAAK,iBAAiBC,KAAK,CAACC,cAAc,UAAUF,KAAK,4BAA4BC,KAAK,CAACE,SAAS,OAAO/V,EAAE2D,UAAUqS,SAAShW,EAAE2D,UAAUsS,SAAS,SAASjW,EAAED,EAAEG,GAAG,IAAI6C,EAAEX,KAAK,GAAG,kBAAkBrC,EAAEG,EAAEH,EAAEA,EAAE,UAAU,GAAGA,EAAE,GAAGA,GAAGgD,EAAEyP,WAAW,OAAM,EAAGzP,EAAEmT,SAAS,iBAAiBnW,EAAE,IAAIA,GAAG,IAAIgD,EAAE4P,QAAQnV,OAAO/B,EAAEuE,GAAGmW,SAASpT,EAAE2P,aAAaxS,EAAEzE,EAAEuE,GAAGb,aAAa4D,EAAE4P,QAAQyD,GAAGrW,IAAItE,EAAEuE,GAAGqW,YAAYtT,EAAE4P,QAAQyD,GAAGrW,KAAI,IAAKG,EAAEzE,EAAEuE,GAAGsW,UAAUvT,EAAE2P,aAAajX,EAAEuE,GAAGmW,SAASpT,EAAE2P,aAAa3P,EAAE4P,QAAQ5P,EAAE2P,YAAY6D,SAASnU,KAAKuF,QAAQ8I,OAAO1N,EAAE2P,YAAY6D,SAASnU,KAAKuF,QAAQ8I,OAAO+F,SAASzT,EAAE2P,YAAY+D,OAAO1T,EAAE4P,SAAS5P,EAAE4P,QAAQ+D,KAAK,SAAS1W,EAAED,GAAGtE,EAAEsE,GAAG8V,KAAK,mBAAmB7V,KAAK+C,EAAEmR,aAAanR,EAAE4P,QAAQ5P,EAAE4T,UAAU3W,EAAE2D,UAAUiT,cAAc,WAAW,IAAsG5W,EAAlGvE,EAAE2G,KAAQ,IAAI3G,EAAEkM,QAAQgJ,eAAc,IAAKlV,EAAEkM,QAAQ6G,iBAAgB,IAAK/S,EAAEkM,QAAQ0J,WAAcrR,EAAEvE,EAAEkX,QAAQyD,GAAG3a,EAAEsW,cAAc8E,aAAY,GAAIpb,EAAEuX,MAAM8D,QAAQ,CAAC5Y,OAAO8B,GAAGvE,EAAEkM,QAAQkJ,SAAS7Q,EAAE2D,UAAUoT,aAAa,SAAS/W,EAAED,GAAG,IAAIG,EAAE,GAAG6C,EAAEX,KAAKW,EAAE6T,iBAAgB,IAAK7T,EAAE4E,QAAQ6I,MAAK,IAAKzN,EAAE4E,QAAQ0J,WAAWrR,GAAGA,IAAG,IAAK+C,EAAEmQ,mBAAkB,IAAKnQ,EAAE4E,QAAQ0J,SAAStO,EAAE2P,YAAYoE,QAAQ,CAACjZ,KAAKmC,GAAG+C,EAAE4E,QAAQkJ,MAAM9N,EAAE4E,QAAQ8H,OAAO1P,GAAGgD,EAAE2P,YAAYoE,QAAQ,CAAC/Y,IAAIiC,GAAG+C,EAAE4E,QAAQkJ,MAAM9N,EAAE4E,QAAQ8H,OAAO1P,IAAG,IAAKgD,EAAE0Q,iBAAgB,IAAK1Q,EAAE4E,QAAQ6I,MAAMzN,EAAE+O,aAAa/O,EAAE+O,aAAarW,EAAE,CAACub,UAAUjU,EAAE+O,cAAcgF,QAAQ,CAACE,UAAUhX,GAAG,CAACiX,SAASlU,EAAE4E,QAAQkJ,MAAMpB,OAAO1M,EAAE4E,QAAQ8H,OAAOyH,KAAK,SAASzb,GAAGA,EAAE6M,KAAK6O,KAAK1b,IAAG,IAAKsH,EAAE4E,QAAQ0J,SAAUnR,EAAE6C,EAAEsQ,UAAU,aAAa5X,EAAE,WAAkCyE,EAAE6C,EAAEsQ,UAAU,iBAAiB5X,EAAE,MAAxDsH,EAAE2P,YAAY0E,IAAIlX,IAAmEiC,SAAS,WAAWpC,GAAGA,EAAEqD,YAAYL,EAAEsU,kBAAkBrX,EAAEsI,KAAK6O,KAAKnX,IAAG,IAAK+C,EAAE4E,QAAQ0J,SAASnR,EAAE6C,EAAEsQ,UAAU,eAAerT,EAAE,gBAAgBE,EAAE6C,EAAEsQ,UAAU,mBAAmBrT,EAAE,WAAW+C,EAAE2P,YAAY0E,IAAIlX,GAAGH,GAAG3E,WAAW,WAAW2H,EAAEuU,oBAAoBvX,EAAEqD,QAAQL,EAAE4E,QAAQkJ,SAAS7Q,EAAE2D,UAAU4T,aAAa,WAAW,IAAWxX,EAALqC,KAASuF,QAAQiH,SAAS,OAAO7O,GAAG,OAAOA,IAAIA,EAAEtE,EAAEsE,GAAGyX,IAAtDpV,KAA4D6R,UAAUlU,GAAGC,EAAE2D,UAAUiL,SAAS,SAAS5O,GAAG,IAAID,EAAEqC,KAAKmV,eAAe,OAAOxX,GAAG,UAAQkC,QAASlC,IAAGA,EAAE2W,KAAK,WAAW,IAAI3W,EAAEtE,EAAE2G,MAAMqV,MAAM,YAAY1X,EAAEoT,WAAWpT,EAAE2X,aAAa1X,GAAE,MAAOA,EAAE2D,UAAU0T,gBAAgB,SAAS5b,GAAG,IAAIuE,EAAEoC,KAAKrC,EAAE,IAAG,IAAKC,EAAE2H,QAAQgI,KAAK5P,EAAEC,EAAEoU,gBAAgBpU,EAAEmU,cAAc,IAAInU,EAAE2H,QAAQkJ,MAAM,MAAM7Q,EAAE2H,QAAQwH,QAAQpP,EAAEC,EAAEoU,gBAAgB,WAAWpU,EAAE2H,QAAQkJ,MAAM,MAAM7Q,EAAE2H,QAAQwH,SAAQ,IAAKnP,EAAE2H,QAAQgI,KAAK3P,EAAE0S,YAAY0E,IAAIrX,GAAGC,EAAE2S,QAAQyD,GAAG3a,GAAG2b,IAAIrX,IAAIC,EAAE2D,UAAUiR,SAAS,WAAW,IAAInZ,EAAE2G,KAAK3G,EAAEqZ,gBAAgBrZ,EAAE+W,WAAW/W,EAAEkM,QAAQgJ,eAAelV,EAAEmW,cAAc+F,YAAYlc,EAAEsZ,iBAAiBtZ,EAAEkM,QAAQqH,iBAAiBhP,EAAE2D,UAAUmR,cAAc,WAAiB1S,KAAOwP,eAAegG,cAAtBxV,KAAsCwP,gBAAgB5R,EAAE2D,UAAUoR,iBAAiB,WAAW,IAAItZ,EAAE2G,KAAKpC,EAAEvE,EAAEsW,aAAatW,EAAEkM,QAAQiJ,eAAenV,EAAEoY,QAAQpY,EAAEkY,aAAalY,EAAEiY,YAAW,IAAKjY,EAAEkM,QAAQmI,WAAW,IAAIrU,EAAEuW,WAAWvW,EAAEsW,aAAa,IAAItW,EAAE+W,WAAW,EAAE/W,EAAEuW,UAAU,EAAE,IAAIvW,EAAEuW,YAAYhS,EAAEvE,EAAEsW,aAAatW,EAAEkM,QAAQiJ,eAAenV,EAAEsW,aAAa,GAAG,IAAItW,EAAEuW,UAAU,KAAKvW,EAAEic,aAAa1X,KAAKA,EAAE2D,UAAUkU,YAAY,WAAW,IAAI7X,EAAEoC,MAAK,IAAKpC,EAAE2H,QAAQgH,SAAS3O,EAAEsS,WAAW7W,EAAEuE,EAAE2H,QAAQkH,WAAWiJ,SAAS,eAAe9X,EAAEqS,WAAW5W,EAAEuE,EAAE2H,QAAQmH,WAAWgJ,SAAS,eAAe9X,EAAEwS,WAAWxS,EAAE2H,QAAQgJ,cAAc3Q,EAAEsS,WAAWyF,YAAY,gBAAgBC,WAAW,wBAAwBhY,EAAEqS,WAAW0F,YAAY,gBAAgBC,WAAW,wBAAwBhY,EAAEwV,SAAShQ,KAAKxF,EAAE2H,QAAQkH,YAAY7O,EAAEsS,WAAWgE,UAAUtW,EAAE2H,QAAQ8G,cAAczO,EAAEwV,SAAShQ,KAAKxF,EAAE2H,QAAQmH,YAAY9O,EAAEqS,WAAW8D,SAASnW,EAAE2H,QAAQ8G,eAAc,IAAKzO,EAAE2H,QAAQmI,UAAU9P,EAAEsS,WAAWwF,SAAS,kBAAkBjC,KAAK,gBAAgB,SAAS7V,EAAEsS,WAAWlJ,IAAIpJ,EAAEqS,YAAYyF,SAAS,gBAAgBjC,KAAK,CAACoC,gBAAgB,OAAOlC,SAAS,SAAS/V,EAAE2D,UAAUuU,UAAU,WAAW,IAAIlY,EAAED,EAAEG,EAAEkC,KAAK,IAAG,IAAKlC,EAAEyH,QAAQ2H,KAAK,CAAC,IAAIpP,EAAE+T,QAAQ6D,SAAS,gBAAgB/X,EAAEtE,EAAE,UAAUqc,SAAS5X,EAAEyH,QAAQ4H,WAAWvP,EAAE,EAAEA,GAAGE,EAAEiY,cAAcnY,GAAG,EAAED,EAAE0W,OAAOhb,EAAE,UAAUgb,OAAOvW,EAAEyH,QAAQyH,aAAahM,KAAKhB,KAAKlC,EAAEF,KAAKE,EAAE+R,MAAMlS,EAAEoW,SAASjW,EAAEyH,QAAQ+G,YAAYxO,EAAE+R,MAAM2D,KAAK,MAAMwC,QAAQN,SAAS,kBAAkB9X,EAAE2D,UAAU0U,SAAS,WAAW,IAAIrY,EAAEoC,KAAKpC,EAAE2S,QAAQ3S,EAAEiU,QAAQsC,SAASvW,EAAE2H,QAAQ8I,MAAM,uBAAuBqH,SAAS,eAAe9X,EAAEwS,WAAWxS,EAAE2S,QAAQnV,OAAOwC,EAAE2S,QAAQ+D,KAAK,SAAS1W,EAAED,GAAGtE,EAAEsE,GAAG8V,KAAK,mBAAmB7V,GAAGwU,KAAK,kBAAkB/Y,EAAEsE,GAAG8V,KAAK,UAAU,MAAM7V,EAAEiU,QAAQ6D,SAAS,gBAAgB9X,EAAE0S,YAAY,IAAI1S,EAAEwS,WAAW/W,EAAE,8BAA8B0a,SAASnW,EAAEiU,SAASjU,EAAE2S,QAAQ2F,QAAQ,8BAA8BhP,SAAStJ,EAAEgT,MAAMhT,EAAE0S,YAAY1M,KAAK,6BAA6BsD,SAAStJ,EAAE0S,YAAY0E,IAAI,UAAU,IAAG,IAAKpX,EAAE2H,QAAQsH,aAAY,IAAKjP,EAAE2H,QAAQoJ,eAAe/Q,EAAE2H,QAAQiJ,eAAe,GAAGnV,EAAE,iBAAiBuE,EAAEiU,SAASuD,IAAI,SAASM,SAAS,iBAAiB9X,EAAEuY,gBAAgBvY,EAAE6X,cAAc7X,EAAEkY,YAAYlY,EAAEwY,aAAaxY,EAAEyY,gBAAgB,iBAAiBzY,EAAE+R,aAAa/R,EAAE+R,aAAa,IAAG,IAAK/R,EAAE2H,QAAQ6H,WAAWxP,EAAEgT,MAAM8E,SAAS,cAAc9X,EAAE2D,UAAU+U,UAAU,WAAW,IAAIjd,EAAQsH,EAAID,EAAE3C,EAAEiC,KAAQlC,EAAE5D,SAASqc,yBAAyB1Y,EAAEE,EAAE8T,QAAQsC,WAAnD,GAA6E,EAAfpW,EAAEwH,QAAQ4I,KAAO,CAAC,IAAIzN,EAAE3C,EAAEwH,QAAQ+I,aAAavQ,EAAEwH,QAAQ4I,KAAKxN,EAAEuF,KAAK6O,KAAKlX,EAAEzC,OAAOsF,GAAGrH,EAAE,EAAEA,EAAEsH,EAAEtH,IAAI,CAAqC,IAApC,IAAI+E,EAAElE,SAASuC,cAAc,OAAWmB,EAAE,EAAEA,EAAEG,EAAEwH,QAAQ4I,KAAKvQ,IAAI,CAAqC,IAApC,IAAIN,EAAEpD,SAASuC,cAAc,OAAWkB,EAAE,EAAEA,EAAEI,EAAEwH,QAAQ+I,aAAa3Q,IAAI,CAAC,IAAIgH,EAAEtL,EAAEqH,GAAG9C,EAAEG,EAAEwH,QAAQ+I,aAAa3Q,GAAGE,EAAE2Y,IAAI7R,IAAIrH,EAAEuF,YAAYhF,EAAE2Y,IAAI7R,IAAIvG,EAAEyE,YAAYvF,GAAGQ,EAAE+E,YAAYzE,GAAGL,EAAE8T,QAAQ4E,QAAQpC,OAAOvW,GAAGC,EAAE8T,QAAQsC,WAAWA,WAAWA,WAAWa,IAAI,CAACnZ,MAAM,IAAIkC,EAAEwH,QAAQ+I,aAAa,IAAIxP,QAAQ,mBAAmBlB,EAAE2D,UAAUmV,gBAAgB,SAAS9Y,EAAED,GAAG,IAAIG,EAAE6C,EAAE9C,EAAE6C,EAAEV,KAAKjC,GAAE,EAAGK,EAAEsC,EAAEmR,QAAQhW,QAAQyB,EAAE3E,OAAOyN,YAAY/M,EAAEV,QAAQkD,QAAQ,GAAG,WAAW6E,EAAEuN,UAAUpQ,EAAEP,EAAE,WAAWoD,EAAEuN,UAAUpQ,EAAEO,EAAE,QAAQsC,EAAEuN,YAAYpQ,EAAEqI,KAAKyQ,IAAIrZ,EAAEc,IAAIsC,EAAE6E,QAAQ2I,YAAYxN,EAAE6E,QAAQ2I,WAAW9S,QAAQ,OAAOsF,EAAE6E,QAAQ2I,WAAW,CAAQ,IAAIpQ,KAAX6C,EAAE,KAAcD,EAAEyQ,YAAYzQ,EAAEyQ,YAAYtM,eAAe/G,MAAK,IAAK4C,EAAE2R,iBAAiBxE,YAAYhQ,EAAE6C,EAAEyQ,YAAYrT,KAAK6C,EAAED,EAAEyQ,YAAYrT,IAAID,EAAE6C,EAAEyQ,YAAYrT,KAAK6C,EAAED,EAAEyQ,YAAYrT,KAAK,OAAO6C,EAAE,OAAOD,EAAEsQ,kBAAkBrQ,IAAID,EAAEsQ,mBAAkBrT,IAAkN+C,EAAEsQ,iBAAiBrQ,EAAE,YAAYD,EAAE0Q,mBAAmBzQ,GAAGD,EAAEkW,QAAQjW,IAAID,EAAE6E,QAAQlM,EAAEwM,OAAO,GAAGnF,EAAE2R,iBAAiB3R,EAAE0Q,mBAAmBzQ,KAAI,IAAK/C,IAAI8C,EAAEiP,aAAajP,EAAE6E,QAAQoI,cAAcjN,EAAEmW,QAAQjZ,IAAIG,EAAE4C,GAAG,OAAOD,EAAEsQ,mBAAmBtQ,EAAEsQ,iBAAiB,KAAKtQ,EAAE6E,QAAQ7E,EAAE2R,kBAAiB,IAAKzU,IAAI8C,EAAEiP,aAAajP,EAAE6E,QAAQoI,cAAcjN,EAAEmW,QAAQjZ,GAAGG,EAAE4C,GAAG/C,IAAG,IAAKG,GAAG2C,EAAEmR,QAAQ5F,QAAQ,aAAa,CAACvL,EAAE3C,MAAMH,EAAE2D,UAAUqR,YAAY,SAAShV,EAAED,GAAG,IAAIG,EAAE6C,EAAID,EAAEV,KAAKjC,EAAE1E,EAAEuE,EAAEkZ,eAAe,OAAO/Y,EAAEgZ,GAAG,MAAMnZ,EAAEkL,iBAAiB/K,EAAEgZ,GAAG,QAAQhZ,EAAEA,EAAEiZ,QAAQ,OAAkDlZ,EAAzC4C,EAAE0P,WAAW1P,EAAE6E,QAAQiJ,gBAAgB,EAAM,GAAG9N,EAAE0P,WAAW1P,EAAEiP,cAAcjP,EAAE6E,QAAQiJ,eAAe5Q,EAAEwU,KAAK6E,SAAS,IAAI,WAAWtW,EAAE,GAAI7C,EAAE4C,EAAE6E,QAAQiJ,eAAe9N,EAAE6E,QAAQgJ,aAAazQ,EAAE4C,EAAE0P,WAAW1P,EAAE6E,QAAQgJ,cAAc7N,EAAE4U,aAAa5U,EAAEiP,aAAahP,GAAE,EAAGhD,GAAG,MAAM,IAAI,OAAOgD,EAAE,GAAI7C,EAAE4C,EAAE6E,QAAQiJ,eAAe1Q,EAAE4C,EAAE0P,WAAW1P,EAAE6E,QAAQgJ,cAAc7N,EAAE4U,aAAa5U,EAAEiP,aAAahP,GAAE,EAAGhD,GAAG,MAAM,IAAI,QAAQ,IAAIS,EAAE,IAAIR,EAAEwU,KAAK8E,MAAM,EAAEtZ,EAAEwU,KAAK8E,OAAOnZ,EAAEmZ,QAAQxW,EAAE6E,QAAQiJ,eAAe9N,EAAE4U,aAAa5U,EAAEyW,eAAe/Y,IAAG,EAAGT,GAAGI,EAAEoW,WAAWlI,QAAQ,SAAS,MAAM,QAAQ,SAASrO,EAAE2D,UAAU4V,eAAe,SAAS9d,GAAG,IAAWuE,EAAEoC,KAAKoX,sBAAsBzZ,EAAE,EAAlC,GAAoCtE,EAAEuE,EAAEA,EAAExC,OAAO,GAAG/B,EAAEuE,EAAEA,EAAExC,OAAO,QAAQ,IAAI,IAAI0C,KAAKF,EAAE,CAAC,GAAGvE,EAAEuE,EAAEE,GAAG,CAACzE,EAAEsE,EAAE,MAAMA,EAAEC,EAAEE,GAAG,OAAOzE,GAAGuE,EAAE2D,UAAU8V,cAAc,WAAW,IAAIzZ,EAAEoC,KAAKpC,EAAE2H,QAAQ2H,MAAM,OAAOtP,EAAEiS,QAAQxW,EAAE,KAAKuE,EAAEiS,OAAOyH,IAAI,cAAc1Z,EAAEgV,aAAa0E,IAAI,mBAAmBje,EAAEoZ,MAAM7U,EAAE2Z,UAAU3Z,GAAE,IAAK0Z,IAAI,mBAAmBje,EAAEoZ,MAAM7U,EAAE2Z,UAAU3Z,GAAE,KAAK,IAAKA,EAAE2H,QAAQ4G,eAAevO,EAAEiS,MAAMyH,IAAI,gBAAgB1Z,EAAEsV,aAAatV,EAAEiU,QAAQyF,IAAI,2BAA0B,IAAK1Z,EAAE2H,QAAQgH,QAAQ3O,EAAEwS,WAAWxS,EAAE2H,QAAQgJ,eAAe3Q,EAAEsS,YAAYtS,EAAEsS,WAAWoH,IAAI,cAAc1Z,EAAEgV,aAAahV,EAAEqS,YAAYrS,EAAEqS,WAAWqH,IAAI,cAAc1Z,EAAEgV,cAAa,IAAKhV,EAAE2H,QAAQ4G,gBAAgBvO,EAAEsS,YAAYtS,EAAEsS,WAAWoH,IAAI,gBAAgB1Z,EAAEsV,YAAYtV,EAAEqS,YAAYrS,EAAEqS,WAAWqH,IAAI,gBAAgB1Z,EAAEsV,cAActV,EAAEgT,MAAM0G,IAAI,mCAAmC1Z,EAAEoV,cAAcpV,EAAEgT,MAAM0G,IAAI,kCAAkC1Z,EAAEoV,cAAcpV,EAAEgT,MAAM0G,IAAI,+BAA+B1Z,EAAEoV,cAAcpV,EAAEgT,MAAM0G,IAAI,qCAAqC1Z,EAAEoV,cAAcpV,EAAEgT,MAAM0G,IAAI,cAAc1Z,EAAEiV,cAAcxZ,EAAEa,UAAUod,IAAI1Z,EAAEqU,iBAAiBrU,EAAE4Z,YAAY5Z,EAAE6Z,sBAAqB,IAAK7Z,EAAE2H,QAAQ4G,eAAevO,EAAEgT,MAAM0G,IAAI,gBAAgB1Z,EAAEsV,aAAY,IAAKtV,EAAE2H,QAAQiI,eAAenU,EAAEuE,EAAE0S,aAAa6D,WAAWmD,IAAI,cAAc1Z,EAAEkV,eAAezZ,EAAEV,QAAQ2e,IAAI,iCAAiC1Z,EAAEuV,YAAYvV,EAAE8Z,mBAAmBre,EAAEV,QAAQ2e,IAAI,sBAAsB1Z,EAAEuV,YAAYvV,EAAE+Z,QAAQte,EAAE,oBAAoBuE,EAAE0S,aAAagH,IAAI,YAAY1Z,EAAEkL,gBAAgBzP,EAAEV,QAAQ2e,IAAI,oBAAoB1Z,EAAEuV,YAAYvV,EAAEmV,cAAcnV,EAAE2D,UAAUkW,mBAAmB,WAAW,IAAI7Z,EAAEoC,KAAKpC,EAAEgT,MAAM0G,IAAI,mBAAmBje,EAAEoZ,MAAM7U,EAAE2Z,UAAU3Z,GAAE,IAAKA,EAAEgT,MAAM0G,IAAI,mBAAmBje,EAAEoZ,MAAM7U,EAAE2Z,UAAU3Z,GAAE,KAAMA,EAAE2D,UAAUqW,YAAY,WAAW,IAAIve,EAAwB,EAApB2G,KAAOuF,QAAQ4I,QAAU9U,EAAzB2G,KAA6BuQ,QAAQ4D,WAAWA,YAAYyB,WAAW,SAAvE5V,KAAkF6R,QAAQ4E,QAAQpC,OAAOhb,KAAKuE,EAAE2D,UAAUsR,aAAa,SAASxZ,IAAG,IAAK2G,KAAK4R,cAAcvY,EAAEwR,2BAA2BxR,EAAEkO,kBAAkBlO,EAAEyP,mBAAmBlL,EAAE2D,UAAUsW,QAAQ,SAASja,GAAG,IAAID,EAAEqC,KAAKrC,EAAE+U,gBAAgB/U,EAAEkT,YAAY,GAAGlT,EAAE0Z,gBAAgBhe,EAAE,gBAAgBsE,EAAEkU,SAASuC,SAASzW,EAAEkS,OAAOlS,EAAEkS,MAAM9I,SAASpJ,EAAEuS,YAAYvS,EAAEuS,WAAW9U,SAASuC,EAAEuS,WAAWyF,YAAY,2CAA2CC,WAAW,sCAAsCZ,IAAI,UAAU,IAAIrX,EAAEyV,SAAShQ,KAAKzF,EAAE4H,QAAQkH,YAAY9O,EAAEuS,WAAWnJ,UAAUpJ,EAAEsS,YAAYtS,EAAEsS,WAAW7U,SAASuC,EAAEsS,WAAW0F,YAAY,2CAA2CC,WAAW,sCAAsCZ,IAAI,UAAU,IAAIrX,EAAEyV,SAAShQ,KAAKzF,EAAE4H,QAAQmH,YAAY/O,EAAEsS,WAAWlJ,UAAUpJ,EAAE4S,UAAU5S,EAAE4S,QAAQoF,YAAY,qEAAqEC,WAAW,eAAeA,WAAW,oBAAoBtB,KAAK,WAAWjb,EAAE2G,MAAMyT,KAAK,QAAQpa,EAAE2G,MAAMoS,KAAK,sBAAsBzU,EAAE2S,YAAY6D,SAASnU,KAAKuF,QAAQ8I,OAAO+F,SAASzW,EAAE2S,YAAY8D,SAASzW,EAAEiT,MAAMwD,SAASzW,EAAEkU,QAAQwC,OAAO1W,EAAE4S,UAAU5S,EAAEia,cAAcja,EAAEkU,QAAQ8D,YAAY,gBAAgBhY,EAAEkU,QAAQ8D,YAAY,qBAAqBhY,EAAEkU,QAAQ8D,YAAY,gBAAgBhY,EAAEoT,WAAU,EAAGnT,GAAGD,EAAEkU,QAAQ5F,QAAQ,UAAU,CAACtO,KAAKC,EAAE2D,UAAU2T,kBAAkB,SAAS7b,GAAG,IAAWsE,EAAE,GAAGA,EAAVqC,KAAcgS,gBAAgB,IAAG,IAAjChS,KAAwCuF,QAAQgI,KAAhDvN,KAAuDsQ,YAAY0E,IAAIrX,GAAvEqC,KAA4EuQ,QAAQyD,GAAG3a,GAAG2b,IAAIrX,IAAIC,EAAE2D,UAAUuW,UAAU,SAASze,EAAEuE,GAAG,IAAID,EAAEqC,MAAK,IAAKrC,EAAE0T,gBAAgB1T,EAAE4S,QAAQyD,GAAG3a,GAAG2b,IAAI,CAAC5F,OAAOzR,EAAE4H,QAAQ6J,SAASzR,EAAE4S,QAAQyD,GAAG3a,GAAGqb,QAAQ,CAACqD,QAAQ,GAAGpa,EAAE4H,QAAQkJ,MAAM9Q,EAAE4H,QAAQ8H,OAAOzP,KAAKD,EAAEsX,gBAAgB5b,GAAGsE,EAAE4S,QAAQyD,GAAG3a,GAAG2b,IAAI,CAAC+C,QAAQ,EAAE3I,OAAOzR,EAAE4H,QAAQ6J,SAASxR,GAAG5E,WAAW,WAAW2E,EAAEuX,kBAAkB7b,GAAGuE,EAAEoD,QAAQrD,EAAE4H,QAAQkJ,SAAS7Q,EAAE2D,UAAUyW,aAAa,SAAS3e,GAAG,IAAIuE,EAAEoC,MAAK,IAAKpC,EAAEyT,eAAezT,EAAE2S,QAAQyD,GAAG3a,GAAGqb,QAAQ,CAACqD,QAAQ,EAAE3I,OAAOxR,EAAE2H,QAAQ6J,OAAO,GAAGxR,EAAE2H,QAAQkJ,MAAM7Q,EAAE2H,QAAQ8H,SAASzP,EAAEqX,gBAAgB5b,GAAGuE,EAAE2S,QAAQyD,GAAG3a,GAAG2b,IAAI,CAAC+C,QAAQ,EAAE3I,OAAOxR,EAAE2H,QAAQ6J,OAAO,MAAMxR,EAAE2D,UAAU0W,aAAara,EAAE2D,UAAU2W,YAAY,SAAS7e,GAAG,IAAIuE,EAAEoC,KAAK,OAAO3G,IAAIuE,EAAEkU,aAAalU,EAAE2S,QAAQ3S,EAAEkW,SAASlW,EAAE0S,YAAY6D,SAASnU,KAAKuF,QAAQ8I,OAAO+F,SAASxW,EAAEkU,aAAajQ,OAAOxI,GAAG0a,SAASnW,EAAE0S,aAAa1S,EAAE2W,WAAW3W,EAAE2D,UAAU4W,aAAa,WAAW,IAAIva,EAAEoC,KAAKpC,EAAEiU,QAAQyF,IAAI,0BAA0Bc,GAAG,yBAAyB,IAAI,SAASza,GAAGA,EAAEkN,2BAA2B,IAAI/M,EAAEzE,EAAE2G,MAAMhH,WAAW,WAAW4E,EAAE2H,QAAQwI,eAAenQ,EAAE0T,SAASxT,EAAEiZ,GAAG,UAAUnZ,EAAE4U,aAAa,MAAM5U,EAAE2D,UAAU8W,WAAWza,EAAE2D,UAAU+W,kBAAkB,WAAW,OAAOtY,KAAK2P,cAAc/R,EAAE2D,UAAUwU,YAAY,WAAW,IAAI1c,EAAE2G,KAAKpC,EAAE,EAAED,EAAE,EAAEG,EAAE,EAAE,IAAG,IAAKzE,EAAEkM,QAAQmI,SAAS,GAAGrU,EAAE+W,YAAY/W,EAAEkM,QAAQgJ,eAAezQ,OAAO,KAAKF,EAAEvE,EAAE+W,cAActS,EAAEF,EAAED,EAAEtE,EAAEkM,QAAQiJ,eAAe7Q,GAAGtE,EAAEkM,QAAQiJ,gBAAgBnV,EAAEkM,QAAQgJ,aAAalV,EAAEkM,QAAQiJ,eAAenV,EAAEkM,QAAQgJ,kBAAkB,IAAG,IAAKlV,EAAEkM,QAAQsH,WAAW/O,EAAEzE,EAAE+W,gBAAgB,GAAG/W,EAAEkM,QAAQiH,SAAS,KAAK5O,EAAEvE,EAAE+W,cAActS,EAAEF,EAAED,EAAEtE,EAAEkM,QAAQiJ,eAAe7Q,GAAGtE,EAAEkM,QAAQiJ,gBAAgBnV,EAAEkM,QAAQgJ,aAAalV,EAAEkM,QAAQiJ,eAAenV,EAAEkM,QAAQgJ,kBAAkBzQ,EAAE,EAAEoI,KAAK6O,MAAM1b,EAAE+W,WAAW/W,EAAEkM,QAAQgJ,cAAclV,EAAEkM,QAAQiJ,gBAAgB,OAAO1Q,EAAE,GAAGF,EAAE2D,UAAUgX,QAAQ,SAASlf,GAAG,IAAIuE,EAAED,EAAEG,EAAE6C,EAAE9C,EAAEmC,KAAKU,EAAE,EAAE,OAAO7C,EAAE4S,YAAY,EAAE9S,EAAEE,EAAE0S,QAAQyF,QAAQvB,aAAY,IAAI,IAAK5W,EAAE0H,QAAQmI,UAAU7P,EAAEuS,WAAWvS,EAAE0H,QAAQgJ,eAAe1Q,EAAE4S,YAAY5S,EAAEwS,WAAWxS,EAAE0H,QAAQgJ,cAAc,EAAE5N,GAAG,GAAE,IAAK9C,EAAE0H,QAAQ0J,WAAU,IAAKpR,EAAE0H,QAAQsH,aAAa,IAAIhP,EAAE0H,QAAQgJ,aAAa5N,GAAG,IAAI,IAAI9C,EAAE0H,QAAQgJ,eAAe5N,GAAG,IAAID,EAAE/C,EAAEE,EAAE0H,QAAQgJ,aAAa5N,GAAG9C,EAAEuS,WAAWvS,EAAE0H,QAAQiJ,gBAAgB,GAAGnV,EAAEwE,EAAE0H,QAAQiJ,eAAe3Q,EAAEuS,YAAYvS,EAAEuS,WAAWvS,EAAE0H,QAAQgJ,eAAuG7N,EAAxFrH,EAAEwE,EAAEuS,YAAYvS,EAAE4S,aAAa5S,EAAE0H,QAAQgJ,cAAclV,EAAEwE,EAAEuS,aAAavS,EAAEwS,YAAY,GAAKxS,EAAE0H,QAAQgJ,cAAclV,EAAEwE,EAAEuS,aAAazS,GAAG,IAAIE,EAAE4S,YAAY5S,EAAEuS,WAAWvS,EAAE0H,QAAQiJ,eAAe3Q,EAAEwS,YAAY,EAAIxS,EAAEuS,WAAWvS,EAAE0H,QAAQiJ,eAAe7Q,GAAG,KAAKtE,EAAEwE,EAAE0H,QAAQgJ,aAAa1Q,EAAEuS,aAAavS,EAAE4S,aAAapX,EAAEwE,EAAE0H,QAAQgJ,aAAa1Q,EAAEuS,YAAYvS,EAAEwS,WAAW3P,GAAGrH,EAAEwE,EAAE0H,QAAQgJ,aAAa1Q,EAAEuS,YAAYzS,GAAGE,EAAEuS,YAAYvS,EAAE0H,QAAQgJ,eAA+B7N,EAAhB7C,EAAE4S,YAAY,IAAO,IAAK5S,EAAE0H,QAAQsH,YAAYhP,EAAEuS,YAAYvS,EAAE0H,QAAQgJ,aAAa1Q,EAAE4S,YAAY5S,EAAEwS,WAAWnK,KAAKsS,MAAM3a,EAAE0H,QAAQgJ,cAAc,EAAE1Q,EAAEwS,WAAWxS,EAAEuS,WAAW,GAAE,IAAKvS,EAAE0H,QAAQsH,aAAY,IAAKhP,EAAE0H,QAAQmI,SAAS7P,EAAE4S,aAAa5S,EAAEwS,WAAWnK,KAAKsS,MAAM3a,EAAE0H,QAAQgJ,aAAa,GAAG1Q,EAAEwS,YAAW,IAAKxS,EAAE0H,QAAQsH,aAAahP,EAAE4S,YAAY,EAAE5S,EAAE4S,aAAa5S,EAAEwS,WAAWnK,KAAKsS,MAAM3a,EAAE0H,QAAQgJ,aAAa,IAAI3Q,GAAE,IAAKC,EAAE0H,QAAQ0J,SAAS5V,EAAEwE,EAAEwS,YAAY,EAAExS,EAAE4S,YAAYpX,EAAEsE,GAAG,EAAE+C,GAAE,IAAK7C,EAAE0H,QAAQyJ,gBAAgBlR,EAAED,EAAEuS,YAAYvS,EAAE0H,QAAQgJ,eAAc,IAAK1Q,EAAE0H,QAAQmI,SAAS7P,EAAEyS,YAAY6D,SAAS,gBAAgBH,GAAG3a,GAAGwE,EAAEyS,YAAY6D,SAAS,gBAAgBH,GAAG3a,EAAEwE,EAAE0H,QAAQgJ,cAAc3Q,GAAE,IAAKC,EAAE0H,QAAQ6I,IAAItQ,EAAE,IAAI,GAAGD,EAAEyS,YAAYzU,QAAQiC,EAAE,GAAG2a,WAAW3a,EAAEjC,SAAS,EAAEiC,EAAE,IAAI,EAAEA,EAAE,GAAG2a,WAAW,GAAE,IAAK5a,EAAE0H,QAAQsH,aAAa/O,EAAED,EAAEuS,YAAYvS,EAAE0H,QAAQgJ,eAAc,IAAK1Q,EAAE0H,QAAQmI,SAAS7P,EAAEyS,YAAY6D,SAAS,gBAAgBH,GAAG3a,GAAGwE,EAAEyS,YAAY6D,SAAS,gBAAgBH,GAAG3a,EAAEwE,EAAE0H,QAAQgJ,aAAa,GAAG3Q,GAAE,IAAKC,EAAE0H,QAAQ6I,IAAItQ,EAAE,IAAI,GAAGD,EAAEyS,YAAYzU,QAAQiC,EAAE,GAAG2a,WAAW3a,EAAEjC,SAAS,EAAEiC,EAAE,IAAI,EAAEA,EAAE,GAAG2a,WAAW,EAAE7a,IAAIC,EAAE+S,MAAM/U,QAAQiC,EAAE4a,cAAc,IAAI9a,GAAGA,EAAE2D,UAAUoX,UAAU/a,EAAE2D,UAAUqX,eAAe,SAASvf,GAAG,OAAO2G,KAAKuF,QAAQlM,IAAIuE,EAAE2D,UAAU6V,oBAAoB,WAAqC,IAA1B,IAAMxZ,EAAEoC,KAAKrC,EAAE,EAAEG,EAAE,EAAE6C,EAAE,GAA+BtH,GAAxB,IAAKuE,EAAE2H,QAAQmI,SAAW9P,EAAEwS,YAAYzS,GAAG,EAAEC,EAAE2H,QAAQiJ,eAAe1Q,GAAG,EAAEF,EAAE2H,QAAQiJ,eAAiB,EAAE5Q,EAAEwS,YAAYzS,EAAEtE,GAAGsH,EAAE4D,KAAK5G,GAAGA,EAAEG,EAAEF,EAAE2H,QAAQiJ,eAAe1Q,GAAGF,EAAE2H,QAAQiJ,gBAAgB5Q,EAAE2H,QAAQgJ,aAAa3Q,EAAE2H,QAAQiJ,eAAe5Q,EAAE2H,QAAQgJ,aAAa,OAAO5N,GAAG/C,EAAE2D,UAAUsX,SAAS,WAAW,OAAO7Y,MAAMpC,EAAE2D,UAAUuX,cAAc,WAAW,IAAIlb,EAAIE,EAAEkC,KAAYrC,GAAE,IAAKG,EAAEyH,QAAQsH,WAAW/O,EAAEuS,WAAWnK,KAAKsS,MAAM1a,EAAEyH,QAAQgJ,aAAa,GAAG,EAArF,OAAuF,IAAKzQ,EAAEyH,QAAQoJ,cAAc7Q,EAAEwS,YAAYkD,KAAK,gBAAgBc,KAAK,SAAS3T,EAAE9C,GAAG,GAAGA,EAAE4a,WAAW9a,EAAEtE,EAAEwE,GAAG6a,aAAa,GAAG,EAAE5a,EAAE4S,UAAU,OAAO9S,EAAEC,GAAE,IAAKqI,KAAK6S,IAAI1f,EAAEuE,GAAG6V,KAAK,oBAAoB3V,EAAE6R,eAAe,GAAG7R,EAAEyH,QAAQiJ,gBAAgB5Q,EAAE2D,UAAUyX,KAAKpb,EAAE2D,UAAU0X,UAAU,SAAS5f,EAAEuE,GAAGoC,KAAK4S,YAAY,CAACR,KAAK,CAAC6E,QAAQ,QAAQC,MAAM/Y,SAAS9E,KAAKuE,IAAIA,EAAE2D,UAAU+R,KAAK,SAAS1V,GAAG,IAAID,EAAEqC,KAAK3G,EAAEsE,EAAEkU,SAASqH,SAAS,uBAAuB7f,EAAEsE,EAAEkU,SAAS6D,SAAS,qBAAqB/X,EAAE2Y,YAAY3Y,EAAEsY,WAAWtY,EAAEwb,WAAWxb,EAAEyb,YAAYzb,EAAE0b,aAAa1b,EAAE2b,mBAAmB3b,EAAE4b,eAAe5b,EAAEyY,aAAazY,EAAE+Y,iBAAgB,GAAI/Y,EAAEwa,gBAAgBva,GAAGD,EAAEkU,QAAQ5F,QAAQ,OAAO,CAACtO,KAAI,IAAKA,EAAE4H,QAAQ4G,eAAexO,EAAE6b,UAAU7b,EAAE4H,QAAQoH,WAAWhP,EAAE8T,QAAO,EAAG9T,EAAE6U,aAAa5U,EAAE2D,UAAUiY,QAAQ,WAAW,IAAI5b,EAAEoC,KAAKrC,EAAEuI,KAAK6O,KAAKnX,EAAEwS,WAAWxS,EAAE2H,QAAQgJ,cAAczQ,EAAEF,EAAEwZ,sBAAsBvV,OAAO,SAASxI,GAAG,OAAU,GAAHA,GAAMA,EAAEuE,EAAEwS,aAAaxS,EAAE2S,QAAQvJ,IAAIpJ,EAAE0S,YAAYkD,KAAK,kBAAkBC,KAAK,CAACC,cAAc,OAAOC,SAAS,OAAOH,KAAK,4BAA4BC,KAAK,CAACE,SAAS,OAAO,OAAO/V,EAAEiS,QAAQjS,EAAE2S,QAAQ6E,IAAIxX,EAAE0S,YAAYkD,KAAK,kBAAkBc,KAAK,SAAS3W,GAAG,IAAIgD,EAAE7C,EAAES,QAAQZ,GAAGtE,EAAE2G,MAAMyT,KAAK,CAACgG,KAAK,WAAW1U,GAAG,cAAcnH,EAAEuV,YAAYxV,EAAEgW,UAAU,KAAK,IAAIhT,GAAGtH,EAAE2G,MAAMyT,KAAK,CAACiG,mBAAmB,sBAAsB9b,EAAEuV,YAAYxS,MAAM/C,EAAEiS,MAAM4D,KAAK,OAAO,WAAWD,KAAK,MAAMc,KAAK,SAAS3T,GAAG,IAAI9C,EAAEC,EAAE6C,GAAGtH,EAAE2G,MAAMyT,KAAK,CAACgG,KAAK,iBAAiBpgB,EAAE2G,MAAMwT,KAAK,UAAUwC,QAAQvC,KAAK,CAACgG,KAAK,MAAM1U,GAAG,sBAAsBnH,EAAEuV,YAAYxS,EAAEgZ,gBAAgB,cAAc/b,EAAEuV,YAAYtV,EAAE+b,aAAajZ,EAAE,EAAE,OAAOhD,EAAEkc,gBAAgB,KAAKlG,SAAS,SAASK,GAAGpW,EAAE+R,cAAc6D,KAAK,UAAUC,KAAK,CAACoG,gBAAgB,OAAOlG,SAAS,MAAMmG,OAAO,IAAI,IAAInZ,EAAE/C,EAAE+R,aAAa9R,EAAE8C,EAAE/C,EAAE2H,QAAQgJ,aAAa5N,EAAE9C,EAAE8C,IAAI/C,EAAE2S,QAAQyD,GAAGrT,GAAG8S,KAAK,WAAW,GAAG7V,EAAE2V,eAAe3V,EAAE2D,UAAUwY,gBAAgB,WAAW,IAAI1gB,EAAE2G,MAAK,IAAK3G,EAAEkM,QAAQgH,QAAQlT,EAAE+W,WAAW/W,EAAEkM,QAAQgJ,eAAelV,EAAE6W,WAAWoH,IAAI,eAAec,GAAG,cAAc,CAACnB,QAAQ,YAAY5d,EAAEuZ,aAAavZ,EAAE4W,WAAWqH,IAAI,eAAec,GAAG,cAAc,CAACnB,QAAQ,QAAQ5d,EAAEuZ,cAAa,IAAKvZ,EAAEkM,QAAQ4G,gBAAgB9S,EAAE6W,WAAWkI,GAAG,gBAAgB/e,EAAE6Z,YAAY7Z,EAAE4W,WAAWmI,GAAG,gBAAgB/e,EAAE6Z,eAAetV,EAAE2D,UAAUyY,cAAc,WAAW,IAAIpc,EAAEoC,MAAK,IAAKpC,EAAE2H,QAAQ2H,OAAO7T,EAAE,KAAKuE,EAAEiS,OAAOuI,GAAG,cAAc,CAACnB,QAAQ,SAASrZ,EAAEgV,cAAa,IAAKhV,EAAE2H,QAAQ4G,eAAevO,EAAEiS,MAAMuI,GAAG,gBAAgBxa,EAAEsV,cAAa,IAAKtV,EAAE2H,QAAQ2H,OAAM,IAAKtP,EAAE2H,QAAQyI,kBAAkB3U,EAAE,KAAKuE,EAAEiS,OAAOuI,GAAG,mBAAmB/e,EAAEoZ,MAAM7U,EAAE2Z,UAAU3Z,GAAE,IAAKwa,GAAG,mBAAmB/e,EAAEoZ,MAAM7U,EAAE2Z,UAAU3Z,GAAE,KAAMA,EAAE2D,UAAU0Y,gBAAgB,WAAW,IAAIrc,EAAEoC,KAAKpC,EAAE2H,QAAQuI,eAAelQ,EAAEgT,MAAMwH,GAAG,mBAAmB/e,EAAEoZ,MAAM7U,EAAE2Z,UAAU3Z,GAAE,IAAKA,EAAEgT,MAAMwH,GAAG,mBAAmB/e,EAAEoZ,MAAM7U,EAAE2Z,UAAU3Z,GAAE,MAAOA,EAAE2D,UAAU+X,iBAAiB,WAAW,IAAI1b,EAAEoC,KAAKpC,EAAEmc,kBAAkBnc,EAAEoc,gBAAgBpc,EAAEqc,kBAAkBrc,EAAEgT,MAAMwH,GAAG,mCAAmC,CAAC8B,OAAO,SAAStc,EAAEoV,cAAcpV,EAAEgT,MAAMwH,GAAG,kCAAkC,CAAC8B,OAAO,QAAQtc,EAAEoV,cAAcpV,EAAEgT,MAAMwH,GAAG,+BAA+B,CAAC8B,OAAO,OAAOtc,EAAEoV,cAAcpV,EAAEgT,MAAMwH,GAAG,qCAAqC,CAAC8B,OAAO,OAAOtc,EAAEoV,cAAcpV,EAAEgT,MAAMwH,GAAG,cAAcxa,EAAEiV,cAAcxZ,EAAEa,UAAUke,GAAGxa,EAAEqU,iBAAiB5Y,EAAEoZ,MAAM7U,EAAE4Z,WAAW5Z,KAAI,IAAKA,EAAE2H,QAAQ4G,eAAevO,EAAEgT,MAAMwH,GAAG,gBAAgBxa,EAAEsV,aAAY,IAAKtV,EAAE2H,QAAQiI,eAAenU,EAAEuE,EAAE0S,aAAa6D,WAAWiE,GAAG,cAAcxa,EAAEkV,eAAezZ,EAAEV,QAAQyf,GAAG,iCAAiCxa,EAAEuV,YAAY9Z,EAAEoZ,MAAM7U,EAAE8Z,kBAAkB9Z,IAAIvE,EAAEV,QAAQyf,GAAG,sBAAsBxa,EAAEuV,YAAY9Z,EAAEoZ,MAAM7U,EAAE+Z,OAAO/Z,IAAIvE,EAAE,oBAAoBuE,EAAE0S,aAAa8H,GAAG,YAAYxa,EAAEkL,gBAAgBzP,EAAEV,QAAQyf,GAAG,oBAAoBxa,EAAEuV,YAAYvV,EAAEmV,aAAa1Z,EAAEuE,EAAEmV,cAAcnV,EAAE2D,UAAU4Y,OAAO,WAAW,IAAI9gB,EAAE2G,MAAK,IAAK3G,EAAEkM,QAAQgH,QAAQlT,EAAE+W,WAAW/W,EAAEkM,QAAQgJ,eAAelV,EAAE6W,WAAWkK,OAAO/gB,EAAE4W,WAAWmK,SAAQ,IAAK/gB,EAAEkM,QAAQ2H,MAAM7T,EAAE+W,WAAW/W,EAAEkM,QAAQgJ,cAAclV,EAAEwW,MAAMuK,QAAQxc,EAAE2D,UAAU2R,WAAW,SAAS7Z,GAAG,IAAIuE,EAAEoC,KAAK3G,EAAEgO,OAAOgT,QAAQnc,MAAM,2BAA2B,KAAK7E,EAAEihB,UAAS,IAAK1c,EAAE2H,QAAQ4G,cAAcvO,EAAEgV,YAAY,CAACR,KAAK,CAAC6E,SAAQ,IAAKrZ,EAAE2H,QAAQ6I,IAAI,OAAO,cAAc,KAAK/U,EAAEihB,UAAS,IAAK1c,EAAE2H,QAAQ4G,eAAevO,EAAEgV,YAAY,CAACR,KAAK,CAAC6E,SAAQ,IAAKrZ,EAAE2H,QAAQ6I,IAAI,WAAW,YAAYxQ,EAAE2D,UAAUqM,SAAS,WAAW,SAAShQ,EAAEA,GAAGvE,EAAE,iBAAiBuE,GAAG0W,KAAK,WAAW,IAAI1W,EAAEvE,EAAE2G,MAAMrC,EAAEtE,EAAE2G,MAAMyT,KAAK,aAAa3V,EAAEzE,EAAE2G,MAAMyT,KAAK,eAAe9S,EAAEtH,EAAE2G,MAAMyT,KAAK,eAAe5V,EAAEgU,QAAQ4B,KAAK,cAAc/S,EAAExG,SAASuC,cAAc,OAAOiE,EAAErE,OAAO,WAAWuB,EAAE8W,QAAQ,CAACqD,QAAQ,GAAG,IAAI,WAAWja,IAAIF,EAAE6V,KAAK,SAAS3V,GAAG6C,GAAG/C,EAAE6V,KAAK,QAAQ9S,IAAI/C,EAAE6V,KAAK,MAAM9V,GAAG+W,QAAQ,CAACqD,QAAQ,GAAG,IAAI,WAAWna,EAAEgY,WAAW,oCAAoCD,YAAY,mBAAmB9X,EAAEgU,QAAQ5F,QAAQ,aAAa,CAACpO,EAAED,EAAED,OAAO+C,EAAEzF,QAAQ,WAAW2C,EAAEgY,WAAW,aAAaD,YAAY,iBAAiBD,SAAS,wBAAwB7X,EAAEgU,QAAQ5F,QAAQ,gBAAgB,CAACpO,EAAED,EAAED,KAAK+C,EAAEsE,IAAIrH,IAAI,IAAIA,EAAEG,EAAE6C,EAAE9C,EAAEmC,KAAK,IAAG,IAAKnC,EAAE0H,QAAQsH,WAAmClM,GAAxB,IAAK9C,EAAE0H,QAAQmI,UAAY5P,EAAED,EAAE8R,cAAc9R,EAAE0H,QAAQgJ,aAAa,EAAE,IAAI1Q,EAAE0H,QAAQgJ,aAAa,GAAGzQ,EAAEoI,KAAKC,IAAI,EAAEtI,EAAE8R,cAAc9R,EAAE0H,QAAQgJ,aAAa,EAAE,IAAM1Q,EAAE0H,QAAQgJ,aAAa,EAAE,EAAE,EAAE1Q,EAAE8R,eAAe7R,EAAED,EAAE0H,QAAQmI,SAAS7P,EAAE0H,QAAQgJ,aAAa1Q,EAAE8R,aAAa9R,EAAE8R,aAAahP,EAAEuF,KAAK6O,KAAKjX,EAAED,EAAE0H,QAAQgJ,eAAc,IAAK1Q,EAAE0H,QAAQgI,OAAS,EAAFzP,GAAKA,IAAI6C,GAAG9C,EAAEuS,YAAYzP,MAAMhD,EAAEE,EAAEgU,QAAQ2B,KAAK,gBAAgBhS,MAAM1D,EAAE6C,GAAG,gBAAgB9C,EAAE0H,QAAQqI,SAAS,IAAI,IAAIlN,EAAE5C,EAAE,EAAEC,EAAE4C,EAAEvC,EAAEP,EAAEgU,QAAQ2B,KAAK,gBAAgBlW,EAAE,EAAEA,EAAEO,EAAE0H,QAAQiJ,eAAelR,IAAIoD,EAAE,IAAIA,EAAE7C,EAAEuS,WAAW,GAAGzS,GAAGA,EAAEA,EAAEqJ,IAAI5I,EAAE4V,GAAGtT,KAAKsG,IAAI5I,EAAE4V,GAAGjW,IAAI2C,IAAI3C,IAAIH,EAAED,GAAGE,EAAEuS,YAAYvS,EAAE0H,QAAQgJ,aAAa3Q,EAAEC,EAAEgU,QAAQ2B,KAAK,iBAAiB3V,EAAE8R,cAAc9R,EAAEuS,WAAWvS,EAAE0H,QAAQgJ,aAAa3Q,EAAEC,EAAEgU,QAAQ2B,KAAK,iBAAiBhS,MAAM,EAAE3D,EAAE0H,QAAQgJ,eAAe,IAAI1Q,EAAE8R,cAAc/R,EAAEC,EAAEgU,QAAQ2B,KAAK,iBAAiBhS,OAAO,EAAE3D,EAAE0H,QAAQgJ,gBAAgB3Q,EAAE2D,UAAU8X,WAAW,WAAW,IAAIhgB,EAAE2G,KAAK3G,EAAE0Z,cAAc1Z,EAAEiX,YAAY0E,IAAI,CAAC+C,QAAQ,IAAI1e,EAAEwY,QAAQ8D,YAAY,iBAAiBtc,EAAE8gB,SAAS,gBAAgB9gB,EAAEkM,QAAQqI,UAAUvU,EAAEkhB,uBAAuB3c,EAAE2D,UAAUiZ,KAAK5c,EAAE2D,UAAUkZ,UAAU,WAAWza,KAAK4S,YAAY,CAACR,KAAK,CAAC6E,QAAQ,WAAWrZ,EAAE2D,UAAUmW,kBAAkB,WAAiB1X,KAAO0W,kBAAP1W,KAA2B+S,eAAenV,EAAE2D,UAAUmZ,MAAM9c,EAAE2D,UAAUoZ,WAAW,WAAiB3a,KAAO0S,gBAAP1S,KAAyByR,QAAO,GAAI7T,EAAE2D,UAAUqZ,KAAKhd,EAAE2D,UAAUsZ,UAAU,WAAW,IAAIxhB,EAAE2G,KAAK3G,EAAEmZ,WAAWnZ,EAAEkM,QAAQoH,UAAS,EAAGtT,EAAEoY,QAAO,EAAGpY,EAAEiY,UAAS,EAAGjY,EAAEkY,aAAY,GAAI3T,EAAE2D,UAAUuZ,UAAU,SAASld,GAAG,IAAID,EAAEqC,KAAKrC,EAAEoT,YAAYpT,EAAEkU,QAAQ5F,QAAQ,cAAc,CAACtO,EAAEC,IAAID,EAAE2R,WAAU,EAAG3R,EAAEyS,WAAWzS,EAAE4H,QAAQgJ,cAAc5Q,EAAEoV,cAAcpV,EAAE+S,UAAU,KAAK/S,EAAE4H,QAAQoH,UAAUhP,EAAE6U,YAAW,IAAK7U,EAAE4H,QAAQ4G,gBAAgBxO,EAAE6b,UAAU7b,EAAE4H,QAAQkI,eAAepU,EAAEsE,EAAE4S,QAAQiG,IAAI7Y,EAAEgS,eAAe8D,KAAK,WAAW,GAAGsH,WAAWnd,EAAE2D,UAAUyZ,KAAKpd,EAAE2D,UAAU0Z,UAAU,WAAWjb,KAAK4S,YAAY,CAACR,KAAK,CAAC6E,QAAQ,eAAerZ,EAAE2D,UAAUuH,eAAe,SAASzP,GAAGA,EAAEyP,kBAAkBlL,EAAE2D,UAAUgZ,oBAAoB,SAAS3c,GAAGA,EAAEA,GAAG,EAAE,IAAID,EAAEG,EAAE6C,EAAE9C,EAAE6C,EAAE3C,EAAEiC,KAAK5B,EAAE/E,EAAE,iBAAiB0E,EAAE8T,SAASzT,EAAEhD,QAAQuC,EAAES,EAAE4X,QAAQlY,EAAEH,EAAE8V,KAAK,aAAa9S,EAAEhD,EAAE8V,KAAK,eAAe5V,EAAEF,EAAE8V,KAAK,eAAe1V,EAAE8T,QAAQ4B,KAAK,eAAe/S,EAAExG,SAASuC,cAAc,QAAQJ,OAAO,WAAWsE,IAAIhD,EAAE8V,KAAK,SAAS9S,GAAG9C,GAAGF,EAAE8V,KAAK,QAAQ5V,IAAIF,EAAE8V,KAAK,MAAM3V,GAAG8X,WAAW,oCAAoCD,YAAY,kBAAiB,IAAK5X,EAAEwH,QAAQ6G,gBAAgBrO,EAAEgV,cAAchV,EAAE8T,QAAQ5F,QAAQ,aAAa,CAAClO,EAAEJ,EAAEG,IAAIC,EAAEwc,uBAAuB7Z,EAAEzF,QAAQ,WAAW2C,EAAE,EAAE5E,WAAW,WAAW+E,EAAEwc,oBAAoB3c,EAAE,IAAI,MAAMD,EAAEiY,WAAW,aAAaD,YAAY,iBAAiBD,SAAS,wBAAwB3X,EAAE8T,QAAQ5F,QAAQ,gBAAgB,CAAClO,EAAEJ,EAAEG,IAAIC,EAAEwc,wBAAwB7Z,EAAEsE,IAAIlH,GAAGC,EAAE8T,QAAQ5F,QAAQ,kBAAkB,CAAClO,KAAKH,EAAE2D,UAAUsV,QAAQ,SAASjZ,GAAG,IAAID,EAAIgD,EAAEX,KAAKlC,EAAE6C,EAAEyP,WAAWzP,EAAE4E,QAAQgJ,cAAc5N,EAAE4E,QAAQmI,UAAU/M,EAAEgP,aAAa7R,IAAI6C,EAAEgP,aAAa7R,GAAG6C,EAAEyP,YAAYzP,EAAE4E,QAAQgJ,eAAe5N,EAAEgP,aAAa,GAAGhS,EAAEgD,EAAEgP,aAAahP,EAAEkX,SAAQ,GAAIxe,EAAEwM,OAAOlF,EAAEA,EAAE0O,SAAS,CAACM,aAAahS,IAAIgD,EAAE2S,OAAO1V,GAAG+C,EAAEiS,YAAY,CAACR,KAAK,CAAC6E,QAAQ,QAAQC,MAAMvZ,KAAI,IAAKC,EAAE2D,UAAU8R,oBAAoB,WAAW,IAAIzV,EAAED,EAAEG,EAAE6C,EAAEX,KAAKnC,EAAE8C,EAAE4E,QAAQ2I,YAAY,KAAK,GAAG,UAAU7U,EAAEyL,KAAKjH,IAAIA,EAAEzC,OAAO,CAA2C,IAAIwC,KAA9C+C,EAAEsN,UAAUtN,EAAE4E,QAAQ0I,WAAW,SAAkBpQ,EAAE,GAAGC,EAAE6C,EAAEwQ,YAAY/V,OAAO,EAAEyC,EAAEgH,eAAejH,GAAG,CAAC,IAAID,EAAEE,EAAED,GAAGsd,WAAc,GAAHpd,GAAM6C,EAAEwQ,YAAYrT,IAAI6C,EAAEwQ,YAAYrT,KAAKH,GAAGgD,EAAEwQ,YAAYgK,OAAOrd,EAAE,GAAGA,IAAI6C,EAAEwQ,YAAY5M,KAAK5G,GAAGgD,EAAEyQ,mBAAmBzT,GAAGE,EAAED,GAAG6H,SAAS9E,EAAEwQ,YAAYiK,KAAK,SAAS/hB,EAAEuE,GAAG,OAAO+C,EAAE4E,QAAQsI,YAAYxU,EAAEuE,EAAEA,EAAEvE,MAAMuE,EAAE2D,UAAUgT,OAAO,WAAW,IAAI3W,EAAEoC,KAAKpC,EAAE2S,QAAQ3S,EAAE0S,YAAY6D,SAASvW,EAAE2H,QAAQ8I,OAAOqH,SAAS,eAAe9X,EAAEwS,WAAWxS,EAAE2S,QAAQnV,OAAOwC,EAAE+R,cAAc/R,EAAEwS,YAAY,IAAIxS,EAAE+R,eAAe/R,EAAE+R,aAAa/R,EAAE+R,aAAa/R,EAAE2H,QAAQiJ,gBAAgB5Q,EAAEwS,YAAYxS,EAAE2H,QAAQgJ,eAAe3Q,EAAE+R,aAAa,GAAG/R,EAAEyV,sBAAsBzV,EAAEub,WAAWvb,EAAEuY,gBAAgBvY,EAAE6X,cAAc7X,EAAE2b,eAAe3b,EAAEmc,kBAAkBnc,EAAEkY,YAAYlY,EAAEwY,aAAaxY,EAAEoc,gBAAgBpc,EAAE6Z,qBAAqB7Z,EAAEqc,kBAAkBrc,EAAE8Y,iBAAgB,GAAG,IAAI,IAAK9Y,EAAE2H,QAAQiI,eAAenU,EAAEuE,EAAE0S,aAAa6D,WAAWiE,GAAG,cAAcxa,EAAEkV,eAAelV,EAAEyY,gBAAgB,iBAAiBzY,EAAE+R,aAAa/R,EAAE+R,aAAa,GAAG/R,EAAEmV,cAAcnV,EAAEua,eAAeva,EAAE6T,QAAQ7T,EAAE2H,QAAQoH,SAAS/O,EAAE4U,WAAW5U,EAAEiU,QAAQ5F,QAAQ,SAAS,CAACrO,KAAKA,EAAE2D,UAAUoW,OAAO,WAAW,IAAI/Z,EAAEoC,KAAK3G,EAAEV,QAAQkD,UAAU+B,EAAEsU,cAAcnZ,aAAa6E,EAAEyd,aAAazd,EAAEyd,YAAY1iB,OAAOK,WAAW,WAAW4E,EAAEsU,YAAY7Y,EAAEV,QAAQkD,QAAQ+B,EAAE8Y,kBAAkB9Y,EAAEmT,WAAWnT,EAAEmV,eAAe,MAAMnV,EAAE2D,UAAU+Z,YAAY1d,EAAE2D,UAAUga,YAAY,SAASliB,EAAEuE,EAAED,GAAG,IAAIG,EAAEkC,KAAK,GAAG3G,EAAE,kBAAkBA,GAAE,KAAMuE,EAAEvE,GAAG,EAAEyE,EAAEsS,WAAW,GAAE,IAAKxS,IAAIvE,EAAEA,EAAEyE,EAAEsS,WAAW,GAAG/W,EAAE,GAAGA,EAAEyE,EAAEsS,WAAW,EAAE,OAAM,EAAGtS,EAAEgW,UAAS,IAAKnW,EAAEG,EAAEwS,YAAY6D,WAAWpN,SAASjJ,EAAEwS,YAAY6D,SAASnU,KAAKuF,QAAQ8I,OAAO2F,GAAG3a,GAAG0N,SAASjJ,EAAEyS,QAAQzS,EAAEwS,YAAY6D,SAASnU,KAAKuF,QAAQ8I,OAAOvQ,EAAEwS,YAAY6D,SAASnU,KAAKuF,QAAQ8I,OAAO+F,SAAStW,EAAEwS,YAAY+D,OAAOvW,EAAEyS,SAASzS,EAAEgU,aAAahU,EAAEyS,QAAQzS,EAAEyW,UAAU3W,EAAE2D,UAAUia,OAAO,SAASniB,GAAG,IAAIuE,EAAED,EAAEG,EAAEkC,KAAKW,EAAE,IAAG,IAAK7C,EAAEyH,QAAQ6I,MAAM/U,GAAGA,GAAGuE,EAAE,QAAQE,EAAE4T,aAAaxL,KAAK6O,KAAK1b,GAAG,KAAK,MAAMsE,EAAE,OAAOG,EAAE4T,aAAaxL,KAAK6O,KAAK1b,GAAG,KAAK,MAAMsH,EAAE7C,EAAE4T,cAAcrY,GAAE,IAAKyE,EAAEgT,sBAAwCnQ,EAAE,MAAQ7C,EAAEuT,eAAgB1Q,EAAE7C,EAAEmT,UAAU,aAAarT,EAAE,KAAKD,EAAE,IAA2BgD,EAAE7C,EAAEmT,UAAU,eAAerT,EAAE,KAAKD,EAAE,UAAvJG,EAAEwS,YAAY0E,IAAIrU,IAAsK/C,EAAE2D,UAAUka,cAAc,WAAW,IAAIpiB,EAAE2G,MAAK,IAAK3G,EAAEkM,QAAQ0J,UAAS,IAAK5V,EAAEkM,QAAQsH,YAAYxT,EAAEuX,MAAMoE,IAAI,CAAC0G,QAAQ,OAAOriB,EAAEkM,QAAQuH,iBAAiBzT,EAAEuX,MAAM9U,OAAOzC,EAAEkX,QAAQyF,QAAQvB,aAAY,GAAIpb,EAAEkM,QAAQgJ,eAAc,IAAKlV,EAAEkM,QAAQsH,YAAYxT,EAAEuX,MAAMoE,IAAI,CAAC0G,QAAQriB,EAAEkM,QAAQuH,cAAc,UAAUzT,EAAEyW,UAAUzW,EAAEuX,MAAM/U,QAAQxC,EAAE0W,WAAW1W,EAAEuX,MAAM9U,UAAS,IAAKzC,EAAEkM,QAAQ0J,WAAU,IAAK5V,EAAEkM,QAAQyJ,eAAe3V,EAAEgX,WAAWnK,KAAK6O,KAAK1b,EAAEyW,UAAUzW,EAAEkM,QAAQgJ,cAAclV,EAAEiX,YAAYzU,MAAMqK,KAAK6O,KAAK1b,EAAEgX,WAAWhX,EAAEiX,YAAY6D,SAAS,gBAAgB/Y,WAAU,IAAK/B,EAAEkM,QAAQyJ,cAAc3V,EAAEiX,YAAYzU,MAAM,IAAIxC,EAAE+W,aAAa/W,EAAEgX,WAAWnK,KAAK6O,KAAK1b,EAAEyW,WAAWzW,EAAEiX,YAAYxU,OAAOoK,KAAK6O,KAAK1b,EAAEkX,QAAQyF,QAAQvB,aAAY,GAAIpb,EAAEiX,YAAY6D,SAAS,gBAAgB/Y,UAAU,IAAIwC,EAAEvE,EAAEkX,QAAQyF,QAAQ0C,YAAW,GAAIrf,EAAEkX,QAAQyF,QAAQna,SAAQ,IAAKxC,EAAEkM,QAAQyJ,eAAe3V,EAAEiX,YAAY6D,SAAS,gBAAgBtY,MAAMxC,EAAEgX,WAAWzS,IAAIA,EAAE2D,UAAUoa,QAAQ,WAAW,IAAI/d,EAAED,EAAEqC,KAAKrC,EAAE4S,QAAQ+D,KAAK,SAASxW,EAAE6C,GAAG/C,EAAED,EAAE0S,WAAWvS,GAAG,GAAE,IAAKH,EAAE4H,QAAQ6I,IAAI/U,EAAEsH,GAAGqU,IAAI,CAACnY,SAAS,WAAWnB,MAAMkC,EAAEjC,IAAI,EAAEyT,OAAOzR,EAAE4H,QAAQ6J,OAAO,EAAE2I,QAAQ,IAAI1e,EAAEsH,GAAGqU,IAAI,CAACnY,SAAS,WAAWpB,KAAKmC,EAAEjC,IAAI,EAAEyT,OAAOzR,EAAE4H,QAAQ6J,OAAO,EAAE2I,QAAQ,MAAMpa,EAAE4S,QAAQyD,GAAGrW,EAAEgS,cAAcqF,IAAI,CAAC5F,OAAOzR,EAAE4H,QAAQ6J,OAAO,EAAE2I,QAAQ,KAAKna,EAAE2D,UAAUqa,UAAU,WAAW,IAAsGhe,EAAlGvE,EAAE2G,KAAQ,IAAI3G,EAAEkM,QAAQgJ,eAAc,IAAKlV,EAAEkM,QAAQ6G,iBAAgB,IAAK/S,EAAEkM,QAAQ0J,WAAcrR,EAAEvE,EAAEkX,QAAQyD,GAAG3a,EAAEsW,cAAc8E,aAAY,GAAIpb,EAAEuX,MAAMoE,IAAI,SAASpX,KAAKA,EAAE2D,UAAUsa,UAAUje,EAAE2D,UAAUua,eAAe,WAAW,IAAIle,EAAED,EAAEG,EAAE6C,EAAE9C,EAAE6C,EAAEV,KAAKjC,GAAE,EAAG,GAAG,WAAW1E,EAAEyL,KAAKwF,UAAU,KAAKxM,EAAEwM,UAAU,GAAGvM,EAAEuM,UAAU,GAAGzM,EAAE,YAAY,WAAWxE,EAAEyL,KAAKwF,UAAU,MAAqB3J,EAAE2J,UAAU,GAAGvM,EAAEuM,UAAU,GAAG,gBAA7CxM,EAAEwM,UAAU,KAA8D,UAAUjR,EAAEyL,KAAKwF,UAAU,IAAIzM,EAAE,kBAAa,IAASyM,UAAU,KAAKzM,EAAE,WAAW,WAAWA,EAAE6C,EAAE6E,QAAQzH,GAAG6C,OAAO,GAAG,aAAa9C,EAAExE,EAAEib,KAAKxW,EAAE,SAASzE,EAAEuE,GAAG8C,EAAE6E,QAAQlM,GAAGuE,SAAS,GAAG,eAAeC,EAAE,IAAIF,KAAKgD,EAAE,GAAG,UAAUtH,EAAEyL,KAAKpE,EAAE6E,QAAQ2I,YAAYxN,EAAE6E,QAAQ2I,WAAW,CAACvN,EAAEhD,QAAQ,CAAC,IAAIC,EAAE8C,EAAE6E,QAAQ2I,WAAW9S,OAAO,EAAK,GAAHwC,GAAM8C,EAAE6E,QAAQ2I,WAAWtQ,GAAGsd,aAAava,EAAEhD,GAAGud,YAAYxa,EAAE6E,QAAQ2I,WAAWiN,OAAOvd,EAAE,GAAGA,IAAI8C,EAAE6E,QAAQ2I,WAAW3J,KAAK5D,EAAEhD,IAAII,IAAI2C,EAAEoT,SAASpT,EAAE6T,WAAW3W,EAAE2D,UAAUwR,YAAY,WAAW,IAAI1Z,EAAE2G,KAAK3G,EAAEoiB,gBAAgBpiB,EAAEuiB,aAAY,IAAKviB,EAAEkM,QAAQgI,KAAKlU,EAAEmiB,OAAOniB,EAAEkf,QAAQlf,EAAEsW,eAAetW,EAAEsiB,UAAUtiB,EAAEwY,QAAQ5F,QAAQ,cAAc,CAAC5S,KAAKuE,EAAE2D,UAAU4X,SAAS,WAAW,IAAI9f,EAAE2G,KAAKpC,EAAE1D,SAASqC,KAAKK,MAAMvD,EAAEqY,cAAa,IAAKrY,EAAEkM,QAAQ0J,SAAS,MAAM,OAAO,QAAQ5V,EAAEqY,aAAarY,EAAEwY,QAAQ6D,SAAS,kBAAkBrc,EAAEwY,QAAQ8D,YAAY,uBAAkB,IAAS/X,EAAEme,uBAAkB,IAASne,EAAEoe,oBAAe,IAASpe,EAAEqe,eAAc,IAAK5iB,EAAEkM,QAAQuJ,SAASzV,EAAEgY,gBAAe,GAAIhY,EAAEkM,QAAQgI,OAAO,iBAAiBlU,EAAEkM,QAAQ6J,OAAO/V,EAAEkM,QAAQ6J,OAAO,IAAI/V,EAAEkM,QAAQ6J,OAAO,GAAG/V,EAAEkM,QAAQ6J,OAAO/V,EAAEqM,SAAS0J,aAAQ,IAASxR,EAAEse,aAAa7iB,EAAE4X,SAAS,aAAa5X,EAAE0Y,cAAc,eAAe1Y,EAAE2Y,eAAe,mBAAc,IAASpU,EAAEue,0BAAqB,IAASve,EAAEwe,oBAAoB/iB,EAAE4X,UAAS,SAAK,IAASrT,EAAEye,eAAehjB,EAAE4X,SAAS,eAAe5X,EAAE0Y,cAAc,iBAAiB1Y,EAAE2Y,eAAe,qBAAgB,IAASpU,EAAEue,0BAAqB,IAASve,EAAE0e,iBAAiBjjB,EAAE4X,UAAS,SAAK,IAASrT,EAAE2e,kBAAkBljB,EAAE4X,SAAS,kBAAkB5X,EAAE0Y,cAAc,oBAAoB1Y,EAAE2Y,eAAe,wBAAmB,IAASpU,EAAEue,0BAAqB,IAASve,EAAEwe,oBAAoB/iB,EAAE4X,UAAS,SAAK,IAASrT,EAAE4e,cAAcnjB,EAAE4X,SAAS,cAAc5X,EAAE0Y,cAAc,gBAAgB1Y,EAAE2Y,eAAe,oBAAe,IAASpU,EAAE4e,cAAcnjB,EAAE4X,UAAS,SAAK,IAASrT,EAAE6e,YAAW,IAAKpjB,EAAE4X,WAAW5X,EAAE4X,SAAS,YAAY5X,EAAE0Y,cAAc,YAAY1Y,EAAE2Y,eAAe,cAAc3Y,EAAEyX,kBAAkBzX,EAAEkM,QAAQwJ,cAAc,OAAO1V,EAAE4X,WAAU,IAAK5X,EAAE4X,UAAUrT,EAAE2D,UAAU8U,gBAAgB,SAAShd,GAAG,IAAQyE,EAAE6C,EAAwMD,EAAkC9C,EAAxOC,EAAEmC,KAAQrC,EAAEE,EAAEgU,QAAQ2B,KAAK,gBAAgBmC,YAAY,2CAA2ClC,KAAK,cAAc,QAAQ5V,EAAE0S,QAAQyD,GAAG3a,GAAGqc,SAAS,kBAAiB,IAAK7X,EAAE0H,QAAQsH,YAAgBnM,EAAE7C,EAAE0H,QAAQgJ,aAAa,GAAG,EAAE,EAAE,EAAE3Q,EAAEsI,KAAKsS,MAAM3a,EAAE0H,QAAQgJ,aAAa,IAAG,IAAK1Q,EAAE0H,QAAQmI,WAAc9P,GAAHvE,GAAMA,GAAGwE,EAAEuS,WAAW,EAAExS,EAAEC,EAAE0S,QAAQ/O,MAAMnI,EAAEuE,EAAE8C,EAAErH,EAAEuE,EAAE,GAAG8X,SAAS,gBAAgBjC,KAAK,cAAc,UAAU3V,EAAED,EAAE0H,QAAQgJ,aAAalV,EAAEsE,EAAE6D,MAAM1D,EAAEF,EAAE,EAAE8C,EAAE5C,EAAEF,EAAE,GAAG8X,SAAS,gBAAgBjC,KAAK,cAAc,UAAU,IAAIpa,EAAEsE,EAAEqW,GAAGrW,EAAEvC,OAAO,EAAEyC,EAAE0H,QAAQgJ,cAAcmH,SAAS,gBAAgBrc,IAAIwE,EAAEuS,WAAW,GAAGzS,EAAEqW,GAAGnW,EAAE0H,QAAQgJ,cAAcmH,SAAS,iBAAiB7X,EAAE0S,QAAQyD,GAAG3a,GAAGqc,SAAS,iBAAwB,GAAHrc,GAAMA,GAAGwE,EAAEuS,WAAWvS,EAAE0H,QAAQgJ,aAAa1Q,EAAE0S,QAAQ/O,MAAMnI,EAAEA,EAAEwE,EAAE0H,QAAQgJ,cAAcmH,SAAS,gBAAgBjC,KAAK,cAAc,SAAS9V,EAAEvC,QAAQyC,EAAE0H,QAAQgJ,aAAa5Q,EAAE+X,SAAS,gBAAgBjC,KAAK,cAAc,UAAU9S,EAAE9C,EAAEuS,WAAWvS,EAAE0H,QAAQgJ,aAAazQ,GAAE,IAAKD,EAAE0H,QAAQmI,SAAS7P,EAAE0H,QAAQgJ,aAAalV,EAAEA,EAAEwE,EAAE0H,QAAQgJ,cAAc1Q,EAAE0H,QAAQiJ,gBAAgB3Q,EAAEuS,WAAW/W,EAAEwE,EAAE0H,QAAQgJ,aAAa5Q,EAAE6D,MAAM1D,GAAGD,EAAE0H,QAAQgJ,aAAa5N,GAAG7C,EAAE6C,GAAG+U,SAAS,gBAAgBjC,KAAK,cAAc,SAAS9V,EAAE6D,MAAM1D,EAAEA,EAAED,EAAE0H,QAAQgJ,cAAcmH,SAAS,gBAAgBjC,KAAK,cAAc,UAAU,aAAa5V,EAAE0H,QAAQqI,UAAU,gBAAgB/P,EAAE0H,QAAQqI,UAAU/P,EAAE+P,YAAYhQ,EAAE2D,UAAU4U,cAAc,WAAW,IAAIvY,EAAED,EAAEG,EAAE6C,EAAEX,KAAK,IAAG,IAAKW,EAAE4E,QAAQgI,OAAO5M,EAAE4E,QAAQsH,YAAW,IAAI,IAAKlM,EAAE4E,QAAQmI,WAAU,IAAK/M,EAAE4E,QAAQgI,OAAO5P,EAAE,KAAKgD,EAAEyP,WAAWzP,EAAE4E,QAAQgJ,cAAc,CAAC,IAAIzQ,GAAE,IAAK6C,EAAE4E,QAAQsH,WAAWlM,EAAE4E,QAAQgJ,aAAa,EAAE5N,EAAE4E,QAAQgJ,aAAa3Q,EAAE+C,EAAEyP,WAAWxS,EAAE+C,EAAEyP,WAAWtS,IAAEF,EAAKD,EAAEC,EAAE,EAAEvE,EAAEsH,EAAE4P,QAAQ5S,IAAI+e,OAAM,GAAIjJ,KAAK,KAAK,IAAIA,KAAK,mBAAmB9V,EAAEgD,EAAEyP,YAAY8D,UAAUvT,EAAE2P,aAAaoF,SAAS,gBAAgB,IAAI9X,EAAE,EAAEA,EAAEE,EAAE6C,EAAEyP,WAAWxS,GAAG,EAAED,EAAEC,EAAEvE,EAAEsH,EAAE4P,QAAQ5S,IAAI+e,OAAM,GAAIjJ,KAAK,KAAK,IAAIA,KAAK,mBAAmB9V,EAAEgD,EAAEyP,YAAY2D,SAASpT,EAAE2P,aAAaoF,SAAS,gBAAgB/U,EAAE2P,YAAYkD,KAAK,iBAAiBA,KAAK,QAAQc,KAAK,WAAWjb,EAAE2G,MAAMyT,KAAK,KAAK,QAAQ7V,EAAE2D,UAAUgW,UAAU,SAASle,GAAcA,GAAL2G,KAAUwS,WAAVxS,KAAuBuR,YAAYlY,GAAGuE,EAAE2D,UAAUuR,cAAc,SAASlV,GAAG,IAAWE,EAAEzE,EAAEuE,EAAEyJ,QAAQ0P,GAAG,gBAAgB1d,EAAEuE,EAAEyJ,QAAQhO,EAAEuE,EAAEyJ,QAAQsV,QAAQ,gBAA2Dhc,GAA3CA,EAAExC,SAASL,EAAE2V,KAAK,uBAA2B,EAAnIzT,KAAwIoQ,YAAxIpQ,KAAsJuF,QAAQgJ,aAA9JvO,KAA6KsV,aAAa3U,GAAE,GAAG,GAA/LX,KAAqMsV,aAAa3U,IAAI/C,EAAE2D,UAAU+T,aAAa,SAASjc,EAAEuE,EAAED,GAAG,IAAIG,EAAE6C,EAAE9C,EAAE6C,EAAE3C,EAAEK,EAAOd,EAAE0C,KAAK,GAAGpC,EAAEA,IAAG,KAAK,IAAKN,EAAEgS,YAAW,IAAKhS,EAAEiI,QAAQ4J,iBAAgB,IAAK7R,EAAEiI,QAAQgI,MAAMjQ,EAAEqS,eAAetW,GAAG,IAAG,IAAKuE,GAAGN,EAAEkP,SAASnT,GAAGyE,EAAEzE,EAAE+E,EAAEd,EAAEib,QAAQza,GAAG4C,EAAEpD,EAAEib,QAAQjb,EAAEqS,cAAcrS,EAAEoS,YAAY,OAAOpS,EAAEoT,UAAUhQ,EAAEpD,EAAEoT,WAAU,IAAKpT,EAAEiI,QAAQmI,WAAU,IAAKpQ,EAAEiI,QAAQsH,aAAaxT,EAAE,GAAGA,EAAEiE,EAAEyY,cAAczY,EAAEiI,QAAQiJ,iBAAgB,IAAKlR,EAAEiI,QAAQgI,OAAOzP,EAAER,EAAEqS,cAAa,IAAKhS,EAAEL,EAAEqX,aAAajU,EAAE,WAAWpD,EAAEwd,UAAUhd,KAAKR,EAAEwd,UAAUhd,SAAS,IAAG,IAAKR,EAAEiI,QAAQmI,WAAU,IAAKpQ,EAAEiI,QAAQsH,aAAaxT,EAAE,GAAGA,EAAEiE,EAAE8S,WAAW9S,EAAEiI,QAAQiJ,iBAAgB,IAAKlR,EAAEiI,QAAQgI,OAAOzP,EAAER,EAAEqS,cAAa,IAAKhS,EAAEL,EAAEqX,aAAajU,EAAE,WAAWpD,EAAEwd,UAAUhd,KAAKR,EAAEwd,UAAUhd,QAAQ,CAAC,GAAGR,EAAEiI,QAAQoH,UAAU6I,cAAclY,EAAEkS,eAAe7O,EAAE7C,EAAE,EAAER,EAAE8S,WAAW9S,EAAEiI,QAAQiJ,gBAAgB,EAAElR,EAAE8S,WAAW9S,EAAE8S,WAAW9S,EAAEiI,QAAQiJ,eAAelR,EAAE8S,WAAWtS,EAAEA,GAAGR,EAAE8S,WAAW9S,EAAE8S,WAAW9S,EAAEiI,QAAQiJ,gBAAgB,EAAE,EAAE1Q,EAAER,EAAE8S,WAAWtS,EAAER,EAAEgS,WAAU,EAAGhS,EAAEuU,QAAQ5F,QAAQ,eAAe,CAAC3O,EAAEA,EAAEqS,aAAahP,IAAI9C,EAAEP,EAAEqS,aAAarS,EAAEqS,aAAahP,EAAErD,EAAE+Y,gBAAgB/Y,EAAEqS,cAAcrS,EAAEiI,QAAQiH,WAAWzO,GAAGA,EAAET,EAAE6X,gBAAgBE,MAAM,aAAajF,YAAYrS,EAAEwH,QAAQgJ,cAAcxQ,EAAEsY,gBAAgB/Y,EAAEqS,cAAcrS,EAAE8Y,aAAa9Y,EAAEic,gBAAe,IAAKjc,EAAEiI,QAAQgI,KAAK,OAAM,IAAK5P,GAAGL,EAAE0a,aAAana,GAAGP,EAAEwa,UAAUnX,EAAE,WAAWrD,EAAEwd,UAAUna,MAAMrD,EAAEwd,UAAUna,QAAQrD,EAAEkX,iBAAgB,IAAK7W,EAAEL,EAAEqX,aAAavW,EAAE,WAAWd,EAAEwd,UAAUna,KAAKrD,EAAEwd,UAAUna,KAAK/C,EAAE2D,UAAU6X,UAAU,WAAW,IAAI/f,EAAE2G,MAAK,IAAK3G,EAAEkM,QAAQgH,QAAQlT,EAAE+W,WAAW/W,EAAEkM,QAAQgJ,eAAelV,EAAE6W,WAAW0M,OAAOvjB,EAAE4W,WAAW2M,SAAQ,IAAKvjB,EAAEkM,QAAQ2H,MAAM7T,EAAE+W,WAAW/W,EAAEkM,QAAQgJ,cAAclV,EAAEwW,MAAM+M,OAAOvjB,EAAEwY,QAAQ6D,SAAS,kBAAkB9X,EAAE2D,UAAUsb,eAAe,WAAW,IAAU/e,EAAE6C,EAAEX,KAAY3G,EAAEsH,EAAEkQ,YAAYiM,OAAOnc,EAAEkQ,YAAYkM,KAAKnf,EAAE+C,EAAEkQ,YAAYmM,OAAOrc,EAAEkQ,YAAYoM,KAAKtf,EAAEuI,KAAKgX,MAAMtf,EAAEvE,GAA1G,OAA8GyE,EAAEoI,KAAKiX,MAAM,IAAIxf,EAAEuI,KAAKkX,KAAK,IAAItf,EAAE,IAAIoI,KAAK6S,IAAIjb,IAAIA,GAAG,IAAO,GAAHA,GAAuCA,GAAG,KAAQ,KAAHA,GAA1C,IAAK6C,EAAE4E,QAAQ6I,IAAI,OAAO,QAA4D,KAAHtQ,GAAQA,GAAG,KAAI,IAAK6C,EAAE4E,QAAQ6I,IAAI,QAAQ,QAAO,IAAKzN,EAAE4E,QAAQ2J,gBAAmB,IAAHpR,GAAOA,GAAG,IAAI,OAAO,KAAK,YAAYF,EAAE2D,UAAU8b,SAAS,SAAShkB,GAAG,IAAIuE,EAAED,EAAEG,EAAEkC,KAAK,GAAGlC,EAAEyR,UAAS,EAAGzR,EAAE6S,SAAQ,EAAG7S,EAAEqS,UAAU,OAAOrS,EAAEqS,WAAU,EAAM,GAAGrS,EAAEyT,aAAY,EAAGzT,EAAE8T,cAAwC,GAA1B9T,EAAE+S,YAAYyM,kBAAgB,IAASxf,EAAE+S,YAAYkM,KAAK,OAAM,EAAG,IAAG,IAAKjf,EAAE+S,YAAY0M,SAASzf,EAAE+T,QAAQ5F,QAAQ,OAAO,CAACnO,EAAEA,EAAE+e,mBAAmB/e,EAAE+S,YAAYyM,aAAaxf,EAAE+S,YAAY2M,SAAS,CAAC,OAAO7f,EAAEG,EAAE+e,kBAAkB,IAAI,OAAO,IAAI,OAAOjf,EAAEE,EAAEyH,QAAQoJ,aAAa7Q,EAAEqZ,eAAerZ,EAAE6R,aAAa7R,EAAEgb,iBAAiBhb,EAAE6R,aAAa7R,EAAEgb,gBAAgBhb,EAAE2R,iBAAiB,EAAE,MAAM,IAAI,QAAQ,IAAI,KAAK7R,EAAEE,EAAEyH,QAAQoJ,aAAa7Q,EAAEqZ,eAAerZ,EAAE6R,aAAa7R,EAAEgb,iBAAiBhb,EAAE6R,aAAa7R,EAAEgb,gBAAgBhb,EAAE2R,iBAAiB,EAAE,YAAY9R,IAAIG,EAAEwX,aAAa1X,GAAGE,EAAE+S,YAAY,GAAG/S,EAAE+T,QAAQ5F,QAAQ,QAAQ,CAACnO,EAAEH,UAAUG,EAAE+S,YAAYiM,SAAShf,EAAE+S,YAAYkM,OAAOjf,EAAEwX,aAAaxX,EAAE6R,cAAc7R,EAAE+S,YAAY,KAAKjT,EAAE2D,UAAUyR,aAAa,SAAS3Z,GAAG,IAAIuE,EAAEoC,KAAK,MAAK,IAAKpC,EAAE2H,QAAQmJ,OAAO,eAAexU,WAAU,IAAK0D,EAAE2H,QAAQmJ,QAAO,IAAK9Q,EAAE2H,QAAQ6H,YAAY,IAAI/T,EAAEyL,KAAKvG,QAAQ,UAAU,OAAOX,EAAEiT,YAAY4M,YAAYpkB,EAAEqkB,oBAAe,IAASrkB,EAAEqkB,cAAcC,QAAQtkB,EAAEqkB,cAAcC,QAAQviB,OAAO,EAAEwC,EAAEiT,YAAY2M,SAAS5f,EAAEkS,UAAUlS,EAAE2H,QAAQsJ,gBAAe,IAAKjR,EAAE2H,QAAQ2J,kBAAkBtR,EAAEiT,YAAY2M,SAAS5f,EAAEmS,WAAWnS,EAAE2H,QAAQsJ,gBAAgBxV,EAAE+Y,KAAK8H,QAAQ,IAAI,QAAQtc,EAAEggB,WAAWvkB,GAAG,MAAM,IAAI,OAAOuE,EAAEigB,UAAUxkB,GAAG,MAAM,IAAI,MAAMuE,EAAEyf,SAAShkB,KAAKuE,EAAE2D,UAAUsc,UAAU,SAASxkB,GAAG,IAAIuE,EAAED,EAAEG,EAAE6C,EAAID,EAAE3C,EAAEiC,KAAYnC,OAAE,IAASxE,EAAEqkB,cAAcrkB,EAAEqkB,cAAcC,QAAQ,KAA1D,SAAkE5f,EAAEwR,UAAUxR,EAAEoS,WAAWtS,GAAG,IAAIA,EAAEzC,UAAUwC,EAAEG,EAAEwa,QAAQxa,EAAE4R,cAAc5R,EAAE8S,YAAYkM,UAAK,IAASlf,EAAEA,EAAE,GAAGigB,MAAMzkB,EAAE0kB,QAAQhgB,EAAE8S,YAAYoM,UAAK,IAASpf,EAAEA,EAAE,GAAGmgB,MAAM3kB,EAAE4kB,QAAQlgB,EAAE8S,YAAYyM,YAAYpX,KAAKiX,MAAMjX,KAAKgY,KAAKhY,KAAKiY,IAAIpgB,EAAE8S,YAAYkM,KAAKhf,EAAE8S,YAAYiM,OAAO,KAAKpc,EAAEwF,KAAKiX,MAAMjX,KAAKgY,KAAKhY,KAAKiY,IAAIpgB,EAAE8S,YAAYoM,KAAKlf,EAAE8S,YAAYmM,OAAO,MAAMjf,EAAEwH,QAAQ2J,kBAAkBnR,EAAE4S,SAAW,EAAFjQ,IAAK3C,EAAEoS,WAAU,KAAQ,IAAKpS,EAAEwH,QAAQ2J,kBAAkBnR,EAAE8S,YAAYyM,YAAY5c,GAAG/C,EAAEI,EAAE8e,sBAAiB,IAASxjB,EAAEqkB,eAAyC,EAA1B3f,EAAE8S,YAAYyM,cAAgBvf,EAAE4S,SAAQ,EAAGtX,EAAEyP,kBAAkBnI,IAAG,IAAK5C,EAAEwH,QAAQ6I,IAAI,GAAG,IAAIrQ,EAAE8S,YAAYkM,KAAKhf,EAAE8S,YAAYiM,OAAO,GAAG,IAAG,IAAK/e,EAAEwH,QAAQ2J,kBAAkBvO,EAAE5C,EAAE8S,YAAYoM,KAAKlf,EAAE8S,YAAYmM,OAAO,GAAG,GAAGlf,EAAEC,EAAE8S,YAAYyM,aAAYvf,EAAE8S,YAAY0M,SAAQ,KAAQxf,EAAEwH,QAAQmI,WAAW,IAAI3P,EAAE4R,cAAc,UAAUhS,GAAGI,EAAE4R,cAAc5R,EAAEgY,eAAe,SAASpY,KAAKG,EAAEC,EAAE8S,YAAYyM,YAAYvf,EAAEwH,QAAQ+H,aAAavP,EAAE8S,YAAY0M,SAAQ,IAAI,IAAKxf,EAAEwH,QAAQ0J,SAASlR,EAAE2S,UAAU9S,EAAEE,EAAE6C,EAAE5C,EAAE2S,UAAU9S,EAAEE,GAAGC,EAAE6S,MAAM9U,SAASiC,EAAE+R,WAAWnP,GAAE,IAAK5C,EAAEwH,QAAQ2J,kBAAkBnR,EAAE2S,UAAU9S,EAAEE,EAAE6C,IAAG,IAAK5C,EAAEwH,QAAQgI,OAAM,IAAKxP,EAAEwH,QAAQqJ,aAAY,IAAK7Q,EAAEuR,WAAWvR,EAAE2S,UAAU,MAAK,QAAS3S,EAAEyd,OAAOzd,EAAE2S,eAAe9S,EAAE2D,UAAUqc,WAAW,SAASvkB,GAAG,IAAIuE,EAAED,EAAEqC,KAAK,GAAGrC,EAAE4T,aAAY,EAAG,IAAI5T,EAAEkT,YAAY4M,aAAa9f,EAAEyS,YAAYzS,EAAE4H,QAAQgJ,aAAa,QAAO5Q,EAAEkT,YAAY,SAAM,IAASxX,EAAEqkB,oBAAe,IAASrkB,EAAEqkB,cAAcC,UAAU/f,EAAEvE,EAAEqkB,cAAcC,QAAQ,IAAIhgB,EAAEkT,YAAYiM,OAAOnf,EAAEkT,YAAYkM,UAAK,IAASnf,EAAEA,EAAEkgB,MAAMzkB,EAAE0kB,QAAQpgB,EAAEkT,YAAYmM,OAAOrf,EAAEkT,YAAYoM,UAAK,IAASrf,EAAEA,EAAEogB,MAAM3kB,EAAE4kB,QAAQtgB,EAAE4R,UAAS,GAAI3R,EAAE2D,UAAU6c,eAAexgB,EAAE2D,UAAU8c,cAAc,WAAW,IAAIhlB,EAAE2G,KAAK,OAAO3G,EAAEyY,eAAezY,EAAEya,SAASza,EAAEiX,YAAY6D,SAASnU,KAAKuF,QAAQ8I,OAAO+F,SAAS/a,EAAEyY,aAAaiC,SAAS1a,EAAEiX,aAAajX,EAAEkb,WAAW3W,EAAE2D,UAAUuS,OAAO,WAAW,IAAIlW,EAAEoC,KAAK3G,EAAE,gBAAgBuE,EAAEiU,SAAS9K,SAASnJ,EAAEiS,OAAOjS,EAAEiS,MAAM9I,SAASnJ,EAAEsS,YAAYtS,EAAEwV,SAAShQ,KAAKxF,EAAE2H,QAAQkH,YAAY7O,EAAEsS,WAAWnJ,SAASnJ,EAAEqS,YAAYrS,EAAEwV,SAAShQ,KAAKxF,EAAE2H,QAAQmH,YAAY9O,EAAEqS,WAAWlJ,SAASnJ,EAAE2S,QAAQoF,YAAY,wDAAwDlC,KAAK,cAAc,QAAQuB,IAAI,QAAQ,KAAKpX,EAAE2D,UAAUqV,QAAQ,SAASvd,GAAS2G,KAAO6R,QAAQ5F,QAAQ,UAAU,CAAjCjM,KAAoC3G,IAApC2G,KAA0C6X,WAAWja,EAAE2D,UAAUgY,aAAa,WAAW,IAAIlgB,EAAE2G,KAAKkG,KAAKsS,MAAMnf,EAAEkM,QAAQgJ,aAAa,IAAG,IAAKlV,EAAEkM,QAAQgH,QAAQlT,EAAE+W,WAAW/W,EAAEkM,QAAQgJ,eAAelV,EAAEkM,QAAQmI,WAAWrU,EAAE6W,WAAWyF,YAAY,kBAAkBlC,KAAK,gBAAgB,SAASpa,EAAE4W,WAAW0F,YAAY,kBAAkBlC,KAAK,gBAAgB,SAAS,IAAIpa,EAAEsW,cAActW,EAAE6W,WAAWwF,SAAS,kBAAkBjC,KAAK,gBAAgB,QAAQpa,EAAE4W,WAAW0F,YAAY,kBAAkBlC,KAAK,gBAAgB,WAAUpa,EAAEsW,cAActW,EAAE+W,WAAW/W,EAAEkM,QAAQgJ,eAAc,IAAKlV,EAAEkM,QAAQsH,YAA2JxT,EAAEsW,cAActW,EAAE+W,WAAW,IAAG,IAAK/W,EAAEkM,QAAQsH,cAA9LxT,EAAE4W,WAAWyF,SAAS,kBAAkBjC,KAAK,gBAAgB,QAAQpa,EAAE6W,WAAWyF,YAAY,kBAAkBlC,KAAK,gBAAgB,YAAuN7V,EAAE2D,UAAU6U,WAAW,WAAW,IAAI/c,EAAE2G,KAAK,OAAO3G,EAAEwW,QAAQxW,EAAEwW,MAAM2D,KAAK,MAAMmC,YAAY,gBAAgBmE,MAAMzgB,EAAEwW,MAAM2D,KAAK,MAAMQ,GAAG9N,KAAKsS,MAAMnf,EAAEsW,aAAatW,EAAEkM,QAAQiJ,iBAAiBkH,SAAS,kBAAkB9X,EAAE2D,UAAUiW,WAAW,WAAiBxX,KAAOuF,QAAQoH,WAAWzS,SAA1B8F,KAAqCwR,QAArCxR,KAA+CuR,aAAY,EAA3DvR,KAAgEuR,aAAY,IAAKlY,EAAEilB,GAAGjJ,MAAM,WAA8F,IAAnF,IAAM1X,EAAEG,EAAEkC,KAAKW,EAAE2J,UAAU,GAAGzM,EAAEyD,MAAMC,UAAUC,MAAMR,KAAKsJ,UAAU,GAAG5J,EAAE5C,EAAE1C,OAAW/B,EAAE,EAAEA,EAAEqH,EAAErH,IAAI,GAAG,UAAQwG,QAASc,SAAG,IAASA,EAAE7C,EAAEzE,GAAGgc,MAAM,IAAIzX,EAAEE,EAAEzE,GAAGsH,GAAGhD,EAAEG,EAAEzE,GAAGgc,MAAM1U,GAAGsB,MAAMnE,EAAEzE,GAAGgc,MAAMxX,QAAG,IAASF,EAAE,OAAOA,EAAE,OAAOG,KCAz2zCkO,OAAO9R,UAAUoK,MAAM,WACrB0H,OAAO,kBAAkBqJ,MAAM,CAC7B3H,UAAU,EACVa,aAAc,EACdC,eAAgB,EAChB/B,UACE,oIACFC,UACE,qIACFwB,WAAY,CACV,CACEgN,WAAY,KACZzV,SAAU,CACR8I,aAAc,EACdC,eAAgB,EAChBd,UAAU,EACVR,MAAM,IAGV,CACEgO,WAAY,IACZzV,SAAU,CACR8I,aAAc,EACdC,eAAgB,IAGpB,CACE0M,WAAY,IACZzV,SAAU,CACR8I,aAAc,EACdC,eAAgB,OAQxBxC,OAAO,uBAAuBqJ,MAAM,CAClC3H,UAAU,EACVa,aAAc,EACdC,eAAgB,EAChB/B,UACE,oIACFC,UACE,qIACFwB,WAAY,CACV,CACEgN,WAAY,KACZzV,SAAU,CACR8I,aAAc,EACdC,eAAgB,EAChBd,UAAU,EACVR,MAAM,IAGV,CACEgO,WAAY,IACZzV,SAAU,CACR8I,aAAc,EACdC,eAAgB,IAGpB,CACE0M,WAAY,IACZzV,SAAU,CACR8I,aAAc,EACdC,eAAgB,OAUxBxC,OAAO,0CAA0CqJ,MAAM,CACrDnI,MAAM,EACNQ,UAAU,EACVe,MAAO,IACPF,aAAc,EACdnC,gBAAgB,EAChBO,UAAU,EACVF,UACE,sIACFC,UACE,uIACFwB,WAAY,CACV,CACEgN,WAAY,IACZzV,SAAU,CACR8I,aAAc,IAGlB,CACE2M,WAAY,IACZzV,SAAU,CACR8I,aAAc,EACdzB,cAAe,YAMvBd,OAAO,wBAAwBqJ,MAAM,CACnCnI,MAAM,EACNQ,UAAU,EACVe,MAAO,IACPF,aAAc,EACdnC,gBAAgB,EAChBK,UACE,sIACFC,UACE,yIAGJV,OAAO,sBAAsBqJ,MAAM,CACjCnI,MAAM,EACNL,YAAY,EACZC,cAAe,QACfyB,aAAc,EACdb,UAAU,EACVf,UAAU,EAEVF,UACE,sIACFC,UACE,uIACFwB,WAAY,CACV,CACEgN,WAAY,KACZzV,SAAU,CACR8I,aAAc,IAGlB,CACE2M,WAAY,IACZzV,SAAU,CACR8I,aAAc,EACdzB,cAAe,SC5IzByR,EAAE,qBAAqBC,MAAM,SAAU5gB,GACrCA,EAAEkL,iBAEF,IAAI2V,EAAQF,EAAEve,MAEVye,EAAMjE,OAAOtB,SAAS,SACxBuF,EAAMC,YAAY,UAClBD,EAAMjE,OAAO7E,YAAY,QACzB8I,EAAMjE,OAAOmE,QAAQ,OAErBF,EAAMC,YAAY,UAClBD,EAAMvX,SAASA,SAASsM,KAAK,cAAcmC,YAAY,QACvD8I,EAAMvX,SAASA,SAASsM,KAAK,cAAcmL,QAAQ,KACnDF,EAAMjE,OAAOkE,YAAY,QACzBD,EAAMjE,OAAOoE,YAAY,QCd7B,IAAIC,WAAa3kB,SAASyF,iBAAiB,6BAE3C,QAA4B,IAAjBkf,WAAW,IAAsC,MAAjBA,WAAW,GAqBpD,IArBgE,IAGvDC,YAAT,SAAqBC,EAAKC,GACxB,IACMC,EAEAC,EAHFC,QAAQC,aACNH,EAAS,IAAII,gBAAgB1mB,OAAOuD,SAASojB,SAC1CC,IAAIR,EAAKC,GACZE,EACFvmB,OAAOuD,SAASqB,SAChB,KACA5E,OAAOuD,SAASuB,KAChB9E,OAAOuD,SAASsjB,SAChB,IACAP,EAAOld,WACTpJ,OAAOwmB,QAAQC,UAAU,CAAEK,KAAMP,GAAU,GAAIA,KAoB1CQ,WAAT,SAAA,GAA+B,IACzBtN,EAAO,CACT8H,OAAQ,8BACRyF,UAHoB,EAAArV,UAAAlP,aAAAW,IAAxB,EAAA,EAA2B,IAMzB6jB,QAAQC,IAAIzN,GAEZ0M,YAAY,YAAa1M,EAAKuN,WAE9B3T,OAAO8T,UAAU,CAAE1nB,OAAO,IAE1B4T,OAAO+T,KAAK,CACVjb,KAAM,OACN1M,OAAO,EACP4nB,QAAS,CAAEC,gBAAiB,YAC5BC,YAAa,cACb3mB,IACE4mB,aAAaC,QACb,WACFhO,KAAMA,EACNiO,WAAY,WACOnmB,SAAS8L,cAAc,kBAC7BtJ,UAAY,WAEzB4jB,QAAS,SAAUC,GACjBX,QAAQC,IAAIU,GACRA,EACermB,SAAS8L,cAAc,kBAC7BtJ,UAAY6jB,EAASnO,KAEhCwN,QAAQC,IAAIU,OAhEdC,MAAQtmB,SAASyF,iBAAiB,6BAiBpCpG,IAAM,IAAIknB,IAAI9nB,OAAOuD,SAAS9C,MAC9BsnB,KAAO,GAAG7U,MAAA,SAAAxS,GAGZmnB,MAAMnnB,GAAGT,iBAAiB,SAAU,SAACgF,GACnC,IAGM+iB,EAHF/iB,EAAEyJ,OAAOuZ,QACXF,KAAKnc,KAAKic,MAAMnnB,GAAG2lB,OACTphB,EAAEyJ,OAAOuZ,UACfD,EAAWD,KAAKniB,QAAQX,EAAEyJ,OAAO2X,OACrC0B,KAAKvF,OAAOwF,EAAU,IAGxBjB,WAAWgB,SATNrnB,EAAI,EAAGA,EAAImnB,MAAMplB,OAAQ/B,IAAGwS,MAAAxS,GCvBvC,IA+CQwnB,aASAC,aAxDJC,WAAa7mB,SAASyF,iBAAiB,oBAE3C,SAASqhB,gBAAgBC,EAAWC,GAClC,IAAI9O,EAAO,CACT8H,OAAQ,4BACR+G,UAAWA,GAGbjV,OAAO,sBAAsBmV,QAAQ,KAErCnoB,WAAW,WACTgT,OAAO+T,KAAK,CACVjb,KAAM,OACNob,YAAa,cACb3mB,IAAK4mB,aAAaC,QAClBhO,KAAMA,EACNiO,WAAY,WACOnmB,SAAS8L,cAAc,uBAG1Csa,QAAS,SAAUC,GAEjB,IACMa,EAFNxB,QAAQC,IAAIU,GACRA,GACEa,EAAalnB,SAAS8L,cAAc,sBACxC9L,SAAS8L,cAAc,sBAAsBxH,QAAQ6iB,KAAOJ,EAC5D/mB,SAAS8L,cAAc,uBAAuBtJ,UAAYukB,EAC1DG,EAAW1kB,UAAY6jB,EAASnO,KAChCpG,OAAO,sBAAsBsV,OAAO,KAElCT,YAAY,GAAGjkB,MAAMkC,QADnBmiB,IAAcC,EACe,OAEA,UAEf,IAAdD,EACFH,YAAY,GAAGlkB,MAAMkC,QAAU,OACV,EAAZmiB,IACTH,YAAY,GAAGlkB,MAAMkC,QAAU,YAGjC8gB,QAAQC,IAAIU,OAIjB,K,0nCAGwB,IAAlBQ,WAAW,IAAuC,MAAjBA,WAAW,MAC/CF,aAAc3mB,SAASyF,iBAAiB,qBAClC,GAAG/G,iBAAiB,QAAS,SAACgF,GACxCA,EAAEkL,iBACF,IAAIyY,EACFrnB,SAAS8L,cAAc,sBAAsBxH,QAAQ6iB,KACnDH,EAAYhnB,SAAS8L,cAAc,sBAAsBxH,QAAQ2H,IACrE6a,gBAAgB7iB,SAASojB,GAAgB,EAAGpjB,SAAS+iB,OAGjDJ,aAAc5mB,SAASyF,iBAAiB,qBAClC,GAAG/G,iBAAiB,QAAS,SAACgF,GACxCA,EAAEkL,iBACF,IAAIyY,EACFrnB,SAAS8L,cAAc,sBAAsBxH,QAAQ6iB,KACnDH,EAAYhnB,SAAS8L,cAAc,sBAAsBxH,QAAQ2H,IACrE6a,gBAAgB7iB,SAASojB,GAAgB,EAAGpjB,SAAS+iB,MAGvDJ,aAAY,GAAGlkB,MAAMkC,QAAU,QCjEjCyf,EAAErkB,UAAUoK,MAAM,WAChBia,EAAE,UAAU9K,KAAK,qBAAsB,eAGzC8K,EAAErkB,UAAUoK,MAAM,WAqCd,SAASkd,IACPzV,MAAMjD,iBAGNyV,EAAE,QAAQ5I,YAAY,6BAGtB4I,EAAE,mBAAmB9K,KAAK,MAAO,IAvCnC8K,EAAE,2BAA2BnG,GAAG,QAAS,SAAUxa,GAEjDA,EAAEkL,iBAEF,IAEI5K,EAAQqgB,EAAE,mBAAmB9K,KAAK,OAAOvV,MAD3C,6EAeE8G,EAAM,+BAbD9G,GAA6B,KAApBA,EAAM,GAAG9C,SAAgB8C,EAAM,IAOlC,oBAUfqgB,EAAE,mBAAmB9K,KAAK,MAAOzO,GAGjCuZ,EAAE,QAAQ7I,SAAS,+BAiBrB6I,EAAE,QAAQnG,GACR,QACA,4CACA,SAAUrM,GAERyV,MAIJjD,EAAE,QAAQkD,MAAM,SAAU7jB,GAEN,KAAdA,EAAE0c,SAEJkH,QC/DR,IAEIE,WAEAC,aACAC,aALAC,cAAgB,EAChBC,kBAAoB,EAMxB,SAASC,oBAAoBC,GAE3B,OADe,IAAI3C,gBAAgB1mB,OAAOuD,SAASojB,QACrC9I,IAAIwL,GAGpB,SAASC,yBAAyBD,GAChC,IAWiCE,EAF7BC,EAAsB,GAAGC,EAAAC,2BATd,IAAIhD,gBAAgB1mB,OAAOuD,SAASojB,QAW3BgD,WAAS,IAAjC,IAAAF,EAAAzhB,MAAAuhB,EAAAE,EAAAvkB,KAAA0kB,MAAmC,CAAA,IAA1BC,EAAIN,EAAAlD,MAEK,oBAAZwD,EAAK,IACPL,EAAoB5d,KAAKie,EAAK,KAIlC,MAAAC,GAAAL,EAAAxkB,EAAA6kB,GAAA,QAAAL,EAAAliB,IACA,OAAOiiB,EAGT,SAASO,oBAAoBV,EAAWW,GACtC,IAAM1D,EAAS,IAAII,gBAAgB1mB,OAAOuD,SAASojB,QACnDL,EAAOM,IAAIyC,EAAWW,GACtBhqB,OAAOwmB,QAAQC,UAAU,GAAI,GAAIljB,SAASsjB,SAAW,IAAMP,GAC3DtmB,OAAOuP,cAAc,IAAIxI,MAAM,aAGjC,SAASkjB,yBAAyBZ,EAAWW,GAC3C,IAAM1D,EAAS,IAAII,gBAAgB1mB,OAAOuD,SAASojB,QACnDL,EAAO5K,OAAO2N,EAAWW,GACzBhqB,OAAOwmB,QAAQC,UAAU,GAAI,GAAIljB,SAASsjB,SAAW,IAAMP,GAC3DtmB,OAAOuP,cAAc,IAAIxI,MAAM,aAGjC,SAASmjB,oBAAoBb,EAAWc,GACtC,IAAM7D,EAAS,IAAII,gBAAgB1mB,OAAOuD,SAASojB,QACnDL,EAAM,OAAQ+C,GAAW,IACIe,EADJC,EAAAX,2BACNS,GAAU,IAA7B,IAAAE,EAAAriB,MAAAoiB,EAAAC,EAAAnlB,KAAA0kB,MAA+B,CAAA,IAAtBU,EAAMF,EAAA/D,MACbC,EAAO5K,OAAO2N,EAAWiB,IAE1B,MAAAR,GAAAO,EAAAplB,EAAA6kB,GAAA,QAAAO,EAAA9iB,IACDvH,OAAOwmB,QAAQC,UAAU,GAAI,GAAIljB,SAASsjB,SAAW,IAAMP,GAC3DtmB,OAAOuP,cAAc,IAAIxI,MAAM,aAGjC,SAASwjB,wBAAwBlB,GAE/B,OADe,IAAI3C,gBAAgB1mB,OAAOuD,SAAS/C,KAAKgqB,OAAO,IACjD3M,IAAIwL,GAoBpB,SAASoB,eACP,IAAItnB,EAASyiB,EAAE,kCAAkCvJ,IAAI,UACrDuJ,EAAE,kCAAkC8E,KAClC,6DAEF9E,EAAE,kCAAkCvJ,IAAI,SAAUlZ,GAGpD,SAASwnB,aACP,IAAIrE,EAAS,IAAII,gBAAgBnlB,SAASgC,SAASojB,QAEnD8D,eAGA,IAAIG,EAAQtE,EAAOzI,IAAI,KAEnBgN,EAAON,wBAAwB,KAC/BO,EAAYxE,EAAOzI,IAAI,aAEvBkN,EAAgBnF,EAAE,eAAeoE,MACjCgB,EAAepF,EAAE,sBAAsBoE,MACvCiB,EAAuBrF,EAAE,cAAcoE,MAGvCe,KAFJF,EAAOD,IAGLhF,EAAE,eAAeoE,IAAIa,GAEnBG,IAAiBH,GACnBjF,EAAE,sBAAsBoE,IAAIa,GAGZ,OAAdC,IACFA,EAAY,IAEVG,IAAyBH,GAC3BlF,EAAE,cAAcoE,IAAIc,GAItB,IAAII,EAAY5E,EAAOzI,IAAI,aAI3B+H,EAAE,2BAA2B5Y,KAAK,WAAW,GAC7C4Y,EAAE,uBAAuB7I,SAAS,UAGV,iBAAbmO,GAAuC,KAAdA,GAClCtF,EAAE,iCAAmCsF,EAAY,KAAKlO,YAAY,UAClE4I,EAAE,2BAA2B5Y,KAAK,WAAW,GAC7C4Y,EAAE,iCAAmCsF,EAAY,KAC9C7N,QACArQ,KAAK,WAAW,KAGnB4Y,EAAE,2BAA2B5Y,KAAK,WAAW,GAC7C4Y,EAAE,2BAA2BvI,QAAQrQ,KAAK,WAAW,IAOvD,IAAIme,EAAgB7B,yBAAyB,mBAE7C,GAAI3gB,MAAMyiB,QAAQD,IAAuC,KAArBA,EAAc,GAAW,CAE3DvF,EAAE,iCAAiC5Y,KAAK,WAAW,GAAO,IACdqe,EADcC,EAAA5B,2BAC3ByB,GAAa,IAA5C,IAAAG,EAAAtjB,MAAAqjB,EAAAC,EAAApmB,KAAA0kB,MAA8C,CAAA,IAArC2B,EAAkBF,EAAAhF,MACzBT,EAAE,uCAAyC2F,EAAqB,KAC7DlO,QACArQ,KAAK,WAAW,IACpB,MAAA8c,GAAAwB,EAAArmB,EAAA6kB,GAAA,QAAAwB,EAAA/jB,SAE4B,iBAAlB4jB,GAAgD,KAAlBA,GACvCvF,EAAE,iCAAiC5Y,KAAK,WAAW,GACnD4Y,EAAE,uCAAyCuF,EAAgB,KACxD9N,QACArQ,KAAK,WAAW,IAGnB4Y,EAAE,iCAAiC5Y,KAAK,WAAW,GASrDwe,gBAIJ,SAASC,kBACP7F,EAAE,6BAA6B8E,KAC7B,0EAIJ,SAASc,gBACe,IAAlBtC,cAEFwC,mBAEAxC,cAAgB7oB,WAAW,WACzB6oB,cAAgB,EAEhBwC,oBACC,KAqEP,SAASA,mBAEP,IAAIC,EAAa/F,EAAE,sBAAsBoE,MACzCpE,EAAE,eAAeoE,IAAI2B,GAErB3rB,OAAO4rB,UAAY5rB,OAAO4rB,WAAa,GACvC5rB,OAAO4rB,UAAUhgB,KAAK,CACpBwH,MAAO,SACPyY,WAAYF,IAGd,IAAIG,EAAYlG,EAAE,0BAA0BmG,YACxCC,EAAU5C,oBAAoB,SAoClC,OAnCI4C,EAAU,IACZA,EAAU,GAEZF,EAAYA,EAAY,UAAYE,EAcpCpG,EAAEwB,KAAK,CACLjb,KAAM,OACNvL,IAAKmoB,WACLtP,KAAMqS,EACNnE,QAAS,SAACsE,GAERrG,EAAE,6BAA6B8E,KAAKuB,GACpCrG,EAAE,kCAAkCvJ,IAAI,SAAU,IAClDhc,WAAW,WACLL,OAAOksB,cAAsC,IAAvBlsB,OAAOksB,cAC/BlsB,OAAOksB,aAAc,EAErBtG,EAAE,cAAc5S,UAAU,KAE3B,QAIA,EAOT,SAASmZ,eAEP,IAAMC,EAAc,IAAIC,qBAAqB,SAAC1C,GAC5CA,EAAQtf,QAAQ,SAACiiB,GACXA,EAAMC,eACRD,EAAM5d,OAAOX,UAAUM,IAAI,WAE3Bie,EAAM5d,OAAOX,UAAUK,OAAO,eAI9BpF,EAAMzH,SAAS8L,cAAc,0BAC/BrE,GACFojB,EAAY9qB,QAAQ0H,GAKtB,IAAMwjB,EAASjrB,SAASyF,iBAAiB,aACnC5F,EAAW,IAAIirB,qBAAqB,SAAC1C,GACzCA,EAAQtf,QAAQ,SAACiiB,GACf,IAEMG,EACAC,EACAC,EAGEC,EACAC,EAGAC,EAwBAC,EAGAF,EAtCJP,EAAMC,gBAGJG,EAAgB,KADhBD,EAAaH,EAAM5d,OAAO7I,QAAQmnB,WAElCL,EAA6B,EAAbF,EAGdG,EAAUN,EAAM5d,OAAO7I,QAAQ+mB,QAC/BC,EAAiBtrB,SAASyI,eAAe4iB,GAGzCE,EAAc,CAClB3gB,KAAM,WACNsN,KAAM,CACJwT,SAAU,CACR,CACExT,KAAM,CAACiT,EAAeD,GACtBS,gBAAiB,CAAC,UAAW,cAInCtgB,QAAS,CACPugB,SAAU,CAAEC,SAAS,GACrBC,MAAO,CAAEC,KAAM,MACfC,OAAQ,GACRC,SAAUb,EACVc,YAAa,EACbC,UAAW,CACTC,MAAO,OAIT3tB,OAAO4tB,eAAeC,IAAIhB,IAC5B7sB,OAAO4tB,eAAe/P,IAAIgP,GAAgB3N,UAEtC6N,EAAQ,IAAIe,MAAMjB,EAAgBC,GACxC9sB,OAAO4tB,eAAehH,IAAIiG,EAAgBE,KAEpCF,EAAiBP,EAAM5d,OAAOrB,cAAc,YAC9CrN,OAAO4tB,eAAeC,IAAIhB,IAC5B7sB,OAAO4tB,eAAe/P,IAAIgP,GAAgB3N,eAKlDsN,EAAOniB,QAAQ,SAAC0iB,GACd3rB,EAASE,QAAQyrB,KAEnB/sB,OAAO4tB,eAAiB,IAAIG,QA+C9B,SAASC,iBAAiBhf,EAAMif,GA0C9B,IAAMC,EAAUlf,GARhB,SAAemf,GAEb,IADA,IAAMC,EAASD,EAAS/kB,WAAWvG,MAAM,IAChCnC,EAAI,EAAGA,EAAI0tB,EAAO3rB,OAAQ/B,KAzBrC,SAAkB2tB,EAAOC,GACvB,IAAMC,EAAkB5lB,MAAM,IAC3ByB,KAAK,GACLvH,MAAM,GACNmG,IAAI,SAACnF,EAAGsK,GAAC,MAAA,SAAA9E,OAAc8E,EAAC,aACxB/D,KAAK,IAER8jB,EAAQM,mBACN,YAAW,2DAAAnlB,OACgDglB,EAAK,kBAAAhlB,OAC1DklB,EAAe,wBAGvB,IAAME,EAAaP,EAAQQ,iBAE3BruB,WACE,WACEouB,EAAWroB,UAAY,WAEzBkoB,EAAQ,EAAI,KAOZK,CAAS,KAAK,GAEhBtuB,WAAW,WAAA,OAnCS+tB,EAmCUA,EAlC9BF,EAAQlnB,iBAAiB,oBAAoBqD,QAAQ,SAACukB,EAAMluB,GAC1DkuB,EAAK3qB,MAAM6f,UAAS,eAAAza,OAAkB,IAAM7D,SAAS4oB,EAAO1tB,IAAG,aAEjEwtB,EAAQjqB,MAAMf,MAAQ,eAJxB,IAAsBkrB,GAmCmB,KACvCS,EAGFC,CAAMb,GAKR,SAASc,iBACkBxtB,SAASyF,iBAChC,8BAIAmlB,eAGsB5qB,SAASyF,iBAAiB,2BAAlD,IAyBIgoB,EAAeztB,SAASyF,iBAAiB,iBACzCgoB,GAEFrG,EADsBhgB,MAAMsmB,KAAKD,GACTA,EAAc,EAAG,KAG3C,IAAIE,EAAa3tB,SAASyF,iBAAiB,mBACvCkoB,GAEFvG,EADoBhgB,MAAMsmB,KAAKC,GACTA,EAAY,EAAG,KAEvC,IAAIC,EAAc5tB,SAASyF,iBAAiB,qBAM5C,SAAS2hB,EAAOyG,EAAOlB,EAASP,EAAO0B,GACrCD,EAAM/kB,QAAQ,SAAU6jB,GACP,IAAIoB,SAAS,CAC1BpB,QAASA,EACTqB,QAAS,WACPrB,EAAQjqB,MAAMypB,UAAY,0BAC1BQ,EAAQjqB,MAAMurB,eAAiB7B,EAAQ,IACvCO,EAAQjqB,MAAMmb,QAAU,KAE1BiQ,OAAQA,EAAS,QAdnBF,GAEFxG,EADqBhgB,MAAMsmB,KAAKE,GACTA,EAAa,EAAG,KAqB3C,SAASM,iBACP,IAAMC,EAAoBnuB,SAASyI,eAAe,iBAUlDhK,OAAOC,iBAAiB,SATL,WACjB,IAAI0vB,EAAI3vB,OAAO4vB,QAGbF,EAAkBtpB,UADZ,KAAJupB,EAC4B,gBAEA,kBAIlC,IAAMzD,EAAc,WAClB,IAAMlgB,EAAIzK,SAASC,gBAAgBwR,WAAazR,SAASqC,KAAKoP,UACtD,EAAJhH,IACFhM,OAAO6vB,sBAAsB3D,GAC7BlsB,OAAO8vB,SAAS,EAAG9jB,EAAIA,EAAI,KAG/B0jB,EAAkBK,QAAU,SAAU9qB,GACpCA,EAAEkL,iBACF+b,KAxfJlsB,OAAOC,iBAAiB,WAAY,WAKhCkpB,kBAFwB,IAAtBA,mBACF/oB,aAAa+oB,mBACO9oB,WAAW,WAE7BsqB,cACC,OAEHvqB,aAAa+oB,mBACO9oB,WAAW,WAC7BsqB,cACC,QAwHPje,iBAAiBnL,SAASyI,eAAe,sBAAuB,CAC9DmD,kBAAmB,IACnBC,YAAa,iBAMftF,UAKiC,mBAAtBR,mBACT/F,SAAStB,iBAAiB,mBAAoB,SAAUgF,GAEtD,IAAIwD,EAAKxD,EAAEyJ,OAEXpH,oBACAmB,EAAGxI,iBAAiB,OAAQ,WAC1BqH,wBAMN+L,OAAO,KACJnK,OAAO,WACN,OAAO7B,KAAK2oB,UAAY3oB,KAAK2oB,WAAazsB,SAASysB,WAEpDjT,SAAS,YACTjC,KAAK,SAAU,UACfA,KAAK,mBAAoB,mBACzB5R,OAAO,WACN,OAAO0c,EAAEve,MAAMwmB,IAAI,OAAOprB,SAE3Bsa,SAAS,gBACTC,YAAY,YAGf3J,OAAO,WAELA,OAAO,2BAA2BqJ,MAAM,CACtC9G,aAAc,EACdC,eAAgB,EAChBnC,aAAc,uBACdK,UACE,mIACFD,UACE,mIACFyB,WAAY,CACV,CACEgN,WAAY,IACZzV,SAAU,CACR8I,aAAc,EACdC,eAAgB,SAmS1BkZ,iBA4BAU,iBAMA,IAAMQ,WAA8C,MAAjC1uB,SAASC,gBAAgB0uB,KAAe,OAAS,SAE9DC,UAC6B,MAAjC5uB,SAASC,gBAAgB0uB,KAAe,aAAe,eAEnDE,SAC6B,MAAjC7uB,SAASC,gBAAgB0uB,KACrB,qCACA,+BACAG,UAC6B,MAAjC9uB,SAASC,gBAAgB0uB,KACrB,kDACA,kDAsSN,SAASI,uBACP,IAAIC,EAAU3K,EACZ,gEAGFqB,QAAQC,IAAIqJ,EAAQ9tB,QAEhB8tB,EAAQ9tB,QACV8tB,EAAQ5U,KAAK,WACXiK,EAAEve,MAAM2F,KAAK,WAAW,KCh4B9B,SAASwjB,kBACPjvB,SAASqC,KAAKmK,UAAUK,OAAO,mBAC/BpO,OAAOC,iBAAiB,UAAWwwB,gBACnCzwB,OAAO6B,oBAAoB,YAAa2uB,iBAG1C,SAASC,eAAexrB,GACR,QAAVA,EAAEmhB,MACJ7kB,SAASqC,KAAKmK,UAAUM,IAAI,mBAC5BrO,OAAO6B,oBAAoB,UAAW4uB,gBACtCzwB,OAAOC,iBAAiB,YAAauwB,kBDykBzCnd,OAAO9R,UAAUoK,MAAM,SAAUia,GAC/BmD,WAAavB,aAAaC,QAI1B7B,EAAE,uBAAuBnG,GAAG,QAAS,SAAUxa,GAC7CA,EAAEkL,iBACFyV,EAAEve,MAAMkH,SAASwX,YAAY,eACzBH,EAAEve,MAAMkH,SAASgS,SAAS,gBAC5BqF,EAAEve,MAAMiN,KAAK6b,WACbvK,EAAE,qBAAqBvI,QAAQ+E,SAE/BwD,EAAEve,MAAMiN,KAAK2b,cAIjBrK,EAAE,oDAAoDnG,GACpD,SACA,SAAUxa,GACR,GAAI2gB,EAAE,qBAAqBoE,MAAMvnB,OAAS,EAOxC,MANqC,KAAjCmjB,EAAE,qBAAqBoE,MACzBpE,EAAE,qBAAqB5Y,KAAK,cAAeojB,UAE3CM,MAAML,WAERprB,EAAEkL,kBACK,EAEDyV,EAAE,qBAAqBoE,MAA/B,IACIzI,EAASqE,EAAE,8BAA8B9K,KAAK,UAElD8K,EAAE,8BAA8B9K,KAAK,SAAUyG,KAKnDqE,EAAE,2BAA2BnG,GAAG,QAAS,WACvCmG,EAAE,qBAAqBoE,IAAI,IAAI5H,UAiBjCwD,EAAE,qBAAqBnG,GAAG,SAAU,SAAUxa,GAC5CA,EAAEkL,iBACF,IAAInI,EAAI4d,EAAE,sBAAsBoE,MAOhC,MANU,cAANhiB,QAA2B5E,IAAN4E,IACvBA,EAAI,IAGN+hB,oBAAoB,IAAK/hB,GACzB+hB,oBAAoB,QAAS,MACtB,IAOqB,EAA1BnE,EAAE,eAAenjB,SACnBkoB,aAQA/E,EAAE,4BAA4BnG,GAAG,QAAS,WACxCmG,EAAE,sBAAsBoE,IAAI,IAAI5H,UAGlCwD,EAAE,YAAYnG,GAAG,QAAS,yBAA0B,SAAUxa,GAM5D,OALAA,EAAEkL,iBACF4Z,oBAAoB,QAAS,KAC7BA,oBAAoB,YAAa,WAG1B,IAITnE,EAAE,YAAYnG,GAAG,QAAS,0BAA2B,SAAUxa,GAI7D,OAHAA,EAAEkL,iBACF4Z,oBAAoB,QAAS,KAC7BA,oBAAoB,YAAa,KAC1B,IAITnE,EAAE,YAAYnG,GAAG,QAAS,iBAAkB,SAAUxa,GACpDA,EAAEkL,iBACF,IAAIwgB,EAAK/K,EAAEve,MAAMyT,KAAK,QAElB8V,EADiB,IAAIlK,gBAAgBiK,GACX9S,IAAI,SAKlC,MAJiB,cAAb+S,QAAyCxtB,IAAbwtB,GAC9B7G,oBAAoB,QAAS6G,KAE/B5wB,OAAOksB,aAAc,KAKvBtG,EAAE,2BAA2BnG,GAAG,QAAS,WAEvCsK,oBAAoB,YADJnE,EAAEve,MAAM2iB,OAExBD,oBAAoB,kBAAmB,IACvCA,oBAAoB,QAAS,OAe/BnE,EAAE,mBAAmBnG,GAAG,QAAS,0BAA2B,SAAUxa,GAC3C2gB,EAAEve,MAAM2iB,MAIjCD,oBAAoB,QAAS,KAC7B,IAAI8G,EAAoB,GACxBjL,EAAE,mCAAmCjK,KAAK,WACxCkV,EAAkBjlB,KAAKga,EAAEve,MAAM2iB,SAGjCE,oBAAoB,kBAAmB2G,KAKzCjL,EAAE,YAAYnG,GAAG,QAAS,oBAAqB,SAAUxa,GACvDA,EAAEkL,iBACF4Z,oBAAoB,QAAS,KAC7B,IAAIe,EAAYlF,EAAEve,MAAMyT,KAAK,WAa7B,OAZIgQ,IAQFf,oBAAoB,IAAK,IACzBA,oBAAoB,YAAae,GACjCf,oBAAoB,YAAa,aAE5B,KAOXnE,EAAE,aAAanG,GAAG,QAAS,QAAS,SAAUxa,GAC5CA,EAAE2J,kBACFgX,EAAEve,MAAMyT,KAAK,WAAY8K,EAAEve,MAAMyT,KAAK,YACtC8K,EAAE3gB,EAAEyJ,QAAQ2P,QAAQ,SAAS0H,YAAY,cAG3CH,EAAE,iBAAiBnG,GAAG,iBAAkB,SAAUxa,GAChDA,EAAEkL,iBAEAyV,EAAE,SAASxH,GAAG,aACdwH,EAAE,SAASxH,GAAG,aACdwH,EAAE,SAASxH,GAAG,aACdwH,EAAE,SAASxH,GAAG,aACdwH,EAAE,SAASxH,GAAG,aACdwH,EAAE,SAASxH,GAAG,aAEdwH,EAAE,2BAA2B/H,IAAI,GAAG5Z,MAAMkC,QAAU,OACpDyf,EAAE,qBAAqB/H,IAAI,GAAG5Z,MAAMkC,QAAU,SAE9Cyf,EAAE,sBAAsB/H,IAAI,GAAG5Z,MAAMkC,QAAU,UAwCnDyf,EApCA,WAEE,IAAMkL,EAAQlL,EAAE,eAOhB,SAASmL,EAAiB3d,GAExBA,EAAMjD,iBAMN,OAAOyV,EAAEwB,KAAK,CACZ3N,KAAMqX,EAAM/E,YACZpE,QAASqJ,EACT7kB,KAAM,OACNvL,IAPc,sDAYlB,SAASowB,EAAqBpJ,GAC5BhC,EAAE,qBAAqB/H,IAAI,GAAG5Z,MAAMkC,QAAU,OAC9Cyf,EAAE,wBAAwB/H,IAAI,GAAG5Z,MAAMkC,QAAU,QAtBjD2qB,EAAMrR,GAAG,SAAUsR,KAgCvBnL,EAAE,oBAAoBnG,GAAG,QAAS,SAAUrM,GAC1C/L,KAAKrB,WAAW+H,UAAUY,OAAO,QACjCtH,KAAKjF,aACH,gBACuC,UAAvCiF,KAAK1E,aAAa,oBAItBijB,EAAE,qBAAqBnG,GAAG,QAAS,SAAUrM,GAC3CA,EAAMjD,iBACNyV,EAAE,qBAAqB/H,IAAI,GAAG5Z,MAAMkC,QAAU,OAC9Cyf,EAAE,2BAA2B/H,IAAI,GAAG5Z,MAAMkC,QAAU,UAIZ,EAAtCyf,EAAE,2BAA2BnjB,QAC/BmjB,EAAE,2BAA2BnG,GAAG,SAAU,WACxC,IAAInQ,EAAOsW,EAAEve,MAAM2iB,MACnBzoB,SAASgC,SAAS9C,KAAO6O,IAgB7BsW,EAZA,WAEuBA,EAAE,mCAERjK,KAAK,WACbiK,EAAEve,MAAMyT,KAAK,WAChB8K,EAAEve,MAAM0V,SAAS,kBACjB6I,EAAE,6BAA6BqL,QAAQrL,EAAEve,YAiB/Cue,EAVA,WAEkBA,EAAE,iCAERjK,KAAK,SAAU4C,GACvB,IAAI2S,EAAetL,EAAC,2BAAAvc,OAA4BkV,IAChDqH,EAAEve,MAAMqU,OAAOwV,OAanBtL,EANA,WACE,IACI7B,EADWxiB,SAAS8L,cAAc,oBACjBgF,WAAU,GAC/B0R,EAAMjZ,gBAAgB,MACtBvJ,SAASyI,eAAe,wBAAwBjG,UAAYggB,EAAMhgB,cAmBtE6hB,EAAE0K,sBAGF,SAAE7qB,GACS,SAAL0rB,EAAMnsB,GAAC,OAAKS,EAAE2rB,eAAepsB,GACxB,SAALqsB,EAAMpsB,GAAC,OAAKQ,EAAE3B,cAAcmB,GAChCQ,EAAEuB,iBA6BS,gCA7BWqD,QAAQ,SAACpF,GAC7B,IAAMN,EAAI0sB,EAAG,KACX1nB,EAAM0nB,EAAG,OACT5gB,EAAI4gB,EAAG,KACPrpB,EAAI/C,EAAEY,QAAQyrB,eACdC,EAAK,2BAA2B9mB,KAAKzC,GACjC,cACA,8BAA8ByC,KAAKzC,GACnC,QACA,oCAAoCyC,KAAKzC,GACzC,UACA,+BAA+ByC,KAAKzC,GACpC,kBACA5E,EACDmuB,IACL5nB,EAAI5F,UACF,oDAAA,gBAAAsF,OACgBpE,EAAE9B,OAAM,+BAAAkG,OAA8BpE,EAAE/B,MAAK,oBAC7D,2QAIFyG,EAAIoE,UAAUM,IAAG,wBAAAhF,OAOwB,cANzC1E,EAAE6sB,YAAW,6BACb7sB,EAAElE,KAAO,+BACTgQ,EAAEiL,OAAOyV,EAAG,aAAcxsB,EAAGwsB,EAAE,eAAA9nB,OAAgBkoB,EAAE,iBACjD5nB,EAAI+R,OAAOjL,GACXxL,EAAEe,WAAW5B,aAAauF,EAAK1E,MA9BnC,CAgCG1D,UCz5BHvB,OAAOC,iBAAiB,UAAWwwB", "file": "main.js", "sourcesContent": ["/*!\n * @copyright Copyright (c) 2017 IcoMoon.io\n * @license   Licensed under MIT license\n *            See https://github.com/Keyamoon/svgxuse\n * @version   1.2.6\n */\n/*jslint browser: true */\n/*global XDomainRequest, MutationObserver, window */\n(function () {\n    \"use strict\";\n    if (typeof window !== \"undefined\" && window.addEventListener) {\n        var cache = Object.create(null); // holds xhr objects to prevent multiple requests\n        var checkUseElems;\n        var tid; // timeout id\n        var debouncedCheck = function () {\n            clearTimeout(tid);\n            tid = setTimeout(checkUseElems, 100);\n        };\n        var unobserveChanges = function () {\n            return;\n        };\n        var observeChanges = function () {\n            var observer;\n            window.addEventListener(\"resize\", debouncedCheck, false);\n            window.addEventListener(\"orientationchange\", debouncedCheck, false);\n            if (window.MutationObserver) {\n                observer = new MutationObserver(debouncedCheck);\n                observer.observe(document.documentElement, {\n                    childList: true,\n                    subtree: true,\n                    attributes: true\n                });\n                unobserveChanges = function () {\n                    try {\n                        observer.disconnect();\n                        window.removeEventListener(\"resize\", debouncedCheck, false);\n                        window.removeEventListener(\"orientationchange\", debouncedCheck, false);\n                    } catch (ignore) {}\n                };\n            } else {\n                document.documentElement.addEventListener(\"DOMSubtreeModified\", debouncedCheck, false);\n                unobserveChanges = function () {\n                    document.documentElement.removeEventListener(\"DOMSubtreeModified\", debouncedCheck, false);\n                    window.removeEventListener(\"resize\", debouncedCheck, false);\n                    window.removeEventListener(\"orientationchange\", debouncedCheck, false);\n                };\n            }\n        };\n        var createRequest = function (url) {\n            // In IE 9, cross origin requests can only be sent using XDomainRequest.\n            // XDomainRequest would fail if CORS headers are not set.\n            // Therefore, XDomainRequest should only be used with cross origin requests.\n            function getOrigin(loc) {\n                var a;\n                if (loc.protocol !== undefined) {\n                    a = loc;\n                } else {\n                    a = document.createElement(\"a\");\n                    a.href = loc;\n                }\n                return a.protocol.replace(/:/g, \"\") + a.host;\n            }\n            var Request;\n            var origin;\n            var origin2;\n            if (window.XMLHttpRequest) {\n                Request = new XMLHttpRequest();\n                origin = getOrigin(location);\n                origin2 = getOrigin(url);\n                if (Request.withCredentials === undefined && origin2 !== \"\" && origin2 !== origin) {\n                    Request = XDomainRequest || undefined;\n                } else {\n                    Request = XMLHttpRequest;\n                }\n            }\n            return Request;\n        };\n        var xlinkNS = \"http://www.w3.org/1999/xlink\";\n        checkUseElems = function () {\n            var base;\n            var bcr;\n            var fallback = \"\"; // optional fallback URL in case no base path to SVG file was given and no symbol definition was found.\n            var hash;\n            var href;\n            var i;\n            var inProgressCount = 0;\n            var isHidden;\n            var Request;\n            var url;\n            var uses;\n            var xhr;\n            function observeIfDone() {\n                // If done with making changes, start watching for chagnes in DOM again\n                inProgressCount -= 1;\n                if (inProgressCount === 0) { // if all xhrs were resolved\n                    unobserveChanges(); // make sure to remove old handlers\n                    observeChanges(); // watch for changes to DOM\n                }\n            }\n            function attrUpdateFunc(spec) {\n                return function () {\n                    if (cache[spec.base] !== true) {\n                        spec.useEl.setAttributeNS(xlinkNS, \"xlink:href\", \"#\" + spec.hash);\n                        if (spec.useEl.hasAttribute(\"href\")) {\n                            spec.useEl.setAttribute(\"href\", \"#\" + spec.hash);\n                        }\n                    }\n                };\n            }\n            function onloadFunc(xhr) {\n                return function () {\n                    var body = document.body;\n                    var x = document.createElement(\"x\");\n                    var svg;\n                    xhr.onload = null;\n                    x.innerHTML = xhr.responseText;\n                    svg = x.getElementsByTagName(\"svg\")[0];\n                    if (svg) {\n                        svg.setAttribute(\"aria-hidden\", \"true\");\n                        svg.style.position = \"absolute\";\n                        svg.style.width = 0;\n                        svg.style.height = 0;\n                        svg.style.overflow = \"hidden\";\n                        body.insertBefore(svg, body.firstChild);\n                    }\n                    observeIfDone();\n                };\n            }\n            function onErrorTimeout(xhr) {\n                return function () {\n                    xhr.onerror = null;\n                    xhr.ontimeout = null;\n                    observeIfDone();\n                };\n            }\n            unobserveChanges(); // stop watching for changes to DOM\n            // find all use elements\n            uses = document.getElementsByTagName(\"use\");\n            for (i = 0; i < uses.length; i += 1) {\n                try {\n                    bcr = uses[i].getBoundingClientRect();\n                } catch (ignore) {\n                    // failed to get bounding rectangle of the use element\n                    bcr = false;\n                }\n                href = uses[i].getAttribute(\"href\")\n                        || uses[i].getAttributeNS(xlinkNS, \"href\")\n                        || uses[i].getAttribute(\"xlink:href\");\n                if (href && href.split) {\n                    url = href.split(\"#\");\n                } else {\n                    url = [\"\", \"\"];\n                }\n                base = url[0];\n                hash = url[1];\n                isHidden = bcr && bcr.left === 0 && bcr.right === 0 && bcr.top === 0 && bcr.bottom === 0;\n                if (bcr && bcr.width === 0 && bcr.height === 0 && !isHidden) {\n                    // the use element is empty\n                    // if there is a reference to an external SVG, try to fetch it\n                    // use the optional fallback URL if there is no reference to an external SVG\n                    if (fallback && !base.length && hash && !document.getElementById(hash)) {\n                        base = fallback;\n                    }\n                    if (uses[i].hasAttribute(\"href\")) {\n                        uses[i].setAttributeNS(xlinkNS, \"xlink:href\", href);\n                    }\n                    if (base.length) {\n                        // schedule updating xlink:href\n                        xhr = cache[base];\n                        if (xhr !== true) {\n                            // true signifies that prepending the SVG was not required\n                            setTimeout(attrUpdateFunc({\n                                useEl: uses[i],\n                                base: base,\n                                hash: hash\n                            }), 0);\n                        }\n                        if (xhr === undefined) {\n                            Request = createRequest(base);\n                            if (Request !== undefined) {\n                                xhr = new Request();\n                                cache[base] = xhr;\n                                xhr.onload = onloadFunc(xhr);\n                                xhr.onerror = onErrorTimeout(xhr);\n                                xhr.ontimeout = onErrorTimeout(xhr);\n                                xhr.open(\"GET\", base);\n                                xhr.send();\n                                inProgressCount += 1;\n                            }\n                        }\n                    }\n                } else {\n                    if (!isHidden) {\n                        if (cache[base] === undefined) {\n                            // remember this URL if the use element was not empty and no request was sent\n                            cache[base] = true;\n                        } else if (cache[base].onload) {\n                            // if it turns out that prepending the SVG is not necessary,\n                            // abort the in-progress xhr.\n                            cache[base].abort();\n                            delete cache[base].onload;\n                            cache[base] = true;\n                        }\n                    } else if (base.length && cache[base]) {\n                        setTimeout(attrUpdateFunc({\n                            useEl: uses[i],\n                            base: base,\n                            hash: hash\n                        }), 0);\n                    }\n                }\n            }\n            uses = \"\";\n            inProgressCount += 1;\n            observeIfDone();\n        };\n        var winLoad;\n        winLoad = function () {\n            window.removeEventListener(\"load\", winLoad, false); // to prevent memory leaks\n            tid = setTimeout(checkUseElems, 0);\n        };\n        if (document.readyState !== \"complete\") {\n            // The load event fires when all resources have finished loading, which allows detecting whether SVG use elements are empty.\n            window.addEventListener(\"load\", winLoad, false);\n        } else {\n            // No need to add a listener if the document is already loaded, initialize immediately.\n            winLoad();\n        }\n    }\n}());\n", "!function(){\"use strict\";if(\"undefined\"!=typeof window){var t=window.navigator.userAgent.match(/Edge\\/(\\d{2})\\./),e=t?parseInt(t[1],10):null,n=!!e&&(16<=e&&e<=18);if(!(\"objectFit\"in document.documentElement.style!=!1)||n){var o=function(t,e,i){var n,o,l,a,d;if((i=i.split(\" \")).length<2&&(i[1]=i[0]),\"x\"===t)n=i[0],o=i[1],l=\"left\",a=\"right\",d=e.clientWidth;else{if(\"y\"!==t)return;n=i[1],o=i[0],l=\"top\",a=\"bottom\",d=e.clientHeight}if(n!==l&&o!==l){if(n!==a&&o!==a)return\"center\"===n||\"50%\"===n?(e.style[l]=\"50%\",void(e.style[\"margin-\"+l]=d/-2+\"px\")):void(0<=n.indexOf(\"%\")?(n=parseInt(n,10))<50?(e.style[l]=n+\"%\",e.style[\"margin-\"+l]=d*(n/-100)+\"px\"):(n=100-n,e.style[a]=n+\"%\",e.style[\"margin-\"+a]=d*(n/-100)+\"px\"):e.style[l]=n);e.style[a]=\"0\"}else e.style[l]=\"0\"},l=function(t){var e=t.dataset?t.dataset.objectFit:t.getAttribute(\"data-object-fit\"),i=t.dataset?t.dataset.objectPosition:t.getAttribute(\"data-object-position\");e=e||\"cover\",i=i||\"50% 50%\";var n=t.parentNode;return function(t){var e=window.getComputedStyle(t,null),i=e.getPropertyValue(\"position\"),n=e.getPropertyValue(\"overflow\"),o=e.getPropertyValue(\"display\");i&&\"static\"!==i||(t.style.position=\"relative\"),\"hidden\"!==n&&(t.style.overflow=\"hidden\"),o&&\"inline\"!==o||(t.style.display=\"block\"),0===t.clientHeight&&(t.style.height=\"100%\"),-1===t.className.indexOf(\"object-fit-polyfill\")&&(t.className=t.className+\" object-fit-polyfill\")}(n),function(t){var e=window.getComputedStyle(t,null),i={\"max-width\":\"none\",\"max-height\":\"none\",\"min-width\":\"0px\",\"min-height\":\"0px\",top:\"auto\",right:\"auto\",bottom:\"auto\",left:\"auto\",\"margin-top\":\"0px\",\"margin-right\":\"0px\",\"margin-bottom\":\"0px\",\"margin-left\":\"0px\"};for(var n in i)e.getPropertyValue(n)!==i[n]&&(t.style[n]=i[n])}(t),t.style.position=\"absolute\",t.style.width=\"auto\",t.style.height=\"auto\",\"scale-down\"===e&&(e=t.clientWidth<n.clientWidth&&t.clientHeight<n.clientHeight?\"none\":\"contain\"),\"none\"===e?(o(\"x\",t,i),void o(\"y\",t,i)):\"fill\"===e?(t.style.width=\"100%\",t.style.height=\"100%\",o(\"x\",t,i),void o(\"y\",t,i)):(t.style.height=\"100%\",void(\"cover\"===e&&t.clientWidth>n.clientWidth||\"contain\"===e&&t.clientWidth<n.clientWidth?(t.style.top=\"0\",t.style.marginTop=\"0\",o(\"x\",t,i)):(t.style.width=\"100%\",t.style.height=\"auto\",t.style.left=\"0\",t.style.marginLeft=\"0\",o(\"y\",t,i))))},i=function(t){if(void 0===t||t instanceof Event)t=document.querySelectorAll(\"[data-object-fit]\");else if(t&&t.nodeName)t=[t];else{if(\"object\"!=typeof t||!t.length||!t[0].nodeName)return!1;t=t}for(var e=0;e<t.length;e++)if(t[e].nodeName){var i=t[e].nodeName.toLowerCase();if(\"img\"===i){if(n)continue;t[e].complete?l(t[e]):t[e].addEventListener(\"load\",function(){l(this)})}else\"video\"===i?0<t[e].readyState?l(t[e]):t[e].addEventListener(\"loadedmetadata\",function(){l(this)}):l(t[e])}return!0};\"loading\"===document.readyState?document.addEventListener(\"DOMContentLoaded\",i):i(),window.addEventListener(\"resize\",i),window.objectFitPolyfill=i}else window.objectFitPolyfill=function(){return!1}}}();", "(function(f){if(typeof exports===\"object\"&&typeof module!==\"undefined\"){module.exports=f()}else if(typeof define===\"function\"&&define.amd){define([],f)}else{var g;if(typeof window!==\"undefined\"){g=window}else if(typeof global!==\"undefined\"){g=global}else if(typeof self!==\"undefined\"){g=self}else{g=this}g.fitvids = f()}})(function(){var define,module,exports;return (function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require==\"function\"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error(\"Cannot find module '\"+o+\"'\");throw f.code=\"MODULE_NOT_FOUND\",f}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require==\"function\"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(require,module,exports){\nvar selectors = [\n  'iframe[src*=\"player.vimeo.com\"]',\n  'iframe[src*=\"youtube.com\"]',\n  'iframe[src*=\"youtube-nocookie.com\"]',\n  'iframe[src*=\"kickstarter.com\"][src*=\"video.html\"]',\n  \"object\"\n];\n\nvar css =\n  \".fluid-width-video-wrapper{width:100%;position:relative;padding:0;}.fluid-width-video-wrapper iframe,.fluid-width-video-wrapper object,.fluid-width-video-wrapper embed {position:absolute;top:0;left:0;width:100%;height:100%;}\";\n\nmodule.exports = function(parentSelector, opts) {\n  parentSelector = parentSelector || \"body\";\n  opts = opts || {};\n\n  if (isObject(parentSelector)) {\n    opts = parentSelector;\n    parentSelector = \"body\";\n  }\n\n  opts.ignore = opts.ignore || \"\";\n  opts.players = opts.players || \"\";\n\n  var containers = queryAll(parentSelector);\n  if (!hasLength(containers)) return;\n\n  if (!document.getElementById(\"fit-vids-style\")) {\n    var head = document.head || document.getElementsByTagName(\"head\")[0];\n    head.appendChild(styles());\n  }\n\n  var custom = toSelectorArray(opts.players);\n  var ignored = toSelectorArray(opts.ignore);\n  var ignoredSelector = ignored.length > 0 ? ignored.join() : null;\n  var selector = selectors.concat(custom).join();\n\n  if (!hasLength(selector)) {\n    return;\n  }\n\n  containers.forEach(function(container) {\n    var videos = queryAll(container, selector);\n\n    videos.forEach(function(video) {\n      if (ignoredSelector && video.matches(ignoredSelector)) {\n        return;\n      }\n      wrap(video);\n    });\n  });\n}\n\nfunction queryAll(el, selector) {\n  if (typeof el === \"string\") {\n    selector = el;\n    el = document;\n  }\n  return Array.prototype.slice.call(el.querySelectorAll(selector));\n}\n\nfunction toSelectorArray(input) {\n  if (typeof input === \"string\") {\n    return input\n      .split(\",\")\n      .map(trim)\n      .filter(hasLength);\n  } else if (isArray(input)) {\n    return flatten(input.map(toSelectorArray).filter(hasLength));\n  }\n  return input || [];\n}\n\nfunction wrap(el) {\n  if (/fluid-width-video-wrapper/.test(el.parentNode.className)) {\n    return;\n  }\n\n  var widthAttr = parseInt(el.getAttribute(\"width\"), 10);\n  var heightAttr = parseInt(el.getAttribute(\"height\"), 10);\n\n  var width = !isNaN(widthAttr) ? widthAttr : el.clientWidth;\n  var height = !isNaN(heightAttr) ? heightAttr : el.clientHeight;\n  var aspect = height / width;\n\n  el.removeAttribute(\"width\");\n  el.removeAttribute(\"height\");\n\n  var wrapper = document.createElement(\"div\");\n  el.parentNode.insertBefore(wrapper, el);\n  wrapper.className = \"fluid-width-video-wrapper\";\n  wrapper.style.paddingTop = aspect * 100 + \"%\";\n  wrapper.appendChild(el);\n}\n\nfunction styles() {\n  var div = document.createElement(\"div\");\n  div.innerHTML = '<p>x</p><style id=\"fit-vids-style\">' + css + \"</style>\";\n  return div.childNodes[1];\n}\n\nfunction hasLength(input) {\n  return input.length > 0;\n}\n\nfunction trim(str) {\n  return str.replace(/^\\s+|\\s+$/g, \"\");\n}\n\nfunction flatten(input) {\n  return [].concat.apply([], input);\n}\n\nfunction isObject(input) {\n  return Object.prototype.toString.call(input) === \"[object Object]\";\n}\n\nfunction isArray(input) {\n  return Object.prototype.toString.call(input) === \"[object Array]\";\n}\n\n},{}]},{},[1])(1)\n});\n", "var scriptUrl = 'https:\\/\\/www.youtube.com\\/s\\/player\\/f96f6702\\/www-widgetapi.vflset\\/www-widgetapi.js';try{var ttPolicy=window.trustedTypes.createPolicy(\"youtube-widget-api\",{createScriptURL:function(x){return x}});scriptUrl=ttPolicy.createScriptURL(scriptUrl)}catch(e){}var YT;if(!window[\"YT\"])YT={loading:0,loaded:0};var YTConfig;if(!window[\"YTConfig\"])YTConfig={\"host\":\"https://www.youtube.com\"};\nif(!YT.loading){YT.loading=1;(function(){var l=[];YT.ready=function(f){if(YT.loaded)f();else l.push(f)};window.onYTReady=function(){YT.loaded=1;for(var i=0;i<l.length;i++)try{l[i]()}catch(e$0){}};YT.setConfig=function(c){for(var k in c)if(c.hasOwnProperty(k))YTConfig[k]=c[k]};var a=document.createElement(\"script\");a.type=\"text/javascript\";a.id=\"www-widgetapi-script\";a.src=scriptUrl;a.async=true;var c=document.currentScript;if(c){var n=c.nonce||c.getAttribute(\"nonce\");if(n)a.setAttribute(\"nonce\",n)}var b=\ndocument.getElementsByTagName(\"script\")[0];b.parentNode.insertBefore(a,b)})()};", "/**\n * Aucor Navigation.js\n * -------------------\n *\n * Features:\n * - only adds classes, no show() or hide()\n * - timer for hover exit: better usability\n * - works with tabs: a11y\n * - desktop menu with touch: doubletap\n * - mobile menu with touch\n * - works at least with 3 levels (probably more)\n *\n */\nlet aucor_navigation = function (menu, options) {\n  var extend = function (defaults, options) {\n    var extended = {};\n    var prop;\n    for (prop in defaults) {\n      if (Object.prototype.hasOwnProperty.call(defaults, prop)) {\n        extended[prop] = defaults[prop];\n      }\n    }\n    for (prop in options) {\n      if (Object.prototype.hasOwnProperty.call(options, prop)) {\n        extended[prop] = options[prop];\n      }\n    }\n    return extended;\n  };\n\n  // Default settings\n  var defaults = {\n    desktop_min_width: 501,\n    menu_toggle: \"#menu-toggle\",\n  };\n\n  var settings = extend(defaults, options),\n    desktop_min_width = 1580, // match this to $menu-visible SASS variable\n    menu_toggle = document.querySelector(settings.menu_toggle),\n    screen_w,\n    hover_timer,\n    focus_timer;\n\n  /**\n   * Is desktop menu\n   *\n   * Checks if window is wider than set desktop limit.\n   *\n   * @return bool is desktop width screen\n   */\n  function is_desktop_menu() {\n    screen_w = Math.max(\n      document.documentElement.clientWidth,\n      window.innerWidth || 0\n    );\n    if (screen_w < desktop_min_width) {\n      return false;\n    }\n    return true;\n  }\n\n  /**\n   * Hover timer (only for desktop menus)\n   *\n   * Keeps sub-menu open for a small time when hover has left the element.\n   */\n  var open_sub_menu = function (e) {\n    if (is_desktop_menu()) {\n      // clear timer\n      clearTimeout(hover_timer);\n\n      // make sure hover_timer did it's thing even if it didn't have time to fire\n      // -> close all .sub-menus that don't belong to this DOM tree\n      var this_tree_submenus = [];\n\n      // get submenus on tree parents\n      var current_parent = this.parentElement;\n      while (!current_parent.isEqualNode(menu)) {\n        if (current_parent.classList.contains(\"sub-menu-wrap\")) {\n          this_tree_submenus.push(current_parent);\n        }\n        current_parent = current_parent.parentElement;\n      }\n\n      // get submenus on tree descendants\n      var current_descendants = this.querySelectorAll(\".sub-menu-wrap\");\n      for (var d = 0; d < current_descendants.length; d++) {\n        this_tree_submenus.push(current_descendants[d]);\n      }\n\n      // fetch all open submenus\n      var all_open_sub_menus = menu.querySelectorAll(\".open\");\n      for (var j = 0; j < all_open_sub_menus.length; j++) {\n        // close the submenu only if not in current tree\n        if (this_tree_submenus.indexOf(all_open_sub_menus[j]) === -1) {\n          all_open_sub_menus[j].classList.remove(\"open\");\n        }\n      }\n\n      // open child sub-menu\n      if (this.querySelector(\".sub-menu-wrap\")) {\n        this.querySelector(\".sub-menu-wrap\").classList.add(\"open\");\n      }\n    }\n  };\n\n  /**\n   * Close menu when hover timer has ended (only for desktop menus)\n   *\n   * Triggers when mouse leaves menu element.\n   */\n  var close_sub_menu = function (e) {\n    var t = this;\n    // create timeout that let's the cursor get outside of menu for a moment\n    if (is_desktop_menu()) {\n      hover_timer = setTimeout(function () {\n        var parent = t.parentElement;\n        while (!parent.isEqualNode(menu)) {\n          parent.classList.remove(\"open\");\n          parent = parent.parentElement;\n        }\n        if (t.querySelector(\".open\")) {\n          t.querySelector(\".open\").classList.remove(\"open\");\n        }\n      }, 250);\n    }\n  };\n\n  var open_submenu_with_click = function (e) {\n    var li = null;\n    var parent = e.target.parentElement;\n    while (!parent.isEqualNode(menu)) {\n      if (parent.classList.contains(\"menu-item\")) {\n        li = parent;\n        break;\n      }\n      parent = parent.parentElement;\n    }\n\n    // toggle .open class to child .sub-menu\n    li.querySelector(\".sub-menu-wrap\").classList.toggle(\"open\");\n\n    // toggle .active class to this <li>\n    if (!is_desktop_menu()) {\n      li.classList.toggle(\"active\");\n    }\n\n    // don't trigger parent(s)\n    e.stopPropagation();\n  };\n\n  // if current page is menu item with children, the dropdown will be open when menu is opened (2023)\n  let current_menu_item = menu.querySelector(\".current-menu-item\");\n\n  if (\n    !is_desktop_menu() &&\n    current_menu_item &&\n    current_menu_item.classList.contains(\"active\")\n  ) {\n    if (current_menu_item.classList.contains(\"menu-item-has-children\")) {\n      current_menu_item.classList.add(\"active\");\n      let current_sub_menu = current_menu_item.querySelector(\".sub-menu-wrap\");\n      if (current_sub_menu) {\n        current_sub_menu.classList.add(\"open\");\n      }\n    }\n  }\n\n  var items_with_children = menu.querySelectorAll(\".menu-item-has-children\");\n  for (var i = 0; i < items_with_children.length; i++) {\n    var item = items_with_children[i];\n    item.addEventListener(\"mouseover\", open_sub_menu);\n    item.addEventListener(\"mouseleave\", close_sub_menu);\n\n    var caret = item.querySelector(\".js-menu-caret\");\n    if (caret) {\n      /* Open sub-menu with click to <button>\n      ----------------------------------------------- */\n      caret.addEventListener(\"click\", open_submenu_with_click);\n    }\n  }\n\n  /* Keyboard (tab)\n  ----------------------------------------------- */\n  var on_link_focus = function (e) {\n    // open sub-menu below\n    var submenu_below = e.target.parentElement.querySelector(\".sub-menu-wrap\");\n    if (submenu_below) {\n      submenu_below.classList.add(\"open\");\n    }\n\n    // open all sub-menus above\n    var parent = e.target.parentElement;\n    while (!parent.isEqualNode(menu)) {\n      if (parent.classList.contains(\"sub-menu-wrap\")) {\n        parent.classList.add(\"open\");\n      }\n      parent = parent.parentElement;\n    }\n  };\n\n  var on_link_blur = function (e) {\n    // close sub-menu below\n    var submenu_below = e.target.parentElement.querySelector(\".sub-menu-wrap\");\n    if (submenu_below) {\n      submenu_below.classList.remove(\"open\");\n    }\n\n    // close all sub-menus above\n    var parent = e.target.parentElement;\n    while (!parent.isEqualNode(menu)) {\n      if (parent.classList.contains(\"sub-menu-wrap\")) {\n        parent.classList.remove(\"open\");\n      }\n      parent = parent.parentElement;\n    }\n  };\n\n  var links = menu.querySelectorAll(\"a\");\n  for (var k = 0; k < links.length; k++) {\n    var link = links[k];\n\n    link.addEventListener(\"focus\", on_link_focus);\n    link.addEventListener(\"blur\", on_link_blur);\n  }\n\n  /* Toggle menu (hamburger)\n  ----------------------------------------------- */\n\n  menu_toggle.addEventListener(\"click\", function () {\n    if (menu_toggle.classList.contains(\"menu-toggle--active\")) {\n      // remove .active class from hamburger icon\n      menu_toggle.classList.remove(\"menu-toggle--active\");\n      menu_toggle.setAttribute(\"aria-expanded\", \"false\");\n\n      // remove .active class to menu container\n      menu.classList.remove(\"active\");\n\n      // focus out of the menu\n      menu_toggle.dispatchEvent(new Event(\"focus\"));\n    } else {\n      // .active class to hamburger icon\n      menu_toggle.classList.add(\"menu-toggle--active\");\n      menu_toggle.setAttribute(\"aria-expanded\", \"true\");\n\n      // .active class to menu container\n      menu.classList.add(\"active\");\n    }\n  });\n\n  /* Empty links \"#\": open sub-menu\n  ----------------------------------------------- */\n\n  //  $menu.find('a[href=\"#\"]').click(function(e) {\n  //\n  //    // don't go to \"#\"\n  //    e.preventDefault();\n  //\n  //    // do the same stuff as clicking to .menu-item-has-children\n  //    $(this).parent('.menu-item-has-children').trigger('click');\n  //\n  //  });\n\n  /* Touch + desktop menu: doubletap\n  ----------------------------------------------- */\n\n  var touchStartFn;\n  var maybeCloseMenuFn;\n\n  if (\"ontouchstart\" in window) {\n    var findAndRemoveClass = function (container, className) {\n      var elements = container.querySelectorAll(\".\" + className);\n      for (var e = 0; e < elements.length; e++) {\n        elements[e].classList.remove(className);\n      }\n    };\n\n    var targetInsideMenu = function (elem) {\n      var isInsideMenu = false;\n      while ((elem = elem.parentElement) !== null) {\n        if (elem.nodeType !== Node.ELEMENT_NODE) {\n          continue;\n        }\n        if (elem.isEqualNode(menu)) {\n          isInsideMenu = true;\n        }\n      }\n      return isInsideMenu;\n    };\n\n    // maybe close menu after it has been opened by tap\n    maybeCloseMenuFn = function (e) {\n      // if the target of the tap isn't menu nor a descendant of menu\n      if (\n        menu !== e.target &&\n        !targetInsideMenu(e.target) &&\n        is_desktop_menu()\n      ) {\n        // reset menu state to default\n        findAndRemoveClass(menu, \"open\");\n        findAndRemoveClass(menu, \"tapped\");\n        findAndRemoveClass(menu, \"active\");\n      }\n\n      // remove this event listener\n      document.removeEventListener(\"ontouchstart\", maybeCloseMenuFn, false);\n    };\n\n    touchStartFn = function (e) {\n      // only fire on desktop menu\n      if (!is_desktop_menu()) {\n        return false;\n      }\n\n      var current_list_item = this.parentElement;\n      var current_parent;\n\n      if (!current_list_item.classList.contains(\"tapped\")) {\n        // first tap: don't go to <a> yet\n        e.preventDefault();\n\n        // remove .tapped class to <li> that don't belong to this DOM tree\n        var this_parents_li = [];\n        current_parent = current_list_item;\n        while (!current_parent.isEqualNode(menu)) {\n          if (current_parent.classList.contains(\"tapped\")) {\n            this_parents_li.push(current_parent);\n          }\n          current_parent = current_parent.parentElement;\n        }\n        var all_tapped = menu.querySelectorAll(\".tapped\");\n        for (var j = 0; j < all_tapped.length; j++) {\n          // Close the submenu only if not in current tree\n          if (this_parents_li.indexOf(all_tapped[j]) === -1) {\n            all_tapped[j].classList.remove(\"tapped\");\n          }\n        }\n\n        // add .tapped class to <li> element\n        current_list_item.classList.add(\"tapped\");\n\n        // close all .sub-menus that don't belong to this DOM tree\n        var this_parents_submenu = [];\n        current_parent = current_list_item;\n        while (!current_parent.isEqualNode(menu)) {\n          if (current_parent.classList.contains(\"open\")) {\n            this_parents_submenu.push(current_parent);\n          }\n          current_parent = current_parent.parentElement;\n        }\n        var all_open_submenus = menu.querySelectorAll(\".open\");\n        for (var t = 0; t < all_open_submenus.length; t++) {\n          // Close the submenu only if not in current tree\n          if (this_parents_submenu.indexOf(all_open_submenus[t]) === -1) {\n            all_open_submenus[t].classList.remove(\"open\");\n          }\n        }\n\n        // open .sub-menu below\n        if (current_list_item.querySelector(\".sub-menu\")) {\n          current_list_item.querySelector(\".sub-menu\").classList.add(\"open\");\n        }\n\n        // open all .sub-menus above\n        current_parent = this.parentElement;\n        while (!current_parent.isEqualNode(menu)) {\n          if (current_parent.classList.contains(\"sub-menu\")) {\n            current_parent.classList.add(\"open\");\n          }\n          current_parent = current_parent.parentElement;\n        }\n\n        // add EventListener to second click\n        document.addEventListener(\"touchstart\", maybeCloseMenuFn, false);\n      } else {\n        // second tap: go to <a>\n\n        // remove .tapped from current <li>\n        current_list_item.classList.remove(\"tapped\");\n\n        // close .sub-menus\n        findAndRemoveClass(menu, \"open\");\n      }\n    };\n\n    // add eventlisteners for each <a> with a sub-menu\n    var parent_links = menu.querySelectorAll(\".menu-item-has-children > a\");\n    for (var p = 0; p < parent_links.length; p++) {\n      parent_links[p].addEventListener(\"touchstart\", touchStartFn, false);\n    }\n  }\n\n  // make the call chainable\n  return this;\n};\n", "/**\n * Content markup enhancements\n */\n\n/**\n * Make tables responsive\n */\nvar responsive_tables_in_content = function () {\n  var tables = document.querySelectorAll(\".wysiwyg .wp-block-table\");\n  if (tables) {\n    for (var i = 0; i < tables.length; i++) {\n      // add modifier class to affected table\n      tables[i].classList.add(\"wp-block-table--responsive\");\n\n      // create new wrapper\n      var wrapper = document.createElement(\"div\");\n\n      // take all classes from table\n      wrapper.setAttribute(\"class\", tables[i].getAttribute(\"class\"));\n\n      // reset table classes\n      tables[i].removeAttribute(\"class\");\n\n      // wrap the table\n      tables[i].parentNode.insertBefore(wrapper, tables[i]);\n      wrapper.appendChild(tables[i]);\n    }\n  }\n};\nresponsive_tables_in_content();\n\n/**\n * Classic Editor image markup \"polyfill\"\n *\n * Wrap old images with captions to make alignment work and markup fit Gutenberg style\n * <figure class=\"wp-caption\"><img></figure> => <div class=\"wp-block-image\"><figure class=\"wp-caption\"><img></figure></div>\n */\nvar wrap_old_images_with_caption = function () {\n  var figures = document.querySelectorAll(\".wysiwyg .wp-caption\");\n  if (figures.length) {\n    for (i = 0; i < figures.length; i++) {\n      if (!figures[i].parentNode.classList.contains(\"wp-block-image\")) {\n        var wrapper = document.createElement(\"div\");\n        wrapper.setAttribute(\"class\", \"wp-block-image\");\n        figures[i].parentNode.insertBefore(wrapper, figures[i]);\n        wrapper.appendChild(figures[i]);\n      }\n    }\n  }\n};\nwrap_old_images_with_caption();\n\n/**\n * Classic Editor aligned image markup \"polyfill\"\n *\n * Wrap old aligned images without caption to make markup fit Gutenberg style\n * <p><img class=\"alignleft\">Text<p> => <div class=\"wp-block-image\"><figure class=\"alignleft\"><img></figure></div><p>Text</p>\n */\nvar wrap_old_aligned_images = function () {\n  var aligned_parent;\n  var aligned = document.querySelectorAll(\n    \".wysiwyg img.alignleft, .wysiwyg img.alignright\"\n  );\n  if (aligned.length) {\n    for (i = 0; i < aligned.length; i++) {\n      // save references\n      aligned_parent = aligned[i].parentNode;\n\n      // if parent is paragraph, unwrap\n      if (aligned_parent.nodeName === \"P\") {\n        aligned_parent.parentNode.insertBefore(aligned[i], aligned_parent);\n        // remove paragraph if its now empty\n        if (aligned_parent.childNodes.length === 0) {\n          aligned_parent.parentNode.removeChild(aligned_parent);\n        }\n      }\n\n      // find and remove alignment from img\n      var alignment = aligned[i].classList.contains(\"alignleft\")\n        ? \"alignleft\"\n        : \"alignright\";\n      aligned[i].classList.remove(alignment);\n\n      // wrap with figure\n      var figure = document.createElement(\"figure\");\n      figure.setAttribute(\"class\", alignment);\n      aligned[i].parentNode.insertBefore(figure, aligned[i]);\n      figure.appendChild(aligned[i]);\n\n      // wrap with .wp-block-image\n      var div = document.createElement(\"div\");\n      div.setAttribute(\"class\", \"wp-block-image\");\n      figure.parentNode.insertBefore(div, figure);\n      div.appendChild(figure);\n    }\n  }\n};\nwrap_old_aligned_images();\n", "/**\n * open and closes sites tab\n */\n\nconst offCanvas = document.querySelector(\".header_sites\"),\n  menuToggle = document.querySelector(\".header__toggle\"),\n  main = document.querySelector(\".main-body\");\n\nconst closingElements = [menuToggle, main];\nconst toggleElements = [offCanvas, main];\n\nfunction openElements(...elements) {\n  const elementsToOpen = [...elements];\n  elementsToOpen.forEach((elementToOpen) =>\n    elementToOpen.classList.toggle(\"open\")\n  );\n}\n\nfunction closeElements(...elements) {\n  const elementsToClose = [...elements];\n  elementsToClose.forEach((elementToClose) =>\n    elementToClose.classList.remove(\"open\")\n  );\n}\n\nmenuToggle.addEventListener(\"click\", (e) => {\n  e.preventDefault();\n  e.stopImmediatePropagation();\n  openElements(...toggleElements);\n});\n\nclosingElements.forEach((closingElem) => {\n  closingElem.addEventListener(\"click\", (e) => {\n    //e.preventDefault();\n    if (\n      main.classList.contains(\"open\") &&\n      !e.target.classList.contains(\"menu-toggle\")\n    ) {\n      closeElements(...toggleElements);\n    }\n  });\n});\n\n/**\n * Copy sitenav to mobilemenu\n */\n\n//const menu = document.querySelector(\".header_sites__sites\");\n\n/* adding external sites to mobile nav */\n\nconst menu = document.querySelector(\".header_sites\");\nconst cloned_menu = menu.cloneNode(true);\nif (document.getElementById(\"primary-navigation__items\") !== undefined) {\n  document.getElementById(\"primary-navigation__items\").appendChild(cloned_menu);\n}\n\n/**\n * Menu scrolling behaviout\n */\n\nvar lastScrollTop = 65;\n\nwindow.addEventListener(\n  \"scroll\",\n  function () {\n    var st = window.pageYOffset || document.documentElement.scrollTop;\n    if (st > lastScrollTop && st > 150) {\n      document.getElementById(\"primary-navigation\").classList.add(\"hide\");\n    } else {\n      document.getElementById(\"primary-navigation\").classList.remove(\"hide\");\n    }\n    lastScrollTop = st;\n  },\n  false\n);\n\n/**\n * Height of menu\n */\n\n// var elmnt = document.getElementById(\"masthead\");\n// document.getElementById(\"content\").style.marginTop = elmnt.offsetHeight + \"px\";\n", "function displayCurrentTab(current, tabsContents, tabsBtns) {\n  for (let i = 0; i < tabsContents.length; i++) {\n    if (current === i) {\n      tabsContents[i].style.height = \"auto\";\n      if (\n        tabsContents[i].parentNode.parentNode.classList.contains(\"scrollto\")\n      ) {\n        tabsContents[i].scrollIntoView({ block: \"start\", behavior: \"smooth\" });\n      }\n    } else {\n      tabsContents[i].style.height = \"0\";\n    }\n  }\n  for (let i = 0; i < tabsBtns.length; i++) {\n    if (current === i) {\n      tabsBtns[i].classList.add(\"on\");\n    } else {\n      tabsBtns[i].classList.remove(\"on\");\n    }\n  }\n}\n\nif (document.querySelector(\".tabs\")) {\n  const tabs = document.querySelectorAll(\".tabs\");\n\n  for (let i = 0; i < tabs.length; i++) {\n    const tabsBtns = tabs[i].querySelectorAll(\".tabs__btn\");\n    const tabsContents = tabs[i].querySelectorAll(\".tabs__content\");\n    const close = tabs[i].querySelectorAll(\".tabs__content--close\");\n\n    if (document.querySelector(\".active_tabs\")) {\n      displayCurrentTab(0, tabsContents, tabsBtns);\n    }\n\n    for (let i = 0; i < close.length; i++) {\n      close[i].addEventListener(\"click\", (event) => {\n        for (let i = 0; i < tabsContents.length; i++) {\n          tabsContents[i].style.height = \"0\";\n        }\n      });\n    }\n\n    tabs[i].addEventListener(\"click\", (event) => {\n      jQuery(\".product__wrap\").trigger(\"resize\");\n      jQuery(\".product__wrap\").trigger(\"resize\");\n      if (\n        event.target.classList.contains(\"tabs__btn\") ||\n        event.target.parentNode.classList.contains(\"tabs__btn\") ||\n        event.target.parentNode.parentNode.classList.contains(\"tabs__btn\")\n      ) {\n        for (let i = 0; i < tabsBtns.length; i++) {\n          if (\n            event.target === tabsBtns[i] ||\n            event.target.parentNode === tabsBtns[i] ||\n            event.target.parentNode.parentNode === tabsBtns[i]\n          ) {\n            displayCurrentTab(i, tabsContents, tabsBtns);\n            break;\n          }\n        }\n      }\n    });\n  }\n}\n", "!function(i){\"use strict\";\"function\"==typeof define&&define.amd?define([\"jquery\"],i):\"undefined\"!=typeof exports?module.exports=i(require(\"jquery\")):i(jQuery)}(function(i){\"use strict\";var e=window.Slick||{};(e=function(){var e=0;return function(t,o){var s,n=this;n.defaults={accessibility:!0,adaptiveHeight:!1,appendArrows:i(t),appendDots:i(t),arrows:!0,asNavFor:null,prevArrow:'<button class=\"slick-prev\" aria-label=\"Previous\" type=\"button\">Previous</button>',nextArrow:'<button class=\"slick-next\" aria-label=\"Next\" type=\"button\">Next</button>',autoplay:!1,autoplaySpeed:3e3,centerMode:!1,centerPadding:\"50px\",cssEase:\"ease\",customPaging:function(e,t){return i('<button type=\"button\" />').text(t+1)},dots:!1,dotsClass:\"slick-dots\",draggable:!0,easing:\"linear\",edgeFriction:.35,fade:!1,focusOnSelect:!1,focusOnChange:!1,infinite:!0,initialSlide:0,lazyLoad:\"ondemand\",mobileFirst:!1,pauseOnHover:!0,pauseOnFocus:!0,pauseOnDotsHover:!1,respondTo:\"window\",responsive:null,rows:1,rtl:!1,slide:\"\",slidesPerRow:1,slidesToShow:1,slidesToScroll:1,speed:500,swipe:!0,swipeToSlide:!1,touchMove:!0,touchThreshold:5,useCSS:!0,useTransform:!0,variableWidth:!1,vertical:!1,verticalSwiping:!1,waitForAnimate:!0,zIndex:1e3},n.initials={animating:!1,dragging:!1,autoPlayTimer:null,currentDirection:0,currentLeft:null,currentSlide:0,direction:1,$dots:null,listWidth:null,listHeight:null,loadIndex:0,$nextArrow:null,$prevArrow:null,scrolling:!1,slideCount:null,slideWidth:null,$slideTrack:null,$slides:null,sliding:!1,slideOffset:0,swipeLeft:null,swiping:!1,$list:null,touchObject:{},transformsEnabled:!1,unslicked:!1},i.extend(n,n.initials),n.activeBreakpoint=null,n.animType=null,n.animProp=null,n.breakpoints=[],n.breakpointSettings=[],n.cssTransitions=!1,n.focussed=!1,n.interrupted=!1,n.hidden=\"hidden\",n.paused=!0,n.positionProp=null,n.respondTo=null,n.rowCount=1,n.shouldClick=!0,n.$slider=i(t),n.$slidesCache=null,n.transformType=null,n.transitionType=null,n.visibilityChange=\"visibilitychange\",n.windowWidth=0,n.windowTimer=null,s=i(t).data(\"slick\")||{},n.options=i.extend({},n.defaults,o,s),n.currentSlide=n.options.initialSlide,n.originalSettings=n.options,void 0!==document.mozHidden?(n.hidden=\"mozHidden\",n.visibilityChange=\"mozvisibilitychange\"):void 0!==document.webkitHidden&&(n.hidden=\"webkitHidden\",n.visibilityChange=\"webkitvisibilitychange\"),n.autoPlay=i.proxy(n.autoPlay,n),n.autoPlayClear=i.proxy(n.autoPlayClear,n),n.autoPlayIterator=i.proxy(n.autoPlayIterator,n),n.changeSlide=i.proxy(n.changeSlide,n),n.clickHandler=i.proxy(n.clickHandler,n),n.selectHandler=i.proxy(n.selectHandler,n),n.setPosition=i.proxy(n.setPosition,n),n.swipeHandler=i.proxy(n.swipeHandler,n),n.dragHandler=i.proxy(n.dragHandler,n),n.keyHandler=i.proxy(n.keyHandler,n),n.instanceUid=e++,n.htmlExpr=/^(?:\\s*(<[\\w\\W]+>)[^>]*)$/,n.registerBreakpoints(),n.init(!0)}}()).prototype.activateADA=function(){this.$slideTrack.find(\".slick-active\").attr({\"aria-hidden\":\"false\"}).find(\"a, input, button, select\").attr({tabindex:\"0\"})},e.prototype.addSlide=e.prototype.slickAdd=function(e,t,o){var s=this;if(\"boolean\"==typeof t)o=t,t=null;else if(t<0||t>=s.slideCount)return!1;s.unload(),\"number\"==typeof t?0===t&&0===s.$slides.length?i(e).appendTo(s.$slideTrack):o?i(e).insertBefore(s.$slides.eq(t)):i(e).insertAfter(s.$slides.eq(t)):!0===o?i(e).prependTo(s.$slideTrack):i(e).appendTo(s.$slideTrack),s.$slides=s.$slideTrack.children(this.options.slide),s.$slideTrack.children(this.options.slide).detach(),s.$slideTrack.append(s.$slides),s.$slides.each(function(e,t){i(t).attr(\"data-slick-index\",e)}),s.$slidesCache=s.$slides,s.reinit()},e.prototype.animateHeight=function(){var i=this;if(1===i.options.slidesToShow&&!0===i.options.adaptiveHeight&&!1===i.options.vertical){var e=i.$slides.eq(i.currentSlide).outerHeight(!0);i.$list.animate({height:e},i.options.speed)}},e.prototype.animateSlide=function(e,t){var o={},s=this;s.animateHeight(),!0===s.options.rtl&&!1===s.options.vertical&&(e=-e),!1===s.transformsEnabled?!1===s.options.vertical?s.$slideTrack.animate({left:e},s.options.speed,s.options.easing,t):s.$slideTrack.animate({top:e},s.options.speed,s.options.easing,t):!1===s.cssTransitions?(!0===s.options.rtl&&(s.currentLeft=-s.currentLeft),i({animStart:s.currentLeft}).animate({animStart:e},{duration:s.options.speed,easing:s.options.easing,step:function(i){i=Math.ceil(i),!1===s.options.vertical?(o[s.animType]=\"translate(\"+i+\"px, 0px)\",s.$slideTrack.css(o)):(o[s.animType]=\"translate(0px,\"+i+\"px)\",s.$slideTrack.css(o))},complete:function(){t&&t.call()}})):(s.applyTransition(),e=Math.ceil(e),!1===s.options.vertical?o[s.animType]=\"translate3d(\"+e+\"px, 0px, 0px)\":o[s.animType]=\"translate3d(0px,\"+e+\"px, 0px)\",s.$slideTrack.css(o),t&&setTimeout(function(){s.disableTransition(),t.call()},s.options.speed))},e.prototype.getNavTarget=function(){var e=this,t=e.options.asNavFor;return t&&null!==t&&(t=i(t).not(e.$slider)),t},e.prototype.asNavFor=function(e){var t=this.getNavTarget();null!==t&&\"object\"==typeof t&&t.each(function(){var t=i(this).slick(\"getSlick\");t.unslicked||t.slideHandler(e,!0)})},e.prototype.applyTransition=function(i){var e=this,t={};!1===e.options.fade?t[e.transitionType]=e.transformType+\" \"+e.options.speed+\"ms \"+e.options.cssEase:t[e.transitionType]=\"opacity \"+e.options.speed+\"ms \"+e.options.cssEase,!1===e.options.fade?e.$slideTrack.css(t):e.$slides.eq(i).css(t)},e.prototype.autoPlay=function(){var i=this;i.autoPlayClear(),i.slideCount>i.options.slidesToShow&&(i.autoPlayTimer=setInterval(i.autoPlayIterator,i.options.autoplaySpeed))},e.prototype.autoPlayClear=function(){var i=this;i.autoPlayTimer&&clearInterval(i.autoPlayTimer)},e.prototype.autoPlayIterator=function(){var i=this,e=i.currentSlide+i.options.slidesToScroll;i.paused||i.interrupted||i.focussed||(!1===i.options.infinite&&(1===i.direction&&i.currentSlide+1===i.slideCount-1?i.direction=0:0===i.direction&&(e=i.currentSlide-i.options.slidesToScroll,i.currentSlide-1==0&&(i.direction=1))),i.slideHandler(e))},e.prototype.buildArrows=function(){var e=this;!0===e.options.arrows&&(e.$prevArrow=i(e.options.prevArrow).addClass(\"slick-arrow\"),e.$nextArrow=i(e.options.nextArrow).addClass(\"slick-arrow\"),e.slideCount>e.options.slidesToShow?(e.$prevArrow.removeClass(\"slick-hidden\").removeAttr(\"aria-hidden tabindex\"),e.$nextArrow.removeClass(\"slick-hidden\").removeAttr(\"aria-hidden tabindex\"),e.htmlExpr.test(e.options.prevArrow)&&e.$prevArrow.prependTo(e.options.appendArrows),e.htmlExpr.test(e.options.nextArrow)&&e.$nextArrow.appendTo(e.options.appendArrows),!0!==e.options.infinite&&e.$prevArrow.addClass(\"slick-disabled\").attr(\"aria-disabled\",\"true\")):e.$prevArrow.add(e.$nextArrow).addClass(\"slick-hidden\").attr({\"aria-disabled\":\"true\",tabindex:\"-1\"}))},e.prototype.buildDots=function(){var e,t,o=this;if(!0===o.options.dots){for(o.$slider.addClass(\"slick-dotted\"),t=i(\"<ul />\").addClass(o.options.dotsClass),e=0;e<=o.getDotCount();e+=1)t.append(i(\"<li />\").append(o.options.customPaging.call(this,o,e)));o.$dots=t.appendTo(o.options.appendDots),o.$dots.find(\"li\").first().addClass(\"slick-active\")}},e.prototype.buildOut=function(){var e=this;e.$slides=e.$slider.children(e.options.slide+\":not(.slick-cloned)\").addClass(\"slick-slide\"),e.slideCount=e.$slides.length,e.$slides.each(function(e,t){i(t).attr(\"data-slick-index\",e).data(\"originalStyling\",i(t).attr(\"style\")||\"\")}),e.$slider.addClass(\"slick-slider\"),e.$slideTrack=0===e.slideCount?i('<div class=\"slick-track\"/>').appendTo(e.$slider):e.$slides.wrapAll('<div class=\"slick-track\"/>').parent(),e.$list=e.$slideTrack.wrap('<div class=\"slick-list\"/>').parent(),e.$slideTrack.css(\"opacity\",0),!0!==e.options.centerMode&&!0!==e.options.swipeToSlide||(e.options.slidesToScroll=1),i(\"img[data-lazy]\",e.$slider).not(\"[src]\").addClass(\"slick-loading\"),e.setupInfinite(),e.buildArrows(),e.buildDots(),e.updateDots(),e.setSlideClasses(\"number\"==typeof e.currentSlide?e.currentSlide:0),!0===e.options.draggable&&e.$list.addClass(\"draggable\")},e.prototype.buildRows=function(){var i,e,t,o,s,n,r,l=this;if(o=document.createDocumentFragment(),n=l.$slider.children(),l.options.rows>1){for(r=l.options.slidesPerRow*l.options.rows,s=Math.ceil(n.length/r),i=0;i<s;i++){var d=document.createElement(\"div\");for(e=0;e<l.options.rows;e++){var a=document.createElement(\"div\");for(t=0;t<l.options.slidesPerRow;t++){var c=i*r+(e*l.options.slidesPerRow+t);n.get(c)&&a.appendChild(n.get(c))}d.appendChild(a)}o.appendChild(d)}l.$slider.empty().append(o),l.$slider.children().children().children().css({width:100/l.options.slidesPerRow+\"%\",display:\"inline-block\"})}},e.prototype.checkResponsive=function(e,t){var o,s,n,r=this,l=!1,d=r.$slider.width(),a=window.innerWidth||i(window).width();if(\"window\"===r.respondTo?n=a:\"slider\"===r.respondTo?n=d:\"min\"===r.respondTo&&(n=Math.min(a,d)),r.options.responsive&&r.options.responsive.length&&null!==r.options.responsive){s=null;for(o in r.breakpoints)r.breakpoints.hasOwnProperty(o)&&(!1===r.originalSettings.mobileFirst?n<r.breakpoints[o]&&(s=r.breakpoints[o]):n>r.breakpoints[o]&&(s=r.breakpoints[o]));null!==s?null!==r.activeBreakpoint?(s!==r.activeBreakpoint||t)&&(r.activeBreakpoint=s,\"unslick\"===r.breakpointSettings[s]?r.unslick(s):(r.options=i.extend({},r.originalSettings,r.breakpointSettings[s]),!0===e&&(r.currentSlide=r.options.initialSlide),r.refresh(e)),l=s):(r.activeBreakpoint=s,\"unslick\"===r.breakpointSettings[s]?r.unslick(s):(r.options=i.extend({},r.originalSettings,r.breakpointSettings[s]),!0===e&&(r.currentSlide=r.options.initialSlide),r.refresh(e)),l=s):null!==r.activeBreakpoint&&(r.activeBreakpoint=null,r.options=r.originalSettings,!0===e&&(r.currentSlide=r.options.initialSlide),r.refresh(e),l=s),e||!1===l||r.$slider.trigger(\"breakpoint\",[r,l])}},e.prototype.changeSlide=function(e,t){var o,s,n,r=this,l=i(e.currentTarget);switch(l.is(\"a\")&&e.preventDefault(),l.is(\"li\")||(l=l.closest(\"li\")),n=r.slideCount%r.options.slidesToScroll!=0,o=n?0:(r.slideCount-r.currentSlide)%r.options.slidesToScroll,e.data.message){case\"previous\":s=0===o?r.options.slidesToScroll:r.options.slidesToShow-o,r.slideCount>r.options.slidesToShow&&r.slideHandler(r.currentSlide-s,!1,t);break;case\"next\":s=0===o?r.options.slidesToScroll:o,r.slideCount>r.options.slidesToShow&&r.slideHandler(r.currentSlide+s,!1,t);break;case\"index\":var d=0===e.data.index?0:e.data.index||l.index()*r.options.slidesToScroll;r.slideHandler(r.checkNavigable(d),!1,t),l.children().trigger(\"focus\");break;default:return}},e.prototype.checkNavigable=function(i){var e,t;if(e=this.getNavigableIndexes(),t=0,i>e[e.length-1])i=e[e.length-1];else for(var o in e){if(i<e[o]){i=t;break}t=e[o]}return i},e.prototype.cleanUpEvents=function(){var e=this;e.options.dots&&null!==e.$dots&&(i(\"li\",e.$dots).off(\"click.slick\",e.changeSlide).off(\"mouseenter.slick\",i.proxy(e.interrupt,e,!0)).off(\"mouseleave.slick\",i.proxy(e.interrupt,e,!1)),!0===e.options.accessibility&&e.$dots.off(\"keydown.slick\",e.keyHandler)),e.$slider.off(\"focus.slick blur.slick\"),!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow&&e.$prevArrow.off(\"click.slick\",e.changeSlide),e.$nextArrow&&e.$nextArrow.off(\"click.slick\",e.changeSlide),!0===e.options.accessibility&&(e.$prevArrow&&e.$prevArrow.off(\"keydown.slick\",e.keyHandler),e.$nextArrow&&e.$nextArrow.off(\"keydown.slick\",e.keyHandler))),e.$list.off(\"touchstart.slick mousedown.slick\",e.swipeHandler),e.$list.off(\"touchmove.slick mousemove.slick\",e.swipeHandler),e.$list.off(\"touchend.slick mouseup.slick\",e.swipeHandler),e.$list.off(\"touchcancel.slick mouseleave.slick\",e.swipeHandler),e.$list.off(\"click.slick\",e.clickHandler),i(document).off(e.visibilityChange,e.visibility),e.cleanUpSlideEvents(),!0===e.options.accessibility&&e.$list.off(\"keydown.slick\",e.keyHandler),!0===e.options.focusOnSelect&&i(e.$slideTrack).children().off(\"click.slick\",e.selectHandler),i(window).off(\"orientationchange.slick.slick-\"+e.instanceUid,e.orientationChange),i(window).off(\"resize.slick.slick-\"+e.instanceUid,e.resize),i(\"[draggable!=true]\",e.$slideTrack).off(\"dragstart\",e.preventDefault),i(window).off(\"load.slick.slick-\"+e.instanceUid,e.setPosition)},e.prototype.cleanUpSlideEvents=function(){var e=this;e.$list.off(\"mouseenter.slick\",i.proxy(e.interrupt,e,!0)),e.$list.off(\"mouseleave.slick\",i.proxy(e.interrupt,e,!1))},e.prototype.cleanUpRows=function(){var i,e=this;e.options.rows>1&&((i=e.$slides.children().children()).removeAttr(\"style\"),e.$slider.empty().append(i))},e.prototype.clickHandler=function(i){!1===this.shouldClick&&(i.stopImmediatePropagation(),i.stopPropagation(),i.preventDefault())},e.prototype.destroy=function(e){var t=this;t.autoPlayClear(),t.touchObject={},t.cleanUpEvents(),i(\".slick-cloned\",t.$slider).detach(),t.$dots&&t.$dots.remove(),t.$prevArrow&&t.$prevArrow.length&&(t.$prevArrow.removeClass(\"slick-disabled slick-arrow slick-hidden\").removeAttr(\"aria-hidden aria-disabled tabindex\").css(\"display\",\"\"),t.htmlExpr.test(t.options.prevArrow)&&t.$prevArrow.remove()),t.$nextArrow&&t.$nextArrow.length&&(t.$nextArrow.removeClass(\"slick-disabled slick-arrow slick-hidden\").removeAttr(\"aria-hidden aria-disabled tabindex\").css(\"display\",\"\"),t.htmlExpr.test(t.options.nextArrow)&&t.$nextArrow.remove()),t.$slides&&(t.$slides.removeClass(\"slick-slide slick-active slick-center slick-visible slick-current\").removeAttr(\"aria-hidden\").removeAttr(\"data-slick-index\").each(function(){i(this).attr(\"style\",i(this).data(\"originalStyling\"))}),t.$slideTrack.children(this.options.slide).detach(),t.$slideTrack.detach(),t.$list.detach(),t.$slider.append(t.$slides)),t.cleanUpRows(),t.$slider.removeClass(\"slick-slider\"),t.$slider.removeClass(\"slick-initialized\"),t.$slider.removeClass(\"slick-dotted\"),t.unslicked=!0,e||t.$slider.trigger(\"destroy\",[t])},e.prototype.disableTransition=function(i){var e=this,t={};t[e.transitionType]=\"\",!1===e.options.fade?e.$slideTrack.css(t):e.$slides.eq(i).css(t)},e.prototype.fadeSlide=function(i,e){var t=this;!1===t.cssTransitions?(t.$slides.eq(i).css({zIndex:t.options.zIndex}),t.$slides.eq(i).animate({opacity:1},t.options.speed,t.options.easing,e)):(t.applyTransition(i),t.$slides.eq(i).css({opacity:1,zIndex:t.options.zIndex}),e&&setTimeout(function(){t.disableTransition(i),e.call()},t.options.speed))},e.prototype.fadeSlideOut=function(i){var e=this;!1===e.cssTransitions?e.$slides.eq(i).animate({opacity:0,zIndex:e.options.zIndex-2},e.options.speed,e.options.easing):(e.applyTransition(i),e.$slides.eq(i).css({opacity:0,zIndex:e.options.zIndex-2}))},e.prototype.filterSlides=e.prototype.slickFilter=function(i){var e=this;null!==i&&(e.$slidesCache=e.$slides,e.unload(),e.$slideTrack.children(this.options.slide).detach(),e.$slidesCache.filter(i).appendTo(e.$slideTrack),e.reinit())},e.prototype.focusHandler=function(){var e=this;e.$slider.off(\"focus.slick blur.slick\").on(\"focus.slick blur.slick\",\"*\",function(t){t.stopImmediatePropagation();var o=i(this);setTimeout(function(){e.options.pauseOnFocus&&(e.focussed=o.is(\":focus\"),e.autoPlay())},0)})},e.prototype.getCurrent=e.prototype.slickCurrentSlide=function(){return this.currentSlide},e.prototype.getDotCount=function(){var i=this,e=0,t=0,o=0;if(!0===i.options.infinite)if(i.slideCount<=i.options.slidesToShow)++o;else for(;e<i.slideCount;)++o,e=t+i.options.slidesToScroll,t+=i.options.slidesToScroll<=i.options.slidesToShow?i.options.slidesToScroll:i.options.slidesToShow;else if(!0===i.options.centerMode)o=i.slideCount;else if(i.options.asNavFor)for(;e<i.slideCount;)++o,e=t+i.options.slidesToScroll,t+=i.options.slidesToScroll<=i.options.slidesToShow?i.options.slidesToScroll:i.options.slidesToShow;else o=1+Math.ceil((i.slideCount-i.options.slidesToShow)/i.options.slidesToScroll);return o-1},e.prototype.getLeft=function(i){var e,t,o,s,n=this,r=0;return n.slideOffset=0,t=n.$slides.first().outerHeight(!0),!0===n.options.infinite?(n.slideCount>n.options.slidesToShow&&(n.slideOffset=n.slideWidth*n.options.slidesToShow*-1,s=-1,!0===n.options.vertical&&!0===n.options.centerMode&&(2===n.options.slidesToShow?s=-1.5:1===n.options.slidesToShow&&(s=-2)),r=t*n.options.slidesToShow*s),n.slideCount%n.options.slidesToScroll!=0&&i+n.options.slidesToScroll>n.slideCount&&n.slideCount>n.options.slidesToShow&&(i>n.slideCount?(n.slideOffset=(n.options.slidesToShow-(i-n.slideCount))*n.slideWidth*-1,r=(n.options.slidesToShow-(i-n.slideCount))*t*-1):(n.slideOffset=n.slideCount%n.options.slidesToScroll*n.slideWidth*-1,r=n.slideCount%n.options.slidesToScroll*t*-1))):i+n.options.slidesToShow>n.slideCount&&(n.slideOffset=(i+n.options.slidesToShow-n.slideCount)*n.slideWidth,r=(i+n.options.slidesToShow-n.slideCount)*t),n.slideCount<=n.options.slidesToShow&&(n.slideOffset=0,r=0),!0===n.options.centerMode&&n.slideCount<=n.options.slidesToShow?n.slideOffset=n.slideWidth*Math.floor(n.options.slidesToShow)/2-n.slideWidth*n.slideCount/2:!0===n.options.centerMode&&!0===n.options.infinite?n.slideOffset+=n.slideWidth*Math.floor(n.options.slidesToShow/2)-n.slideWidth:!0===n.options.centerMode&&(n.slideOffset=0,n.slideOffset+=n.slideWidth*Math.floor(n.options.slidesToShow/2)),e=!1===n.options.vertical?i*n.slideWidth*-1+n.slideOffset:i*t*-1+r,!0===n.options.variableWidth&&(o=n.slideCount<=n.options.slidesToShow||!1===n.options.infinite?n.$slideTrack.children(\".slick-slide\").eq(i):n.$slideTrack.children(\".slick-slide\").eq(i+n.options.slidesToShow),e=!0===n.options.rtl?o[0]?-1*(n.$slideTrack.width()-o[0].offsetLeft-o.width()):0:o[0]?-1*o[0].offsetLeft:0,!0===n.options.centerMode&&(o=n.slideCount<=n.options.slidesToShow||!1===n.options.infinite?n.$slideTrack.children(\".slick-slide\").eq(i):n.$slideTrack.children(\".slick-slide\").eq(i+n.options.slidesToShow+1),e=!0===n.options.rtl?o[0]?-1*(n.$slideTrack.width()-o[0].offsetLeft-o.width()):0:o[0]?-1*o[0].offsetLeft:0,e+=(n.$list.width()-o.outerWidth())/2)),e},e.prototype.getOption=e.prototype.slickGetOption=function(i){return this.options[i]},e.prototype.getNavigableIndexes=function(){var i,e=this,t=0,o=0,s=[];for(!1===e.options.infinite?i=e.slideCount:(t=-1*e.options.slidesToScroll,o=-1*e.options.slidesToScroll,i=2*e.slideCount);t<i;)s.push(t),t=o+e.options.slidesToScroll,o+=e.options.slidesToScroll<=e.options.slidesToShow?e.options.slidesToScroll:e.options.slidesToShow;return s},e.prototype.getSlick=function(){return this},e.prototype.getSlideCount=function(){var e,t,o=this;return t=!0===o.options.centerMode?o.slideWidth*Math.floor(o.options.slidesToShow/2):0,!0===o.options.swipeToSlide?(o.$slideTrack.find(\".slick-slide\").each(function(s,n){if(n.offsetLeft-t+i(n).outerWidth()/2>-1*o.swipeLeft)return e=n,!1}),Math.abs(i(e).attr(\"data-slick-index\")-o.currentSlide)||1):o.options.slidesToScroll},e.prototype.goTo=e.prototype.slickGoTo=function(i,e){this.changeSlide({data:{message:\"index\",index:parseInt(i)}},e)},e.prototype.init=function(e){var t=this;i(t.$slider).hasClass(\"slick-initialized\")||(i(t.$slider).addClass(\"slick-initialized\"),t.buildRows(),t.buildOut(),t.setProps(),t.startLoad(),t.loadSlider(),t.initializeEvents(),t.updateArrows(),t.updateDots(),t.checkResponsive(!0),t.focusHandler()),e&&t.$slider.trigger(\"init\",[t]),!0===t.options.accessibility&&t.initADA(),t.options.autoplay&&(t.paused=!1,t.autoPlay())},e.prototype.initADA=function(){var e=this,t=Math.ceil(e.slideCount/e.options.slidesToShow),o=e.getNavigableIndexes().filter(function(i){return i>=0&&i<e.slideCount});e.$slides.add(e.$slideTrack.find(\".slick-cloned\")).attr({\"aria-hidden\":\"true\",tabindex:\"-1\"}).find(\"a, input, button, select\").attr({tabindex:\"-1\"}),null!==e.$dots&&(e.$slides.not(e.$slideTrack.find(\".slick-cloned\")).each(function(t){var s=o.indexOf(t);i(this).attr({role:\"tabpanel\",id:\"slick-slide\"+e.instanceUid+t,tabindex:-1}),-1!==s&&i(this).attr({\"aria-describedby\":\"slick-slide-control\"+e.instanceUid+s})}),e.$dots.attr(\"role\",\"tablist\").find(\"li\").each(function(s){var n=o[s];i(this).attr({role:\"presentation\"}),i(this).find(\"button\").first().attr({role:\"tab\",id:\"slick-slide-control\"+e.instanceUid+s,\"aria-controls\":\"slick-slide\"+e.instanceUid+n,\"aria-label\":s+1+\" of \"+t,\"aria-selected\":null,tabindex:\"-1\"})}).eq(e.currentSlide).find(\"button\").attr({\"aria-selected\":\"true\",tabindex:\"0\"}).end());for(var s=e.currentSlide,n=s+e.options.slidesToShow;s<n;s++)e.$slides.eq(s).attr(\"tabindex\",0);e.activateADA()},e.prototype.initArrowEvents=function(){var i=this;!0===i.options.arrows&&i.slideCount>i.options.slidesToShow&&(i.$prevArrow.off(\"click.slick\").on(\"click.slick\",{message:\"previous\"},i.changeSlide),i.$nextArrow.off(\"click.slick\").on(\"click.slick\",{message:\"next\"},i.changeSlide),!0===i.options.accessibility&&(i.$prevArrow.on(\"keydown.slick\",i.keyHandler),i.$nextArrow.on(\"keydown.slick\",i.keyHandler)))},e.prototype.initDotEvents=function(){var e=this;!0===e.options.dots&&(i(\"li\",e.$dots).on(\"click.slick\",{message:\"index\"},e.changeSlide),!0===e.options.accessibility&&e.$dots.on(\"keydown.slick\",e.keyHandler)),!0===e.options.dots&&!0===e.options.pauseOnDotsHover&&i(\"li\",e.$dots).on(\"mouseenter.slick\",i.proxy(e.interrupt,e,!0)).on(\"mouseleave.slick\",i.proxy(e.interrupt,e,!1))},e.prototype.initSlideEvents=function(){var e=this;e.options.pauseOnHover&&(e.$list.on(\"mouseenter.slick\",i.proxy(e.interrupt,e,!0)),e.$list.on(\"mouseleave.slick\",i.proxy(e.interrupt,e,!1)))},e.prototype.initializeEvents=function(){var e=this;e.initArrowEvents(),e.initDotEvents(),e.initSlideEvents(),e.$list.on(\"touchstart.slick mousedown.slick\",{action:\"start\"},e.swipeHandler),e.$list.on(\"touchmove.slick mousemove.slick\",{action:\"move\"},e.swipeHandler),e.$list.on(\"touchend.slick mouseup.slick\",{action:\"end\"},e.swipeHandler),e.$list.on(\"touchcancel.slick mouseleave.slick\",{action:\"end\"},e.swipeHandler),e.$list.on(\"click.slick\",e.clickHandler),i(document).on(e.visibilityChange,i.proxy(e.visibility,e)),!0===e.options.accessibility&&e.$list.on(\"keydown.slick\",e.keyHandler),!0===e.options.focusOnSelect&&i(e.$slideTrack).children().on(\"click.slick\",e.selectHandler),i(window).on(\"orientationchange.slick.slick-\"+e.instanceUid,i.proxy(e.orientationChange,e)),i(window).on(\"resize.slick.slick-\"+e.instanceUid,i.proxy(e.resize,e)),i(\"[draggable!=true]\",e.$slideTrack).on(\"dragstart\",e.preventDefault),i(window).on(\"load.slick.slick-\"+e.instanceUid,e.setPosition),i(e.setPosition)},e.prototype.initUI=function(){var i=this;!0===i.options.arrows&&i.slideCount>i.options.slidesToShow&&(i.$prevArrow.show(),i.$nextArrow.show()),!0===i.options.dots&&i.slideCount>i.options.slidesToShow&&i.$dots.show()},e.prototype.keyHandler=function(i){var e=this;i.target.tagName.match(\"TEXTAREA|INPUT|SELECT\")||(37===i.keyCode&&!0===e.options.accessibility?e.changeSlide({data:{message:!0===e.options.rtl?\"next\":\"previous\"}}):39===i.keyCode&&!0===e.options.accessibility&&e.changeSlide({data:{message:!0===e.options.rtl?\"previous\":\"next\"}}))},e.prototype.lazyLoad=function(){function e(e){i(\"img[data-lazy]\",e).each(function(){var e=i(this),t=i(this).attr(\"data-lazy\"),o=i(this).attr(\"data-srcset\"),s=i(this).attr(\"data-sizes\")||n.$slider.attr(\"data-sizes\"),r=document.createElement(\"img\");r.onload=function(){e.animate({opacity:0},100,function(){o&&(e.attr(\"srcset\",o),s&&e.attr(\"sizes\",s)),e.attr(\"src\",t).animate({opacity:1},200,function(){e.removeAttr(\"data-lazy data-srcset data-sizes\").removeClass(\"slick-loading\")}),n.$slider.trigger(\"lazyLoaded\",[n,e,t])})},r.onerror=function(){e.removeAttr(\"data-lazy\").removeClass(\"slick-loading\").addClass(\"slick-lazyload-error\"),n.$slider.trigger(\"lazyLoadError\",[n,e,t])},r.src=t})}var t,o,s,n=this;if(!0===n.options.centerMode?!0===n.options.infinite?s=(o=n.currentSlide+(n.options.slidesToShow/2+1))+n.options.slidesToShow+2:(o=Math.max(0,n.currentSlide-(n.options.slidesToShow/2+1)),s=n.options.slidesToShow/2+1+2+n.currentSlide):(o=n.options.infinite?n.options.slidesToShow+n.currentSlide:n.currentSlide,s=Math.ceil(o+n.options.slidesToShow),!0===n.options.fade&&(o>0&&o--,s<=n.slideCount&&s++)),t=n.$slider.find(\".slick-slide\").slice(o,s),\"anticipated\"===n.options.lazyLoad)for(var r=o-1,l=s,d=n.$slider.find(\".slick-slide\"),a=0;a<n.options.slidesToScroll;a++)r<0&&(r=n.slideCount-1),t=(t=t.add(d.eq(r))).add(d.eq(l)),r--,l++;e(t),n.slideCount<=n.options.slidesToShow?e(n.$slider.find(\".slick-slide\")):n.currentSlide>=n.slideCount-n.options.slidesToShow?e(n.$slider.find(\".slick-cloned\").slice(0,n.options.slidesToShow)):0===n.currentSlide&&e(n.$slider.find(\".slick-cloned\").slice(-1*n.options.slidesToShow))},e.prototype.loadSlider=function(){var i=this;i.setPosition(),i.$slideTrack.css({opacity:1}),i.$slider.removeClass(\"slick-loading\"),i.initUI(),\"progressive\"===i.options.lazyLoad&&i.progressiveLazyLoad()},e.prototype.next=e.prototype.slickNext=function(){this.changeSlide({data:{message:\"next\"}})},e.prototype.orientationChange=function(){var i=this;i.checkResponsive(),i.setPosition()},e.prototype.pause=e.prototype.slickPause=function(){var i=this;i.autoPlayClear(),i.paused=!0},e.prototype.play=e.prototype.slickPlay=function(){var i=this;i.autoPlay(),i.options.autoplay=!0,i.paused=!1,i.focussed=!1,i.interrupted=!1},e.prototype.postSlide=function(e){var t=this;t.unslicked||(t.$slider.trigger(\"afterChange\",[t,e]),t.animating=!1,t.slideCount>t.options.slidesToShow&&t.setPosition(),t.swipeLeft=null,t.options.autoplay&&t.autoPlay(),!0===t.options.accessibility&&(t.initADA(),t.options.focusOnChange&&i(t.$slides.get(t.currentSlide)).attr(\"tabindex\",0).focus()))},e.prototype.prev=e.prototype.slickPrev=function(){this.changeSlide({data:{message:\"previous\"}})},e.prototype.preventDefault=function(i){i.preventDefault()},e.prototype.progressiveLazyLoad=function(e){e=e||1;var t,o,s,n,r,l=this,d=i(\"img[data-lazy]\",l.$slider);d.length?(t=d.first(),o=t.attr(\"data-lazy\"),s=t.attr(\"data-srcset\"),n=t.attr(\"data-sizes\")||l.$slider.attr(\"data-sizes\"),(r=document.createElement(\"img\")).onload=function(){s&&(t.attr(\"srcset\",s),n&&t.attr(\"sizes\",n)),t.attr(\"src\",o).removeAttr(\"data-lazy data-srcset data-sizes\").removeClass(\"slick-loading\"),!0===l.options.adaptiveHeight&&l.setPosition(),l.$slider.trigger(\"lazyLoaded\",[l,t,o]),l.progressiveLazyLoad()},r.onerror=function(){e<3?setTimeout(function(){l.progressiveLazyLoad(e+1)},500):(t.removeAttr(\"data-lazy\").removeClass(\"slick-loading\").addClass(\"slick-lazyload-error\"),l.$slider.trigger(\"lazyLoadError\",[l,t,o]),l.progressiveLazyLoad())},r.src=o):l.$slider.trigger(\"allImagesLoaded\",[l])},e.prototype.refresh=function(e){var t,o,s=this;o=s.slideCount-s.options.slidesToShow,!s.options.infinite&&s.currentSlide>o&&(s.currentSlide=o),s.slideCount<=s.options.slidesToShow&&(s.currentSlide=0),t=s.currentSlide,s.destroy(!0),i.extend(s,s.initials,{currentSlide:t}),s.init(),e||s.changeSlide({data:{message:\"index\",index:t}},!1)},e.prototype.registerBreakpoints=function(){var e,t,o,s=this,n=s.options.responsive||null;if(\"array\"===i.type(n)&&n.length){s.respondTo=s.options.respondTo||\"window\";for(e in n)if(o=s.breakpoints.length-1,n.hasOwnProperty(e)){for(t=n[e].breakpoint;o>=0;)s.breakpoints[o]&&s.breakpoints[o]===t&&s.breakpoints.splice(o,1),o--;s.breakpoints.push(t),s.breakpointSettings[t]=n[e].settings}s.breakpoints.sort(function(i,e){return s.options.mobileFirst?i-e:e-i})}},e.prototype.reinit=function(){var e=this;e.$slides=e.$slideTrack.children(e.options.slide).addClass(\"slick-slide\"),e.slideCount=e.$slides.length,e.currentSlide>=e.slideCount&&0!==e.currentSlide&&(e.currentSlide=e.currentSlide-e.options.slidesToScroll),e.slideCount<=e.options.slidesToShow&&(e.currentSlide=0),e.registerBreakpoints(),e.setProps(),e.setupInfinite(),e.buildArrows(),e.updateArrows(),e.initArrowEvents(),e.buildDots(),e.updateDots(),e.initDotEvents(),e.cleanUpSlideEvents(),e.initSlideEvents(),e.checkResponsive(!1,!0),!0===e.options.focusOnSelect&&i(e.$slideTrack).children().on(\"click.slick\",e.selectHandler),e.setSlideClasses(\"number\"==typeof e.currentSlide?e.currentSlide:0),e.setPosition(),e.focusHandler(),e.paused=!e.options.autoplay,e.autoPlay(),e.$slider.trigger(\"reInit\",[e])},e.prototype.resize=function(){var e=this;i(window).width()!==e.windowWidth&&(clearTimeout(e.windowDelay),e.windowDelay=window.setTimeout(function(){e.windowWidth=i(window).width(),e.checkResponsive(),e.unslicked||e.setPosition()},50))},e.prototype.removeSlide=e.prototype.slickRemove=function(i,e,t){var o=this;if(i=\"boolean\"==typeof i?!0===(e=i)?0:o.slideCount-1:!0===e?--i:i,o.slideCount<1||i<0||i>o.slideCount-1)return!1;o.unload(),!0===t?o.$slideTrack.children().remove():o.$slideTrack.children(this.options.slide).eq(i).remove(),o.$slides=o.$slideTrack.children(this.options.slide),o.$slideTrack.children(this.options.slide).detach(),o.$slideTrack.append(o.$slides),o.$slidesCache=o.$slides,o.reinit()},e.prototype.setCSS=function(i){var e,t,o=this,s={};!0===o.options.rtl&&(i=-i),e=\"left\"==o.positionProp?Math.ceil(i)+\"px\":\"0px\",t=\"top\"==o.positionProp?Math.ceil(i)+\"px\":\"0px\",s[o.positionProp]=i,!1===o.transformsEnabled?o.$slideTrack.css(s):(s={},!1===o.cssTransitions?(s[o.animType]=\"translate(\"+e+\", \"+t+\")\",o.$slideTrack.css(s)):(s[o.animType]=\"translate3d(\"+e+\", \"+t+\", 0px)\",o.$slideTrack.css(s)))},e.prototype.setDimensions=function(){var i=this;!1===i.options.vertical?!0===i.options.centerMode&&i.$list.css({padding:\"0px \"+i.options.centerPadding}):(i.$list.height(i.$slides.first().outerHeight(!0)*i.options.slidesToShow),!0===i.options.centerMode&&i.$list.css({padding:i.options.centerPadding+\" 0px\"})),i.listWidth=i.$list.width(),i.listHeight=i.$list.height(),!1===i.options.vertical&&!1===i.options.variableWidth?(i.slideWidth=Math.ceil(i.listWidth/i.options.slidesToShow),i.$slideTrack.width(Math.ceil(i.slideWidth*i.$slideTrack.children(\".slick-slide\").length))):!0===i.options.variableWidth?i.$slideTrack.width(5e3*i.slideCount):(i.slideWidth=Math.ceil(i.listWidth),i.$slideTrack.height(Math.ceil(i.$slides.first().outerHeight(!0)*i.$slideTrack.children(\".slick-slide\").length)));var e=i.$slides.first().outerWidth(!0)-i.$slides.first().width();!1===i.options.variableWidth&&i.$slideTrack.children(\".slick-slide\").width(i.slideWidth-e)},e.prototype.setFade=function(){var e,t=this;t.$slides.each(function(o,s){e=t.slideWidth*o*-1,!0===t.options.rtl?i(s).css({position:\"relative\",right:e,top:0,zIndex:t.options.zIndex-2,opacity:0}):i(s).css({position:\"relative\",left:e,top:0,zIndex:t.options.zIndex-2,opacity:0})}),t.$slides.eq(t.currentSlide).css({zIndex:t.options.zIndex-1,opacity:1})},e.prototype.setHeight=function(){var i=this;if(1===i.options.slidesToShow&&!0===i.options.adaptiveHeight&&!1===i.options.vertical){var e=i.$slides.eq(i.currentSlide).outerHeight(!0);i.$list.css(\"height\",e)}},e.prototype.setOption=e.prototype.slickSetOption=function(){var e,t,o,s,n,r=this,l=!1;if(\"object\"===i.type(arguments[0])?(o=arguments[0],l=arguments[1],n=\"multiple\"):\"string\"===i.type(arguments[0])&&(o=arguments[0],s=arguments[1],l=arguments[2],\"responsive\"===arguments[0]&&\"array\"===i.type(arguments[1])?n=\"responsive\":void 0!==arguments[1]&&(n=\"single\")),\"single\"===n)r.options[o]=s;else if(\"multiple\"===n)i.each(o,function(i,e){r.options[i]=e});else if(\"responsive\"===n)for(t in s)if(\"array\"!==i.type(r.options.responsive))r.options.responsive=[s[t]];else{for(e=r.options.responsive.length-1;e>=0;)r.options.responsive[e].breakpoint===s[t].breakpoint&&r.options.responsive.splice(e,1),e--;r.options.responsive.push(s[t])}l&&(r.unload(),r.reinit())},e.prototype.setPosition=function(){var i=this;i.setDimensions(),i.setHeight(),!1===i.options.fade?i.setCSS(i.getLeft(i.currentSlide)):i.setFade(),i.$slider.trigger(\"setPosition\",[i])},e.prototype.setProps=function(){var i=this,e=document.body.style;i.positionProp=!0===i.options.vertical?\"top\":\"left\",\"top\"===i.positionProp?i.$slider.addClass(\"slick-vertical\"):i.$slider.removeClass(\"slick-vertical\"),void 0===e.WebkitTransition&&void 0===e.MozTransition&&void 0===e.msTransition||!0===i.options.useCSS&&(i.cssTransitions=!0),i.options.fade&&(\"number\"==typeof i.options.zIndex?i.options.zIndex<3&&(i.options.zIndex=3):i.options.zIndex=i.defaults.zIndex),void 0!==e.OTransform&&(i.animType=\"OTransform\",i.transformType=\"-o-transform\",i.transitionType=\"OTransition\",void 0===e.perspectiveProperty&&void 0===e.webkitPerspective&&(i.animType=!1)),void 0!==e.MozTransform&&(i.animType=\"MozTransform\",i.transformType=\"-moz-transform\",i.transitionType=\"MozTransition\",void 0===e.perspectiveProperty&&void 0===e.MozPerspective&&(i.animType=!1)),void 0!==e.webkitTransform&&(i.animType=\"webkitTransform\",i.transformType=\"-webkit-transform\",i.transitionType=\"webkitTransition\",void 0===e.perspectiveProperty&&void 0===e.webkitPerspective&&(i.animType=!1)),void 0!==e.msTransform&&(i.animType=\"msTransform\",i.transformType=\"-ms-transform\",i.transitionType=\"msTransition\",void 0===e.msTransform&&(i.animType=!1)),void 0!==e.transform&&!1!==i.animType&&(i.animType=\"transform\",i.transformType=\"transform\",i.transitionType=\"transition\"),i.transformsEnabled=i.options.useTransform&&null!==i.animType&&!1!==i.animType},e.prototype.setSlideClasses=function(i){var e,t,o,s,n=this;if(t=n.$slider.find(\".slick-slide\").removeClass(\"slick-active slick-center slick-current\").attr(\"aria-hidden\",\"true\"),n.$slides.eq(i).addClass(\"slick-current\"),!0===n.options.centerMode){var r=n.options.slidesToShow%2==0?1:0;e=Math.floor(n.options.slidesToShow/2),!0===n.options.infinite&&(i>=e&&i<=n.slideCount-1-e?n.$slides.slice(i-e+r,i+e+1).addClass(\"slick-active\").attr(\"aria-hidden\",\"false\"):(o=n.options.slidesToShow+i,t.slice(o-e+1+r,o+e+2).addClass(\"slick-active\").attr(\"aria-hidden\",\"false\")),0===i?t.eq(t.length-1-n.options.slidesToShow).addClass(\"slick-center\"):i===n.slideCount-1&&t.eq(n.options.slidesToShow).addClass(\"slick-center\")),n.$slides.eq(i).addClass(\"slick-center\")}else i>=0&&i<=n.slideCount-n.options.slidesToShow?n.$slides.slice(i,i+n.options.slidesToShow).addClass(\"slick-active\").attr(\"aria-hidden\",\"false\"):t.length<=n.options.slidesToShow?t.addClass(\"slick-active\").attr(\"aria-hidden\",\"false\"):(s=n.slideCount%n.options.slidesToShow,o=!0===n.options.infinite?n.options.slidesToShow+i:i,n.options.slidesToShow==n.options.slidesToScroll&&n.slideCount-i<n.options.slidesToShow?t.slice(o-(n.options.slidesToShow-s),o+s).addClass(\"slick-active\").attr(\"aria-hidden\",\"false\"):t.slice(o,o+n.options.slidesToShow).addClass(\"slick-active\").attr(\"aria-hidden\",\"false\"));\"ondemand\"!==n.options.lazyLoad&&\"anticipated\"!==n.options.lazyLoad||n.lazyLoad()},e.prototype.setupInfinite=function(){var e,t,o,s=this;if(!0===s.options.fade&&(s.options.centerMode=!1),!0===s.options.infinite&&!1===s.options.fade&&(t=null,s.slideCount>s.options.slidesToShow)){for(o=!0===s.options.centerMode?s.options.slidesToShow+1:s.options.slidesToShow,e=s.slideCount;e>s.slideCount-o;e-=1)t=e-1,i(s.$slides[t]).clone(!0).attr(\"id\",\"\").attr(\"data-slick-index\",t-s.slideCount).prependTo(s.$slideTrack).addClass(\"slick-cloned\");for(e=0;e<o+s.slideCount;e+=1)t=e,i(s.$slides[t]).clone(!0).attr(\"id\",\"\").attr(\"data-slick-index\",t+s.slideCount).appendTo(s.$slideTrack).addClass(\"slick-cloned\");s.$slideTrack.find(\".slick-cloned\").find(\"[id]\").each(function(){i(this).attr(\"id\",\"\")})}},e.prototype.interrupt=function(i){var e=this;i||e.autoPlay(),e.interrupted=i},e.prototype.selectHandler=function(e){var t=this,o=i(e.target).is(\".slick-slide\")?i(e.target):i(e.target).parents(\".slick-slide\"),s=parseInt(o.attr(\"data-slick-index\"));s||(s=0),t.slideCount<=t.options.slidesToShow?t.slideHandler(s,!1,!0):t.slideHandler(s)},e.prototype.slideHandler=function(i,e,t){var o,s,n,r,l,d=null,a=this;if(e=e||!1,!(!0===a.animating&&!0===a.options.waitForAnimate||!0===a.options.fade&&a.currentSlide===i))if(!1===e&&a.asNavFor(i),o=i,d=a.getLeft(o),r=a.getLeft(a.currentSlide),a.currentLeft=null===a.swipeLeft?r:a.swipeLeft,!1===a.options.infinite&&!1===a.options.centerMode&&(i<0||i>a.getDotCount()*a.options.slidesToScroll))!1===a.options.fade&&(o=a.currentSlide,!0!==t?a.animateSlide(r,function(){a.postSlide(o)}):a.postSlide(o));else if(!1===a.options.infinite&&!0===a.options.centerMode&&(i<0||i>a.slideCount-a.options.slidesToScroll))!1===a.options.fade&&(o=a.currentSlide,!0!==t?a.animateSlide(r,function(){a.postSlide(o)}):a.postSlide(o));else{if(a.options.autoplay&&clearInterval(a.autoPlayTimer),s=o<0?a.slideCount%a.options.slidesToScroll!=0?a.slideCount-a.slideCount%a.options.slidesToScroll:a.slideCount+o:o>=a.slideCount?a.slideCount%a.options.slidesToScroll!=0?0:o-a.slideCount:o,a.animating=!0,a.$slider.trigger(\"beforeChange\",[a,a.currentSlide,s]),n=a.currentSlide,a.currentSlide=s,a.setSlideClasses(a.currentSlide),a.options.asNavFor&&(l=(l=a.getNavTarget()).slick(\"getSlick\")).slideCount<=l.options.slidesToShow&&l.setSlideClasses(a.currentSlide),a.updateDots(),a.updateArrows(),!0===a.options.fade)return!0!==t?(a.fadeSlideOut(n),a.fadeSlide(s,function(){a.postSlide(s)})):a.postSlide(s),void a.animateHeight();!0!==t?a.animateSlide(d,function(){a.postSlide(s)}):a.postSlide(s)}},e.prototype.startLoad=function(){var i=this;!0===i.options.arrows&&i.slideCount>i.options.slidesToShow&&(i.$prevArrow.hide(),i.$nextArrow.hide()),!0===i.options.dots&&i.slideCount>i.options.slidesToShow&&i.$dots.hide(),i.$slider.addClass(\"slick-loading\")},e.prototype.swipeDirection=function(){var i,e,t,o,s=this;return i=s.touchObject.startX-s.touchObject.curX,e=s.touchObject.startY-s.touchObject.curY,t=Math.atan2(e,i),(o=Math.round(180*t/Math.PI))<0&&(o=360-Math.abs(o)),o<=45&&o>=0?!1===s.options.rtl?\"left\":\"right\":o<=360&&o>=315?!1===s.options.rtl?\"left\":\"right\":o>=135&&o<=225?!1===s.options.rtl?\"right\":\"left\":!0===s.options.verticalSwiping?o>=35&&o<=135?\"down\":\"up\":\"vertical\"},e.prototype.swipeEnd=function(i){var e,t,o=this;if(o.dragging=!1,o.swiping=!1,o.scrolling)return o.scrolling=!1,!1;if(o.interrupted=!1,o.shouldClick=!(o.touchObject.swipeLength>10),void 0===o.touchObject.curX)return!1;if(!0===o.touchObject.edgeHit&&o.$slider.trigger(\"edge\",[o,o.swipeDirection()]),o.touchObject.swipeLength>=o.touchObject.minSwipe){switch(t=o.swipeDirection()){case\"left\":case\"down\":e=o.options.swipeToSlide?o.checkNavigable(o.currentSlide+o.getSlideCount()):o.currentSlide+o.getSlideCount(),o.currentDirection=0;break;case\"right\":case\"up\":e=o.options.swipeToSlide?o.checkNavigable(o.currentSlide-o.getSlideCount()):o.currentSlide-o.getSlideCount(),o.currentDirection=1}\"vertical\"!=t&&(o.slideHandler(e),o.touchObject={},o.$slider.trigger(\"swipe\",[o,t]))}else o.touchObject.startX!==o.touchObject.curX&&(o.slideHandler(o.currentSlide),o.touchObject={})},e.prototype.swipeHandler=function(i){var e=this;if(!(!1===e.options.swipe||\"ontouchend\"in document&&!1===e.options.swipe||!1===e.options.draggable&&-1!==i.type.indexOf(\"mouse\")))switch(e.touchObject.fingerCount=i.originalEvent&&void 0!==i.originalEvent.touches?i.originalEvent.touches.length:1,e.touchObject.minSwipe=e.listWidth/e.options.touchThreshold,!0===e.options.verticalSwiping&&(e.touchObject.minSwipe=e.listHeight/e.options.touchThreshold),i.data.action){case\"start\":e.swipeStart(i);break;case\"move\":e.swipeMove(i);break;case\"end\":e.swipeEnd(i)}},e.prototype.swipeMove=function(i){var e,t,o,s,n,r,l=this;return n=void 0!==i.originalEvent?i.originalEvent.touches:null,!(!l.dragging||l.scrolling||n&&1!==n.length)&&(e=l.getLeft(l.currentSlide),l.touchObject.curX=void 0!==n?n[0].pageX:i.clientX,l.touchObject.curY=void 0!==n?n[0].pageY:i.clientY,l.touchObject.swipeLength=Math.round(Math.sqrt(Math.pow(l.touchObject.curX-l.touchObject.startX,2))),r=Math.round(Math.sqrt(Math.pow(l.touchObject.curY-l.touchObject.startY,2))),!l.options.verticalSwiping&&!l.swiping&&r>4?(l.scrolling=!0,!1):(!0===l.options.verticalSwiping&&(l.touchObject.swipeLength=r),t=l.swipeDirection(),void 0!==i.originalEvent&&l.touchObject.swipeLength>4&&(l.swiping=!0,i.preventDefault()),s=(!1===l.options.rtl?1:-1)*(l.touchObject.curX>l.touchObject.startX?1:-1),!0===l.options.verticalSwiping&&(s=l.touchObject.curY>l.touchObject.startY?1:-1),o=l.touchObject.swipeLength,l.touchObject.edgeHit=!1,!1===l.options.infinite&&(0===l.currentSlide&&\"right\"===t||l.currentSlide>=l.getDotCount()&&\"left\"===t)&&(o=l.touchObject.swipeLength*l.options.edgeFriction,l.touchObject.edgeHit=!0),!1===l.options.vertical?l.swipeLeft=e+o*s:l.swipeLeft=e+o*(l.$list.height()/l.listWidth)*s,!0===l.options.verticalSwiping&&(l.swipeLeft=e+o*s),!0!==l.options.fade&&!1!==l.options.touchMove&&(!0===l.animating?(l.swipeLeft=null,!1):void l.setCSS(l.swipeLeft))))},e.prototype.swipeStart=function(i){var e,t=this;if(t.interrupted=!0,1!==t.touchObject.fingerCount||t.slideCount<=t.options.slidesToShow)return t.touchObject={},!1;void 0!==i.originalEvent&&void 0!==i.originalEvent.touches&&(e=i.originalEvent.touches[0]),t.touchObject.startX=t.touchObject.curX=void 0!==e?e.pageX:i.clientX,t.touchObject.startY=t.touchObject.curY=void 0!==e?e.pageY:i.clientY,t.dragging=!0},e.prototype.unfilterSlides=e.prototype.slickUnfilter=function(){var i=this;null!==i.$slidesCache&&(i.unload(),i.$slideTrack.children(this.options.slide).detach(),i.$slidesCache.appendTo(i.$slideTrack),i.reinit())},e.prototype.unload=function(){var e=this;i(\".slick-cloned\",e.$slider).remove(),e.$dots&&e.$dots.remove(),e.$prevArrow&&e.htmlExpr.test(e.options.prevArrow)&&e.$prevArrow.remove(),e.$nextArrow&&e.htmlExpr.test(e.options.nextArrow)&&e.$nextArrow.remove(),e.$slides.removeClass(\"slick-slide slick-active slick-visible slick-current\").attr(\"aria-hidden\",\"true\").css(\"width\",\"\")},e.prototype.unslick=function(i){var e=this;e.$slider.trigger(\"unslick\",[e,i]),e.destroy()},e.prototype.updateArrows=function(){var i=this;Math.floor(i.options.slidesToShow/2),!0===i.options.arrows&&i.slideCount>i.options.slidesToShow&&!i.options.infinite&&(i.$prevArrow.removeClass(\"slick-disabled\").attr(\"aria-disabled\",\"false\"),i.$nextArrow.removeClass(\"slick-disabled\").attr(\"aria-disabled\",\"false\"),0===i.currentSlide?(i.$prevArrow.addClass(\"slick-disabled\").attr(\"aria-disabled\",\"true\"),i.$nextArrow.removeClass(\"slick-disabled\").attr(\"aria-disabled\",\"false\")):i.currentSlide>=i.slideCount-i.options.slidesToShow&&!1===i.options.centerMode?(i.$nextArrow.addClass(\"slick-disabled\").attr(\"aria-disabled\",\"true\"),i.$prevArrow.removeClass(\"slick-disabled\").attr(\"aria-disabled\",\"false\")):i.currentSlide>=i.slideCount-1&&!0===i.options.centerMode&&(i.$nextArrow.addClass(\"slick-disabled\").attr(\"aria-disabled\",\"true\"),i.$prevArrow.removeClass(\"slick-disabled\").attr(\"aria-disabled\",\"false\")))},e.prototype.updateDots=function(){var i=this;null!==i.$dots&&(i.$dots.find(\"li\").removeClass(\"slick-active\").end(),i.$dots.find(\"li\").eq(Math.floor(i.currentSlide/i.options.slidesToScroll)).addClass(\"slick-active\"))},e.prototype.visibility=function(){var i=this;i.options.autoplay&&(document[i.hidden]?i.interrupted=!0:i.interrupted=!1)},i.fn.slick=function(){var i,t,o=this,s=arguments[0],n=Array.prototype.slice.call(arguments,1),r=o.length;for(i=0;i<r;i++)if(\"object\"==typeof s||void 0===s?o[i].slick=new e(o[i],s):t=o[i].slick[s].apply(o[i].slick,n),void 0!==t)return t;return o}});\n", "jQuery(document).ready(function () {\n  jQuery(\".product__wrap\").slick({\n    infinite: true,\n    slidesToShow: 4,\n    slidesToScroll: 1,\n    prevArrow:\n      \"<img alt='edellinen' class='a-left control-c prev slick-prev' src='/wp-content/themes/kauppakamari/dist/images/chevron_left.png'>\",\n    nextArrow:\n      \"<img alt='seuraava' class='a-right control-c next slick-next' src='/wp-content/themes/kauppakamari/dist/images/chevron_right.png'>\",\n    responsive: [\n      {\n        breakpoint: 1024,\n        settings: {\n          slidesToShow: 3,\n          slidesToScroll: 3,\n          infinite: true,\n          dots: true,\n        },\n      },\n      {\n        breakpoint: 600,\n        settings: {\n          slidesToShow: 2,\n          slidesToScroll: 2,\n        },\n      },\n      {\n        breakpoint: 480,\n        settings: {\n          slidesToShow: 1,\n          slidesToScroll: 1,\n        },\n      },\n      // You can unslick at a given breakpoint now by adding:\n      // settings: \"unslick\"\n      // instead of a settings object\n    ],\n  });\n  jQuery(\".block-trainer-list\").slick({\n    infinite: true,\n    slidesToShow: 4,\n    slidesToScroll: 1,\n    prevArrow:\n      \"<img alt='edellinen' class='a-left control-c prev slick-prev' src='/wp-content/themes/kauppakamari/dist/images/chevron_left.png'>\",\n    nextArrow:\n      \"<img alt='seuraava' class='a-right control-c next slick-next' src='/wp-content/themes/kauppakamari/dist/images/chevron_right.png'>\",\n    responsive: [\n      {\n        breakpoint: 1024,\n        settings: {\n          slidesToShow: 3,\n          slidesToScroll: 3,\n          infinite: true,\n          dots: true,\n        },\n      },\n      {\n        breakpoint: 600,\n        settings: {\n          slidesToShow: 2,\n          slidesToScroll: 2,\n        },\n      },\n      {\n        breakpoint: 480,\n        settings: {\n          slidesToShow: 1,\n          slidesToScroll: 1,\n        },\n      },\n      // You can unslick at a given breakpoint now by adding:\n      // settings: \"unslick\"\n      // instead of a settings object\n    ],\n  });\n\n  //shopify carousel:\n  jQuery(\".shopify-scroller-inner.carousel-slick\").slick({\n    dots: false,\n    infinite: false,\n    speed: 300,\n    slidesToShow: 4,\n    adaptiveHeight: true,\n    autoplay: false,\n    prevArrow:\n      \"<img alt='edellinen' class='a-left control-c prev slick-prev' src='/wp-content/themes/kauppakamari/dist/images/chevron_left_c.png'>\",\n    nextArrow:\n      \"<img alt='seuraava' class='a-right control-c next slick-next' src='/wp-content/themes/kauppakamari/dist/images/chevron_right_c.png'>\",\n    responsive: [\n      {\n        breakpoint: 900,\n        settings: {\n          slidesToShow: 2,\n        },\n      },\n      {\n        breakpoint: 480,\n        settings: {\n          slidesToShow: 1,\n          centerPadding: \"50px\",\n        },\n      },\n    ],\n  });\n\n  jQuery(\".block-memberstories\").slick({\n    dots: false,\n    infinite: true,\n    speed: 300,\n    slidesToShow: 1,\n    adaptiveHeight: true,\n    prevArrow:\n      \"<img alt='edellinen' class='a-left control-c prev slick-prev' src='/wp-content/themes/kauppakamari/dist/images/chevron_left_c.png'>\",\n    nextArrow:\n      \"<img alt='seuraava' class='a-right control-c next slick-next' src='/wp-content/themes/kauppakamari/dist/images/chevron_right_c.png'>\",\n  });\n\n  jQuery(\".block-new_members\").slick({\n    dots: false,\n    centerMode: true,\n    centerPadding: \"120px\",\n    slidesToShow: 3,\n    infinite: true,\n    autoplay: false,\n    //adaptiveHeight: true,\n    prevArrow:\n      \"<img alt='edellinen' class='a-left control-c prev slick-prev' src='/wp-content/themes/kauppakamari/dist/images/chevron_left_c.png'>\",\n    nextArrow:\n      \"<img alt='seuraava' class='a-right control-c next slick-next' src='/wp-content/themes/kauppakamari/dist/images/chevron_right_c.png'>\",\n    responsive: [\n      {\n        breakpoint: 1199,\n        settings: {\n          slidesToShow: 2,\n        },\n      },\n      {\n        breakpoint: 767,\n        settings: {\n          slidesToShow: 1,\n          centerPadding: 0,\n        },\n      },\n    ],\n  });\n});\n", "$(\".accordion-toggle\").click(function (e) {\n  e.preventDefault();\n\n  var $this = $(this);\n\n  if ($this.next().hasClass(\"show\")) {\n    $this.toggleClass(\"active\");\n    $this.next().removeClass(\"show\");\n    $this.next().slideUp(350);\n  } else {\n    $this.toggleClass(\"active\");\n    $this.parent().parent().find(\"div .inner\").removeClass(\"show\");\n    $this.parent().parent().find(\"div .inner\").slideUp(350);\n    $this.next().toggleClass(\"show\");\n    $this.next().slideToggle(350);\n  }\n});\n", "var checkboxes = document.querySelectorAll(\".contact__checkboxes--box\");\n\nif (typeof checkboxes[0] != \"undefined\" && checkboxes[0] != null) {\n  const boxes = document.querySelectorAll(\".contact__checkboxes--box\");\n\n  function setGetParam(key, value) {\n    if (history.pushState) {\n      var params = new URLSearchParams(window.location.search);\n      params.set(key, value);\n      var newUrl =\n        window.location.protocol +\n        \"//\" +\n        window.location.host +\n        window.location.pathname +\n        \"?\" +\n        params.toString();\n      window.history.pushState({ path: newUrl }, \"\", newUrl);\n    }\n  }\n\n  var url = new URL(window.location.href);\n  let cats = [];\n\n  for (let i = 0; i < boxes.length; i++) {\n    boxes[i].addEventListener(\"change\", (e) => {\n      if (e.target.checked) {\n        cats.push(boxes[i].value);\n      } else if (!e.target.checked) {\n        let catindex = cats.indexOf(e.target.value);\n        cats.splice(catindex, 1);\n      } else {\n      }\n      nyt_filter(cats);\n    });\n  }\n\n  function nyt_filter(cats = \"\") {\n    let data = {\n      action: \"kauppakamari_filter_contact\",\n      peoplecat: cats,\n    };\n\n    console.log(data);\n\n    setGetParam(\"kategoria\", data.peoplecat);\n\n    jQuery.ajaxSetup({ cache: false });\n\n    jQuery.ajax({\n      type: \"POST\", // *GET, POST, PUT, DELETE, etc.\n      cache: false,\n      headers: { \"cache-control\": \"no-cache\" },\n      credentials: \"same-origin\", // include, *same-origin, omit\n      url:\n        frontendajax.ajaxurl +\n        '?lang=\"\"' /* disables polylang lang filter, some are fi, some empty */,\n      data: data,\n      beforeSend: function (xhr) {\n        let posts_list = document.querySelector(\".contact__list\");\n        posts_list.innerHTML = \"Loading\";\n      },\n      success: function (response) {\n        console.log(response);\n        if (response) {\n          let posts_list = document.querySelector(\".contact__list\");\n          posts_list.innerHTML = response.data;\n        } else {\n          console.log(response);\n        }\n      },\n    });\n  }\n}\n", "var checkarrow = document.querySelectorAll(\".clubs_next_page\");\n\nfunction nyt_change_page(next_page, max_pages) {\n  let data = {\n    action: \"kauppakamari_filter_clubs\",\n    next_page: next_page,\n  };\n\n  jQuery(\".block-clubs__ajax\").fadeOut(500);\n\n  setTimeout(function () {\n    jQuery.ajax({\n      type: \"POST\", // *GET, POST, PUT, DELETE, etc.\n      credentials: \"same-origin\", // include, *same-origin, omit\n      url: frontendajax.ajaxurl,\n      data: data,\n      beforeSend: function (xhr) {\n        let posts_list = document.querySelector(\".block-clubs__ajax\");\n        //posts_list.innerHTML = \"...\";\n      },\n      success: function (response) {\n        console.log(response);\n        if (response) {\n          let posts_list = document.querySelector(\".block-clubs__ajax\");\n          document.querySelector(\".block-clubs__wrap\").dataset.page = next_page;\n          document.querySelector(\".clubs_current_page\").innerHTML = next_page;\n          posts_list.innerHTML = response.data;\n          jQuery(\".block-clubs__ajax\").fadeIn(500);\n          if (next_page === max_pages) {\n            button_next[0].style.display = \"none\";\n          } else {\n            button_next[0].style.display = \"initial\";\n          }\n          if (next_page === 1) {\n            button_prev[0].style.display = \"none\";\n          } else if (next_page > 1) {\n            button_prev[0].style.display = \"initial\";\n          }\n        } else {\n          console.log(response);\n        }\n      },\n    });\n  }, 500);\n}\n\nif (typeof checkarrow[0] !== \"undefined\" && checkarrow[0] != null) {\n  const button_next = document.querySelectorAll(\".clubs_next_page\");\n  button_next[0].addEventListener(\"click\", (e) => {\n    e.preventDefault();\n    let current_page =\n      document.querySelector(\".block-clubs__wrap\").dataset.page;\n    let max_pages = document.querySelector(\".block-clubs__wrap\").dataset.max;\n    nyt_change_page(parseInt(current_page) + 1, parseInt(max_pages));\n  });\n\n  const button_prev = document.querySelectorAll(\".clubs_prev_page\");\n  button_prev[0].addEventListener(\"click\", (e) => {\n    e.preventDefault();\n    let current_page =\n      document.querySelector(\".block-clubs__wrap\").dataset.page;\n    let max_pages = document.querySelector(\".block-clubs__wrap\").dataset.max;\n    nyt_change_page(parseInt(current_page) - 1, parseInt(max_pages));\n  });\n\n  button_prev[0].style.display = \"none\";\n}\n", "$(document).ready(function () {\n  $(\"iframe\").attr(\"data-cookieconsent\", \"marketing\");\n});\n\n$(document).ready(function () {\n  /* Toggle Video Modal\n  -----------------------------------------*/\n  function toggle_video_modal() {\n    // Click on video thumbnail or link\n    $(\".js-trigger-video-modal\").on(\"click\", function (e) {\n      // prevent default behavior for a-tags, button tags, etc.\n      e.preventDefault();\n\n      var regExp =\n        /^.*((youtu.be\\/)|(v\\/)|(\\/u\\/\\w\\/)|(embed\\/)|(watch\\?))\\??v?=?([^#&?]*).*/;\n      var match = $(\"#youtube-iframe\").attr(\"src\").match(regExp);\n      var id = match && match[7].length === 11 ? match[7] : false;\n\n      //var id = $(\"#youtube-iframe\").attr('data-youtube-id');\n\n      // Autoplay when the modal appears\n      // Note: this is intetnionally disabled on most mobile devices\n      // If critical on mobile, then some alternate method is needed\n      var autoplay = \"?autoplay=1\";\n\n      // Don't show the 'Related Videos' view when the video ends\n      var related_no = \"&rel=0\";\n\n      // String the ID and param variables together\n      var src = \"//www.youtube.com/embed/\" + id + autoplay + related_no;\n\n      // Pass the YouTube video ID into the iframe template...\n      // Set the source on the iframe to match the video ID\n      $(\"#youtube-iframe\").attr(\"src\", src);\n\n      // Add class to the body to visually reveal the modal\n      $(\"body\").addClass(\"show-video-modal noscroll\");\n      //$('#youtube-iframe').playVideo();\n    });\n\n    // Close and Reset the Video Modal\n    function close_video_modal() {\n      event.preventDefault();\n\n      // re-hide the video modal\n      $(\"body\").removeClass(\"show-video-modal noscroll\");\n\n      // reset the source attribute for the iframe template, kills the video\n      $(\"#youtube-iframe\").attr(\"src\", \"\");\n\n      //callPlayer('youtube-iframe', 'stopVideo');\n    }\n    // if the 'close' button/element, or the overlay are clicked\n    $(\"body\").on(\n      \"click\",\n      \".close-video-modal, .video-modal .overlay\",\n      function (event) {\n        // call the close and reset function\n        close_video_modal();\n      }\n    );\n    // if the ESC key is tapped\n    $(\"body\").keyup(function (e) {\n      // ESC key maps to keycode `27`\n      if (e.keyCode === 27) {\n        // call the close and reset function\n        close_video_modal();\n      }\n    });\n  }\n  toggle_video_modal();\n});\n", "/* ==========================================================================\n  main.js\n========================================================================== */\nvar searchTimeout = 0;\nvar hashChangeTimeout = 0;\nvar search_url;\n\nvar paramsString;\nvar searchParams;\n\nfunction getQueryStringParam(paramName) {\n  const params = new URLSearchParams(window.location.search);\n  return params.get(paramName);\n}\n\nfunction getQueryStringArrayParam(paramName) {\n  const params = new URLSearchParams(window.location.search);\n  /*\n  for (const key in params) {\n    if (params.hasOwnProperty(key)) {\n      console.log('Looping, ', key);\n      console.log('val: ', params.key);\n    }\n  }\n  */\n  var post_category_array = [];\n\n  for (var pair of params.entries()) {\n    //console.log(pair[0]+ ', '+ pair[1]);\n    if (pair[0] === \"post_category[]\") {\n      post_category_array.push(pair[1]);\n    }\n  }\n\n  //return params.get(paramName);\n  return post_category_array;\n}\n\nfunction setQueryStringParam(paramName, val) {\n  const params = new URLSearchParams(window.location.search);\n  params.set(paramName, val);\n  window.history.pushState({}, \"\", location.pathname + \"?\" + params);\n  window.dispatchEvent(new Event(\"popstate\"));\n}\n\nfunction setQueryStringArrayParam(paramName, val) {\n  const params = new URLSearchParams(window.location.search);\n  params.append(paramName, val);\n  window.history.pushState({}, \"\", location.pathname + \"?\" + params);\n  window.dispatchEvent(new Event(\"popstate\"));\n}\n\nfunction setQueryStringArray(paramName, paramArray) {\n  const params = new URLSearchParams(window.location.search);\n  params.delete(paramName);\n  for (var newVal of paramArray) {\n    params.append(paramName, newVal);\n    //console.log('added to paramArray', newVal, paramArray);\n  }\n  window.history.pushState({}, \"\", location.pathname + \"?\" + params);\n  window.dispatchEvent(new Event(\"popstate\"));\n}\n\nfunction getHashQueryStringParam(paramName) {\n  const params = new URLSearchParams(window.location.hash.substr(1));\n  return params.get(paramName);\n}\n\nwindow.addEventListener(\"popstate\", function () {\n  //console.log(\"URL CHANGED! popstate. hashChangeTimeout: \", hashChangeTimeout);\n  //searchInit();\n  if (hashChangeTimeout === 0) {\n    clearTimeout(hashChangeTimeout);\n    hashChangeTimeout = setTimeout(function () {\n      //always wait small time\n      searchInit();\n    }, 100);\n  } else {\n    clearTimeout(hashChangeTimeout);\n    hashChangeTimeout = setTimeout(function () {\n      searchInit();\n    }, 800);\n  }\n});\n\nfunction emptyResults() {\n  var height = $(\".main.search-results-container\").css(\"height\");\n  $(\".main.search-results-container\").html(\n    '<div class=\"loader loader--big-margins\">Ladataan...</div>'\n  );\n  $(\".main.search-results-container\").css(\"height\", height);\n}\n\nfunction searchInit() {\n  var params = new URLSearchParams(document.location.search);\n  //params.get(paramName)\n  emptyResults();\n\n  //var s_php = getQueryStringParam('s');\n  var s_php = params.get(\"s\");\n  //var s_js = getHashQueryStringParam('s');\n  var s_js = getHashQueryStringParam(\"s\");\n  var people_id = params.get(\"people_id\");\n\n  var s_form_hidden = $(\"#search_key\").val();\n  var s_form_input = $(\".main-search-input\").val();\n  var people_id_form_input = $(\"#people_id\").val();\n  s_js = s_php;\n\n  if (s_form_hidden !== s_js) {\n    $(\"#search_key\").val(s_js);\n  }\n  if (s_form_input !== s_js) {\n    $(\".main-search-input\").val(s_js);\n  }\n\n  if (people_id === null) {\n    people_id = \"\";\n  }\n  if (people_id_form_input !== people_id) {\n    $(\"#people_id\").val(people_id);\n  }\n\n  //post_type\n  var post_type = params.get(\"post_type\");\n\n  //select post type var\n\n  $(\".post_category_checkbox\").prop(\"checked\", false); //uncheck all checkboxes\n  $(\".main-category-list\").addClass(\"hidden\");\n  //?$('input[name=\"post_category[]\"]').prop('checked', false);\n\n  if (typeof post_type == \"string\" && post_type !== \"\") {\n    $(\".main-category-list[data-slug=\" + post_type + \"]\").removeClass(\"hidden\");\n    $('input[name=\"post_type\"]').prop(\"checked\", false);\n    $('input[name=\"post_type\"][value=' + post_type + \"]\")\n      .first()\n      .prop(\"checked\", true);\n  } else {\n    //select \"All\" the first\n    $('input[name=\"post_type\"]').prop(\"checked\", false);\n    $('input[name=\"post_type\"]').first().prop(\"checked\", true); //eg. All\n  }\n\n  //post category checkboxes:\n\n  //gets only one:\n  //var post_category = params.get('post_category[]');\n  var post_category = getQueryStringArrayParam(\"post_category[]\");\n\n  if (Array.isArray(post_category) && post_category[0] !== \"\") {\n    //alert('Oli array osoiterivillä array of post_category');\n    $('input[name=\"post_category[]\"]').prop(\"checked\", false);\n    for (var post_category_slug of post_category) {\n      $('input[name=\"post_category[]\"][value=' + post_category_slug + \"]\")\n        .first()\n        .prop(\"checked\", true);\n    }\n  } else {\n    if (typeof post_category === \"string\" && post_category !== \"\") {\n      $('input[name=\"post_category[]\"]').prop(\"checked\", false);\n      $('input[name=\"post_category[]\"][value=' + post_category + \"]\")\n        .first()\n        .prop(\"checked\", true);\n      // bug, what if same in multiple parents? or maybe cannot be because wordpress prevents\n    } else {\n      $('input[name=\"post_category[]\"]').prop(\"checked\", false);\n      //$('input[name=\"post_category[]\"]').first().prop('checked', true); //eg. All\n    }\n  }\n\n  //0 DEBUG DEBUG\n  if (0 && post_type !== \"people\" && people_id < 1 && s_js.length < 3) {\n    noSearchMessage();\n  } else {\n    triggerSearch();\n  }\n}\n\nfunction noSearchMessage() {\n  $(\".search-results-container\").html(\n    '<p class=\"no-search-made\">Anna hakusana, vähimmäispituus 3 merkkiä</p>'\n  );\n}\n\nfunction triggerSearch() {\n  if (searchTimeout === 0) {\n    //$('#main_search_form').submit();\n    getSearchResults();\n  } else {\n    searchTimeout = setTimeout(function () {\n      searchTimeout = 0;\n      //$('#main_search_form').submit();\n      getSearchResults();\n    }, 1000);\n  }\n}\n\n/**\n * Navigation\n */\naucor_navigation(document.getElementById(\"primary-navigation\"), {\n  desktop_min_width: 890, // min width in pixels\n  menu_toggle: \"#menu-toggle\", // selector for toggle\n});\n\n/**\n * Responsive videos\n */\nfitvids();\n\n/**\n * Polyfill object-fit for lazyloaded\n */\nif (typeof objectFitPolyfill === \"function\") {\n  document.addEventListener(\"lazybeforeunveil\", function (e) {\n    // current <img> element\n    var el = e.target;\n\n    objectFitPolyfill();\n    el.addEventListener(\"load\", function () {\n      objectFitPolyfill();\n    });\n  });\n}\n\n// External link handler - add icon\njQuery(\"a\")\n  .filter(function () {\n    return this.hostname && this.hostname !== location.hostname;\n  })\n  .addClass(\"external\")\n  .attr(\"target\", \"_blank\")\n  .attr(\"aria-description\", \"ulkoinen linkki\")\n  .filter(function () {\n    return $(this).has(\"img\").length;\n  })\n  .addClass(\"external-img\")\n  .removeClass(\"external\");\n\n// Slick slider\njQuery(function () {\n  // console.log('slicki', jQuery().slick)\n  jQuery(\".block-slides-container\").slick({\n    slidesToShow: 2,\n    slidesToScroll: 1,\n    appendArrows: \".block-slider-arrows\",\n    nextArrow:\n      '<button class=\"slider-arrow-container\" aria-label=\"Seuraava uratarina\"><i class=\"fa-sharp fa-solid fa-angle-right\"></i></button>',\n    prevArrow:\n      '<button class=\"slider-arrow-container\" aria-label=\"Edellinen uratarina\"><i class=\"fa-sharp fa-solid fa-angle-left\"></i></button>',\n    responsive: [\n      {\n        breakpoint: 1000,\n        settings: {\n          slidesToShow: 1,\n          slidesToScroll: 1,\n        },\n      },\n    ],\n  });\n});\n\nfunction getSearchResults() {\n  //e.preventDefault();\n  var search_key = $(\".main-search-input\").val(); //S\n  $(\"#search_key\").val(search_key); //copy to form\n\n  window.dataLayer = window.dataLayer || [];\n  window.dataLayer.push({\n    event: \"search\",\n    searchTerm: search_key,\n  });\n\n  var form_data = $(\"#search-filter-primary\").serialize();\n  var pageNum = getQueryStringParam(\"paged\");\n  if (pageNum < 2) {\n    pageNum = 1;\n  }\n  form_data = form_data + \"&paged=\" + pageNum;\n\n  //search_options = search_options.concat(form);\n\n  //Object.assign(\n  //search_options = {\n  //  ...search_options,\n  //  ...form\n  //};\n\n  //var data = {};\n  ///$(\"#search-filter-primary\").serializeArray().map(function(x){search_options[x.name] = x.value;});\n  //console.debug(form_data);\n\n  $.ajax({\n    type: \"POST\",\n    url: search_url,\n    data: form_data,\n    success: (res) => {\n      //console.log(res);\n      $(\".search-results-container\").html(res);\n      $(\".main.search-results-container\").css(\"height\", \"\");\n      setTimeout(function () {\n        if (window.scrollToTop && window.scrollToTop === true) {\n          window.scrollToTop = false;\n          /*$('html, body').scrollTop($(\"#content\").offset().top);*/\n          $(\"html, body\").scrollTop(0);\n        }\n      }, 40);\n    },\n  });\n\n  return false;\n}\n\n/* 2023 new js elements\n-----------------------------------*/\n\n// front page charts 2023\nfunction createCharts() {\n  // observer for the map 'chart'\n  const mapobserver = new IntersectionObserver((entries) => {\n    entries.forEach((entry) => {\n      if (entry.isIntersecting) {\n        entry.target.classList.add(\"animate\");\n      } else {\n        entry.target.classList.remove(\"animate\");\n      }\n    });\n  });\n  const map = document.querySelector(\".block-membership__map\");\n  if (map) {\n    mapobserver.observe(map);\n  }\n  // map end --\n\n  // observer for doughnuts\n  const charts = document.querySelectorAll(\".is-chart\");\n  const observer = new IntersectionObserver((entries) => {\n    entries.forEach((entry) => {\n      if (entry.isIntersecting) {\n        // Dynamic chart data from acf value\n        let chartValue = entry.target.dataset.textvalue;\n        let chartLeftover = 100 - chartValue;\n        let chartRotation = chartValue * 2;\n\n        // Get the chart from DOM\n        const chartId = entry.target.dataset.chartId;\n        const chartContainer = document.getElementById(chartId);\n\n        // Full chart configuration with dynamic data\n        const chartConfig = {\n          type: \"doughnut\",\n          data: {\n            datasets: [\n              {\n                data: [chartLeftover, chartValue],\n                backgroundColor: [\"#A5C9E7\", \"#FD9180\"],\n              },\n            ],\n          },\n          options: {\n            tooltips: { enabled: false },\n            hover: { mode: null },\n            events: [],\n            rotation: chartRotation,\n            borderWidth: 0,\n            animation: {\n              delay: 250,\n            },\n          },\n        };\n        if (window.chartInstances.has(chartContainer)) {\n          window.chartInstances.get(chartContainer).destroy();\n        }\n        const chart = new Chart(chartContainer, chartConfig);\n        window.chartInstances.set(chartContainer, chart);\n      } else {\n        const chartContainer = entry.target.querySelector(\"is-chart\");\n        if (window.chartInstances.has(chartContainer)) {\n          window.chartInstances.get(chartContainer).destroy();\n        }\n      }\n    });\n  });\n  charts.forEach((chart) => {\n    observer.observe(chart);\n  });\n  window.chartInstances = new WeakMap();\n}\n// front page charts end --\n\n/* Number countup animation (not used) */\n/* Count numbers up on  */\n/*if ( $( \".count-js\" ).length ) {\n  var $window = $(window);\n  var $elem = $(\".count-js\")\n  var itemsCounted = true;\n\n  function isScrolledIntoView($elem, $window) {\n      var docViewTop = $window.scrollTop();\n      var docViewBottom = docViewTop + $window.height();\n\n      var elemTop = $elem.offset().top;\n      var elemBottom = elemTop + $elem.height();\n\n      return ((elemBottom <= docViewBottom) && (elemTop >= docViewTop));\n  }\n\n  $(document).on(\"scroll\", function () {\n      if (isScrolledIntoView($elem, $window)) {\n        if (itemsCounted) {\n         counterCount();\n         itemsCounted = false;\n       }\n      }\n  });\n\nfunction counterCount(){\n$('.count-js').each(function () {\n    $(this).prop('Counter',0).animate({\n        Counter: $(this).text()\n    }, {\n        duration: 3000,\n        easing: 'swing',\n        step: function (now) {\n            $(this).text(Math.ceil(now));\n        }\n      });\n    });\n  }\n}*/\n// --\n\n// 2023 Scrolling number load\nfunction scrollingNumbers(item, destination) {\n  let currNum;\n  let fresh = true;\n\n  function scrollNumber(digits) {\n    element.querySelectorAll(\"span[data-value]\").forEach((tick, i) => {\n      tick.style.transform = `translateY(-${100 * parseInt(digits[i])}%)`;\n    });\n    element.style.width = \"max-content\";\n  }\n\n  function addDigit(digit, fresh) {\n    const possibleNumbers = Array(10)\n      .join(0)\n      .split(0)\n      .map((x, j) => `<span>${j}</span>`)\n      .join(\"\");\n\n    element.insertAdjacentHTML(\n      \"beforeend\",\n      `<span style=\"transform: translateY(-1000%)\" data-value=\"${digit}\">\n          ${possibleNumbers}\n          </span>`\n    );\n    const firstDigit = element.lastElementChild;\n\n    setTimeout(\n      () => {\n        firstDigit.className = \"visible\";\n      },\n      fresh ? 0 : 2000\n    );\n  }\n\n  function setup(startNum) {\n    const digits = startNum.toString().split(\"\");\n    for (let i = 0; i < digits.length; i++) {\n      addDigit(\"0\", true);\n    }\n    setTimeout(() => scrollNumber(digits), 200);\n    currNum = startNum;\n  }\n  const element = item;\n  setup(destination);\n}\n// number scroll end\n\n// Fade in animations for chosen blocks 2023\nfunction checkExistence() {\n  let membership__charts = document.querySelectorAll(\n    \".block-membership__charts\"\n  );\n  if (membership__charts) {\n    // initializing the charts here\n    createCharts();\n  }\n\n  let animation_numbers = document.querySelectorAll(\".block-animated-numbers\");\n  if (animation_numbers) {\n    /*let animationTriggered = false;\n\n    const observer = new IntersectionObserver((entries)=>{ // observer to load animations when scrolled on\n      entries.forEach((entry) => {\n        if (entry.isIntersecting && !animationTriggered) {\n          nmbr.forEach((animatedElement) => {\n            animatedElement.classList.add('animate');\n            if(animatedElement.classList.contains('animate')) {\n              let destinationValue = animatedElement.dataset.destination;\n              scrollingNumbers(animatedElement, destinationValue); // Number animation\n            }\n          });\n          animationTriggered = true;\n        }\n      });\n    })\n    let nmbr = document.querySelectorAll('.animated-number');\n\n    nmbr.forEach(animatedElement => {\n      observer.observe(animatedElement);\n    });*/\n  }\n\n  let small__lift2 = document.querySelectorAll(\".small-lift-2\");\n  if (small__lift2) {\n    let small__lift2Arr = Array.from(small__lift2);\n    fadeIn(small__lift2Arr, small__lift2, 0, 110);\n  }\n\n  let highlight1 = document.querySelectorAll(\".highlight-info\");\n  if (highlight1) {\n    let highlight1Arr = Array.from(highlight1);\n    fadeIn(highlight1Arr, highlight1, 0, 150);\n  }\n  let text__image = document.querySelectorAll(\".block-text-image\");\n  if (text__image) {\n    let text__imageArr = Array.from(text__image);\n    fadeIn(text__imageArr, text__image, 0, 150);\n  }\n\n  function fadeIn(array, element, delay, offset) {\n    array.forEach(function (element) {\n      var waypoint = new Waypoint({\n        element: element,\n        handler: function () {\n          element.style.animation = \"fadeInElement 1s ease 1\";\n          element.style.animationDelay = delay + \"s\";\n          element.style.opacity = \"1\";\n        },\n        offset: offset + \"%\",\n      });\n    });\n  }\n}\ncheckExistence();\n// fade in end--\n\n// Footer 'scroll to top' function 2023\nfunction scrollFunction() {\n  const scrollToTopButton = document.getElementById(\"scroll-up-btn\");\n  const scrollFunc = () => {\n    let y = window.scrollY;\n\n    if (y > 1500) {\n      scrollToTopButton.className = \"top-link show\";\n    } else {\n      scrollToTopButton.className = \"top-link hide\";\n    }\n  };\n  window.addEventListener(\"scroll\", scrollFunc);\n  const scrollToTop = () => {\n    const c = document.documentElement.scrollTop || document.body.scrollTop;\n    if (c > 0) {\n      window.requestAnimationFrame(scrollToTop);\n      window.scrollTo(0, c - c / 7);\n    }\n  };\n  scrollToTopButton.onclick = function (e) {\n    e.preventDefault();\n    scrollToTop();\n  };\n}\nscrollFunction();\n// scroll to top end--\n\n/* //2023 new js elements\n-----------------------------------*/\n\nconst searchText = document.documentElement.lang == \"fi\" ? \"Haku\" : \"Search\";\n\nconst closeText =\n  document.documentElement.lang == \"fi\" ? \"Sulje haku\" : \"Close search\";\n\nconst minthree =\n  document.documentElement.lang == \"fi\"\n    ? \"Kirjoita vähintään kolme kirjainta\"\n    : \"Write at least three letters\";\nconst minthree2 =\n  document.documentElement.lang == \"fi\"\n    ? \"Kirjoita vähintään kolme kirjainta hakukenttään\"\n    : \"Write at least three letters to the search form\";\n\njQuery(document).ready(function ($) {\n  search_url = frontendajax.ajaxurl; /* from header */\n  /*on page load, variables update */\n\n  /* top search modal */\n  $(\".search-menu-button\").on(\"click\", function (e) {\n    e.preventDefault();\n    $(this).parent().toggleClass(\"search-open\");\n    if ($(this).parent().hasClass(\"search-open\")) {\n      $(this).text(closeText);\n      $(\".top-search-input\").first().focus();\n    } else {\n      $(this).text(searchText);\n    }\n  });\n  /* add search val from modal search to # */\n  $(\".search-top-container .search-top-container-form\").on(\n    \"submit\",\n    function (e) {\n      if ($(\".top-search-input\").val().length < 3) {\n        if ($(\".top-search-input\").val() === \"\") {\n          $(\".top-search-input\").prop(\"placeholder\", minthree);\n        } else {\n          alert(minthree2);\n        }\n        e.preventDefault();\n        return false;\n      }\n      var s = $(\".top-search-input\").val();\n      var action = $(\".search-top-container-form\").attr(\"action\");\n      action = action; /* + '#!s='+s; */\n      $(\".search-top-container-form\").attr(\"action\", action);\n    }\n  );\n\n  // Top search clear input\n  $(\".top-search-input-clear\").on(\"click\", function () {\n    $(\".top-search-input\").val(\"\").focus();\n  });\n\n  /* top search modal end */\n\n  /* main search function 2022 */\n\n  //auto submit search page form on load:\n  //if($('#search_key').val() !== '' && typeof($('#search_key').val()) !== 'undefined') {\n  //  setTimeout(function() {\n  //    //$('#main_search_form').submit();\n  //    //getSearchResults();\n  //    //window.dispatchEvent(new Event(\"popstate\"));\n  //    //do nothing\n  //  }, 30);\n  //}\n\n  $(\"#main_search_form\").on(\"submit\", function (e) {\n    e.preventDefault();\n    var s = $(\".main-search-input\").val();\n    if (s === \"undefined\" || s === undefined) {\n      s = \"\";\n    }\n    //?? $('#search_key').val(s);\n    setQueryStringParam(\"s\", s);\n    setQueryStringParam(\"paged\", \"1\");\n    return false;\n  });\n\n  /* main search function 2022 end */\n\n  /* main search filters 2022 */\n\n  if ($(\"body.search\").length > 0) {\n    searchInit();\n\n    //window.addEventListener(\"hashchange\", (e) => {\n    //window.addEventListener(\"popstate\", (e) => {\n    //    console.log(\"HASH CHANGED:\", location.hash);\n    //    searchInit();\n    //});\n\n    $(\".main-search-input-clear\").on(\"click\", function () {\n      $(\".main-search-input\").val(\"\").focus();\n    });\n\n    $(\"#primary\").on(\"click\", \"#show-more-people-link\", function (e) {\n      e.preventDefault();\n      setQueryStringParam(\"paged\", \"1\");\n      setQueryStringParam(\"post_type\", \"people\");\n      //$('input[name=\"post_type\"]').prop('checked', false); //uncheck all checkboxes\n      //$('input[name=\"post_type\"][value=people]').click();\n      return false;\n    });\n\n    //remove selected writer\n    $(\"#primary\").on(\"click\", \".remove_selected_writer\", function (e) {\n      e.preventDefault();\n      setQueryStringParam(\"paged\", \"1\");\n      setQueryStringParam(\"people_id\", \"\");\n      return false;\n    });\n\n    /* pagination click, get pagenum from clicked link and set to url */\n    $(\"#primary\").on(\"click\", \"a.page-numbers\", function (e) {\n      e.preventDefault();\n      var hr = $(this).attr(\"href\");\n      var urlPagedParams = new URLSearchParams(hr);\n      var urlPaged = urlPagedParams.get(\"paged\");\n      if (urlPaged !== \"undefined\" && urlPaged !== undefined) {\n        setQueryStringParam(\"paged\", urlPaged);\n      }\n      window.scrollToTop = true;\n      return false;\n    });\n\n    /* post type change, change sub-filter list eg. sisältötyypit */\n    $('input[name=\"post_type\"]').on(\"click\", function () {\n      var post_type = $(this).val();\n      setQueryStringParam(\"post_type\", post_type);\n      setQueryStringParam(\"post_category[]\", \"\"); //remove all post_category[]\n      setQueryStringParam(\"paged\", \"1\");\n\n      /* TODO MOVE TO INIT:\n      $('.post_category_checkbox').prop('checked', false); //uncheck all checkboxes\n      console.log(post_type);\n      $('.main-category-list').addClass('hidden');\n      if(typeof(post_type) == 'string' && post_type != '') {\n        $('.main-category-list[data-slug='+post_type+']').removeClass('hidden');\n      }\n      console.log('@@todo trigger search');\n      //triggerSearch();\n      */\n    });\n\n    /* post category change, update array of teemat */\n    $(\".search-filters\").on(\"click\", \".post_category_checkbox\", function (e) {\n      var post_category_this = $(this).val();\n      //if($(this).is(':checked')) {\n      //  setVal\n      //}\n      setQueryStringParam(\"paged\", \"1\");\n      var post_category_arr = [];\n      $(\".post_category_checkbox:checked\").each(function () {\n        post_category_arr.push($(this).val());\n      });\n\n      setQueryStringArray(\"post_category[]\", post_category_arr);\n      //setQueryStringArrayParam('post_category[]', post_category_this);\n    });\n\n    //search by writer link click / people_id / person_id:\n    $(\"#primary\").on(\"click\", \".search_by_writer\", function (e) {\n      e.preventDefault();\n      setQueryStringParam(\"paged\", \"1\");\n      var people_id = $(this).attr(\"data-id\");\n      if (people_id) {\n        /*\n        $('#people_id').val(people_id);\n        $(\"input[name=post_type][value=uutiset]\").attr('checked', 'checked');\n        $('.main-search-input').val('');\n        $('#search_key').val('');\n        triggerSearch();\n        */\n        setQueryStringParam(\"s\", \"\"); //need to empty search word\n        setQueryStringParam(\"people_id\", people_id);\n        setQueryStringParam(\"post_type\", \"uutiset\");\n      }\n      return false;\n    });\n  }\n  //if end\n\n  /* main search filters 2022 end */\n\n  $(\"label.btn\").on(\"click\", \"input\", function (e) {\n    e.stopPropagation();\n    $(this).attr(\"checked\", !$(this).attr(\"checked\"));\n    $(e.target).closest(\"label\").toggleClass(\"btn-flat\");\n  });\n\n  $(\".btn_checkbox\").on(\"click keypress\", function (e) {\n    e.preventDefault();\n    if (\n      $(\"#ch_1\").is(\":checked\") ||\n      $(\"#ch_2\").is(\":checked\") ||\n      $(\"#ch_3\").is(\":checked\") ||\n      $(\"#ch_4\").is(\":checked\") ||\n      $(\"#ch_5\").is(\":checked\") ||\n      $(\"#ch_6\").is(\":checked\")\n    ) {\n      $(\".newsletter__checkboxes\").get(0).style.display = \"none\";\n      $(\".newsletter__info\").get(0).style.display = \"block\";\n    } else {\n      $(\".newsletter__error\").get(0).style.display = \"block\";\n    }\n  });\n\n  function formAjaxHandler() {\n    // form selector (CHANGE to match <form> in the HTML)\n    const $form = $(\".newsletter\");\n\n    function init() {\n      // bind form submit listener\n      $form.on(\"submit\", handleFormSubmit);\n    }\n\n    function handleFormSubmit(event) {\n      // prevent default submit event because it will be handled as ajax request\n      event.preventDefault();\n\n      // use rf=json (request format = json)\n      const formUrl = \"https://www.kauppakamarinuutiskirjeet.fi/?rf=json\";\n\n      // send form to the given URL as XHR POST\n      return $.ajax({\n        data: $form.serialize(),\n        success: handleRequestSuccess,\n        type: \"POST\",\n        url: formUrl,\n      });\n    }\n\n    // do something with the form response\n    function handleRequestSuccess(response) {\n      $(\".newsletter__info\").get(0).style.display = \"none\";\n      $(\".newsletter__success\").get(0).style.display = \"block\";\n      //console.log(response);\n    }\n\n    return init();\n  }\n\n  // initialize form ajax handler on Document Ready\n  $(formAjaxHandler);\n\n  $(\".subpages-toggle\").on(\"click\", function (event) {\n    this.parentNode.classList.toggle(\"open\");\n    this.setAttribute(\n      \"aria-expanded\",\n      this.getAttribute(\"aria-expanded\") === \"false\"\n    );\n  });\n\n  $(\"#newsletter__back\").on(\"click\", function (event) {\n    event.preventDefault();\n    $(\".newsletter__info\").get(0).style.display = \"none\";\n    $(\".newsletter__checkboxes\").get(0).style.display = \"block\";\n  });\n\n  //new filter 2022 articles mobile\n  if ($(\".article-filter--select\").length > 0) {\n    $(\".article-filter--select\").on(\"change\", function () {\n      var link = $(this).val();\n      document.location.href = link;\n    });\n  }\n\n  function rearrangeIcegram() {\n    // accessibility (to get rid of reversed order)\n    let rearrangeItems = $(\"form.es_subscription_form > div\");\n\n    rearrangeItems.each(function () {\n      if (!$(this).attr(\"class\")) {\n        $(this).addClass(\"es-extra-field\");\n        $(\"form.es_subscription_form\").prepend($(this));\n      }\n    });\n  }\n\n  $(rearrangeIcegram);\n\n  function icegramDescriptions() {\n    // move descriptions below the corresponding checkbox\n    let listTable = $(\".ig-es-form-list-selection td\");\n\n    listTable.each(function (index) {\n      let descToAppend = $(`.subscribe .description-${index}`);\n      $(this).append(descToAppend);\n    });\n  }\n\n  $(icegramDescriptions);\n\n  // Header join now button fix\n  function fixJoinnow() {\n    let original = document.querySelector(\".header-join-now\");\n    let clone = original.cloneNode(true);\n    clone.removeAttribute(\"id\");\n    document.getElementById(\"header-join-now-copy\").innerHTML = clone.innerHTML;\n  }\n  $(fixJoinnow);\n});\n\n// Make Icregram Express list selection checkboxes checked (not possible in plugin dashboard)\nfunction icegramListSelection() {\n  let $inputs = $(\n    '.subscribe .ig-es-form-list-selection input[type=\"checkbox\"]'\n  );\n\n  console.log($inputs.length);\n\n  if ($inputs.length) {\n    $inputs.each(function () {\n      $(this).prop(\"checked\", true);\n    });\n  }\n}\n$(icegramListSelection);\n\n// Enable placeholders on top of CookieBot-blocked content\n((d, i, m) => {\n  let ct = (t) => d.createTextNode(t);\n  let ce = (e) => d.createElement(e);\n  d.querySelectorAll(i).forEach((e) => {\n    const a = ce(\"a\"),\n      div = ce(\"div\"),\n      p = ce(\"p\"),\n      s = e.dataset.cookieblockSrc,\n      sp = /google\\.com\\/maps\\/embed/.test(s)\n        ? \"Google Maps\"\n        : /player\\.vimeo\\.com\\/video\\//.test(s)\n        ? \"Vimeo\"\n        : /youtube(-nocookie)?\\.com\\/embed\\//.test(s)\n        ? \"YouTube\"\n        : /w\\.soundcloud\\.com\\/player\\//.test(s)\n        ? \"SoundCloud\"\n        : undefined;\n    if (!sp) return;\n    div.innerHTML =\n      `<div style=\"background-color:#CCC;display:inline-` +\n      `block;height:${e.height}px;position:relative;width:${e.width}px;\"><div style=` +\n      '\"background-color:#848484;border-radius:15px;height:50%;position:absolute;' +\n      'transform:translate(50%,50%);width:50%;\"><p style=\"color:#FFF;font-size:7.5em;' +\n      \"position:relative;top:50%;left:50%;margin:0;text-align:center;transform:translate\" +\n      '(-50%,-50%);\">&ctdot;</p></div>';\n    div.classList.add(`cookieconsent-optout-${m}`);\n    a.textContent = `ja hyväksy kaikki evästeet`;\n    a.href = \"javascript:Cookiebot.renew()\";\n    p.append(ct(\"Ole hyvä \"), a, ct(` nähdäksesi ${sp} -sisällön.`));\n    div.append(p);\n    e.parentNode.insertBefore(div, e);\n  });\n})(document, \"iframe[data-cookieblock-src]\", \"marketing\");\n", "function handleMouseDown() {\n  document.body.classList.remove(\"user-is-tabbing\");\n  window.addEventListener(\"keydown\", handleFirstTab);\n  window.removeEventListener(\"mousedown\", handleMouseDown);\n}\n\nfunction handleFirstTab(e) {\n  if (e.key === \"Tab\") {\n    document.body.classList.add(\"user-is-tabbing\");\n    window.removeEventListener(\"keydown\", handleFirstTab);\n    window.addEventListener(\"mousedown\", handleMouseDown);\n  }\n}\n\nwindow.addEventListener(\"keydown\", handleFirstTab);\n"], "sourceRoot": "assets/scripts/"}