/* ==========================================================================
 # Search-new 2022 - main view styles
========================================================================== */


.main.search-results-container {
  min-height: 600px;
  display: block;
}

html:lang(fi) {
  body.search .hero__container {
    padding: 2rem 1.5rem 4rem 1.5rem;
  }
}

/*.search-results / .search-no-results  // .search is body class */
.search .primary--search-fi {
  @include wrap();
  display: flex;
  margin-top: 0px;
  padding-top: 0px;
  aside {
    width: 25%;
    order: 0;
    .sub-pages__title {
      display: none;
    }
    @media screen and (max-width: 1124px) {
      width: 35%;
    }
    @include breakpoint($sub-s) {
      h3 {
        font-size: 1.25rem;
      }
    }
  }
  @media screen and (max-width: 860px) {
      flex-wrap: wrap;
      aside {
        width: 100%;
      }
  }
}

main.search-results-container {
  width: 75%;
  order: 1;
  padding-left: 3rem;
  padding-top: 1.5rem;
  @include breakpoint($sub-l) {
    width: 100%;
    padding-left: 2rem;
  }
  @media screen and (max-width: 1200px) {
    width: 100%;
    padding-left: 1.5rem;
  }
  @media screen and (max-width: 860px) {
    width: 100%;
    padding-left: 0;
  }
}


.search-nav {
  color: $primary;

  .box-title:after {
    position: absolute;
    content: "";
    display: inline-block;
    background-repeat: no-repeat;
    background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='27px' height='19px' viewBox='0 0 27 19' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3EGroup 2%3C/title%3E%3Cg id='HSKK-haku' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='HSKK---HAKU-4' transform='translate(-437.000000, -407.000000)' fill='%23002662' fill-rule='nonzero'%3E%3Cg id='Group-2' transform='translate(437.965101, 407.000000)'%3E%3Cpolygon id='Shape' transform='translate(23.034899, 4.000000) rotate(1.000000) translate(-23.034899, -4.000000) ' points='21.0526565 5.03475251 21.0177517 3.03505712 25.0060359 2.96524749 25.0520473 4.96494288'%3E%3C/polygon%3E%3Cpolygon id='Shape-Copy' transform='translate(6.284899, 4.000000) rotate(1.000000) translate(-6.284899, -4.000000) ' points='0.0533038085 5.10892524 0.0183989956 3.10922985 12.5164952 2.89107476 12.5514 4.89077015'%3E%3C/polygon%3E%3Cpolygon id='Shape-Copy-2' transform='translate(19.784899, 15.000000) rotate(1.000000) translate(-19.784899, -15.000000) ' points='14.5531515 16.0914728 14.5182467 14.0917774 25.0166475 13.9085272 25.0515523 15.9082226'%3E%3C/polygon%3E%3Cpolygon id='Shape-Copy-3' transform='translate(4.034899, 15.000000) rotate(1.000000) translate(-4.034899, -15.000000) ' points='0.0529611225 16.0696573 0.0180563096 14.0699619 8.01683787 13.9303427 8.05174268 15.9300381'%3E%3C/polygon%3E%3Cpath d='M16.0348995,0 C18.2440385,0 20.0348995,1.790861 20.0348995,4 C20.0348995,6.209139 18.2440385,8 16.0348995,8 C13.8257605,8 12.0348995,6.209139 12.0348995,4 C12.0348995,1.790861 13.8257605,0 16.0348995,0 Z M16.0348995,2 C14.93033,2 14.0348995,2.8954305 14.0348995,4 C14.0348995,5.1045695 14.93033,6 16.0348995,6 C17.139469,6 18.0348995,5.1045695 18.0348995,4 C18.0348995,2.8954305 17.139469,2 16.0348995,2 Z' id='Combined-Shape'%3E%3C/path%3E%3Cpath d='M11.0348995,11 C13.2440385,11 15.0348995,12.790861 15.0348995,15 C15.0348995,17.209139 13.2440385,19 11.0348995,19 C8.8257605,19 7.0348995,17.209139 7.0348995,15 C7.0348995,12.790861 8.8257605,11 11.0348995,11 Z M11.0348995,13 C9.93033,13 9.0348995,13.8954305 9.0348995,15 C9.0348995,16.1045695 9.93033,17 11.0348995,17 C12.139469,17 13.0348995,16.1045695 13.0348995,15 C13.0348995,13.8954305 12.139469,13 11.0348995,13 Z' id='Combined-Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    width: 32px;
    height: 32px;
    margin-top: 10px;
    margin-left: 10px;
  }


  .search-nav-box {
    // padding-left: 40px;
    // padding-top: 30px;
    // padding-bottom: 50px;
    // background-color: #E4ECF6;
    padding: 1rem 0 2rem 2rem;
    background-color: $light-blue-25;
    @media screen and (max-width: 1124px) {
      // padding-left: 20px;
    }
    h3 {
      font-size: 18px;
      margin-bottom: 10px;
    }
  }

  .box-guide {
    padding-right: 40px;
    @media screen and (max-width: $menu-visible) {
      display: none;
    }
    h2:after {
      content: none;
    }

    margin-top: 20px;
    p {
      font-size: 16px;
      margin: 8px 0 8px;
    }
    a {
      color: #002662;
      text-decoration: underline;
      &:hover {
        text-decoration: underline;
      }
      &.external {
        @include external-link-dark;
      }
    }
  }

}


.search-filters label {
  display: flex;
  align-items: center;
  width: max-content;
  clear: both;
  font-size: 16px;
  padding: 3px 0px;
  cursor: pointer;
  input[type=radio]
  /*, input[type=checkbox]*/ {
    // margin-left: -25px;
    margin-right: 12px;
    width: 16px;
    height: 16px;
    &:checked, &:hover{
      accent-color: $link-use;
    }
  }
  input[type=radio] {
    margin: 0;
    margin-right: 0.5rem;
  }
  // custom checkbox
  input[type=checkbox] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }
  .custom-checkbox {
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    margin-right: .5rem;
    background-color: #fff;
    border: 2px solid $light-blue;
  }
  input:checked ~ .custom-checkbox:after{
    display: block;
    // custom 'checked' icon
    content:"\f00c";
    font-family: 'Font Awesome 6 Sharp';
    font-weight: 900;
    text-align: center;
    line-height: normal;
  }
  input:focus ~ .custom-checkbox {
    outline: 1px solid $light-blue;
    //outline-offset: 0.5px;
  }
  // --
  span {
    vertical-align: top;
    display: inline-block;
    line-height: 22px;
  }
}



.search-filters .main-category-list {
  margin-top: 10px;
  margin-bottom: 10px;
  margin-right: 15px;

  h3 {
    margin-bottom: 10px;
    margin-top: 30px;
  }

}

.search-filters .main-category-list.hidden {
  display: none;
  margin: 0;
}


.search-results-posts-pages {
  @include wrap;
  @include extra-narrow-content-left;
}


.search-results-info {
  font-weight: 700;
  font-size: 24px;
  text-decoration: none;
  color: #002662;
  margin-top: 0;
  margin-bottom: 40px;
  padding-left: 1.5rem;
}

.search-results-none,
.no-search-made {
    display: block;
    min-height: 300px;
    margin-top: 42px;
}

.writer_selected {
  display: block;
  padding: 0px;
  &>* {
    padding-left: 1.5rem;
  }
}


/* pagination */

.search-pagination {
  margin-top: 60px;
  margin-bottom: 40px;

  .page-numbers {
    line-height: 30px;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 0.23px;
    text-align: center;
    text-decoration: none;
  }

  .page-numbers:not(.next):not(.prev) {
    display: inline-block;
    width: 30px;
    height: 30px;
    // background-color: #D8E3F3;
    background-color: $light-blue-25;
    color: #002662;
    border-radius: 30px;
    margin-left: 10px;
    margin-right: 10px;
  }
  .next,
  .prev {
    color: #002662;
  }

  .page-numbers.current:not(.next):not(.prev) {
    background-color: #002662;
    color: #fff;
  }
}



@media screen and (max-width: $menu-visible) {
  body.search {
    .search-results-info,
    .search-results--people,
    .search-results-posts-pages {
      padding-left: 0;
    }
  }
}
