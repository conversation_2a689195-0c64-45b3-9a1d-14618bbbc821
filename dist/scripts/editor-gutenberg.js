"use strict";function displayCurrentTab(t,e,r){for(var o=0;o<e.length;o++)t===o?(e[o].style.height="auto",e[o].parentNode.parentNode.classList.contains("scrollto")&&e[o].scrollIntoView({block:"start",behavior:"smooth"})):e[o].style.height="0";for(var a=0;a<r.length;a++)t===a?r[a].classList.add("on"):r[a].classList.remove("on")}if(wp.domReady(function(){wp.blocks.unregisterBlockStyle("core/button","outline"),wp.blocks.unregisterBlockStyle("core/button","squared"),wp.blocks.unregisterBlockStyle("core/separator","dots"),wp.blocks.unregisterBlockStyle("core/separator","wide"),wp.blocks.unregisterBlockStyle("core/quote","large")}),document.querySelector(".tabs"))for(var tabs=document.querySelectorAll(".tabs"),_loop=function(){var r=tabs[i].querySelectorAll(".tabs__btn"),o=tabs[i].querySelectorAll(".tabs__content"),t=tabs[i].querySelectorAll(".tabs__content--close");document.querySelector(".active_tabs")&&displayCurrentTab(0,o,r);for(var e=0;e<t.length;e++)t[e].addEventListener("click",function(t){for(var e=0;e<o.length;e++)o[e].style.height="0"});tabs[i].addEventListener("click",function(t){if(jQuery(".product__wrap").trigger("resize"),jQuery(".product__wrap").trigger("resize"),t.target.classList.contains("tabs__btn")||t.target.parentNode.classList.contains("tabs__btn")||t.target.parentNode.parentNode.classList.contains("tabs__btn"))for(var e=0;e<r.length;e++)if(t.target===r[e]||t.target.parentNode===r[e]||t.target.parentNode.parentNode===r[e]){displayCurrentTab(e,o,r);break}})},i=0;i<tabs.length;i++)_loop();
//# sourceMappingURL=editor-gutenberg.js.map
