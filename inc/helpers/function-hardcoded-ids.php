<?php
/**
 * Hardcoded IDs
 *
 * @package kauppakamari
 */

/**
 * Get hardcoded ID by key
 *
 * @param string $key a name of hardcoded id
 *
 * @return int harcoded id
 */
function kauppakamari_get_hardcoded_id($key = '') {

  switch ($key) {

    // case 'example':
    //   return 123;
    case 'koulutus':
      return 7;
      break;
    case 'kamari':
      return 1;
      break;
    case 'img_placeholder':
      return 192;
      break;
    default:
      return 0;

  }

}
