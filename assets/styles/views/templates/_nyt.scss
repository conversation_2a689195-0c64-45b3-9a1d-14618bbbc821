#primary {
  &.has-sidebar {
    @include wrap;
    display: flex;
    main {
      flex: 3;
      margin-right: 2rem;
      padding-right: 2rem;
      border-right: 2px solid $grey-darker;
      @include breakpoint($sub-l) {
        width: 100%;
        margin-right: 0rem;
        padding-right: 0rem;
        border-right: 0px solid $grey-darker;
      }
    }
    aside {
      flex: 1;
      img {
        display: none;
      }
      .sidebar-widget {
        margin-bottom: 2rem;
        &__header {
          display: flex;
          justify-content: space-between;
          border-bottom: 1px solid $grey-darker;
          padding-bottom: .5rem;
         &--title {
           font-weight: bold;
           text-transform: uppercase;
         }
        }
      }
      .recent-posts {
        &-item {
          display: block;
          border-bottom: 1px solid $grey-dark;
          color: black;
          text-decoration: none;
          &:hover {
            text-decoration: underline;
          }
          h5 {
            margin: 0rem 0 .5rem;
            font-weight: 500;

          }
          .date {
            margin-top: .5rem;
            color: $grey-aa;
            display: block;
            font-weight: bold;
          }

        }
      }
      .block-news-list__item {
        padding: 1rem 0;
        h5 {
          font-weight: normal;
        }
      }
      .icon-twitter {
        color: #00aced;
      }
      .twitter-feed {
        &--item {

        }
        &--meta {
          display: flex;
          margin-bottom: .25rem;
          color: $grey-aa;
          .date {
            font-weight: bold;
            margin-right: .5rem;
          }
          .time {
            margin-left: .5rem;
          }
        }
        a {
          text-decoration: none;
          color: #2C2C2C;
        }
        h5 {
          margin-bottom: .25rem;
          color: #3E3E3E;
          font-size: 1rem;
          font-weight: 600;
        }
      }
      @include breakpoint($sub-l) {
        display: none;
      }
    }
  }
}
.kauppakamarinyt_list_links {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  width: 100%;
  margin: 0 auto;
  border-bottom: 1px solid $grey-dark;
  a {
    padding: 1rem;
    font-weight: 400;
    text-decoration: none;
  }
}
.load-more {
  @include button;
  margin-top: 1.5rem;
  display: block;
  width: auto;
  min-width: 150px;
  text-align: center;
  border: 1px solid $brand;
  color: white;
  background: $brand;
  width: fit-content;
  margin: 2rem auto;
  &:hover {
    color: white;
  }
  &.external {
    background-color: #002663;
    border: 1px solid #002663;
    color: white;
    padding: 0.75rem 4.5rem 0.75rem 3rem;
    &:after {
      position: absolute;
      background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23ffffff' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
      background-position: center;
      background-size: 84%;
      width: 1.1rem;
      height: 1rem;
      display: inline-block;
      content: " ";
      margin-left: 8px;
      color: transparent;
      background-repeat: no-repeat;
    }
    &:hover {
      background-color: white;
      color: #002663;
      &:after {
        background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23002663' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
      }
    }
  }
  &:hover {
    background-color: $primary;
    border-color: $primary;
    color: white;
  }
}

.kauppakamarinyt_list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.kauppakamarinyt {
  .kauppakamarinyt_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    border-bottom: 1px solid $grey-darker;
    .kauppakamarinyt_filter_title {
      font-size: 1rem;
      font-weight: 700;
      margin:0;
    }
    @include breakpoint($sub-l) {
      display: block;
      .kauppakamarinyt_filter_title {
        font-size: 1.5rem;
        text-align: center;
        margin: 1.5rem 0;
      }
    }
  }
  /* The container must be positioned relative: */
  .kauppakamarinyt_select_wrap span {
    margin-left: 4rem;
  }
  .kauppakamarinyt_select {
    position: relative;
    width: 225px;
  }

  .kauppakamarinyt_select select {
    display: none; /*hide original SELECT element: */
  }

  /* Style the arrow inside the select element: */
  .select-selected:after {
    position: absolute;
    content: "";
    top: 14px;
    right: 10px;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-color: black transparent transparent transparent;
  }

  /* Point the arrow upwards when the select box is open (active): */
  .select-selected.select-arrow-active:after {
    border-color: transparent transparent black transparent;
    top: 7px;
  }

  /* style the items (options), including the selected item: */
  .select-items div,.select-selected {
    color: black;
    padding: 8px 16px;
    border: 1px solid transparent;
    border-color: transparent transparent rgba(0, 0, 0, 0.1) transparent;
    cursor: pointer;
  }

  .select-selected {
    //background-color: $grey-light;
    color: $brand;
    border-bottom: 2px solid $brand;
  }


  /* Style items (options): */
  .select-items {
    position: absolute;
    background-color: white;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 99;
    box-shadow: 0 5px 13px -10px #333;
  }

  /* Hide the items when the select box is closed: */
  .select-hide {
    display: none;
  }

  .select-items div:hover, .same-as-selected {
    background-color: rgba(0, 0, 0, 0.1);
  }
}

.kauppakamarinyt_select_wrap {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  @include breakpoint($sub-l) {
    flex-wrap: wrap;
  }
  span {
    margin-right: 1rem;
  }
}

.kauppakamarinyt_select_column {
  display: flex;
  justify-content: space-between;
  align-items: center;
  @include breakpoint($sub-l) {
    margin-bottom: 1rem;
  }
}

.nyt__checkboxes {
  margin-bottom: 2rem;
  label {
  margin-right: 10px;
  margin-bottom: .5rem;
  padding: 10px 15px;
  border: 2px solid $primary;
  text-align: center;
  color: $primary;
  //min-width: 350px;
  font-weight: 600;
  transition: all .1s ease-in-out;
  }

  input:checked + label {
  color: white;
  background-color: $primary ;
  }
  &--wrap {
    display: flex;
    flex-wrap: wrap;
  }
  .hidden {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
  }

}

.aside__nyt-link {
  margin-bottom: 2rem;
  margin-top: 1rem;
  display: block;

  img {
    display: block !important;
  }
}

.kauppakamarinyt .wysiwyg {
  padding-left: 0;
}

body.page-template-template-NYT {
  .breadcrumb-container {
    margin-bottom: 0px;
  }

  .nyt-main {
    margin-bottom: 38px;
  }
}
