/* ==========================================================================
 # Teaser
========================================================================== */

.teaser-container {
  @include narrow-content;
  margin-left: auto;
  margin-right: auto;
}

.teaser {
  display: block;
  justify-content: space-between;
  margin-bottom: $gap;
  border-top: 1px solid $separatorblue;
  padding-top: 1.5rem;
  width: 100%;

  @include breakpoint($s) {
    display: flex;
  }

  &--large {
    display: block;
    width: 100%;
    border-bottom: 1px solid $separatorblue;
    border-top: none;
    margin-bottom: 2rem;
    padding-bottom: 0.5rem;

    img {
      width: 100%;
      margin-bottom: 1rem;
    }

    .teaser__thumbnail {
      margin-right: 0;
      max-width: 100%;
    }
  }

  &--medium {
    display: block;
    border-top: none;
    width: 100%;

    @include breakpoint($m) {
      width: calc(50% - 1.5rem);
    }

    img {
      width: 100%;
      margin-bottom: 1rem;
    }

    .teaser__thumbnail {
      margin-right: 0;
      max-width: 100%;
    }
  }

  &__thumbnail {
    flex-shrink: 0;
    margin-right: $gap;
    margin-bottom: $gap;
    width: 100%;

    @include breakpoint($s) {
      max-width: 30%;
      margin-bottom: 0;
    }

    a,
    img {
      display: block;
      line-height: 0;
    }
  }

  &__content {
    flex-grow: 1;
  }

  &__header {
    margin-bottom: 8px;

    &__title {
      font-size: 2.20rem;
      font-weight: 700;
      margin: 0;

      @media screen and (max-width: 580px) {
        font-size: 23px;
        hyphens: auto;
      }

      a {
        text-decoration: none;
        // color: $base-font-color;
        color: $primary;
      }
    }

    &__date {
      display: block;
      font-size: .875rem;
    }

  }

  &__summary {
    @include child-margin-reset;

    p {
      font-size: 1rem;
    }

    a {
      text-decoration: none;
      color: $base-font-color;
    }
  }

}





/* teaser 50 / 50 archive */

.teaser-container--archive {
  @include wrap;

  &--path {
    font-size: 16px;
  }
}

.teaser-new-2cols {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
  justify-content: space-between;
  text-align: left;
  border-bottom: 3px solid $separatorblue;
  margin-bottom: 2rem;

  @media (min-width: 45rem) and (max-width: 69.9375rem) {
    gap: 2rem;
  }

  @include breakpoint($sub-m) {
    display: block;
  }
}


.teaser-new-2cols .teaser-1-subcol__item {
  width: calc(50% - 1.5rem);
  background-color: transparent;
  padding: 0;
  display: block;
  -ms-flex-pack: justify;
  justify-content: space-between;
  background-color: transparent;
  padding: 0;
  display: block;
  justify-content: space-between;

  @include breakpoint($sub-l) {
    width: 100%;
    margin-bottom: 1.5rem;
  }

  &--content {
    padding-bottom: 16px;
    font-size: 16px;
    color: #000;
  }

  img {
    width: 100%;
    max-width: 100%;
  }

  .entry-meta {
    margin-top: 8px;
    margin-bottom: 2px;
  }

  .title {
        font-size: 24px;
    font-weight: 700;
    line-height: 32px;
    margin-bottom: 8px;
  }

  .date {
    font-size: 15.0px;
    margin: .5rem 0rem;
    display: inline-block;
    color: $grey-darker;
    font-weight: 600;
    text-transform: capitalize;

  }

  .time {
    color: $grey-darker;
  }

  .location {
    color: $grey-darker;
  }

  a {
    color: $primary;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

/* title bigger, but not in home page */
body:not(.home) {
  .teaser-new-2cols .teaser-1-subcol__item {
    .title {
      // @include h2-new-bigger;
    }
  }
}





/* teaser-new-wide */

.teaser-new-wide {

  border-top: none;
  border-bottom: none;

  .entry-meta__date {}

  @include breakpoint($sub-m) {
    .entry-meta__term.entry-meta__term-new>a:not(:first-child) {
      display: none;
      /* on mobile show only one pill */
    }
  }

  @media (min-width: 35rem) {
    .teaser__thumbnail {
      max-width: 38%;
    }
  }

  .teaser__header__title {

    /*@include h2-new-bigger;*/
    a:hover {
      text-decoration: underline;
    }
  }

  .teaser__summary {
    font-size: 16px;
    color: #000;
  }

  .teaser__content {
    padding-bottom: 40px;
    border-bottom: 2px solid $separatorblue;
  }

}


/* title bigger, but not in home page */
body:not(.home) {
  .teaser-new-wide {
    .teaser__header__title {
      font-size: 24px;
      font-weight: 700;
      line-height: 32px;
      margin-bottom: 8px;
    }
  }
}
