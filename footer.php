<?php

/**
 * The template for displaying the footer.
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package kauppakamari
 */

?>

</div><!-- #content -->



<?php if (!is_front_page() && !is_koulutus() && !is_singular(array('post', 'release')) && !get_field('hide_rns')) { ?>
    <!-- rns place -->

<?php } else {
} ?>

<?php if (get_locale() === 'fi') : ?>

    <?php
    if (
        is_page('19') && !is_koulutus()
        || is_singular(array('post', 'release')) && !is_koulutus()
        || is_page_template('template-landingpage-blank.php') && !is_koulutus() && get_field('formi') === 'new_articles'
    ) { ?>
        <div class="subscribe">
            <div class="subscribe__wrap">
                <fieldset>
                    <legend class="screen-reader-text"><?php echo get_field('ajankohtaista_title', 'options'); ?></legend>
                    <h2><?php echo get_field('ajankohtaista_text', 'options'); ?></h2>
                    <?php if (!empty(get_field('ajankohtaista_accompany', 'options'))) : ?>
                        <p class="accompany"><?php echo get_field('ajankohtaista_accompany', 'options') ?></p>
                    <?php endif; ?>
                    <?php if (get_field('ajankohtaista_1_description', 'options') && get_field('ajankohtaista_2_description', 'options')) : ?>
                        <span class="description description-0"><?= get_field('ajankohtaista_1_description', 'options'); ?></span>
                        <span class="description description-1"><?= get_field('ajankohtaista_2_description', 'options'); ?></span>
                    <?php endif; ?>

                    <?php echo do_shortcode('[email-subscribers-form id="1"]'); ?>
                </fieldset>
            </div>
        </div>
    <?php } elseif (get_field('hide_newsletter') == false) {
        // get_template_part('partials/form');
    }
    ?>
    <?php
    if ($footer_content = get_field('footer_fi', 'options'))  ?>

    <div class="footer-content">
        <div class="footer-content__column footer-content__column-primary">
            <div>
                <div class="branding">
                    <span class="branding__title">
                        <span class="screen-reader-text">Kauppakamari<?php bloginfo('name'); ?></span>
                        <img src="<?php echo get_template_directory_uri() . '/dist/images/HSKK_logo_footer.svg'; ?>" alt="Helsingin seudun kauppakamari logo" />
                    </span>
                </div>

                <p class="footer-column-header">Yhteystiedot</p>
                <div class="text-content">
                    <?php echo $footer_content['content'] ?>
                </div>
            </div>
        </div>

        <div class="svg-wrapper hide-on-desktop">
            <img class="mobile-footer-svg" src="<?php echo get_template_directory_uri() . '/dist/images/footer-svg_1.svg'; ?>" alt="">
            <img class="mobile-footer-svg" src="<?php echo get_template_directory_uri() . '/dist/images/footer-svg_2.svg'; ?>" alt="">
            <img class="mobile-footer-svg" src="<?php echo get_template_directory_uri() . '/dist/images/footer-svg_3.svg'; ?>" alt="">
        </div>

        <div class="footer-content__column footer-content__column-second">

            <div class="footer-content__column-second__links">
                <div class="svg-wrapper hide-on-mobile">
                    <img src="<?php echo get_template_directory_uri() . '/dist/images/footer-svg_1.svg'; ?>" alt="">
                    <br>
                </div>
                <p class="footer-column-header">Pikalinkit</p>
                <?php
                $links = $footer_content['links'];
                if (!empty($links)) {
                    foreach ($links as $link) { ?>
                        <a class="footer-content__link" href="<?php echo $link['link']['url']; ?>" target="<?php echo $link['link']['target']; ?>">
                            <?php echo $link['link']['title']; ?>
                        </a>
                <?php }
                }
                ?>
            </div>

            <div class="footer-content__column-second__sites">
                <div class="svg-wrapper hide-on-mobile">
                    <img src="<?php echo get_template_directory_uri() . '/dist/images/footer-svg_2.svg'; ?>" alt="">
                    <br>
                </div>
                <p class="footer-column-header">Palvelumme verkossa</p>
                <div class="footer-links">
                    <?php
                    function footerCmp($a, $b)
                    {
                        return strcmp($a['footer_order'], $b['footer_order']);
                    }

                    $sites = kauppakamari_get_sites();
                    if (!empty($sites)) {
                        //change order and remove 0
                        foreach ($sites as $s => $site) {
                            if ($site['footer_order'] == 0) {
                                unset($sites[$s]); //remove if footer_order is 0
                            }
                        }

                        usort($sites, "footerCmp");
                        reset($sites);

                        foreach ($sites as $site) { ?>
                            <a class="footer-content__site" href="<?php echo $site['link']['url']; ?>" target="<?php echo $site['link']['target']; ?>">
                                <?php echo $site['link']['title']; ?>
                            </a>
                        <?php } ?>
                </div>
            </div>

            <div class="footer-content__column-second__social">
                <div class="site-footer__social2">
                    <div class="svg-wrapper hide-on-mobile">
                        <img src="<?php echo get_template_directory_uri() . '/dist/images/footer-svg_3.svg'; ?>" alt="">
                        <br>
                    </div>
                    <?php get_template_part('partials/navigation/menu-social2'); ?>
                </div>
            </div>

        </div>
    </div>

<?php }
?>

<?php endif; ?>

<footer id="colophon" class="site-footer" itemscope itemtype="http://schema.org/WPFooter">

    <div class="site-footer__container">

        <div class="site-footer__links">
            <?php get_template_part('partials/navigation/menu-footer'); ?>
        </div>

        <div class="empty">
        </div>

    </div>

    <!-- scroll up button -->
    <a href="#" id="scroll-up-btn" aria-label="<?php echo pll__('Takaisin ylös'); ?>" title="<?php echo pll__('Takaisin ylös'); ?>">
        <span class="fa-solid fa-sharp fa-chevron-up fa-xl"></span>
    </a>

</footer><!-- #colophon -->
</div><!-- #page -->

<?php wp_footer(); ?>

</body>

</html>