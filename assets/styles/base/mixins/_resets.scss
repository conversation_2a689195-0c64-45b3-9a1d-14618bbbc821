/* ==========================================================================
 # Reset mixins
========================================================================== */

// reset first and last children margins
@mixin child-margin-reset {
  & > :first-child {
    margin-top: 0;
  }
  & > :last-child {
    margin-bottom: 0;
  }
}

// reset <a> styles
@mixin link-reset {
  color: inherit;
  text-decoration: none;
}

// reset <li> styles
@mixin list-reset {
  list-style: none;
  margin: 0;
  padding: 0;
  text-indent: none;
}

// reset <button> styles
@mixin button-reset {
  background: none;
  border: 0;
  border-radius: 0;
  color: inherit;
  font: inherit;
  line-height: normal;
  overflow: visible;
  padding: 0;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
}

// reset <input> styles
@mixin input-reset {
  background: none;
  border: 0;
  border-radius: 0;
  box-shadow: none;
}


// new H1 style for news landing, bigger, mostly used in the <H2>
@mixin h1-new-bigger {
  font-size: 38px;
  font-weight: 600;
  line-height: 43px;
  @include breakpoint($sub-m) {
    font-size: 30px;
    font-weight: 700;
  }
}
// new H2 style for news landing, bigger, mostly used in the <H3>
@mixin h2-new-bigger {
  font-size: 38px;
  font-weight: 600;
  line-height: 32px;
  @include breakpoint($sub-m) {
    font-size: 30px;
    font-weight: 600;
  }
}

// new H3 style for news landing, bigger, mostly used in the blocks
@mixin h3-new-bigger {
  font-size: 24px;
  font-weight: 600;
  line-height: 32px;
  @include breakpoint($sub-m) {
    font-size: 24px;
    font-weight: 700;
  }
}
