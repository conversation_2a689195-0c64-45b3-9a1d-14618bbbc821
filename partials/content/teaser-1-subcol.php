<?php
/**
 * Template part: Teaser-1-col, sub part of 50/50 block
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package kauppakamari
 */

?>

<div id="teaser-<?php the_ID(); ?>" <?php post_class('teaser-1-subcol teaser-1-subcol__item teaser-new--' . get_post_type()); ?>>

  <?php if (has_post_thumbnail()) : ?>
    <div class="teaser-1-subcol__item--teaser__thumbnail">
      <a href="<?php the_permalink(); ?>" tabindex="-1">
        <?php echo kauppakamari_get_image(get_post_thumbnail_id(), 'article'); ?>
      </a>
    </div>
  <?php else : ?>
    <div class="teaser-1-subcol__item--teaser__thumbnail">
      <a href="<?php the_permalink(); ?>" tabindex="-1">
        <?php echo kauppakamari_get_image(kauppakamari_get_hardcoded_id('img_placeholder'), 'article'); ?>
      </a>
    </div>
  <?php endif; ?>

  <div class="teaser-1-subcol__item--content">

    <?php
    global $show_three_meta;
    if(!isset($show_three_meta)) {
      $show_three_meta = false;
    }
    echo kauppakamari_get_post_meta_new(!$show_three_meta); /* show 3 if we are in the archive page */ ?>
      <?php //the_title(sprintf('<h3 class="teaser__header__title teaser__header__title-new"><a href="%s" rel="bookmark">', esc_url(get_permalink())), '</a></h3>');
      ?>
      <a href="<?php echo get_the_permalink(); ?>">
        <?php
        $col_title = the_title('', '', false);
        ?>
        <h3 class="title"><?php echo $col_title; ?></h3>
      </a>

    <div class="teaser-1-subcol__item--summary">
      <?php
      if(strlen($col_title) < 120) {
        $col_ingress = get_field('ingress', get_the_ID());
        if ($col_ingress && strlen($col_ingress) < 220) {
          echo $col_ingress;
        } else {
          echo excerpt(16);
        }
      }
      else {
        echo excerpt(16);
      }
      ?>
    </div>

  </div>

</div>
