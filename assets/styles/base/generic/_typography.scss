/* ==========================================================================
 # Typography - Text settings in any context and everywhere
========================================================================== */

html {
  color: $primary;
  font-family: $font-text;
  font-size: 93.75%;
  line-height: $line-height;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

// New frontpage typography
.is-front-page {
  p,
  .p {
    display: block;
    font-size: 16px;
    @include breakpoint($m) {
      font-size: 18px;
    }
    font-weight: $ws-regular;
    margin: 0 0 1.5rem;
  }

  h1,
  .h1 {
    display: block;
    font-size: 72px;
    @include breakpoint($sub-m) {
      font-size: 38px;
      //line-height: 57px;
      line-height: 1.25;
    }
    font-weight: $ws-semibold;
    line-height: 1.125;
    color: $primary;
    margin: 0 0 1.5rem;
  }

  h2,
  .h2 {
    display: block;
    font-size: 38px;
    @include breakpoint($sub-s) {
      font-size: 28px;
      line-height: 42px;
    }
    font-weight: $ws-semibold;
    line-height: 57px;
    color: $primary;
    margin: 1.25rem 0;
  }

  h3,
  .h3 {
    display: block;
    font-size: 28px;
    @include breakpoint($sub-s) {
      font-size: 28px;
      line-height: 34px;
    }
    font-weight: $ws-semibold;
    line-height: 42px;
    color: $primary;
    margin-bottom: 1.25rem;
  }

  h4,
  .h4 {
    display: block;
    font-size: 22px;
    @include breakpoint($sub-s) {
      font-size: 18px;
      line-height: 28px;
    }
    font-weight: $ws-semibold;
    line-height: 34px;
    color: $primary;
    margin-bottom: 1.25rem;
  }

  h5,
  .h5 {
    display: block;
    font-size: 18px;
    @include breakpoint($sub-s) {
      font-size: 18px;
      line-height: 28px;
    }
    font-weight: $ws-semibold;
    line-height: 28px;
    color: $primary;
    margin-bottom: 0;
  }
}

/* Paragraphs
----------------------------------------------- */

p,
.p {
  display: block;
  font-size: 16px;

  @include breakpoint($s) {
    font-size: 18px;
  }

  font-weight: $normal;
  line-height: $line-height;
  margin: 0 0 1.5rem;
  &.lead {
    font-size: 16px;
    @include breakpoint($s) {
      font-size: 18px;
    }
  }
}

.entry__content {
  h1 {
    display: none;
  }
}

.entry__content li {
  font-size: 18px;
}

.main .wp-block-columns .wp-block-column p {
  font-size: 18px;
}

/* Titles
----------------------------------------------- */

// New titles (not frontpage)
h1,
.h1 {
  display: block;
  font-size: 38px;
  @include breakpoint($sub-s) {
    font-size: 30px;
  }
  font-weight: $ws-semibold;
  line-height: 1.125;
  margin: 0 0 2rem;
}

h2,
.h2 {
  display: block;
  font-size: 28px;
  @include breakpoint($sub-s) {
    font-size: 22px;
  }
  font-weight: $ws-semibold;
  line-height: 1.25;
  margin: 1.5rem 0;
}

h3,
.h3 {
  display: block;
  font-size: 22px;
  @include breakpoint($sub-s) {
    font-size: 18px;
  }
  font-weight: $ws-semibold;
  line-height: $line-height;
  margin-bottom: 1.5rem;
}

h4,
.h4 {
  display: block;
  font-size: 20px;
  @include breakpoint($sub-s) {
    font-size: 16px;
  }
  font-weight: $ws-semibold;
  line-height: $line-height;
  margin-bottom: 1.5rem;
}

h5,
.h5 {
  display: block;
  font-size: 16px;
  @include breakpoint($sub-s) {
    font-size: 16px;
  }
  font-weight: $ws-semibold;
  line-height: $line-height;
  margin-bottom: 0;
}

h6,
.h6 {
  display: block;
  font-size: 1rem;
  @include breakpoint($s) {
    font-size: 16px;
  }
  font-weight: $bold;
  line-height: $line-height;
  margin-bottom: 0;
}

/* Lists
----------------------------------------------- */

ul,
ol {
  margin: 0 0 1rem;
  li {
    //font-size: 1rem;
    @include breakpoint($s) {
      //font-size: 1rem;
    }
  }
}

/* Links
----------------------------------------------- */

a {
  color: $brand;
  &:hover,
  &:active,
  &:focus {
  }
}

/* Accessibility
----------------------------------------------- */

a:focus {
  outline: 1px dotted;
}

.screen-reader-text {
  @include visuallyhidden;
}

.skip-to-content {
  background-color: #f1f1f1;
  color: $link-use;
  box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.2);
  display: block;
  left: -9999em;
  outline: none;
  padding: 1rem 1.5rem;
  text-decoration: none;
  text-transform: none;
  top: -9999em;

  .user-is-tabbing &:focus {
    clip: auto;
    height: auto;
    left: 0.5rem;
    top: 0.5rem;
    width: auto;
    z-index: 100000;
    color: $link-base;
  }
}

button:focus,
input[type="button"]:focus,
input[type="reset"]:focus,
input[type="submit"]:focus,
.social-share-link:focus {
  outline: 1px dotted;
  outline-offset: -2px;
}

h1.new-title {
  margin-top: 41px; /* adjusts to same place than 2 column page start */
}

a[href^="http"]:not([href*="helsinki.chamber.fi"]):not(.external):not(
    .wp-block-button__link
  ):not(.external-img) {
  @include external-link;
}

a[href^="http"]:not(.wp-block-button__link):not(
    [href*="helsinki.chamber.fi"]
  ):not(.external):after {
  margin-top: 4px;
  margin-left: 4px;
}

.sk-ww-linkedin-page-post a {
  position: relative;
  &:after {
    display: none !important;
  }
}

.event-list-wrapper {
  a[href^="http"]:not(.wp-block-button__link):not(
      [href*="helsinki.chamber.fi"]
    ):not(.external-img):not(.external):after {
    display: none;
  }
}
