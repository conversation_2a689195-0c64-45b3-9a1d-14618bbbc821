<?php
$item = $args['item'];
?>

      <div class="block-text-image__item <?php
        if ($item['background-color'] == "Roosa") {
          echo 'background-coral';
        }
        elseif ($item['background-color'] == "Vaal<PERSON>sininen") {
          echo 'background-lightblue';
        }
        else {echo 'background-white';}
      ?>">
        <div class="block-text-image__content">
          <span class="title">
            <?php echo $item['title'] ?: "Otsikko"; ; ?>
          </span>
          <span class="content">
            <?php echo $item['content'] ?: "Sisältö"; ?>
          <span>
          <?php if($item['link']) {
              echo '<a href="' . $item['link']['url'] . '" ><span>' . $item['link']['title'] . '</span></a>';
            }
          ?>
        </div>
        <?php 
          if($item['show_icon'] == true ) {
            echo '<img class="addition-graphic" src="'.get_template_directory_uri() . '/dist/images/text-image-ratas.svg'.'" alt="">';
          }
          ?>
        <?php
        if($icon = $item['image']) {
          //echo '<img src="' . $icon['sizes']['medium_large'] . '" alt="' . $item['title'] . '" />';
          if($item['title'] == '') { $item['title'] = 'Artikkelikuva'; }
          echo '<div class="text__img_bg_image bg_image_1" style="background-image: url('.$icon['sizes']['medium_large'].')" aria-description="'.$item['title'].'">';
          echo '</div>';
        }
        ?>
      </div>

