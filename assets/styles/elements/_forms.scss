/* ==========================================================================
 # Forms
========================================================================== */

/* Generic form styles
----------------------------------------------- */

fieldset {
  padding: 0;
  margin: 0;
  border: 0;
  min-width: 0;
}

legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 1rem;
  border: 0;
  text-transform: none;
}

label {
  max-width: 100%;
}

input[type="radio"],
input[type="checkbox"] {
  margin: 0.25rem 0 0;
  line-height: normal;
}

select,
textarea,
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"] {
  max-width: 100%;
  padding: 0.5rem 1rem;
  color: $base-font-color;
  background: $white;
}

textarea {
  height: 180px;
}

button,
input[type="submit"] {
}

/* Search form
----------------------------------------------- */

.search-form {
  display: flex;
  max-width: 30rem;
  &__input {
    flex-grow: 1;
    margin-right: 0.5rem;
  }
  &__submit {
    @include button;
  }

  // 404 search form
  &--404 {
    margin: $gap auto 0;
  }

  // no search results form
  &--search-empty {
    margin: $gap auto 0;
  }
}

.gform_wrapper .gf_progressbar {
  border: none !important;
  padding: 0px !important;
}
.gform_wrapper .gf_progressbar_percentage {
  background: $brand !important;
}

.gform_wrapper .percentbar_blue {
  background-color: $brand !important;
  color: #fff !important;
  height: 5px !important;
  span {
    display: none !important;
  }
}
.gform_wrapper .gf_progressbar:after {
  content: "";
  display: block;
  width: 100%;
  z-index: 990;
  height: 5px !important;
  margin-top: -5px !important;
}

.gf_left_half,
.gf_left_half {
  margin-bottom: 1rem !important;
}

.gform_legacy_markup_wrapper
  .field_description_below
  .gfield_description.gform_fileupload_rules {
  display: block;
}
