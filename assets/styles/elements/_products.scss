.product__wrap {
  display: flex;
  background-color: $grey;
  padding: 2rem 0;

 .product {
   max-width: 286px;
   width: 100%;
   margin-right: 1rem;
   background-color: white;
   box-shadow: 0 0 13px -5px rgba(0,0,0,.4);
   &--image {
    object-fit: cover;
    img {
      max-width: 100%;
    }
   }
   &--name {
    display: block;
    padding: 1rem;
    font-weight: 600;
   }
   a {
    align-self: flex-end;
    @include button;
    width: 100%;
    margin: 1rem;
    max-width: 186px;
    text-align: center;
    border: 1px solid $brand;
    color: $brand;
    background: transparent;
    margin-bottom: .75rem;
    &:hover {
      color: white;
    }
    &.external {
      background-color: #002663;
      border: 1px solid #002663;
      color: white;
      padding: 0.75rem 4.5rem 0.75rem 3rem;
      &:after {
        position: absolute;
        background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23ffffff' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-position: center;
        background-size: 84%;
        width: 1.1rem;
        height: 1rem;
        display: inline-block;
        content: " ";
        margin-left: 8px;
        color: transparent;
        background-repeat: no-repeat;
      }
      &:hover {
        background-color: white;
        color: #002663;
        &:after {
          background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23002663' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
      }
    }
    &:hover {
      background-color: $primary;
      border-color: $primary;
      color: white;
    }
  }
 }
}

.slick-initialized .slick-slide {
  display: flex !important;
  flex-wrap: wrap;
  padding-bottom: 2rem;
}

.slick-track {
    //display: flex !important;
}

.slick-slide {
    height: inherit !important;
}

.slick-next, .slick-prev {
  position: absolute;
  display: block;
  height: 3.5rem !important;
  width: 3.5rem !important;
  max-height: 3.5rem !important;
  max-width: 3.5rem !important;
  min-height: 3.5rem !important;
  min-width: 3.5rem !important;
  line-height: 0;
  font-size: 0;
  cursor: pointer;
  top: 50%;
  transform: translateY(-50%);
  padding: 0;
  background-color: white !important;
  border: none;
  z-index: 10;
}

.slick-next:before, .slick-prev:before {
  font-family: slick;
  font-size: 20px;
  line-height: 1;
  color: $brand !important;
  opacity: 0!important;
}
.slick-prev:after {
  content: "" !important;
}

.slick-next:before {
  content: "" !important;

}
