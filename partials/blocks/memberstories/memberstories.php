<?php

/**
 * text image
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-memberstories-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-memberstories';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$items = get_field('story');

?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">

  <?php
    foreach($items as $item) { ?>

      <div class="block-memberstories__item">
        <div class="block-memberstories__img" style="background-image: url('<?php echo $item['img']['sizes']['medium_large']; ?>');">
        </div>
        <div class="block-memberstories__content">
          <span class="subject">
            <?php echo $item['subject'] ?: "Aihe"; ?>
          </span>
          <span class="quote h3">
            <?php echo $item['quote'] ?: "Lainaus"; ?>
            </span>
          <span class="name">
            <?php echo $item['name'] ?: "Etunimi Sukunimi"; ?>
            </span>
          <span class="company">
            <?php echo $item['company'] ?: "Yritys AB"; ?>
          </span>
          <?php if($item['link']) {
              echo '<a href="' . $item['link']['url'] . '" >' . $item['link']['title'] . '</a>';
            }
          ?>
        </div>
      </div>
    <?php
    }
  ?>

</div>
