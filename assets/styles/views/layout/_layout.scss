/* ==========================================================================
 # Layout
========================================================================== */

.primary {
  padding-top: 1.5rem;
  padding-bottom: $gap-big * 2;

  .page-template-template-landingpage-new & {
    padding-top: 0;
  }

  /* -------------------------------------------------------
    Pages
  ---------------------------------------------------------*/

  &--page {
    .main {
      //@include narrow-content;
    }
  }

  /* -------------------------------------------------------
    Singular
  ---------------------------------------------------------*/

  &--single {
    .main {
      //@include narrow-content;
    }
  }

  /* -------------------------------------------------------
    Search
  ---------------------------------------------------------*/

  &--search {
    @include wrap;
  }

  /* -------------------------------------------------------
    Index & archives
  ---------------------------------------------------------*/

  &--index {
    @include wrap;
  }

  /* -------------------------------------------------------
    404
  ---------------------------------------------------------*/

  &--404 {
    @include wrap;

    .main {
      max-width: 40rem;
      margin-left: auto;
      margin-right: auto;
    }
  }
}

#content {
  margin-top: 110px; // /desktop
  @include breakpoint($menu-visible) {
    margin-top: 118px !important; // desktop
  }
}
