{"version": 3, "sources": ["../assets/scripts/components/tabs.js", "../assets/scripts/editor-gutenberg.js"], "names": ["displayCurrentTab", "current", "tabsContents", "tabsBtns", "i", "length", "style", "height", "parentNode", "classList", "contains", "scrollIntoView", "block", "behavior", "add", "remove", "wp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blocks", "unregisterBlockStyle", "document", "querySelector", "tabs", "querySelectorAll", "_loop", "close", "addEventListener", "event", "j<PERSON><PERSON><PERSON>", "trigger", "target"], "mappings": "aAAA,SAASA,kBAAkBC,EAASC,EAAcC,GAChD,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAaG,OAAQD,IACnCH,IAAYG,GACdF,EAAaE,GAAGE,MAAMC,OAAS,OAE7BL,EAAaE,GAAGI,WAAWA,WAAWC,UAAUC,SAAS,aAEzDR,EAAaE,GAAGO,eAAe,CAAEC,MAAO,QAASC,SAAU,YAG7DX,EAAaE,GAAGE,MAAMC,OAAS,IAGnC,IAAK,IAAIH,EAAI,EAAGA,EAAID,EAASE,OAAQD,IAC/BH,IAAYG,EACdD,EAASC,GAAGK,UAAUK,IAAI,MAE1BX,EAASC,GAAGK,UAAUM,OAAO,MAKnC,GCfAC,GAAGC,SAAS,WAEVD,GAAGE,OAAOC,qBAAqB,cAAe,WAC9CH,GAAGE,OAAOC,qBAAqB,cAAe,WAG9CH,GAAGE,OAAOC,qBAAqB,iBAAkB,QACjDH,GAAGE,OAAOC,qBAAqB,iBAAkB,QAGjDH,GAAGE,OAAOC,qBAAqB,aAAc,WDK3CC,SAASC,cAAc,SAGzB,IAFA,IAAMC,KAAOF,SAASG,iBAAiB,SAASC,MAAA,WAG9C,IAAMrB,EAAWmB,KAAKlB,GAAGmB,iBAAiB,cACpCrB,EAAeoB,KAAKlB,GAAGmB,iBAAiB,kBACxCE,EAAQH,KAAKlB,GAAGmB,iBAAiB,yBAEnCH,SAASC,cAAc,iBACzBrB,kBAAkB,EAAGE,EAAcC,GAGrC,IAAK,IAAIC,EAAI,EAAGA,EAAIqB,EAAMpB,OAAQD,IAChCqB,EAAMrB,GAAGsB,iBAAiB,QAAS,SAACC,GAClC,IAAK,IAAIvB,EAAI,EAAGA,EAAIF,EAAaG,OAAQD,IACvCF,EAAaE,GAAGE,MAAMC,OAAS,MAKrCe,KAAKlB,GAAGsB,iBAAiB,QAAS,SAACC,GAGjC,GAFAC,OAAO,kBAAkBC,QAAQ,UACjCD,OAAO,kBAAkBC,QAAQ,UAE/BF,EAAMG,OAAOrB,UAAUC,SAAS,cAChCiB,EAAMG,OAAOtB,WAAWC,UAAUC,SAAS,cAC3CiB,EAAMG,OAAOtB,WAAWA,WAAWC,UAAUC,SAAS,aAEtD,IAAK,IAAIN,EAAI,EAAGA,EAAID,EAASE,OAAQD,IACnC,GACEuB,EAAMG,SAAW3B,EAASC,IAC1BuB,EAAMG,OAAOtB,aAAeL,EAASC,IACrCuB,EAAMG,OAAOtB,WAAWA,aAAeL,EAASC,GAChD,CACAJ,kBAAkBI,EAAGF,EAAcC,GACnC,UAhCDC,EAAI,EAAGA,EAAIkB,KAAKjB,OAAQD,IAAGoB", "file": "editor-gutenberg.js", "sourcesContent": ["function displayCurrentTab(current, tabsContents, tabsBtns) {\n  for (let i = 0; i < tabsContents.length; i++) {\n    if (current === i) {\n      tabsContents[i].style.height = \"auto\";\n      if (\n        tabsContents[i].parentNode.parentNode.classList.contains(\"scrollto\")\n      ) {\n        tabsContents[i].scrollIntoView({ block: \"start\", behavior: \"smooth\" });\n      }\n    } else {\n      tabsContents[i].style.height = \"0\";\n    }\n  }\n  for (let i = 0; i < tabsBtns.length; i++) {\n    if (current === i) {\n      tabsBtns[i].classList.add(\"on\");\n    } else {\n      tabsBtns[i].classList.remove(\"on\");\n    }\n  }\n}\n\nif (document.querySelector(\".tabs\")) {\n  const tabs = document.querySelectorAll(\".tabs\");\n\n  for (let i = 0; i < tabs.length; i++) {\n    const tabsBtns = tabs[i].querySelectorAll(\".tabs__btn\");\n    const tabsContents = tabs[i].querySelectorAll(\".tabs__content\");\n    const close = tabs[i].querySelectorAll(\".tabs__content--close\");\n\n    if (document.querySelector(\".active_tabs\")) {\n      displayCurrentTab(0, tabsContents, tabsBtns);\n    }\n\n    for (let i = 0; i < close.length; i++) {\n      close[i].addEventListener(\"click\", (event) => {\n        for (let i = 0; i < tabsContents.length; i++) {\n          tabsContents[i].style.height = \"0\";\n        }\n      });\n    }\n\n    tabs[i].addEventListener(\"click\", (event) => {\n      jQuery(\".product__wrap\").trigger(\"resize\");\n      jQuery(\".product__wrap\").trigger(\"resize\");\n      if (\n        event.target.classList.contains(\"tabs__btn\") ||\n        event.target.parentNode.classList.contains(\"tabs__btn\") ||\n        event.target.parentNode.parentNode.classList.contains(\"tabs__btn\")\n      ) {\n        for (let i = 0; i < tabsBtns.length; i++) {\n          if (\n            event.target === tabsBtns[i] ||\n            event.target.parentNode === tabsBtns[i] ||\n            event.target.parentNode.parentNode === tabsBtns[i]\n          ) {\n            displayCurrentTab(i, tabsContents, tabsBtns);\n            break;\n          }\n        }\n      }\n    });\n  }\n}\n", "/* ==========================================================================\n  editor-gutenberg.js\n========================================================================== */\n\n/**\n * Modify style variants.\n */\nwp.domReady(function () {\n  // remove button styles\n  wp.blocks.unregisterBlockStyle(\"core/button\", \"outline\");\n  wp.blocks.unregisterBlockStyle(\"core/button\", \"squared\");\n\n  // remove separator styles\n  wp.blocks.unregisterBlockStyle(\"core/separator\", \"dots\");\n  wp.blocks.unregisterBlockStyle(\"core/separator\", \"wide\");\n\n  // remove quote styles\n  wp.blocks.unregisterBlockStyle(\"core/quote\", \"large\");\n\n  //  // add lead paragraph style\n  //  wp.blocks.registerBlockStyle('core/paragraph', {\n  //    name: 'lead',\n  //    label: 'Lead paragraph',\n  //  });\n});\n"], "sourceRoot": "assets/scripts/"}