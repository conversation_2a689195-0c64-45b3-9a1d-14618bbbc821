.wysiwyg .alignwide.block-news-lift-1-2{

}
.block-news-lift-1-2-outer {

  &>h2 {
    /*@include h1-new-bigger;*/
    a {
      color: $primary;
      text-decoration: none;
      &:hover {
        text-decoration: underline;
      }
    }
  }

.block-news-lift-1-2-bottom {
  margin-bottom: 28px;
  text-align: center;
  @include breakpoint($sub-s) {
    margin-bottom: 40px;
  }
  a {
    @include bigtextlink;
  }
}

}
/* title bigger, but not in home page */
body:not(.home) {
  .block-news-lift-1-2-outer  {
    &>h2 {
      @include h1-new-bigger;
    }
  }
}




.block-news-lift-1-2 {
  display: flex;
  justify-content: space-between;
  background-color: white;
  position: relative;
  margin-top: 1rem !important;
  padding: 0rem 0 0.5rem;
  .teaser {
    margin-top: 0;
    padding-top: 0;
    .teaser__header__title.teaser__header__title-new {
      /*@include h2-new-bigger;*/
    }
    .teaser__header__title.teaser__header__title-new:hover {
      text-decoration: underline;
    }
    .teaser__summary {
      font-size: 16px;
    }
  }
  @include breakpoint($sub-l) {
    display: block;
    img {
      /*display: none;*/
    }
  }
  @include breakpoint($sub-s) {
    display: block;
    img {
      display: none;
    }
  }
  &__wrap {
    .block-news-lift-1-2__ajax {
      margin-bottom: 1.5rem;
      display: block;
    }
    width: calc(50% - 1.5rem);
    min-height: 420px;
    .teaser--large {
      border-bottom: 0;
    }
    @include breakpoint($sub-l) {
      width: 100%;
    }
  }
  &__top-title {
    margin-top: 0px;
    margin-bottom: 8px;
  }
  &__item {

    display: grid;
    /*
    flex-direction: row;
    align-content: center;
    flex-wrap: wrap;
    justify-content: space-between;
    */
    grid-template-columns: 260px auto;
    grid-column-gap: 5px;
    grid-row-gap: 10px;

    &:first-child {
      padding-bottom: 30px;
      border-bottom: 3px solid $separatorblue;
    }

    &:nth-child(2) {
       margin-top: 30px;
    }
    @include breakpoint($sub-s) {
      display: block;
    }
    &--content {
      padding-bottom: 16px;
      font-size: 16px;
    }

    img {
      width: 90%;
      max-width: 90%
    }
    &--teaser__thumbnail {
      .entry-meta {
        margin-top: 6px;
        margin-bottom: 20px;

      }
    }
    .title {
      /*@include h2-new-bigger;*/
      margin-top: -10px;
      margin-bottom: 8px;
      padding-top: 0px;
    }
    .date {
      margin: .5rem 0rem;
      display: inline-block;
      color: $grey-darker;
      font-weight: 600;
      text-transform: capitalize;

    }
    .time {
      color: $grey-darker;
    }
    .location {
      color: $grey-darker;
    }
    .separator {
      color: $grey-darker;
      font-weight: 500;
      font-size: 1rem;
      margin: 0 .5rem .25rem;
    }
    a {
      color: $primary;
      text-decoration: none;
      &:hover, &:focus {
        text-decoration: underline !important;
      }
    }
    &--hidden {
      padding: 1em;
      overflow: hidden;
      display: none;
      width: 100%;
      background-color: $grey-light;
      &.show {
        /*display: block;*/
      }
    }
    .icon-wrap {
      display: flex;
      align-items: center;
      margin-bottom: .5rem;
      color: $primary;
      font-weight: 600;
    }
    .icon {
      color: white;
      background-color: $primary;
      transition: all .3s ease;
      margin-right: .5rem;
      width: 1.5rem;
      height: 1.5rem;
    }
    .accordion-toggle {
      cursor: pointer;
    }
    .active .icon {
      transform: rotate(90deg);
    }
  }
  &-header {
    display: flex;
    justify-content: space-between;
    img {
      height: 2.25rem;
      width: auto;
    }

  }
  &__all {
    align-self: flex-end;
    @include button;
    width: 100%;
    max-width: 186px;
    text-align: center;
    border: 1px solid $separatorblue;
    color: $brand;
    background: transparent;
    margin-bottom: .75rem;
    margin-top: 1.5rem;
    padding: 0.60rem 1.15rem;
    /*&:hover {
      color: white;
    }
    &.external {
      background-color: #002663;
      border: 1px solid #002663;
      color: white;
      padding: 0.75rem 4.5rem 0.75rem 3rem;
      &:after {
        position: absolute;
        background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23ffffff' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-position: center;
        background-size: 84%;
        width: 1.1rem;
        height: 1rem;
        display: inline-block;
        content: " ";
        margin-left: 8px;
        color: transparent;
        background-repeat: no-repeat;
      }
      &:hover {
        background-color: white;
        color: #002663;
        &:after {
          background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23002663' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
      }
    }
    &:hover {
      background-color: $brand;
      color: white;
    }*/
  }
}


/* title bigger, but not in home page */

body.page-template-template-landingpage {
  .block-news-lift-1-2-outer {
    & > h2 {
    @include h1-new-bigger;
    }
  }
}


body {


  .block-news-lift-1-2 {

    .teaser {
      .teaser__header__title.teaser__header__title-new {
        @include h3-new-bigger;
      }
    }
    &__item {
      .title {
        @include h3-new-bigger;
      }
    }
  }
}
