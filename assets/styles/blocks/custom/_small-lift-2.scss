/* 2 Small lifts */

.small-lift-2 {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 1.5rem !important;
    margin-top: 1.5rem !important;

    opacity: 0;

    &__wrap {
        display: flex;
        flex-direction: column;
    }

    &__item {
        // background color selection
        &.background-coral {
            background: $coral-25 !important;
        }
        &.background-lightblue {
            background: $light-blue-25 !important;
        }
        &.background-white {
            background: transparent !important;
            box-shadow: 0 0 13px -5px rgba(0,0,0,.4) !important;
        }

        & .title {
            margin: 0;
            font-size: 20px;
            font-weight: $ws-semibold;
        }
        & .content {
            margin: 1rem 0 2rem;
        }
        color: $primary;
        width: 100%;
        align-items: center;
        display: grid;
        grid-auto-flow: column;
        text-align: center;
        margin-bottom: 0;
        padding: 2rem 3rem;
    }
    &__body {
        display: flex;
        flex-direction: column;
        font-size: 16px;
        margin-bottom: 2rem;
    }
    &__button, &__mobile-button {
        @include newbutton;
        width: 100% !important;
        text-align: center;
        margin: auto;
    }
    &__image {
       /* width: 100%;
        min-height: 300px;*/
        background-size: cover;
        //min-width: 210px;
        //max-width: 225px;
        height: 250px;
        //max-height: 375px;
        //min-height: 250px;
        width: 175px;
        aspect-ratio: 175 / 250;
        background-position: 50%;
    }
}

@include breakpoint(min-width 819px) {
    .small-lift-2 {
        &__item {
            width: calc(50% - .75rem);
            .title {
                font-size: 22px;
            }
        }
        &__mobile-button {
            display: none;
        }
    }
}

@include breakpoint(max-width 819px) {
    .small-lift-2 {
        margin: 3rem -1.5rem;
        &__item {
            padding: 1.5rem 1.5rem;
            grid-auto-flow: row;
            justify-items: center;
            &:first-of-type {
                margin-bottom: 1.5rem;
            }
        }
        &__button {
            display: none;
        }
        &__mobile-button {
            order: 1;
            margin-top: 1rem;
        }
        &__body {
            margin-bottom: 0;
            & .content {
                margin: 1.5rem 0 2rem;
            }
        }
    }
}

@include breakpoint(min-width 1170px) {
    .small-lift-2 {
        &__item {
            width: calc(50% - 2rem);
            margin: 1rem 0;
            gap: 1rem;
            text-align: left;
        }
        &__body {
            margin-bottom: 0;
        }
        &__image {
            background-size: cover;
            height: 250px;
            width: 175px;
            background-position: 50%;
        }
        &__button {
            margin: 0 auto;
        }
        &__mobile-button {
            display: none;
        }
    }
}
@include breakpoint($xl) {
    .small-lift-2 {
        &__item {
            gap: 2rem;
        }
        &__body {
            // margin-right: 3rem;
        }
    }
}
