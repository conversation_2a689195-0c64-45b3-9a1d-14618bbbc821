.news-lift-slider {
	display: flex;
	overflow-x: scroll;
	scroll-snap-type: x mandatory;
	gap: 2.5rem;
	padding-bottom: 50px;
	margin-bottom: 20px;
}

.news-lift-slider::-webkit-scrollbar {
	-webkit-appearance: none;
	height: 8px !important;
	border-radius: 8px;
	background-color: rgba(29, 57, 127, 0.1);
}

.news-lift-slider::-webkit-scrollbar-thumb {
	border-radius: 8px;
	background-color: rgba(29, 57, 127, 0.4);
}

.news-lift-slider-wrapper.alignfull {
	width: 100vw;
	margin-left: calc(50% - 50vw);
}

.page-template-default .news-lift-slider-wrapper.alignfull,
.page-template-template-NYT .news-lift-slider-wrapper.alignfull {
	width: 100%;
	margin-left: 0;
}

.news-lift-slider-wrapper {
	width: 100%;
	position: relative;
}

.submit-btn {
	position: absolute;
	bottom: 1.5rem;
	left: 50%;
	z-index: 9999;
}

.alignwide {
	max-width: 98rem;
	margin: auto;
}

.page-template-default .alignwide,
.page-template-template-NYT .alignwide {
	max-width: 100%;
	margin: 0 2rem;
}

.news-lift-heading {
	display: flex;
	gap: 150px;
	align-items: baseline;
	margin: 0 20px 20px 20px;
}

.news-lift-heading h2 {
	font-size: 38px;
	color: #002662;
	font-weight: 600;
}

.news-lift-heading a {
	font-size: 18px;
	font-weight: 600;
	color: #002662;
	text-decoration: none;
	position: relative;
}

.news-lift-heading a:hover {
	text-decoration: underline;
}

.news-lift-heading a::after {
	content: "";
	position: absolute;
	top: 5px;
	width: 8px;
	height: 13px;
	background-image: url("../../../assets/arrow.svg");
	background-size: contain;
	background-repeat: no-repeat;
	margin-left: 12px;
}

@media (max-width: 1520px) {
	.news-lift-slider {
		margin: 0 20px 20px 20px;
	}

	.page-template-default .news-lift-slider,
	.page-template-template-NYT .news-lift-slider {
		margin: 0 0 20px 0;
	}

	.news-lift-heading {
		margin: 0 40px 20px 40px;
	}

	.page-template-default .news-lift-heading,
	.page-template-template-NYT .news-lift-heading {
		margin: 0 20px 20px 20px;
	}
}

@media (max-width: 1190px) and (min-width: 1120px) {
	.page-template-default .news-lift-heading {
		display: block;
	}
}

@media (max-width: 1119px) {
	.page-template-default .news-lift-slider-wrapper.alignfull,
	.page-template-template-NYT .news-lift-slider-wrapper.alignfull {
		width: 100vw;
		margin-left: -3rem;
	}
}

@media (max-width: 788px) {
	.news-lift-heading {
		display: block;
	}
}

@media (max-width: 640px) {
	.alignwide {
		max-width: 100%;
		margin: auto;
	}
}
