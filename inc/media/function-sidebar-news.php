<?php
function kaup<PERSON><PERSON><PERSON>i_sidebar_news_block() {

    if ( !function_exists('get_field') || !get_field('show_news') ) {
        return;
    }

    $category_ids = get_field('category');
    $show_link = get_field('show_read_all');
    $hide_images = get_field('hide_news_img');

    $posts_per_page = $hide_images ? 5 : 3;

    if ( !is_array($category_ids) ) {
        $category_ids = array($category_ids);
    }

    $args = array(
        'post_type' => 'post',
        'posts_per_page' => $posts_per_page,
        'cat' => $category_ids,
    );

    $news_query = new WP_Query($args);

    if ( $news_query->have_posts() ) {
        echo '<p class="sidebar-news-heading">Uutiset</p>';
        while ( $news_query->have_posts() ) {
            $news_query->the_post();

            $post_date = get_the_date();
            $post_category = get_the_category();
            $category_name = !empty($post_category) ? esc_html($post_category[0]->name) : '';
            $post_url = get_permalink();
            $title = get_the_title();

            echo '<div class="wp-block-group sidebar-news-body">';

            if ( !$hide_images && has_post_thumbnail() ) {
                echo '<div class="sidebar-news-thumbnail">';
                echo '<a href="' . esc_url($post_url) . '">';
                the_post_thumbnail('medium');
                echo '</a>';
                echo '</div>';
            }

            echo '<div class="wp-block-group sidebar-news-meta">';
            if ($post_date) {
                echo '<p class="sidebar-meta-date">' . esc_html($post_date) . '</p>';
            }
            if ($category_name) {
                echo '<p class="sidebar-meta-category" style="text-transform: uppercase;">' . esc_html($category_name) . '</p>';
            }
            echo '</div>';

            echo '<p class="wp-block-heading sidebar-news-article-heading">';
            echo '<a href="' . esc_url($post_url) . '">' . esc_html($title) . '</a>';
            echo '</p>';

            echo '<hr class="wp-block-separator sidebar-news-hr">';
            echo '</div>';
        }

        if ( $show_link && count( $category_ids ) === 1 ) {
            echo '<p><a href="' . esc_url( get_category_link( $category_ids[0] ) ) . '" class="sidebar-news-link">Lue kaikki artikkelit</a></p>';
        }

        wp_reset_postdata();
    }
}
