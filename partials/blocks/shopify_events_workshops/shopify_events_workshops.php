<?php

// Create id attribute allowing for custom "anchor" value.
$id = 'block-shopify-events-workshops' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-shopify-events-workshops';

?>

<div class="<?php echo $className?>">
    

    <script type="module" crossorigin src="/wp-content/themes/kauppakamari/vue-event-calendar/dist/assets/index.js"></script>
    <link rel="stylesheet" href="/wp-content/themes/kauppakamari/vue-event-calendar/dist/assets/index.css">
    <script type="module">try{import.meta.url;import("_").catch(()=>1);}catch(e){}window.__vite_is_modern_browser=true;</script>
    <script type="module">!function(){if(window.__vite_is_modern_browser)return;console.warn("vite: loading legacy build because dynamic import or import.meta.url is unsupported, syntax error above should be ignored");var e=document.getElementById("vite-legacy-polyfill"),n=document.createElement("script");n.src=e.src,n.onload=function(){System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))},document.body.appendChild(n)}();</script>

    <div id="event-calendar-wp-app"></div>
    
    <script nomodule>!function(){var e=document,t=e.createElement("script");if(!("noModule"in t)&&"onbeforeload"in t){var n=!1;e.addEventListener("beforeload",(function(e){if(e.target===t)n=!0;else if(!e.target.hasAttribute("nomodule")||!n)return;e.preventDefault()}),!0),t.type="module",t.src=".",e.head.appendChild(t),t.remove()}}();</script>
    <script nomodule crossorigin id="vite-legacy-polyfill" src="/wp-content/themes/kauppakamari/vue-event-calendar/dist/assets/polyfills-legacy.js"></script>
    <script nomodule crossorigin id="vite-legacy-entry" data-src="/wp-content/themes/kauppakamari/vue-event-calendar/dist/assets/index-legacy.js">System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))</script>

</div>
<style>
    .acf-block-preview .block-shopify-events-workshops {
        border: 1px solid black;
    }

    .acf-block-preview .block-shopify-events-workshops:before {
        content: "Tapahtuma- ja koulutuslistaus (ei esikatselua)";
        display: inline-block;
        padding: 0 10px;
        margin-left: 10px;
        margin-top: 10px;
    }

</style>