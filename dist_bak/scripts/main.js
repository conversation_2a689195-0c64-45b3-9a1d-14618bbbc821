"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}!function(){var y,e,w,k,b,t,i;"undefined"!=typeof window&&window.addEventListener&&(y=Object.create(null),w=function(){clearTimeout(e),e=setTimeout(t,100)},k=function(){},b="http://www.w3.org/1999/xlink",t=function(){var e,t,i,o,n,s,r,a,l,d,c,p,u,h=0;function f(){var e;0===--h&&(k(),window.addEventListener("resize",w,!1),window.addEventListener("orientationchange",w,!1),k=window.MutationObserver?((e=new MutationObserver(w)).observe(document.documentElement,{childList:!0,subtree:!0,attributes:!0}),function(){try{e.disconnect(),window.removeEventListener("resize",w,!1),window.removeEventListener("orientationchange",w,!1)}catch(e){}}):(document.documentElement.addEventListener("DOMSubtreeModified",w,!1),function(){document.documentElement.removeEventListener("DOMSubtreeModified",w,!1),window.removeEventListener("resize",w,!1),window.removeEventListener("orientationchange",w,!1)}))}function v(e){return function(){!0!==y[e.base]&&(e.useEl.setAttributeNS(b,"xlink:href","#"+e.hash),e.useEl.hasAttribute("href")&&e.useEl.setAttribute("href","#"+e.hash))}}function m(e){return function(){e.onerror=null,e.ontimeout=null,f()}}for(k(),a=document.getElementsByTagName("use"),n=0;n<a.length;n+=1){try{t=a[n].getBoundingClientRect()}catch(e){t=!1}e=(r=(o=a[n].getAttribute("href")||a[n].getAttributeNS(b,"href")||a[n].getAttribute("xlink:href"))&&o.split?o.split("#"):["",""])[0],i=r[1],s=t&&0===t.left&&0===t.right&&0===t.top&&0===t.bottom,t&&0===t.width&&0===t.height&&!s?(a[n].hasAttribute("href")&&a[n].setAttributeNS(b,"xlink:href",o),e.length&&(!0!==(l=y[e])&&setTimeout(v({useEl:a[n],base:e,hash:i}),0),void 0===l&&(d=e,u=p=c=void 0,window.XMLHttpRequest&&(c=new XMLHttpRequest,p=g(location),u=g(d),c=void 0===c.withCredentials&&""!==u&&u!==p?XDomainRequest||void 0:XMLHttpRequest),void 0!==c&&(l=new c,(y[e]=l).onload=function(o){return function(){var e,t=document.body,i=document.createElement("x");o.onload=null,i.innerHTML=o.responseText,(e=i.getElementsByTagName("svg")[0])&&(e.setAttribute("aria-hidden","true"),e.style.position="absolute",e.style.width=0,e.style.height=0,e.style.overflow="hidden",t.insertBefore(e,t.firstChild)),f()}}(l),l.onerror=m(l),l.ontimeout=m(l),l.open("GET",e),l.send(),h+=1)))):s?e.length&&y[e]&&setTimeout(v({useEl:a[n],base:e,hash:i}),0):void 0===y[e]?y[e]=!0:y[e].onload&&(y[e].abort(),delete y[e].onload,y[e]=!0)}function g(e){var t;return void 0!==e.protocol?t=e:(t=document.createElement("a")).href=e,t.protocol.replace(/:/g,"")+t.host}a="",h+=1,f()},i=function(){window.removeEventListener("load",i,!1),e=setTimeout(t,0)},"complete"!==document.readyState?window.addEventListener("load",i,!1):i())}(),function(){var e,o,d,n,t;"undefined"!=typeof window&&(e=window.navigator.userAgent.match(/Edge\/(\d{2})\./),o=!!e&&16<=parseInt(e[1],10),"objectFit"in document.documentElement.style==0||o?(d=function(e,t,i){var o,n,s,r,a;if((i=i.split(" ")).length<2&&(i[1]=i[0]),"x"===e)o=i[0],n=i[1],s="left",r="right",a=t.clientWidth;else{if("y"!==e)return;o=i[1],n=i[0],s="top",r="bottom",a=t.clientHeight}if(o!==s&&n!==s){if(o!==r&&n!==r)return"center"===o||"50%"===o?(t.style[s]="50%",void(t.style["margin-"+s]=a/-2+"px")):void(0<=o.indexOf("%")?(o=parseInt(o))<50?(t.style[s]=o+"%",t.style["margin-"+s]=a*(o/-100)+"px"):(o=100-o,t.style[r]=o+"%",t.style["margin-"+r]=a*(o/-100)+"px"):t.style[s]=o);t.style[r]="0"}else t.style[s]="0"},n=function(e){var t,i,o,n,s,r=(r=e.dataset?e.dataset.objectFit:e.getAttribute("data-object-fit"))||"cover",a=(a=e.dataset?e.dataset.objectPosition:e.getAttribute("data-object-position"))||"50% 50%",l=e.parentNode;return t=l,i=window.getComputedStyle(t,null),o=i.getPropertyValue("position"),n=i.getPropertyValue("overflow"),s=i.getPropertyValue("display"),o&&"static"!==o||(t.style.position="relative"),"hidden"!==n&&(t.style.overflow="hidden"),s&&"inline"!==s||(t.style.display="block"),0===t.clientHeight&&(t.style.height="100%"),-1===t.className.indexOf("object-fit-polyfill")&&(t.className=t.className+" object-fit-polyfill"),function(e){var t,i=window.getComputedStyle(e,null),o={"max-width":"none","max-height":"none","min-width":"0px","min-height":"0px",top:"auto",right:"auto",bottom:"auto",left:"auto","margin-top":"0px","margin-right":"0px","margin-bottom":"0px","margin-left":"0px"};for(t in o)i.getPropertyValue(t)!==o[t]&&(e.style[t]=o[t])}(e),e.style.position="absolute",e.style.width="auto",e.style.height="auto","scale-down"===r&&(r=e.clientWidth<l.clientWidth&&e.clientHeight<l.clientHeight?"none":"contain"),"none"===r?(d("x",e,a),void d("y",e,a)):"fill"===r?(e.style.width="100%",e.style.height="100%",d("x",e,a),void d("y",e,a)):(e.style.height="100%",void("cover"===r&&e.clientWidth>l.clientWidth||"contain"===r&&e.clientWidth<l.clientWidth?(e.style.top="0",e.style.marginTop="0",d("x",e,a)):(e.style.width="100%",e.style.height="auto",e.style.left="0",e.style.marginLeft="0",d("y",e,a))))},t=function(e){if(void 0===e||e instanceof Event)e=document.querySelectorAll("[data-object-fit]");else if(e&&e.nodeName)e=[e];else if("object"!=_typeof(e)||!e.length||!e[0].nodeName)return!1;for(var t=0;t<e.length;t++)if(e[t].nodeName){var i=e[t].nodeName.toLowerCase();if("img"===i){if(o)continue;e[t].complete?n(e[t]):e[t].addEventListener("load",function(){n(this)})}else"video"!==i||0<e[t].readyState?n(e[t]):e[t].addEventListener("loadedmetadata",function(){n(this)})}return!0},"loading"===document.readyState?document.addEventListener("DOMContentLoaded",t):t(),window.addEventListener("resize",t),window.objectFitPolyfill=t):window.objectFitPolyfill=function(){return!1})}(),function(e){"object"===("undefined"==typeof exports?"undefined":_typeof(exports))&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).fitvids=e()}(function(){return function s(r,a,l){function d(i,e){if(!a[i]){if(!r[i]){var t="function"==typeof require&&require;if(!e&&t)return t(i,!0);if(c)return c(i,!0);var o=new Error("Cannot find module '"+i+"'");throw o.code="MODULE_NOT_FOUND",o}var n=a[i]={exports:{}};r[i][0].call(n.exports,function(e){var t=r[i][1][e];return d(t||e)},n,n.exports,s,r,a,l)}return a[i].exports}for(var c="function"==typeof require&&require,e=0;e<l.length;e++)d(l[e]);return d}({1:[function(e,t,i){var d=['iframe[src*="player.vimeo.com"]','iframe[src*="youtube.com"]','iframe[src*="youtube-nocookie.com"]','iframe[src*="kickstarter.com"][src*="video.html"]',"object"];function c(e,t){return"string"==typeof e&&(t=e,e=document),Array.prototype.slice.call(e.querySelectorAll(t))}function p(e){return"string"==typeof e?e.split(",").map(o).filter(u):(i=e,"[object Array]"===Object.prototype.toString.call(i)?(t=e.map(p).filter(u),[].concat.apply([],t)):e||[]);var t,i}function u(e){return 0<e.length}function o(e){return e.replace(/^\s+|\s+$/g,"")}t.exports=function(e,t){var i;t=t||{},i=e=e||"body","[object Object]"===Object.prototype.toString.call(i)&&(t=e,e="body"),t.ignore=t.ignore||"",t.players=t.players||"";var o,n,s,r,a,l=c(e);u(l)&&(document.getElementById("fit-vids-style")||(document.head||document.getElementsByTagName("head")[0]).appendChild(((o=document.createElement("div")).innerHTML='<p>x</p><style id="fit-vids-style">.fluid-width-video-wrapper{width:100%;position:relative;padding:0;}.fluid-width-video-wrapper iframe,.fluid-width-video-wrapper object,.fluid-width-video-wrapper embed {position:absolute;top:0;left:0;width:100%;height:100%;}</style>',o.childNodes[1])),n=p(t.players),s=p(t.ignore),r=0<s.length?s.join():null,u(a=d.concat(n).join())&&l.forEach(function(e){c(e,a).forEach(function(e){r&&e.matches(r)||function(e){if(/fluid-width-video-wrapper/.test(e.parentNode.className))return;var t=parseInt(e.getAttribute("width"),10),i=parseInt(e.getAttribute("height"),10),o=isNaN(t)?e.clientWidth:t,n=(isNaN(i)?e.clientHeight:i)/o;e.removeAttribute("width"),e.removeAttribute("height");var s=document.createElement("div");e.parentNode.insertBefore(s,e),s.className="fluid-width-video-wrapper",s.style.paddingTop=100*n+"%",s.appendChild(e)}(e)})}))}},{}]},{},[1])(1)});var aucor_navigation=function(d,e){var r,t=function(e,t){var i,o={};for(i in e)Object.prototype.hasOwnProperty.call(e,i)&&(o[i]=e[i]);for(i in t)Object.prototype.hasOwnProperty.call(t,i)&&(o[i]=t[i]);return o}({desktop_min_width:501,menu_toggle:"#menu-toggle"},e),i=t.desktop_min_width,o=document.querySelector(t.menu_toggle);function c(){return!(Math.max(document.documentElement.clientWidth,window.innerWidth||0)<i)}for(var n=function(){if(c()){clearTimeout(r);for(var e=[],t=this.parentElement;!t.isEqualNode(d);)t.classList.contains("sub-menu-wrap")&&e.push(t),t=t.parentElement;for(var i=this.querySelectorAll(".sub-menu-wrap"),o=0;o<i.length;o++)e.push(i[o]);for(var n=d.querySelectorAll(".open"),s=0;s<n.length;s++)-1===e.indexOf(n[s])&&n[s].classList.remove("open");this.querySelector(".sub-menu-wrap")&&this.querySelector(".sub-menu-wrap").classList.add("open")}},s=function(){var t=this;c()&&(r=setTimeout(function(){for(var e=t.parentElement;!e.isEqualNode(d);)e.classList.remove("open"),e=e.parentElement;t.querySelector(".open")&&t.querySelector(".open").classList.remove("open")},750))},a=function(e){for(var t=null,i=e.target.parentElement;!i.isEqualNode(d);){if(i.classList.contains("menu-item")){t=i;break}i=i.parentElement}t.querySelector(".sub-menu-wrap").classList.toggle("open"),c()||t.classList.toggle("active"),e.stopPropagation()},l=d.querySelectorAll(".menu-item-has-children"),p=0;p<l.length;p++){var u=l[p];u.addEventListener("mouseover",n),u.addEventListener("mouseleave",s);var h=u.querySelector(".js-menu-caret");h&&h.addEventListener("click",a)}for(var f=function(e){var t=e.target.parentElement.querySelector(".sub-menu-wrap");t&&t.classList.add("open");for(var i=e.target.parentElement;!i.isEqualNode(d);)i.classList.contains("sub-menu-wrap")&&i.classList.add("open"),i=i.parentElement},v=function(e){var t=e.target.parentElement.querySelector(".sub-menu-wrap");t&&t.classList.remove("open");for(var i=e.target.parentElement;!i.isEqualNode(d);)i.classList.contains("sub-menu-wrap")&&i.classList.remove("open"),i=i.parentElement},m=d.querySelectorAll("a"),g=0;g<m.length;g++){var y=m[g];y.addEventListener("focus",f),y.addEventListener("blur",v)}if(o.addEventListener("click",function(){o.classList.contains("menu-toggle--active")?(o.classList.remove("menu-toggle--active"),o.setAttribute("aria-expanded","false"),d.classList.remove("active"),o.dispatchEvent(new Event("focus"))):(o.classList.add("menu-toggle--active"),o.setAttribute("aria-expanded","true"),d.classList.add("active"))}),"ontouchstart"in window)for(var w=function(e,t){for(var i=e.querySelectorAll("."+t),o=0;o<i.length;o++)i[o].classList.remove(t)},k=function(e){d!==e.target&&!function(e){for(var t=!1;null!==(e=e.parentElement);)e.nodeType===Node.ELEMENT_NODE&&e.isEqualNode(d)&&(t=!0);return t}(e.target)&&c()&&(w(d,"open"),w(d,"tapped"),w(d,"active")),document.removeEventListener("ontouchstart",k,!1)},b=function(e){if(!c())return!1;var t=this.parentElement;if(t.classList.contains("tapped"))t.classList.remove("tapped"),w(d,"open");else{e.preventDefault();for(var i=[],o=t;!o.isEqualNode(d);)o.classList.contains("tapped")&&i.push(o),o=o.parentElement;for(var n=d.querySelectorAll(".tapped"),s=0;s<n.length;s++)-1===i.indexOf(n[s])&&n[s].classList.remove("tapped");t.classList.add("tapped");var r=[];for(o=t;!o.isEqualNode(d);)o.classList.contains("open")&&r.push(o),o=o.parentElement;for(var a=d.querySelectorAll(".open"),l=0;l<a.length;l++)-1===r.indexOf(a[l])&&a[l].classList.remove("open");for(t.querySelector(".sub-menu")&&t.querySelector(".sub-menu").classList.add("open"),o=this.parentElement;!o.isEqualNode(d);)o.classList.contains("sub-menu")&&o.classList.add("open"),o=o.parentElement;document.addEventListener("touchstart",k,!1)}},S=d.querySelectorAll(".menu-item-has-children > a"),T=0;T<S.length;T++)S[T].addEventListener("touchstart",b,!1);return this},responsive_tables_in_content=function(){var e=document.querySelectorAll(".wysiwyg .wp-block-table");if(e)for(var t=0;t<e.length;t++){e[t].classList.add("wp-block-table--responsive");var i=document.createElement("div");i.setAttribute("class",e[t].getAttribute("class")),e[t].removeAttribute("class"),e[t].parentNode.insertBefore(i,e[t]),i.appendChild(e[t])}};responsive_tables_in_content();var wrap_old_images_with_caption=function(){var e,t=document.querySelectorAll(".wysiwyg .wp-caption");if(t.length)for(i=0;i<t.length;i++){t[i].parentNode.classList.contains("wp-block-image")||((e=document.createElement("div")).setAttribute("class","wp-block-image"),t[i].parentNode.insertBefore(e,t[i]),e.appendChild(t[i]))}};wrap_old_images_with_caption();var wrap_old_aligned_images=function(){var e,t=document.querySelectorAll(".wysiwyg img.alignleft, .wysiwyg img.alignright");if(t.length)for(i=0;i<t.length;i++){"P"===(e=t[i].parentNode).nodeName&&(e.parentNode.insertBefore(t[i],e),0===e.childNodes.length&&e.parentNode.removeChild(e));var o=t[i].classList.contains("alignleft")?"alignleft":"alignright";t[i].classList.remove(o);var n=document.createElement("figure");n.setAttribute("class",o),t[i].parentNode.insertBefore(n,t[i]),n.appendChild(t[i]);var s=document.createElement("div");s.setAttribute("class","wp-block-image"),n.parentNode.insertBefore(s,n),s.appendChild(n)}};wrap_old_aligned_images();var offCanvas=document.querySelector(".header_sites__sites"),menuToggle=document.querySelector(".header_sites__toggle"),main=document.querySelector(".main-body"),closingElements=[menuToggle,main],toggleElements=[offCanvas,main];function openElements(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];[].concat(t).forEach(function(e){return e.classList.toggle("open")})}function closeElements(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];[].concat(t).forEach(function(e){return e.classList.remove("open")})}menuToggle.addEventListener("click",function(e){e.preventDefault(),e.stopImmediatePropagation(),openElements.apply(void 0,toggleElements)}),closingElements.forEach(function(e){e.addEventListener("click",function(e){main.classList.contains("open")&&!e.target.classList.contains("menu-toggle")&&closeElements.apply(void 0,toggleElements)})});var menu=document.querySelector(".header_sites__sites"),cloned_menu=menu.cloneNode(!0);void 0!==document.getElementById("primary-navigation__items")&&document.getElementById("primary-navigation__items").appendChild(cloned_menu);var lastScrollTop=65;window.addEventListener("scroll",function(){var e=window.pageYOffset||document.documentElement.scrollTop;lastScrollTop<e&&150<e?document.getElementById("primary-navigation").classList.add("hide"):document.getElementById("primary-navigation").classList.remove("hide"),lastScrollTop=e},!1);var elmnt=document.getElementById("masthead");function displayCurrentTab(e,t,i){for(var o=0;o<t.length;o++)e===o?(t[o].style.height="auto",t[o].parentNode.parentNode.classList.contains("scrollto")&&t[o].scrollIntoView({block:"start",behavior:"smooth"})):t[o].style.height="0";for(var n=0;n<i.length;n++)e===n?i[n].classList.add("on"):i[n].classList.remove("on")}if(document.getElementById("content").style.marginTop=elmnt.offsetHeight+"px",document.querySelector(".tabs"))for(var tabs=document.querySelectorAll(".tabs"),_loop=function(e){var i=tabs[e].querySelectorAll(".tabs__btn"),o=tabs[e].querySelectorAll(".tabs__content"),t=tabs[e].querySelectorAll(".tabs__content--close");document.querySelector(".active_tabs")&&displayCurrentTab(0,o,i);for(var n=0;n<t.length;n++)t[n].addEventListener("click",function(e){for(var t=0;t<o.length;t++)o[t].style.height="0"});tabs[e].addEventListener("click",function(e){if(jQuery(".product__wrap").trigger("resize"),jQuery(".product__wrap").trigger("resize"),e.target.classList.contains("tabs__btn")||e.target.parentNode.classList.contains("tabs__btn")||e.target.parentNode.parentNode.classList.contains("tabs__btn"))for(var t=0;t<i.length;t++)if(e.target===i[t]||e.target.parentNode===i[t]||e.target.parentNode.parentNode===i[t]){displayCurrentTab(t,o,i);break}})},i=0;i<tabs.length;i++)_loop(i);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"undefined"!=typeof exports?module.exports=e(require("jquery")):e(jQuery)}(function(d){var n,r=window.Slick||{};n=0,(r=function(e,t){var i,o=this;o.defaults={accessibility:!0,adaptiveHeight:!1,appendArrows:d(e),appendDots:d(e),arrows:!0,asNavFor:null,prevArrow:'<button class="slick-prev" aria-label="Previous" type="button">Previous</button>',nextArrow:'<button class="slick-next" aria-label="Next" type="button">Next</button>',autoplay:!1,autoplaySpeed:3e3,centerMode:!1,centerPadding:"50px",cssEase:"ease",customPaging:function(e,t){return d('<button type="button" />').text(t+1)},dots:!1,dotsClass:"slick-dots",draggable:!0,easing:"linear",edgeFriction:.35,fade:!1,focusOnSelect:!1,focusOnChange:!1,infinite:!0,initialSlide:0,lazyLoad:"ondemand",mobileFirst:!1,pauseOnHover:!0,pauseOnFocus:!0,pauseOnDotsHover:!1,respondTo:"window",responsive:null,rows:1,rtl:!1,slide:"",slidesPerRow:1,slidesToShow:1,slidesToScroll:1,speed:500,swipe:!0,swipeToSlide:!1,touchMove:!0,touchThreshold:5,useCSS:!0,useTransform:!0,variableWidth:!1,vertical:!1,verticalSwiping:!1,waitForAnimate:!0,zIndex:1e3},o.initials={animating:!1,dragging:!1,autoPlayTimer:null,currentDirection:0,currentLeft:null,currentSlide:0,direction:1,$dots:null,listWidth:null,listHeight:null,loadIndex:0,$nextArrow:null,$prevArrow:null,scrolling:!1,slideCount:null,slideWidth:null,$slideTrack:null,$slides:null,sliding:!1,slideOffset:0,swipeLeft:null,swiping:!1,$list:null,touchObject:{},transformsEnabled:!1,unslicked:!1},d.extend(o,o.initials),o.activeBreakpoint=null,o.animType=null,o.animProp=null,o.breakpoints=[],o.breakpointSettings=[],o.cssTransitions=!1,o.focussed=!1,o.interrupted=!1,o.hidden="hidden",o.paused=!0,o.positionProp=null,o.respondTo=null,o.rowCount=1,o.shouldClick=!0,o.$slider=d(e),o.$slidesCache=null,o.transformType=null,o.transitionType=null,o.visibilityChange="visibilitychange",o.windowWidth=0,o.windowTimer=null,i=d(e).data("slick")||{},o.options=d.extend({},o.defaults,t,i),o.currentSlide=o.options.initialSlide,o.originalSettings=o.options,void 0!==document.mozHidden?(o.hidden="mozHidden",o.visibilityChange="mozvisibilitychange"):void 0!==document.webkitHidden&&(o.hidden="webkitHidden",o.visibilityChange="webkitvisibilitychange"),o.autoPlay=d.proxy(o.autoPlay,o),o.autoPlayClear=d.proxy(o.autoPlayClear,o),o.autoPlayIterator=d.proxy(o.autoPlayIterator,o),o.changeSlide=d.proxy(o.changeSlide,o),o.clickHandler=d.proxy(o.clickHandler,o),o.selectHandler=d.proxy(o.selectHandler,o),o.setPosition=d.proxy(o.setPosition,o),o.swipeHandler=d.proxy(o.swipeHandler,o),o.dragHandler=d.proxy(o.dragHandler,o),o.keyHandler=d.proxy(o.keyHandler,o),o.instanceUid=n++,o.htmlExpr=/^(?:\s*(<[\w\W]+>)[^>]*)$/,o.registerBreakpoints(),o.init(!0)}).prototype.activateADA=function(){this.$slideTrack.find(".slick-active").attr({"aria-hidden":"false"}).find("a, input, button, select").attr({tabindex:"0"})},r.prototype.addSlide=r.prototype.slickAdd=function(e,t,i){var o=this;if("boolean"==typeof t)i=t,t=null;else if(t<0||t>=o.slideCount)return!1;o.unload(),"number"==typeof t?0===t&&0===o.$slides.length?d(e).appendTo(o.$slideTrack):i?d(e).insertBefore(o.$slides.eq(t)):d(e).insertAfter(o.$slides.eq(t)):!0===i?d(e).prependTo(o.$slideTrack):d(e).appendTo(o.$slideTrack),o.$slides=o.$slideTrack.children(this.options.slide),o.$slideTrack.children(this.options.slide).detach(),o.$slideTrack.append(o.$slides),o.$slides.each(function(e,t){d(t).attr("data-slick-index",e)}),o.$slidesCache=o.$slides,o.reinit()},r.prototype.animateHeight=function(){var e,t=this;1===t.options.slidesToShow&&!0===t.options.adaptiveHeight&&!1===t.options.vertical&&(e=t.$slides.eq(t.currentSlide).outerHeight(!0),t.$list.animate({height:e},t.options.speed))},r.prototype.animateSlide=function(e,t){var i={},o=this;o.animateHeight(),!0===o.options.rtl&&!1===o.options.vertical&&(e=-e),!1===o.transformsEnabled?!1===o.options.vertical?o.$slideTrack.animate({left:e},o.options.speed,o.options.easing,t):o.$slideTrack.animate({top:e},o.options.speed,o.options.easing,t):!1===o.cssTransitions?(!0===o.options.rtl&&(o.currentLeft=-o.currentLeft),d({animStart:o.currentLeft}).animate({animStart:e},{duration:o.options.speed,easing:o.options.easing,step:function(e){e=Math.ceil(e),!1===o.options.vertical?i[o.animType]="translate("+e+"px, 0px)":i[o.animType]="translate(0px,"+e+"px)",o.$slideTrack.css(i)},complete:function(){t&&t.call()}})):(o.applyTransition(),e=Math.ceil(e),!1===o.options.vertical?i[o.animType]="translate3d("+e+"px, 0px, 0px)":i[o.animType]="translate3d(0px,"+e+"px, 0px)",o.$slideTrack.css(i),t&&setTimeout(function(){o.disableTransition(),t.call()},o.options.speed))},r.prototype.getNavTarget=function(){var e=this.options.asNavFor;return e&&null!==e&&(e=d(e).not(this.$slider)),e},r.prototype.asNavFor=function(t){var e=this.getNavTarget();null!==e&&"object"==_typeof(e)&&e.each(function(){var e=d(this).slick("getSlick");e.unslicked||e.slideHandler(t,!0)})},r.prototype.applyTransition=function(e){var t=this,i={};!1===t.options.fade?i[t.transitionType]=t.transformType+" "+t.options.speed+"ms "+t.options.cssEase:i[t.transitionType]="opacity "+t.options.speed+"ms "+t.options.cssEase,!1===t.options.fade?t.$slideTrack.css(i):t.$slides.eq(e).css(i)},r.prototype.autoPlay=function(){var e=this;e.autoPlayClear(),e.slideCount>e.options.slidesToShow&&(e.autoPlayTimer=setInterval(e.autoPlayIterator,e.options.autoplaySpeed))},r.prototype.autoPlayClear=function(){this.autoPlayTimer&&clearInterval(this.autoPlayTimer)},r.prototype.autoPlayIterator=function(){var e=this,t=e.currentSlide+e.options.slidesToScroll;e.paused||e.interrupted||e.focussed||(!1===e.options.infinite&&(1===e.direction&&e.currentSlide+1===e.slideCount-1?e.direction=0:0===e.direction&&(t=e.currentSlide-e.options.slidesToScroll,e.currentSlide-1==0&&(e.direction=1))),e.slideHandler(t))},r.prototype.buildArrows=function(){var e=this;!0===e.options.arrows&&(e.$prevArrow=d(e.options.prevArrow).addClass("slick-arrow"),e.$nextArrow=d(e.options.nextArrow).addClass("slick-arrow"),e.slideCount>e.options.slidesToShow?(e.$prevArrow.removeClass("slick-hidden").removeAttr("aria-hidden tabindex"),e.$nextArrow.removeClass("slick-hidden").removeAttr("aria-hidden tabindex"),e.htmlExpr.test(e.options.prevArrow)&&e.$prevArrow.prependTo(e.options.appendArrows),e.htmlExpr.test(e.options.nextArrow)&&e.$nextArrow.appendTo(e.options.appendArrows),!0!==e.options.infinite&&e.$prevArrow.addClass("slick-disabled").attr("aria-disabled","true")):e.$prevArrow.add(e.$nextArrow).addClass("slick-hidden").attr({"aria-disabled":"true",tabindex:"-1"}))},r.prototype.buildDots=function(){var e,t,i=this;if(!0===i.options.dots){for(i.$slider.addClass("slick-dotted"),t=d("<ul />").addClass(i.options.dotsClass),e=0;e<=i.getDotCount();e+=1)t.append(d("<li />").append(i.options.customPaging.call(this,i,e)));i.$dots=t.appendTo(i.options.appendDots),i.$dots.find("li").first().addClass("slick-active")}},r.prototype.buildOut=function(){var e=this;e.$slides=e.$slider.children(e.options.slide+":not(.slick-cloned)").addClass("slick-slide"),e.slideCount=e.$slides.length,e.$slides.each(function(e,t){d(t).attr("data-slick-index",e).data("originalStyling",d(t).attr("style")||"")}),e.$slider.addClass("slick-slider"),e.$slideTrack=0===e.slideCount?d('<div class="slick-track"/>').appendTo(e.$slider):e.$slides.wrapAll('<div class="slick-track"/>').parent(),e.$list=e.$slideTrack.wrap('<div class="slick-list"/>').parent(),e.$slideTrack.css("opacity",0),!0!==e.options.centerMode&&!0!==e.options.swipeToSlide||(e.options.slidesToScroll=1),d("img[data-lazy]",e.$slider).not("[src]").addClass("slick-loading"),e.setupInfinite(),e.buildArrows(),e.buildDots(),e.updateDots(),e.setSlideClasses("number"==typeof e.currentSlide?e.currentSlide:0),!0===e.options.draggable&&e.$list.addClass("draggable")},r.prototype.buildRows=function(){var e,t,i,o=this,n=document.createDocumentFragment(),s=o.$slider.children();if(1<o.options.rows){for(i=o.options.slidesPerRow*o.options.rows,t=Math.ceil(s.length/i),e=0;e<t;e++){for(var r=document.createElement("div"),a=0;a<o.options.rows;a++){for(var l=document.createElement("div"),d=0;d<o.options.slidesPerRow;d++){var c=e*i+(a*o.options.slidesPerRow+d);s.get(c)&&l.appendChild(s.get(c))}r.appendChild(l)}n.appendChild(r)}o.$slider.empty().append(n),o.$slider.children().children().children().css({width:100/o.options.slidesPerRow+"%",display:"inline-block"})}},r.prototype.checkResponsive=function(e,t){var i,o,n,s=this,r=!1,a=s.$slider.width(),l=window.innerWidth||d(window).width();if("window"===s.respondTo?n=l:"slider"===s.respondTo?n=a:"min"===s.respondTo&&(n=Math.min(l,a)),s.options.responsive&&s.options.responsive.length&&null!==s.options.responsive){for(i in o=null,s.breakpoints)s.breakpoints.hasOwnProperty(i)&&(!1===s.originalSettings.mobileFirst?n<s.breakpoints[i]&&(o=s.breakpoints[i]):n>s.breakpoints[i]&&(o=s.breakpoints[i]));null!==o?null!==s.activeBreakpoint&&o===s.activeBreakpoint&&!t||(s.activeBreakpoint=o,"unslick"===s.breakpointSettings[o]?s.unslick(o):(s.options=d.extend({},s.originalSettings,s.breakpointSettings[o]),!0===e&&(s.currentSlide=s.options.initialSlide),s.refresh(e)),r=o):null!==s.activeBreakpoint&&(s.activeBreakpoint=null,s.options=s.originalSettings,!0===e&&(s.currentSlide=s.options.initialSlide),s.refresh(e),r=o),e||!1===r||s.$slider.trigger("breakpoint",[s,r])}},r.prototype.changeSlide=function(e,t){var i,o,n=this,s=d(e.currentTarget);switch(s.is("a")&&e.preventDefault(),s.is("li")||(s=s.closest("li")),i=n.slideCount%n.options.slidesToScroll!=0?0:(n.slideCount-n.currentSlide)%n.options.slidesToScroll,e.data.message){case"previous":o=0==i?n.options.slidesToScroll:n.options.slidesToShow-i,n.slideCount>n.options.slidesToShow&&n.slideHandler(n.currentSlide-o,!1,t);break;case"next":o=0==i?n.options.slidesToScroll:i,n.slideCount>n.options.slidesToShow&&n.slideHandler(n.currentSlide+o,!1,t);break;case"index":var r=0===e.data.index?0:e.data.index||s.index()*n.options.slidesToScroll;n.slideHandler(n.checkNavigable(r),!1,t),s.children().trigger("focus");break;default:return}},r.prototype.checkNavigable=function(e){var t=this.getNavigableIndexes(),i=0;if(e>t[t.length-1])e=t[t.length-1];else for(var o in t){if(e<t[o]){e=i;break}i=t[o]}return e},r.prototype.cleanUpEvents=function(){var e=this;e.options.dots&&null!==e.$dots&&(d("li",e.$dots).off("click.slick",e.changeSlide).off("mouseenter.slick",d.proxy(e.interrupt,e,!0)).off("mouseleave.slick",d.proxy(e.interrupt,e,!1)),!0===e.options.accessibility&&e.$dots.off("keydown.slick",e.keyHandler)),e.$slider.off("focus.slick blur.slick"),!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow&&e.$prevArrow.off("click.slick",e.changeSlide),e.$nextArrow&&e.$nextArrow.off("click.slick",e.changeSlide),!0===e.options.accessibility&&(e.$prevArrow&&e.$prevArrow.off("keydown.slick",e.keyHandler),e.$nextArrow&&e.$nextArrow.off("keydown.slick",e.keyHandler))),e.$list.off("touchstart.slick mousedown.slick",e.swipeHandler),e.$list.off("touchmove.slick mousemove.slick",e.swipeHandler),e.$list.off("touchend.slick mouseup.slick",e.swipeHandler),e.$list.off("touchcancel.slick mouseleave.slick",e.swipeHandler),e.$list.off("click.slick",e.clickHandler),d(document).off(e.visibilityChange,e.visibility),e.cleanUpSlideEvents(),!0===e.options.accessibility&&e.$list.off("keydown.slick",e.keyHandler),!0===e.options.focusOnSelect&&d(e.$slideTrack).children().off("click.slick",e.selectHandler),d(window).off("orientationchange.slick.slick-"+e.instanceUid,e.orientationChange),d(window).off("resize.slick.slick-"+e.instanceUid,e.resize),d("[draggable!=true]",e.$slideTrack).off("dragstart",e.preventDefault),d(window).off("load.slick.slick-"+e.instanceUid,e.setPosition)},r.prototype.cleanUpSlideEvents=function(){var e=this;e.$list.off("mouseenter.slick",d.proxy(e.interrupt,e,!0)),e.$list.off("mouseleave.slick",d.proxy(e.interrupt,e,!1))},r.prototype.cleanUpRows=function(){var e;1<this.options.rows&&((e=this.$slides.children().children()).removeAttr("style"),this.$slider.empty().append(e))},r.prototype.clickHandler=function(e){!1===this.shouldClick&&(e.stopImmediatePropagation(),e.stopPropagation(),e.preventDefault())},r.prototype.destroy=function(e){var t=this;t.autoPlayClear(),t.touchObject={},t.cleanUpEvents(),d(".slick-cloned",t.$slider).detach(),t.$dots&&t.$dots.remove(),t.$prevArrow&&t.$prevArrow.length&&(t.$prevArrow.removeClass("slick-disabled slick-arrow slick-hidden").removeAttr("aria-hidden aria-disabled tabindex").css("display",""),t.htmlExpr.test(t.options.prevArrow)&&t.$prevArrow.remove()),t.$nextArrow&&t.$nextArrow.length&&(t.$nextArrow.removeClass("slick-disabled slick-arrow slick-hidden").removeAttr("aria-hidden aria-disabled tabindex").css("display",""),t.htmlExpr.test(t.options.nextArrow)&&t.$nextArrow.remove()),t.$slides&&(t.$slides.removeClass("slick-slide slick-active slick-center slick-visible slick-current").removeAttr("aria-hidden").removeAttr("data-slick-index").each(function(){d(this).attr("style",d(this).data("originalStyling"))}),t.$slideTrack.children(this.options.slide).detach(),t.$slideTrack.detach(),t.$list.detach(),t.$slider.append(t.$slides)),t.cleanUpRows(),t.$slider.removeClass("slick-slider"),t.$slider.removeClass("slick-initialized"),t.$slider.removeClass("slick-dotted"),t.unslicked=!0,e||t.$slider.trigger("destroy",[t])},r.prototype.disableTransition=function(e){var t={};t[this.transitionType]="",!1===this.options.fade?this.$slideTrack.css(t):this.$slides.eq(e).css(t)},r.prototype.fadeSlide=function(e,t){var i=this;!1===i.cssTransitions?(i.$slides.eq(e).css({zIndex:i.options.zIndex}),i.$slides.eq(e).animate({opacity:1},i.options.speed,i.options.easing,t)):(i.applyTransition(e),i.$slides.eq(e).css({opacity:1,zIndex:i.options.zIndex}),t&&setTimeout(function(){i.disableTransition(e),t.call()},i.options.speed))},r.prototype.fadeSlideOut=function(e){var t=this;!1===t.cssTransitions?t.$slides.eq(e).animate({opacity:0,zIndex:t.options.zIndex-2},t.options.speed,t.options.easing):(t.applyTransition(e),t.$slides.eq(e).css({opacity:0,zIndex:t.options.zIndex-2}))},r.prototype.filterSlides=r.prototype.slickFilter=function(e){var t=this;null!==e&&(t.$slidesCache=t.$slides,t.unload(),t.$slideTrack.children(this.options.slide).detach(),t.$slidesCache.filter(e).appendTo(t.$slideTrack),t.reinit())},r.prototype.focusHandler=function(){var i=this;i.$slider.off("focus.slick blur.slick").on("focus.slick blur.slick","*",function(e){e.stopImmediatePropagation();var t=d(this);setTimeout(function(){i.options.pauseOnFocus&&(i.focussed=t.is(":focus"),i.autoPlay())},0)})},r.prototype.getCurrent=r.prototype.slickCurrentSlide=function(){return this.currentSlide},r.prototype.getDotCount=function(){var e=this,t=0,i=0,o=0;if(!0===e.options.infinite)if(e.slideCount<=e.options.slidesToShow)++o;else for(;t<e.slideCount;)++o,t=i+e.options.slidesToScroll,i+=e.options.slidesToScroll<=e.options.slidesToShow?e.options.slidesToScroll:e.options.slidesToShow;else if(!0===e.options.centerMode)o=e.slideCount;else if(e.options.asNavFor)for(;t<e.slideCount;)++o,t=i+e.options.slidesToScroll,i+=e.options.slidesToScroll<=e.options.slidesToShow?e.options.slidesToScroll:e.options.slidesToShow;else o=1+Math.ceil((e.slideCount-e.options.slidesToShow)/e.options.slidesToScroll);return o-1},r.prototype.getLeft=function(e){var t,i,o,n,s=this,r=0;return s.slideOffset=0,i=s.$slides.first().outerHeight(!0),!0===s.options.infinite?(s.slideCount>s.options.slidesToShow&&(s.slideOffset=s.slideWidth*s.options.slidesToShow*-1,n=-1,!0===s.options.vertical&&!0===s.options.centerMode&&(2===s.options.slidesToShow?n=-1.5:1===s.options.slidesToShow&&(n=-2)),r=i*s.options.slidesToShow*n),s.slideCount%s.options.slidesToScroll!=0&&e+s.options.slidesToScroll>s.slideCount&&s.slideCount>s.options.slidesToShow&&(r=e>s.slideCount?(s.slideOffset=(s.options.slidesToShow-(e-s.slideCount))*s.slideWidth*-1,(s.options.slidesToShow-(e-s.slideCount))*i*-1):(s.slideOffset=s.slideCount%s.options.slidesToScroll*s.slideWidth*-1,s.slideCount%s.options.slidesToScroll*i*-1))):e+s.options.slidesToShow>s.slideCount&&(s.slideOffset=(e+s.options.slidesToShow-s.slideCount)*s.slideWidth,r=(e+s.options.slidesToShow-s.slideCount)*i),s.slideCount<=s.options.slidesToShow&&(r=s.slideOffset=0),!0===s.options.centerMode&&s.slideCount<=s.options.slidesToShow?s.slideOffset=s.slideWidth*Math.floor(s.options.slidesToShow)/2-s.slideWidth*s.slideCount/2:!0===s.options.centerMode&&!0===s.options.infinite?s.slideOffset+=s.slideWidth*Math.floor(s.options.slidesToShow/2)-s.slideWidth:!0===s.options.centerMode&&(s.slideOffset=0,s.slideOffset+=s.slideWidth*Math.floor(s.options.slidesToShow/2)),t=!1===s.options.vertical?e*s.slideWidth*-1+s.slideOffset:e*i*-1+r,!0===s.options.variableWidth&&(o=s.slideCount<=s.options.slidesToShow||!1===s.options.infinite?s.$slideTrack.children(".slick-slide").eq(e):s.$slideTrack.children(".slick-slide").eq(e+s.options.slidesToShow),t=!0===s.options.rtl?o[0]?-1*(s.$slideTrack.width()-o[0].offsetLeft-o.width()):0:o[0]?-1*o[0].offsetLeft:0,!0===s.options.centerMode&&(o=s.slideCount<=s.options.slidesToShow||!1===s.options.infinite?s.$slideTrack.children(".slick-slide").eq(e):s.$slideTrack.children(".slick-slide").eq(e+s.options.slidesToShow+1),t=!0===s.options.rtl?o[0]?-1*(s.$slideTrack.width()-o[0].offsetLeft-o.width()):0:o[0]?-1*o[0].offsetLeft:0,t+=(s.$list.width()-o.outerWidth())/2)),t},r.prototype.getOption=r.prototype.slickGetOption=function(e){return this.options[e]},r.prototype.getNavigableIndexes=function(){for(var e=this,t=0,i=0,o=[],n=!1===e.options.infinite?e.slideCount:(t=-1*e.options.slidesToScroll,i=-1*e.options.slidesToScroll,2*e.slideCount);t<n;)o.push(t),t=i+e.options.slidesToScroll,i+=e.options.slidesToScroll<=e.options.slidesToShow?e.options.slidesToScroll:e.options.slidesToShow;return o},r.prototype.getSlick=function(){return this},r.prototype.getSlideCount=function(){var i,o=this,n=!0===o.options.centerMode?o.slideWidth*Math.floor(o.options.slidesToShow/2):0;return!0===o.options.swipeToSlide?(o.$slideTrack.find(".slick-slide").each(function(e,t){if(t.offsetLeft-n+d(t).outerWidth()/2>-1*o.swipeLeft)return i=t,!1}),Math.abs(d(i).attr("data-slick-index")-o.currentSlide)||1):o.options.slidesToScroll},r.prototype.goTo=r.prototype.slickGoTo=function(e,t){this.changeSlide({data:{message:"index",index:parseInt(e)}},t)},r.prototype.init=function(e){var t=this;d(t.$slider).hasClass("slick-initialized")||(d(t.$slider).addClass("slick-initialized"),t.buildRows(),t.buildOut(),t.setProps(),t.startLoad(),t.loadSlider(),t.initializeEvents(),t.updateArrows(),t.updateDots(),t.checkResponsive(!0),t.focusHandler()),e&&t.$slider.trigger("init",[t]),!0===t.options.accessibility&&t.initADA(),t.options.autoplay&&(t.paused=!1,t.autoPlay())},r.prototype.initADA=function(){var i=this,o=Math.ceil(i.slideCount/i.options.slidesToShow),n=i.getNavigableIndexes().filter(function(e){return 0<=e&&e<i.slideCount});i.$slides.add(i.$slideTrack.find(".slick-cloned")).attr({"aria-hidden":"true",tabindex:"-1"}).find("a, input, button, select").attr({tabindex:"-1"}),null!==i.$dots&&(i.$slides.not(i.$slideTrack.find(".slick-cloned")).each(function(e){var t=n.indexOf(e);d(this).attr({role:"tabpanel",id:"slick-slide"+i.instanceUid+e,tabindex:-1}),-1!==t&&d(this).attr({"aria-describedby":"slick-slide-control"+i.instanceUid+t})}),i.$dots.attr("role","tablist").find("li").each(function(e){var t=n[e];d(this).attr({role:"presentation"}),d(this).find("button").first().attr({role:"tab",id:"slick-slide-control"+i.instanceUid+e,"aria-controls":"slick-slide"+i.instanceUid+t,"aria-label":e+1+" of "+o,"aria-selected":null,tabindex:"-1"})}).eq(i.currentSlide).find("button").attr({"aria-selected":"true",tabindex:"0"}).end());for(var e=i.currentSlide,t=e+i.options.slidesToShow;e<t;e++)i.$slides.eq(e).attr("tabindex",0);i.activateADA()},r.prototype.initArrowEvents=function(){var e=this;!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow.off("click.slick").on("click.slick",{message:"previous"},e.changeSlide),e.$nextArrow.off("click.slick").on("click.slick",{message:"next"},e.changeSlide),!0===e.options.accessibility&&(e.$prevArrow.on("keydown.slick",e.keyHandler),e.$nextArrow.on("keydown.slick",e.keyHandler)))},r.prototype.initDotEvents=function(){var e=this;!0===e.options.dots&&(d("li",e.$dots).on("click.slick",{message:"index"},e.changeSlide),!0===e.options.accessibility&&e.$dots.on("keydown.slick",e.keyHandler)),!0===e.options.dots&&!0===e.options.pauseOnDotsHover&&d("li",e.$dots).on("mouseenter.slick",d.proxy(e.interrupt,e,!0)).on("mouseleave.slick",d.proxy(e.interrupt,e,!1))},r.prototype.initSlideEvents=function(){var e=this;e.options.pauseOnHover&&(e.$list.on("mouseenter.slick",d.proxy(e.interrupt,e,!0)),e.$list.on("mouseleave.slick",d.proxy(e.interrupt,e,!1)))},r.prototype.initializeEvents=function(){var e=this;e.initArrowEvents(),e.initDotEvents(),e.initSlideEvents(),e.$list.on("touchstart.slick mousedown.slick",{action:"start"},e.swipeHandler),e.$list.on("touchmove.slick mousemove.slick",{action:"move"},e.swipeHandler),e.$list.on("touchend.slick mouseup.slick",{action:"end"},e.swipeHandler),e.$list.on("touchcancel.slick mouseleave.slick",{action:"end"},e.swipeHandler),e.$list.on("click.slick",e.clickHandler),d(document).on(e.visibilityChange,d.proxy(e.visibility,e)),!0===e.options.accessibility&&e.$list.on("keydown.slick",e.keyHandler),!0===e.options.focusOnSelect&&d(e.$slideTrack).children().on("click.slick",e.selectHandler),d(window).on("orientationchange.slick.slick-"+e.instanceUid,d.proxy(e.orientationChange,e)),d(window).on("resize.slick.slick-"+e.instanceUid,d.proxy(e.resize,e)),d("[draggable!=true]",e.$slideTrack).on("dragstart",e.preventDefault),d(window).on("load.slick.slick-"+e.instanceUid,e.setPosition),d(e.setPosition)},r.prototype.initUI=function(){var e=this;!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow.show(),e.$nextArrow.show()),!0===e.options.dots&&e.slideCount>e.options.slidesToShow&&e.$dots.show()},r.prototype.keyHandler=function(e){var t=this;e.target.tagName.match("TEXTAREA|INPUT|SELECT")||(37===e.keyCode&&!0===t.options.accessibility?t.changeSlide({data:{message:!0===t.options.rtl?"next":"previous"}}):39===e.keyCode&&!0===t.options.accessibility&&t.changeSlide({data:{message:!0===t.options.rtl?"previous":"next"}}))},r.prototype.lazyLoad=function(){function e(e){d("img[data-lazy]",e).each(function(){var e=d(this),t=d(this).attr("data-lazy"),i=d(this).attr("data-srcset"),o=d(this).attr("data-sizes")||s.$slider.attr("data-sizes"),n=document.createElement("img");n.onload=function(){e.animate({opacity:0},100,function(){i&&(e.attr("srcset",i),o&&e.attr("sizes",o)),e.attr("src",t).animate({opacity:1},200,function(){e.removeAttr("data-lazy data-srcset data-sizes").removeClass("slick-loading")}),s.$slider.trigger("lazyLoaded",[s,e,t])})},n.onerror=function(){e.removeAttr("data-lazy").removeClass("slick-loading").addClass("slick-lazyload-error"),s.$slider.trigger("lazyLoadError",[s,e,t])},n.src=t})}var t,i,o,s=this;if(!0===s.options.centerMode?o=!0===s.options.infinite?(i=s.currentSlide+(s.options.slidesToShow/2+1))+s.options.slidesToShow+2:(i=Math.max(0,s.currentSlide-(s.options.slidesToShow/2+1)),s.options.slidesToShow/2+1+2+s.currentSlide):(i=s.options.infinite?s.options.slidesToShow+s.currentSlide:s.currentSlide,o=Math.ceil(i+s.options.slidesToShow),!0===s.options.fade&&(0<i&&i--,o<=s.slideCount&&o++)),t=s.$slider.find(".slick-slide").slice(i,o),"anticipated"===s.options.lazyLoad)for(var n=i-1,r=o,a=s.$slider.find(".slick-slide"),l=0;l<s.options.slidesToScroll;l++)n<0&&(n=s.slideCount-1),t=(t=t.add(a.eq(n))).add(a.eq(r)),n--,r++;e(t),s.slideCount<=s.options.slidesToShow?e(s.$slider.find(".slick-slide")):s.currentSlide>=s.slideCount-s.options.slidesToShow?e(s.$slider.find(".slick-cloned").slice(0,s.options.slidesToShow)):0===s.currentSlide&&e(s.$slider.find(".slick-cloned").slice(-1*s.options.slidesToShow))},r.prototype.loadSlider=function(){var e=this;e.setPosition(),e.$slideTrack.css({opacity:1}),e.$slider.removeClass("slick-loading"),e.initUI(),"progressive"===e.options.lazyLoad&&e.progressiveLazyLoad()},r.prototype.next=r.prototype.slickNext=function(){this.changeSlide({data:{message:"next"}})},r.prototype.orientationChange=function(){this.checkResponsive(),this.setPosition()},r.prototype.pause=r.prototype.slickPause=function(){this.autoPlayClear(),this.paused=!0},r.prototype.play=r.prototype.slickPlay=function(){var e=this;e.autoPlay(),e.options.autoplay=!0,e.paused=!1,e.focussed=!1,e.interrupted=!1},r.prototype.postSlide=function(e){var t=this;t.unslicked||(t.$slider.trigger("afterChange",[t,e]),t.animating=!1,t.slideCount>t.options.slidesToShow&&t.setPosition(),t.swipeLeft=null,t.options.autoplay&&t.autoPlay(),!0===t.options.accessibility&&(t.initADA(),t.options.focusOnChange&&d(t.$slides.get(t.currentSlide)).attr("tabindex",0).focus()))},r.prototype.prev=r.prototype.slickPrev=function(){this.changeSlide({data:{message:"previous"}})},r.prototype.preventDefault=function(e){e.preventDefault()},r.prototype.progressiveLazyLoad=function(e){e=e||1;var t,i,o,n,s,r=this,a=d("img[data-lazy]",r.$slider);a.length?(t=a.first(),i=t.attr("data-lazy"),o=t.attr("data-srcset"),n=t.attr("data-sizes")||r.$slider.attr("data-sizes"),(s=document.createElement("img")).onload=function(){o&&(t.attr("srcset",o),n&&t.attr("sizes",n)),t.attr("src",i).removeAttr("data-lazy data-srcset data-sizes").removeClass("slick-loading"),!0===r.options.adaptiveHeight&&r.setPosition(),r.$slider.trigger("lazyLoaded",[r,t,i]),r.progressiveLazyLoad()},s.onerror=function(){e<3?setTimeout(function(){r.progressiveLazyLoad(e+1)},500):(t.removeAttr("data-lazy").removeClass("slick-loading").addClass("slick-lazyload-error"),r.$slider.trigger("lazyLoadError",[r,t,i]),r.progressiveLazyLoad())},s.src=i):r.$slider.trigger("allImagesLoaded",[r])},r.prototype.refresh=function(e){var t,i=this,o=i.slideCount-i.options.slidesToShow;!i.options.infinite&&i.currentSlide>o&&(i.currentSlide=o),i.slideCount<=i.options.slidesToShow&&(i.currentSlide=0),t=i.currentSlide,i.destroy(!0),d.extend(i,i.initials,{currentSlide:t}),i.init(),e||i.changeSlide({data:{message:"index",index:t}},!1)},r.prototype.registerBreakpoints=function(){var e,t,i,o=this,n=o.options.responsive||null;if("array"===d.type(n)&&n.length){for(e in o.respondTo=o.options.respondTo||"window",n)if(i=o.breakpoints.length-1,n.hasOwnProperty(e)){for(t=n[e].breakpoint;0<=i;)o.breakpoints[i]&&o.breakpoints[i]===t&&o.breakpoints.splice(i,1),i--;o.breakpoints.push(t),o.breakpointSettings[t]=n[e].settings}o.breakpoints.sort(function(e,t){return o.options.mobileFirst?e-t:t-e})}},r.prototype.reinit=function(){var e=this;e.$slides=e.$slideTrack.children(e.options.slide).addClass("slick-slide"),e.slideCount=e.$slides.length,e.currentSlide>=e.slideCount&&0!==e.currentSlide&&(e.currentSlide=e.currentSlide-e.options.slidesToScroll),e.slideCount<=e.options.slidesToShow&&(e.currentSlide=0),e.registerBreakpoints(),e.setProps(),e.setupInfinite(),e.buildArrows(),e.updateArrows(),e.initArrowEvents(),e.buildDots(),e.updateDots(),e.initDotEvents(),e.cleanUpSlideEvents(),e.initSlideEvents(),e.checkResponsive(!1,!0),!0===e.options.focusOnSelect&&d(e.$slideTrack).children().on("click.slick",e.selectHandler),e.setSlideClasses("number"==typeof e.currentSlide?e.currentSlide:0),e.setPosition(),e.focusHandler(),e.paused=!e.options.autoplay,e.autoPlay(),e.$slider.trigger("reInit",[e])},r.prototype.resize=function(){var e=this;d(window).width()!==e.windowWidth&&(clearTimeout(e.windowDelay),e.windowDelay=window.setTimeout(function(){e.windowWidth=d(window).width(),e.checkResponsive(),e.unslicked||e.setPosition()},50))},r.prototype.removeSlide=r.prototype.slickRemove=function(e,t,i){var o=this;if(e="boolean"==typeof e?!0===(t=e)?0:o.slideCount-1:!0===t?--e:e,o.slideCount<1||e<0||e>o.slideCount-1)return!1;o.unload(),!0===i?o.$slideTrack.children().remove():o.$slideTrack.children(this.options.slide).eq(e).remove(),o.$slides=o.$slideTrack.children(this.options.slide),o.$slideTrack.children(this.options.slide).detach(),o.$slideTrack.append(o.$slides),o.$slidesCache=o.$slides,o.reinit()},r.prototype.setCSS=function(e){var t,i,o=this,n={};!0===o.options.rtl&&(e=-e),t="left"==o.positionProp?Math.ceil(e)+"px":"0px",i="top"==o.positionProp?Math.ceil(e)+"px":"0px",n[o.positionProp]=e,!1===o.transformsEnabled||(!(n={})===o.cssTransitions?n[o.animType]="translate("+t+", "+i+")":n[o.animType]="translate3d("+t+", "+i+", 0px)"),o.$slideTrack.css(n)},r.prototype.setDimensions=function(){var e=this;!1===e.options.vertical?!0===e.options.centerMode&&e.$list.css({padding:"0px "+e.options.centerPadding}):(e.$list.height(e.$slides.first().outerHeight(!0)*e.options.slidesToShow),!0===e.options.centerMode&&e.$list.css({padding:e.options.centerPadding+" 0px"})),e.listWidth=e.$list.width(),e.listHeight=e.$list.height(),!1===e.options.vertical&&!1===e.options.variableWidth?(e.slideWidth=Math.ceil(e.listWidth/e.options.slidesToShow),e.$slideTrack.width(Math.ceil(e.slideWidth*e.$slideTrack.children(".slick-slide").length))):!0===e.options.variableWidth?e.$slideTrack.width(5e3*e.slideCount):(e.slideWidth=Math.ceil(e.listWidth),e.$slideTrack.height(Math.ceil(e.$slides.first().outerHeight(!0)*e.$slideTrack.children(".slick-slide").length)));var t=e.$slides.first().outerWidth(!0)-e.$slides.first().width();!1===e.options.variableWidth&&e.$slideTrack.children(".slick-slide").width(e.slideWidth-t)},r.prototype.setFade=function(){var i,o=this;o.$slides.each(function(e,t){i=o.slideWidth*e*-1,!0===o.options.rtl?d(t).css({position:"relative",right:i,top:0,zIndex:o.options.zIndex-2,opacity:0}):d(t).css({position:"relative",left:i,top:0,zIndex:o.options.zIndex-2,opacity:0})}),o.$slides.eq(o.currentSlide).css({zIndex:o.options.zIndex-1,opacity:1})},r.prototype.setHeight=function(){var e,t=this;1===t.options.slidesToShow&&!0===t.options.adaptiveHeight&&!1===t.options.vertical&&(e=t.$slides.eq(t.currentSlide).outerHeight(!0),t.$list.css("height",e))},r.prototype.setOption=r.prototype.slickSetOption=function(){var e,t,i,o,n,s=this,r=!1;if("object"===d.type(arguments[0])?(i=arguments[0],r=arguments[1],n="multiple"):"string"===d.type(arguments[0])&&(o=arguments[1],r=arguments[2],"responsive"===(i=arguments[0])&&"array"===d.type(arguments[1])?n="responsive":void 0!==arguments[1]&&(n="single")),"single"===n)s.options[i]=o;else if("multiple"===n)d.each(i,function(e,t){s.options[e]=t});else if("responsive"===n)for(t in o)if("array"!==d.type(s.options.responsive))s.options.responsive=[o[t]];else{for(e=s.options.responsive.length-1;0<=e;)s.options.responsive[e].breakpoint===o[t].breakpoint&&s.options.responsive.splice(e,1),e--;s.options.responsive.push(o[t])}r&&(s.unload(),s.reinit())},r.prototype.setPosition=function(){var e=this;e.setDimensions(),e.setHeight(),!1===e.options.fade?e.setCSS(e.getLeft(e.currentSlide)):e.setFade(),e.$slider.trigger("setPosition",[e])},r.prototype.setProps=function(){var e=this,t=document.body.style;e.positionProp=!0===e.options.vertical?"top":"left","top"===e.positionProp?e.$slider.addClass("slick-vertical"):e.$slider.removeClass("slick-vertical"),void 0===t.WebkitTransition&&void 0===t.MozTransition&&void 0===t.msTransition||!0===e.options.useCSS&&(e.cssTransitions=!0),e.options.fade&&("number"==typeof e.options.zIndex?e.options.zIndex<3&&(e.options.zIndex=3):e.options.zIndex=e.defaults.zIndex),void 0!==t.OTransform&&(e.animType="OTransform",e.transformType="-o-transform",e.transitionType="OTransition",void 0===t.perspectiveProperty&&void 0===t.webkitPerspective&&(e.animType=!1)),void 0!==t.MozTransform&&(e.animType="MozTransform",e.transformType="-moz-transform",e.transitionType="MozTransition",void 0===t.perspectiveProperty&&void 0===t.MozPerspective&&(e.animType=!1)),void 0!==t.webkitTransform&&(e.animType="webkitTransform",e.transformType="-webkit-transform",e.transitionType="webkitTransition",void 0===t.perspectiveProperty&&void 0===t.webkitPerspective&&(e.animType=!1)),void 0!==t.msTransform&&(e.animType="msTransform",e.transformType="-ms-transform",e.transitionType="msTransition",void 0===t.msTransform&&(e.animType=!1)),void 0!==t.transform&&!1!==e.animType&&(e.animType="transform",e.transformType="transform",e.transitionType="transition"),e.transformsEnabled=e.options.useTransform&&null!==e.animType&&!1!==e.animType},r.prototype.setSlideClasses=function(e){var t,i,o,n,s=this,r=s.$slider.find(".slick-slide").removeClass("slick-active slick-center slick-current").attr("aria-hidden","true");s.$slides.eq(e).addClass("slick-current"),!0===s.options.centerMode?(o=s.options.slidesToShow%2==0?1:0,n=Math.floor(s.options.slidesToShow/2),!0===s.options.infinite&&(n<=e&&e<=s.slideCount-1-n?s.$slides.slice(e-n+o,e+n+1).addClass("slick-active").attr("aria-hidden","false"):(t=s.options.slidesToShow+e,r.slice(t-n+1+o,t+n+2).addClass("slick-active").attr("aria-hidden","false")),0===e?r.eq(r.length-1-s.options.slidesToShow).addClass("slick-center"):e===s.slideCount-1&&r.eq(s.options.slidesToShow).addClass("slick-center")),s.$slides.eq(e).addClass("slick-center")):0<=e&&e<=s.slideCount-s.options.slidesToShow?s.$slides.slice(e,e+s.options.slidesToShow).addClass("slick-active").attr("aria-hidden","false"):r.length<=s.options.slidesToShow?r.addClass("slick-active").attr("aria-hidden","false"):(i=s.slideCount%s.options.slidesToShow,t=!0===s.options.infinite?s.options.slidesToShow+e:e,s.options.slidesToShow==s.options.slidesToScroll&&s.slideCount-e<s.options.slidesToShow?r.slice(t-(s.options.slidesToShow-i),t+i).addClass("slick-active").attr("aria-hidden","false"):r.slice(t,t+s.options.slidesToShow).addClass("slick-active").attr("aria-hidden","false")),"ondemand"!==s.options.lazyLoad&&"anticipated"!==s.options.lazyLoad||s.lazyLoad()},r.prototype.setupInfinite=function(){var e,t,i,o=this;if(!0===o.options.fade&&(o.options.centerMode=!1),!0===o.options.infinite&&!1===o.options.fade&&(t=null,o.slideCount>o.options.slidesToShow)){for(i=!0===o.options.centerMode?o.options.slidesToShow+1:o.options.slidesToShow,e=o.slideCount;e>o.slideCount-i;--e)t=e-1,d(o.$slides[t]).clone(!0).attr("id","").attr("data-slick-index",t-o.slideCount).prependTo(o.$slideTrack).addClass("slick-cloned");for(e=0;e<i+o.slideCount;e+=1)t=e,d(o.$slides[t]).clone(!0).attr("id","").attr("data-slick-index",t+o.slideCount).appendTo(o.$slideTrack).addClass("slick-cloned");o.$slideTrack.find(".slick-cloned").find("[id]").each(function(){d(this).attr("id","")})}},r.prototype.interrupt=function(e){e||this.autoPlay(),this.interrupted=e},r.prototype.selectHandler=function(e){var t=d(e.target).is(".slick-slide")?d(e.target):d(e.target).parents(".slick-slide"),i=(i=parseInt(t.attr("data-slick-index")))||0;this.slideCount<=this.options.slidesToShow?this.slideHandler(i,!1,!0):this.slideHandler(i)},r.prototype.slideHandler=function(e,t,i){var o,n,s,r,a,l,d=this;if(t=t||!1,!(!0===d.animating&&!0===d.options.waitForAnimate||!0===d.options.fade&&d.currentSlide===e))if(!1===t&&d.asNavFor(e),o=e,l=d.getLeft(o),r=d.getLeft(d.currentSlide),d.currentLeft=null===d.swipeLeft?r:d.swipeLeft,!1===d.options.infinite&&!1===d.options.centerMode&&(e<0||e>d.getDotCount()*d.options.slidesToScroll))!1===d.options.fade&&(o=d.currentSlide,!0!==i?d.animateSlide(r,function(){d.postSlide(o)}):d.postSlide(o));else if(!1===d.options.infinite&&!0===d.options.centerMode&&(e<0||e>d.slideCount-d.options.slidesToScroll))!1===d.options.fade&&(o=d.currentSlide,!0!==i?d.animateSlide(r,function(){d.postSlide(o)}):d.postSlide(o));else{if(d.options.autoplay&&clearInterval(d.autoPlayTimer),n=o<0?d.slideCount%d.options.slidesToScroll!=0?d.slideCount-d.slideCount%d.options.slidesToScroll:d.slideCount+o:o>=d.slideCount?d.slideCount%d.options.slidesToScroll!=0?0:o-d.slideCount:o,d.animating=!0,d.$slider.trigger("beforeChange",[d,d.currentSlide,n]),s=d.currentSlide,d.currentSlide=n,d.setSlideClasses(d.currentSlide),d.options.asNavFor&&(a=(a=d.getNavTarget()).slick("getSlick")).slideCount<=a.options.slidesToShow&&a.setSlideClasses(d.currentSlide),d.updateDots(),d.updateArrows(),!0===d.options.fade)return!0!==i?(d.fadeSlideOut(s),d.fadeSlide(n,function(){d.postSlide(n)})):d.postSlide(n),void d.animateHeight();!0!==i?d.animateSlide(l,function(){d.postSlide(n)}):d.postSlide(n)}},r.prototype.startLoad=function(){var e=this;!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow.hide(),e.$nextArrow.hide()),!0===e.options.dots&&e.slideCount>e.options.slidesToShow&&e.$dots.hide(),e.$slider.addClass("slick-loading")},r.prototype.swipeDirection=function(){var e,t=this,i=t.touchObject.startX-t.touchObject.curX,o=t.touchObject.startY-t.touchObject.curY,n=Math.atan2(o,i);return(e=Math.round(180*n/Math.PI))<0&&(e=360-Math.abs(e)),e<=45&&0<=e||e<=360&&315<=e?!1===t.options.rtl?"left":"right":135<=e&&e<=225?!1===t.options.rtl?"right":"left":!0===t.options.verticalSwiping?35<=e&&e<=135?"down":"up":"vertical"},r.prototype.swipeEnd=function(e){var t,i,o=this;if(o.dragging=!1,o.swiping=!1,o.scrolling)return o.scrolling=!1;if(o.interrupted=!1,o.shouldClick=!(10<o.touchObject.swipeLength),void 0===o.touchObject.curX)return!1;if(!0===o.touchObject.edgeHit&&o.$slider.trigger("edge",[o,o.swipeDirection()]),o.touchObject.swipeLength>=o.touchObject.minSwipe){switch(i=o.swipeDirection()){case"left":case"down":t=o.options.swipeToSlide?o.checkNavigable(o.currentSlide+o.getSlideCount()):o.currentSlide+o.getSlideCount(),o.currentDirection=0;break;case"right":case"up":t=o.options.swipeToSlide?o.checkNavigable(o.currentSlide-o.getSlideCount()):o.currentSlide-o.getSlideCount(),o.currentDirection=1}"vertical"!=i&&(o.slideHandler(t),o.touchObject={},o.$slider.trigger("swipe",[o,i]))}else o.touchObject.startX!==o.touchObject.curX&&(o.slideHandler(o.currentSlide),o.touchObject={})},r.prototype.swipeHandler=function(e){var t=this;if(!(!1===t.options.swipe||"ontouchend"in document&&!1===t.options.swipe||!1===t.options.draggable&&-1!==e.type.indexOf("mouse")))switch(t.touchObject.fingerCount=e.originalEvent&&void 0!==e.originalEvent.touches?e.originalEvent.touches.length:1,t.touchObject.minSwipe=t.listWidth/t.options.touchThreshold,!0===t.options.verticalSwiping&&(t.touchObject.minSwipe=t.listHeight/t.options.touchThreshold),e.data.action){case"start":t.swipeStart(e);break;case"move":t.swipeMove(e);break;case"end":t.swipeEnd(e)}},r.prototype.swipeMove=function(e){var t,i,o,n,s,r=this,a=void 0!==e.originalEvent?e.originalEvent.touches:null;return!(!r.dragging||r.scrolling||a&&1!==a.length)&&(t=r.getLeft(r.currentSlide),r.touchObject.curX=void 0!==a?a[0].pageX:e.clientX,r.touchObject.curY=void 0!==a?a[0].pageY:e.clientY,r.touchObject.swipeLength=Math.round(Math.sqrt(Math.pow(r.touchObject.curX-r.touchObject.startX,2))),s=Math.round(Math.sqrt(Math.pow(r.touchObject.curY-r.touchObject.startY,2))),!r.options.verticalSwiping&&!r.swiping&&4<s?!(r.scrolling=!0):(!0===r.options.verticalSwiping&&(r.touchObject.swipeLength=s),i=r.swipeDirection(),void 0!==e.originalEvent&&4<r.touchObject.swipeLength&&(r.swiping=!0,e.preventDefault()),n=(!1===r.options.rtl?1:-1)*(r.touchObject.curX>r.touchObject.startX?1:-1),!0===r.options.verticalSwiping&&(n=r.touchObject.curY>r.touchObject.startY?1:-1),o=r.touchObject.swipeLength,(r.touchObject.edgeHit=!1)===r.options.infinite&&(0===r.currentSlide&&"right"===i||r.currentSlide>=r.getDotCount()&&"left"===i)&&(o=r.touchObject.swipeLength*r.options.edgeFriction,r.touchObject.edgeHit=!0),!1===r.options.vertical?r.swipeLeft=t+o*n:r.swipeLeft=t+o*(r.$list.height()/r.listWidth)*n,!0===r.options.verticalSwiping&&(r.swipeLeft=t+o*n),!0!==r.options.fade&&!1!==r.options.touchMove&&(!0===r.animating?(r.swipeLeft=null,!1):void r.setCSS(r.swipeLeft))))},r.prototype.swipeStart=function(e){var t,i=this;if(i.interrupted=!0,1!==i.touchObject.fingerCount||i.slideCount<=i.options.slidesToShow)return!(i.touchObject={});void 0!==e.originalEvent&&void 0!==e.originalEvent.touches&&(t=e.originalEvent.touches[0]),i.touchObject.startX=i.touchObject.curX=void 0!==t?t.pageX:e.clientX,i.touchObject.startY=i.touchObject.curY=void 0!==t?t.pageY:e.clientY,i.dragging=!0},r.prototype.unfilterSlides=r.prototype.slickUnfilter=function(){var e=this;null!==e.$slidesCache&&(e.unload(),e.$slideTrack.children(this.options.slide).detach(),e.$slidesCache.appendTo(e.$slideTrack),e.reinit())},r.prototype.unload=function(){var e=this;d(".slick-cloned",e.$slider).remove(),e.$dots&&e.$dots.remove(),e.$prevArrow&&e.htmlExpr.test(e.options.prevArrow)&&e.$prevArrow.remove(),e.$nextArrow&&e.htmlExpr.test(e.options.nextArrow)&&e.$nextArrow.remove(),e.$slides.removeClass("slick-slide slick-active slick-visible slick-current").attr("aria-hidden","true").css("width","")},r.prototype.unslick=function(e){this.$slider.trigger("unslick",[this,e]),this.destroy()},r.prototype.updateArrows=function(){var e=this;Math.floor(e.options.slidesToShow/2),!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&!e.options.infinite&&(e.$prevArrow.removeClass("slick-disabled").attr("aria-disabled","false"),e.$nextArrow.removeClass("slick-disabled").attr("aria-disabled","false"),0===e.currentSlide?(e.$prevArrow.addClass("slick-disabled").attr("aria-disabled","true"),e.$nextArrow.removeClass("slick-disabled").attr("aria-disabled","false")):(e.currentSlide>=e.slideCount-e.options.slidesToShow&&!1===e.options.centerMode||e.currentSlide>=e.slideCount-1&&!0===e.options.centerMode)&&(e.$nextArrow.addClass("slick-disabled").attr("aria-disabled","true"),e.$prevArrow.removeClass("slick-disabled").attr("aria-disabled","false")))},r.prototype.updateDots=function(){var e=this;null!==e.$dots&&(e.$dots.find("li").removeClass("slick-active").end(),e.$dots.find("li").eq(Math.floor(e.currentSlide/e.options.slidesToScroll)).addClass("slick-active"))},r.prototype.visibility=function(){this.options.autoplay&&(document[this.hidden]?this.interrupted=!0:this.interrupted=!1)},d.fn.slick=function(){for(var e,t=this,i=arguments[0],o=Array.prototype.slice.call(arguments,1),n=t.length,s=0;s<n;s++)if("object"==_typeof(i)||void 0===i?t[s].slick=new r(t[s],i):e=t[s].slick[i].apply(t[s].slick,o),void 0!==e)return e;return t}}),jQuery(document).ready(function(){jQuery(".product__wrap").slick({infinite:!0,slidesToShow:4,slidesToScroll:1,prevArrow:"<img alt='edellinen' class='a-left control-c prev slick-prev' src='/wp-content/themes/kauppakamari/dist/images/chevron_left.png'>",nextArrow:"<img alt='seuraava' class='a-right control-c next slick-next' src='/wp-content/themes/kauppakamari/dist/images/chevron_right.png'>",responsive:[{breakpoint:1024,settings:{slidesToShow:3,slidesToScroll:3,infinite:!0,dots:!0}},{breakpoint:600,settings:{slidesToShow:2,slidesToScroll:2}},{breakpoint:480,settings:{slidesToShow:1,slidesToScroll:1}}]}),jQuery(".block-trainer-list").slick({infinite:!0,slidesToShow:4,slidesToScroll:1,prevArrow:"<img alt='edellinen' class='a-left control-c prev slick-prev' src='/wp-content/themes/kauppakamari/dist/images/chevron_left.png'>",nextArrow:"<img alt='seuraava' class='a-right control-c next slick-next' src='/wp-content/themes/kauppakamari/dist/images/chevron_right.png'>",responsive:[{breakpoint:1024,settings:{slidesToShow:3,slidesToScroll:3,infinite:!0,dots:!0}},{breakpoint:600,settings:{slidesToShow:2,slidesToScroll:2}},{breakpoint:480,settings:{slidesToShow:1,slidesToScroll:1}}]}),jQuery(".shopify-scroller-inner.carousel-slick").slick({dots:!1,infinite:!1,speed:300,slidesToShow:4,adaptiveHeight:!0,autoplay:!1,prevArrow:"<img alt='edellinen' class='a-left control-c prev slick-prev' src='/wp-content/themes/kauppakamari/dist/images/chevron_left_c.png'>",nextArrow:"<img alt='seuraava' class='a-right control-c next slick-next' src='/wp-content/themes/kauppakamari/dist/images/chevron_right_c.png'>",responsive:[{breakpoint:900,settings:{slidesToShow:2}},{breakpoint:480,settings:{slidesToShow:1,centerPadding:"50px"}}]}),jQuery(".block-memberstories").slick({dots:!1,infinite:!0,speed:300,slidesToShow:1,adaptiveHeight:!0,prevArrow:"<img alt='edellinen' class='a-left control-c prev slick-prev' src='/wp-content/themes/kauppakamari/dist/images/chevron_left_c.png'>",nextArrow:"<img alt='seuraava' class='a-right control-c next slick-next' src='/wp-content/themes/kauppakamari/dist/images/chevron_right_c.png'>"}),jQuery(".block-new_members").slick({dots:!1,centerMode:!0,centerPadding:"120px",slidesToShow:3,infinite:!0,autoplay:!1,prevArrow:"<img alt='edellinen' class='a-left control-c prev slick-prev' src='/wp-content/themes/kauppakamari/dist/images/chevron_left_c.png'>",nextArrow:"<img alt='seuraava' class='a-right control-c next slick-next' src='/wp-content/themes/kauppakamari/dist/images/chevron_right_c.png'>",responsive:[{breakpoint:1199,settings:{slidesToShow:2}},{breakpoint:767,settings:{slidesToShow:1,centerPadding:0}}]})}),$(".accordion-toggle").click(function(e){e.preventDefault();var t=$(this);t.next().hasClass("show")?(t.toggleClass("active"),t.next().removeClass("show"),t.next().slideUp(350)):(t.toggleClass("active"),t.parent().parent().find("div .inner").removeClass("show"),t.parent().parent().find("div .inner").slideUp(350),t.next().toggleClass("show"),t.next().slideToggle(350))});var url,checkboxes=document.querySelectorAll(".contact__checkboxes--box");void 0!==checkboxes[0]&&null!=checkboxes[0]&&function(){function o(e){var t,i,o,n,s={action:"kauppakamari_filter_contact",peoplecat:0<arguments.length&&void 0!==e?e:""};console.log(s),t="kategoria",i=s.peoplecat,history.pushState&&((o=new URLSearchParams(window.location.search)).set(t,i),n=window.location.protocol+"//"+window.location.host+window.location.pathname+"?"+o.toString(),window.history.pushState({path:n},"",n)),jQuery.ajaxSetup({cache:!1}),jQuery.ajax({type:"POST",cache:!1,headers:{"cache-control":"no-cache"},credentials:"same-origin",url:frontendajax.ajaxurl+'?lang=""',data:s,beforeSend:function(){document.querySelector(".contact__list").innerHTML="Loading"},success:function(e){console.log(e),e?document.querySelector(".contact__list").innerHTML=e.data:console.log(e)}})}var n=document.querySelectorAll(".contact__checkboxes--box");url=new URL(window.location.href);for(var s=[],e=0;e<n.length;e++)!function(i){n[i].addEventListener("change",function(e){var t;e.target.checked?s.push(n[i].value):e.target.checked||(t=s.indexOf(e.target.value),s.splice(t,1)),o(s)})}(e)}();var _button_next,_button_prev,checkarrow=document.querySelectorAll(".clubs_next_page");function nyt_change_page(i,o){var e={action:"kauppakamari_filter_clubs",next_page:i};jQuery(".block-clubs__ajax").fadeOut(500),setTimeout(function(){jQuery.ajax({type:"POST",credentials:"same-origin",url:frontendajax.ajaxurl,data:e,beforeSend:function(){document.querySelector(".block-clubs__ajax")},success:function(e){var t;console.log(e),e?(t=document.querySelector(".block-clubs__ajax"),document.querySelector(".block-clubs__wrap").dataset.page=i,document.querySelector(".clubs_current_page").innerHTML=i,t.innerHTML=e.data,jQuery(".block-clubs__ajax").fadeIn(500),button_next[0].style.display=i===o?"none":"initial",1===i?button_prev[0].style.display="none":1<i&&(button_prev[0].style.display="initial")):console.log(e)}})},500)}function _createForOfIteratorHelper(e,t){var i;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(i=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var o=0,n=function(){};return{s:n,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,r=!0,a=!1;return{s:function(){i=e[Symbol.iterator]()},n:function(){var e=i.next();return r=e.done,e},e:function(e){a=!0,s=e},f:function(){try{r||null==i.return||i.return()}finally{if(a)throw s}}}}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,o=new Array(t);i<t;i++)o[i]=e[i];return o}void 0!==checkarrow[0]&&null!=checkarrow[0]&&((_button_next=document.querySelectorAll(".clubs_next_page"))[0].addEventListener("click",function(e){e.preventDefault();var t=document.querySelector(".block-clubs__wrap").dataset.page,i=document.querySelector(".block-clubs__wrap").dataset.max;nyt_change_page(parseInt(t)+1,parseInt(i))}),(_button_prev=document.querySelectorAll(".clubs_prev_page"))[0].addEventListener("click",function(e){e.preventDefault();var t=document.querySelector(".block-clubs__wrap").dataset.page,i=document.querySelector(".block-clubs__wrap").dataset.max;nyt_change_page(parseInt(t)-1,parseInt(i))}),_button_prev[0].style.display="none"),$(document).ready(function(){$("iframe").attr("data-cookieconsent","marketing")}),$(document).ready(function(){function t(){event.preventDefault(),$("body").removeClass("show-video-modal noscroll"),$("#youtube-iframe").attr("src","")}$(".js-trigger-video-modal").on("click",function(e){e.preventDefault();var t=$("#youtube-iframe").attr("src").match(/^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#&?]*).*/),i="//www.youtube.com/embed/"+(!(!t||11!==t[7].length)&&t[7])+"?autoplay=1&rel=0";$("#youtube-iframe").attr("src",i),$("body").addClass("show-video-modal noscroll")}),$("body").on("click",".close-video-modal, .video-modal .overlay",function(e){t()}),$("body").keyup(function(e){27===e.keyCode&&t()})});var search_url,paramsString,searchParams,searchTimeout=0,hashChangeTimeout=0;function getQueryStringParam(e){return new URLSearchParams(window.location.search).get(e)}function getQueryStringArrayParam(e){var t,i=[],o=_createForOfIteratorHelper(new URLSearchParams(window.location.search).entries());try{for(o.s();!(t=o.n()).done;){var n=t.value;"post_category[]"===n[0]&&i.push(n[1])}}catch(e){o.e(e)}finally{o.f()}return i}function setQueryStringParam(e,t){var i=new URLSearchParams(window.location.search);i.set(e,t),window.history.pushState({},"",location.pathname+"?"+i),window.dispatchEvent(new Event("popstate"))}function setQueryStringArrayParam(e,t){var i=new URLSearchParams(window.location.search);i.append(e,t),window.history.pushState({},"",location.pathname+"?"+i),window.dispatchEvent(new Event("popstate"))}function setQueryStringArray(e,t){var i=new URLSearchParams(window.location.search);i.delete(e);var o,n=_createForOfIteratorHelper(t);try{for(n.s();!(o=n.n()).done;){var s=o.value;i.append(e,s)}}catch(e){n.e(e)}finally{n.f()}window.history.pushState({},"",location.pathname+"?"+i),window.dispatchEvent(new Event("popstate"))}function getHashQueryStringParam(e){return new URLSearchParams(window.location.hash.substr(1)).get(e)}function emptyResults(){var e=$(".main.search-results-container").css("height");$(".main.search-results-container").html('<div class="loader loader--big-margins">Ladataan...</div>'),$(".main.search-results-container").css("height",e)}function searchInit(){var e=new URLSearchParams(document.location.search);emptyResults();var t=e.get("s"),i=getHashQueryStringParam("s"),o=e.get("people_id"),n=$("#search_key").val(),s=$(".main-search-input").val(),r=$("#people_id").val();n!==(i=t)&&$("#search_key").val(i),s!==i&&$(".main-search-input").val(i),null===o&&(o=""),r!==o&&$("#people_id").val(o);var a=e.get("post_type");$(".post_category_checkbox").prop("checked",!1),$(".main-category-list").addClass("hidden"),"string"==typeof a&&""!==a?($(".main-category-list[data-slug="+a+"]").removeClass("hidden"),$('input[name="post_type"]').prop("checked",!1),$('input[name="post_type"][value='+a+"]").first().prop("checked",!0)):($('input[name="post_type"]').prop("checked",!1),$('input[name="post_type"]').first().prop("checked",!0));var l=getQueryStringArrayParam("post_category[]");if(Array.isArray(l)&&""!==l[0]){$('input[name="post_category[]"]').prop("checked",!1);var d,c=_createForOfIteratorHelper(l);try{for(c.s();!(d=c.n()).done;){var p=d.value;$('input[name="post_category[]"][value='+p+"]").first().prop("checked",!0)}}catch(e){c.e(e)}finally{c.f()}}else"string"==typeof l&&""!==l?($('input[name="post_category[]"]').prop("checked",!1),$('input[name="post_category[]"][value='+l+"]").first().prop("checked",!0)):$('input[name="post_category[]"]').prop("checked",!1);triggerSearch()}function noSearchMessage(){$(".search-results-container").html('<p class="no-search-made">Anna hakusana, vähimmäispituus 3 merkkiä</p>')}function triggerSearch(){0===searchTimeout?getSearchResults():searchTimeout=setTimeout(function(){searchTimeout=0,getSearchResults()},1e3)}function getSearchResults(){var e=$(".main-search-input").val();$("#search_key").val(e),window.dataLayer=window.dataLayer||[],window.dataLayer.push({event:"search",searchTerm:e});var t=$("#search-filter-primary").serialize(),i=getQueryStringParam("paged");return i<2&&(i=1),t=t+"&paged="+i,$.ajax({type:"POST",url:search_url,data:t,success:function(e){$(".search-results-container").html(e),$(".main.search-results-container").css("height",""),setTimeout(function(){window.scrollToTop&&!0===window.scrollToTop&&(window.scrollToTop=!1,$("html, body").scrollTop(0))},40)}}),!1}window.addEventListener("popstate",function(){hashChangeTimeout=0===hashChangeTimeout?(clearTimeout(hashChangeTimeout),setTimeout(function(){searchInit()},100)):(clearTimeout(hashChangeTimeout),setTimeout(function(){searchInit()},800))}),aucor_navigation(document.getElementById("primary-navigation"),{desktop_min_width:890,menu_toggle:"#menu-toggle"}),fitvids(),"function"==typeof objectFitPolyfill&&document.addEventListener("lazybeforeunveil",function(e){var t=e.target;objectFitPolyfill(),t.addEventListener("load",function(){objectFitPolyfill()})}),jQuery("a").filter(function(){return this.hostname&&this.hostname!==location.hostname}).addClass("external").attr("target","_blank").filter(function(){return $(this).has("img").length}).addClass("external-img").removeClass("external"),jQuery(document).ready(function(o){search_url=frontendajax.ajaxurl,o(".search-menu-button").on("click",function(e){e.preventDefault(),o(this).parent().toggleClass("search-open"),o(this).parent().hasClass("search-open")?(o(this).text("Sulje haku"),o(".top-search-input").first().focus()):o(this).text("Haku")}),o(".search-top-container .search-top-container-form").on("submit",function(e){if(o(".top-search-input").val().length<3)return""===o(".top-search-input").val()?o(".top-search-input").prop("placeholder","Kirjoita vähintään kolme kirjainta"):alert("Kirjoita vähintään kolme kirjainta hakukenttään"),e.preventDefault(),!1;o(".top-search-input").val();var t=o(".search-top-container-form").attr("action");o(".search-top-container-form").attr("action",t)}),o("#main_search_form").on("submit",function(e){e.preventDefault();var t=o(".main-search-input").val();return"undefined"!==t&&void 0!==t||(t=""),setQueryStringParam("s",t),setQueryStringParam("paged","1"),!1}),0<o("body.search").length&&(searchInit(),o(".main-search-input-clear").on("click",function(){o(".main-search-input").val("").focus()}),o("#primary").on("click","#show-more-people-link",function(e){return e.preventDefault(),setQueryStringParam("paged","1"),setQueryStringParam("post_type","people"),!1}),o("#primary").on("click",".remove_selected_writer",function(e){return e.preventDefault(),setQueryStringParam("paged","1"),setQueryStringParam("people_id",""),!1}),o("#primary").on("click","a.page-numbers",function(e){e.preventDefault();var t=o(this).attr("href"),i=new URLSearchParams(t).get("paged");return"undefined"!==i&&void 0!==i&&setQueryStringParam("paged",i),!(window.scrollToTop=!0)}),o('input[name="post_type"]').on("click",function(){setQueryStringParam("post_type",o(this).val()),setQueryStringParam("post_category[]",""),setQueryStringParam("paged","1")}),o(".search-filters").on("click",".post_category_checkbox",function(e){o(this).val();setQueryStringParam("paged","1");var t=[];o(".post_category_checkbox:checked").each(function(){t.push(o(this).val())}),setQueryStringArray("post_category[]",t)}),o("#primary").on("click",".search_by_writer",function(e){e.preventDefault(),setQueryStringParam("paged","1");var t=o(this).attr("data-id");return t&&(setQueryStringParam("s",""),setQueryStringParam("people_id",t),setQueryStringParam("post_type","uutiset")),!1})),o("label.btn").on("click","input",function(e){e.stopPropagation(),o(this).attr("checked",!o(this).attr("checked")),o(e.target).closest("label").toggleClass("btn-flat")}),o(".btn_checkbox").on("click keydown",function(e){e.preventDefault(),o("#ch_1").is(":checked")||o("#ch_2").is(":checked")||o("#ch_3").is(":checked")||o("#ch_4").is(":checked")||o("#ch_5").is(":checked")||o("#ch_6").is(":checked")?(o(".newsletter__checkboxes").get(0).style.display="none",o(".newsletter__info").get(0).style.display="block"):o(".newsletter__error").get(0).style.display="block"}),o(function(){var t=o(".newsletter");function e(e){e.preventDefault();return o.ajax({data:t.serialize(),success:i,type:"POST",url:"https://www.kauppakamarinuutiskirjeet.fi/?rf=json"})}function i(e){o(".newsletter__info").get(0).style.display="none",o(".newsletter__success").get(0).style.display="block"}t.on("submit",e)}),o(".subpages-toggle").on("click",function(e){this.parentNode.classList.toggle("open")}),o("#newsletter__back").on("click",function(e){e.preventDefault(),o(".newsletter__info").get(0).style.display="none",o(".newsletter__checkboxes").get(0).style.display="block"}),0<o(".article-filter--select").length&&o(".article-filter--select").on("change",function(){var e=o(this).val();document.location.href=e})});