.block-text-image {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin: 4rem auto !important;
  
  .is-front-page & {
    opacity: 0;
  }

  &__item {

    // background color selection
    &.background-coral {
      background: $coral-25 !important;
    }
    &.background-lightblue {
      background: $light-blue-25 !important;
    }
    &.background-white {
      background: transparent !important;
      border: 2px solid $light-blue-25;
    }
    color: $primary;
    width: 100%;
    align-items: center;
    display: flex;
    margin-bottom: 1rem;
    margin-top: 2rem;
    position: relative;
    & .addition-graphic {
      position: absolute;
      display: block;
      // mobile
      width: 68px;
      top: -34px;
      right: 16px;
      z-index: 2;
    }
    @include breakpoint($m) {
      width: calc(50% - 1.5rem);
      .addition-graphic {
        width: 92px;
        right: 0;
      }
    }
    @include breakpoint($l) {
      .addition-graphic {
        width: 92px;
        right: 170px;
      }
    }
    @include breakpoint($sub-l) {
      flex-direction: column-reverse;
    }

    .title {
      font-weight: 600;
      display: block;
      font-size: 22px;
      margin-top: 0px;
      margin-bottom: 1.25rem;
      @include breakpoint($m) {
        font-size: 20px;
      }
    }
  }

  .text__img_bg_image {
    display: block;
    position: relative;
    width: 100%;
    background-size: cover;
    background-position: 50%;
    min-height: 260px;

    body:not(.page-template-default) & {
      @media screen and (min-width: 1124px) {
        /*old style*/
        min-width: 210px;
        max-width: 225px;
        height: 100%;
        max-height: 500px;
        min-height: 225px;
      }
    }
  }

  img:not(.addition-graphic) {
    min-width: 225px;
    max-width: 225px;
    height: 100%;
    max-height: 500px;
    min-height: 225px;
    object-fit: cover;
    @include breakpoint($sub-l) {
      order: 1;
      min-width: 100%;
      max-width: 100%;
    }
  }
  &__content {
    position: relative;
    padding: 2rem;
    flex: 1;
    width: 100%;
    p {
     font-size: 18px;
    }
    @include breakpoint($sub-l) {
      order: 2;
    }
  }
  a {
    @include textlink;
    // font-size: 16px !important;
    margin-top: 1.5rem;
    display: block;
    width: fit-content;
    text-align: left;
    background: transparent;
  }
}
.page-template-default {
  .block-text-image__item {
    flex-direction: column;
    width: calc(50% - 1.5rem);
    .content {
      p {
        font-size: 16px !important;
      }
    }
    .text__img_bg_image {
      order: -1;
      width: 100%;
    }
    @include breakpoint(max-width 860px) {
      flex-direction: column;
      width: 100%;
      .content, .text__img_bg_image {
        width: 100%;
      }
      .block-text-image__content {
        padding: 2rem 1.5rem;
      }
    }
  }
}


/* responsivity for text__img_bg_image, needs different size for subnav page layout */

body.page-template-default .block-text-image .text__img_bg_image {
  /*aside is hidden */
  @include breakpoint($sub-l) {
    height: 300px;
  }
  @include breakpoint(max-width 860px) {
    height: 360px;
  }
  @include breakpoint($sub-s) {
    height: min-content;
  }
}

body.page-template-template-landingpage-blank .block-text-image .text__img_bg_image {
  /* same as before*/
  @media screen and (min-width: 1120px) {
    min-width: 165px;
    max-width: 225px;
    height: 100%;
    max-height: 375px;
    min-height: 225px;
  }

  @include breakpoint($sub-l) {
    order: 1;
    min-width: 100%;
    max-width: 100%;
    height: 280px;
  }
}
