<?php
//search functions 2022
//add_filter( 'relevanssi_modify_wp_query', 'post_search_tax_query');
//https://www.relevanssi.com/knowledge-base/multiple-custom-taxonomies/


// does not work
//function rlv_postsperpage( $limits ) {
//  if ( is_search() ) {
//    global $wp_query;
//    $wp_query->query_vars['posts_per_page'] = 3;
//  }
//  return $limits;
//}


add_filter('query_vars', 'introduce_qvs');
function introduce_qvs($qv)
{
    $qv[] = 'article_type';
    return $qv;
}

add_filter('relevanssi_search_filters', 'rlv_check_tax_query');
function rlv_check_tax_query($args)
{
    return $args;
}

/*remove shopify scroller | from excerpt */
add_filter('relevanssi_excerpt_content', 'rlv_remove_noindex_class');
function rlv_remove_noindex_class($content)
{
    //not work: return preg_replace( '#<(.*) class="sep".*?</\1>#ms', ' ', $content );
    return str_replace('|', ' ', $content);
}




function get_writer_name_by_id($id)
{
    $people_id = (int) $id;
    $title = get_the_title($people_id);
    if (strlen($title) < 3) return 'Tuntematon';
    return $title;
}

function all_taxonomies($postID)
{
    $r = wp_get_post_terms($postID);
    $ret = ' -- ';
    if (!empty($r)) {
        foreach ($r as $i) {
            $ret .= $i->name . '|';
        }
        return $ret;
    } else return $r;
}

function all_categories($postID)
{
    $r = wp_get_post_terms($postID, 'category');
    $ret = ' -- ';
    if (!empty($r)) {
        foreach ($r as $i) {
            $ret .= $i->name . ' | ';
        }
        return $ret;
    } else return 'no cats';
}

function article_category_term_id_from_slug($term_slug)
{
    $lang = 'fi';

    $found_terms = get_terms(array(
        'slug'       => $term_slug,
        'taxonomy'   => 'category',
        'lang'       => $lang,
        'hide_empty' => false,
        'fields'     => 'ids',
    ));
    if (is_array($found_terms) && !empty($found_terms)) {
        return $found_terms[0];
    } else {
        error_log('Ei löytynyt category slugilla ' . $term_slug . ' termiä, tarkista kielivalinta ja slugin oikeellisuus - search-functions.php');
    }
    return 0;
}


function article_type_term_id_from_slug($term_slug)
{
    $lang = 'fi';

    $found_terms = get_terms(array(
        'slug'       => $term_slug,
        'taxonomy'   => 'article_type',
        'lang'       => $lang,
        'hide_empty' => false,
        'fields'     => 'ids',
    ));

    //error_log('$found_terms: ');
    //error_log(' ' . print_r($found_terms, true));

    if (is_array($found_terms) && !empty($found_terms)) {
        return $found_terms[0];
    } else {
        error_log('Ei löytynyt article_type slugilla ' . $term_slug . ' termiä, tarkista kielivalinta ja slugin oikeellisuus - search-functions.php');
    }
    return 0;

    //  $t = get_term_by('slug', $term_slug, 'article_type');
    // //error_log('t:');
    // //error_log(print_r($t, true));
    // //error_log(print_r($t->term_id, true));
    //  if(!empty($t)) {
    //    //error_log(print_r($t, true));
    //    return $t->term_id;
    //  }
    //  error_log('Ei löytynyt slugilla '.$term_slug.' termiä');
}




function teaser_search_result()
{
    global $post;
    $article_types = get_the_terms($post, 'article_type');
    $category = get_the_terms($post, 'category');
    $post_type = get_post_type();

    //$pt = get_post_types(['name' => $pts], 'objects');
    //$postType = $pt[$pts]->labels->name;
    $post_type_print = $post_type;
    if ($post_type == 'page') {
        $post_type_print = pll__('Sivu');
    } else if ($post_type == 'release') {
        $post_type_print = pll__("Tiedote");
    } else if ($post_type == 'post') {
        $post_type_print = pll__('Artikkeli');
        if (!empty($article_types) && is_array($article_types)) {
            $post_type_print = $article_types[0]->name;
        }
    }
    ob_start();
?>
    <div class="teaser_search_result">
        <div class="info teaser_search_result__post_type">
            <?= pll__($post_type_print) ?>
            <?php
            /*
         if($post_type !== 'page') {
          echo '<small>';
          echo all_categories($post->ID);
          echo '</small>';'
         }
         */
            ?>
        </div>
        <div class="info teaser_search_result__date">
            <?php
            if ($post_type !== 'page' && $post_type !== 'people') {
                echo get_the_date('d.m.Y', $post->ID);
            }
            ?>
        </div>
        <h2 class="teaser_search_result__title">
            <a href="<?= get_the_permalink() ?>" aria-label="Linkki sisältöön: <?= get_the_title() ?>">
                <?= get_the_title() ?>
            </a>
        </h2>
        <div class="teaser_search_result__excerpt">
            <?= get_the_excerpt() ?>
        </div>
    </div>
<?php

    $result = ob_get_clean();
    //??
    //echo apply_filters( 'the_rnb_search_result_element', $result );
    echo $result;
}



function hskk_search($lang = "fi")
{

    $return = [];
    $post_counter = 0;
    $posts_per_page = 10;

    $default_post_types = ['page', 'post', 'release'];
    /*@@TODO: this should get post types from taxonomies and merge with post+release */
    $date_order_post_types = ['post', 'uutiset', 'release', 'tiedotteet', 'neuvonnan-artikkelit', 'kolumnit-ja-tarinat', 'lausunnot'];


    $post_type = $_POST['post_type'] !== '' ? $_POST['post_type'] : '';
    if (isset($_POST['post_category']) && $_POST['post_category'] !== '') {
        $post_categories = (array) $_POST['post_category'];
        if ($post_categories[0] == '') {
            $post_categories = null;
        }
    } else {
        $post_categories = null;
    }
    $search_key = $_POST['search_key'] !== '' ? sanitize_text_field($_POST['search_key']) : '';
    $people_id = $_POST['people_id'] !== '' ? (int) sanitize_text_field($_POST['people_id']) : 0;
    //$orderby = $_POST['orderby'] !== 'undefined' ? $_POST['orderby'] : '';

    //if(isset($_POST['post_category']) && $_POST['post_category'] !== '') {
    $paged = (int) $_POST['paged'];
    //}
    //else {
    //  $paged = 1;
    //}

    if (isset($search_key) && strlen($search_key) > 2) {
        if ($lang == "fi" || empty($lang)) {
            echo '<p class="search-results-info">Tulokset hakusanalle &quot;' . sanitize_text_field($search_key) . '&quot;</p>';
        } else {
            echo '<p class="search-results-info" data-lang="' . $lang . '">Results for &quot;' . sanitize_text_field($search_key) . '&quot;</p>';
        }
    }

    $full_people_search = false;
    //PERSON SEARCH 3 - if nothing selected but search key provided
    if (($post_type == '' || $post_type == 'people') && (strlen($search_key) > 2 || $post_type == 'people') && $paged < 2) {
        if ($post_type == 'people') {
            $full_people_search = true;
        }
        $person_args = array(
            'post_type'         => array('people'),
            'post_status'       =>  'publish',
            'posts_per_page'    => 200,
            's'                 =>  $search_key,
            //'paged'             =>  $paged,
        );

        $query = new WP_Query();
        $query->parse_query($person_args);
        relevanssi_do_query($query);

        $ppl_qty = 0;
        echo '<div class="search-results--people">';
        while ($query->have_posts()) {
            if ($ppl_qty == 0) {

                if ($lang == "fi") {
                    echo '<p class="cat">Yhteystiedot</p>';
                    echo '<p class="contact-info-text">Henkilöstön sähköpostiosoitteet <NAME_EMAIL></p>';
                } else {
                    echo '<p class="cat">Contact information</p>';
                    echo '<p class="contact-info-text">Personnel email <NAME_EMAIL></p>';
                }
                echo '<div class="search-results--people--container">';
            }
            $ppl_qty++;
            $query->the_post();
            if ($full_people_search) {
                get_template_part('partials/content/teaser-people');
            } else {
                get_template_part('partials/content/teaser-people', '', 'simple');
            }
            if ($ppl_qty >= 3 && !$full_people_search) {
                break;
            }
        }
        //while end people
        echo '</div>';

        $count = $query->found_posts;
        //echo '<p>Löydettiin '.$count.' kpl henkilöä</p>';
        if (!$full_people_search && $count > $ppl_qty) {
            echo '<p class="show-more-people"><a href="#" id="show-more-people-link">Näytä lisää yhteystietoja</a></p>';
        }
        echo '</div>';
        wp_reset_postdata();
    }
    //IF PERSON SEARCH 3 END

    if ($full_people_search) {
        die('<!-- full people search, no pages or pagination -->');
    }

    //pagination if not people search:
    if (!$full_people_search) {
        // The filter function doesn't work, commented out
        // add_filter( 'post_limits', 'rlv_postsperpage' );
    }


    //if post type not given, use sort filter to make pages-thisyear-rest:
    if ($post_type == '') {
        add_filter('relevanssi_hits_filter', 'rlv_pages_top_sorting');

        //echo '<br>Käytetään default sortteria<br><br>';
        $args = array(
            'post_type'         =>  $default_post_types,
            'post_status'       =>  'publish',
            'posts_per_page'    => $posts_per_page,
            'lang'              =>  $lang,
            's'                 =>  sanitize_text_field($search_key),
            'paged'             =>  $paged,
            'offset'             =>  $offset
        );
        if ($people_id > 0) {
            $writer_args_arr = array(
                'meta_key'  => 'writer_object',
                'meta_value' => $people_id
            );
            $args = array_merge($args, $writer_args_arr);
        }
    } else if ($post_type == 'page') {
        $post_type_arg = 'page';

        $args = array(
            'post_type'         =>  $post_type_arg,
            'post_status'       =>  'publish',
            'posts_per_page'    => $posts_per_page,
            'lang'              =>  $lang,
            's'                 =>  sanitize_text_field($search_key),
            'paged'             =>  $paged,
            'offset'             =>  $offset
        );
    } else {
        /* POST TYPE IS POST, FILTER taxonomy:article_type AND maybe also taxonomy:category */
        $term_id_to_filter = article_type_term_id_from_slug($post_type);

        if ($post_type == 'tiedotteet' || $post_type == 'release') {
            $post_type_arg = 'release';
        } else {
            $post_type_arg = 'post';
        }

        $orderby = '';
        if (in_array($post_type, $date_order_post_types)) {
            $orderby = array('orderby' => 'date', 'order' => 'DESC');
        }

        $args = array(
            'post_type'         =>  $post_type_arg,
            'post_status'       =>  'publish',
            //'orderby'           =>  'date',
            //'order'             =>  'DESC',
            'posts_per_page'    => $posts_per_page,
            'lang'              =>  $lang,
            's'                 =>  sanitize_text_field($search_key),
            'paged'             =>  $paged,
            'offset'             =>  $offset,
            'tax_query'         => array(
                'relation' => 'OR'
            )
        );
        if (is_array($orderby)) {
            $args = array_merge($args, $orderby);
        }

        //if given also post_categories, need relevanssi filter:
        if (is_array($post_categories) && count($post_categories) > 0 && !empty($post_categories)) {
            $terms = array();
            //echo '<p>DEB: Annettu post category, needs 2. tax_query ';
            //print_r($post_categories);
            //echo '</p>';

            if ($post_type == 'lausunnot') {

                foreach ($post_categories as $single_category_slug) {
                    if (strlen($single_category_slug) == 4) {
                        $year_sub_sub_query[] = array(
                            'year' => $single_category_slug,
                        );
                    }
                }
                if (!empty($year_sub_sub_query)) {
                    $year_sub_query = array(
                        'date_query' => array(
                            'relation' => 'OR',
                            $year_sub_sub_query
                        )
                    );
                }
            } else {

                $tax_sub_query = array(
                    'relation' => 'OR'
                );

                foreach ($post_categories as $single_category_slug) {
                    if (strlen($single_category_slug) > 2 && strlen($single_category_slug) < 60) {
                        //can skip or add some here if needed
                        $term_slug = sanitize_text_field($single_category_slug);
                        //$terms[] = article_category_term_id_from_slug($term_slug);
                        $term_id = article_category_term_id_from_slug($term_slug);

                        //add each to sub query array:
                        $tax_sub_query[] = array(
                            'taxonomy' => 'category',
                            'field'    => 'term_id',
                            'terms'    => $term_id
                        );
                    }
                }
            }

            if ($term_id_to_filter < 2) {
                error_log('Given term_id_to_filter parameter was incorrect no int from term. (e:43522)');
                error_log(print_r($post_type, true));
                echo '<p class="error">Virhe 22: Annetulla sisältötyypillä ei löytynyt sisältöä.</p>';
                die();
            }
            echo '<!-- <p>term id  : ' . $term_id_to_filter . '</p>-->';
            $args['tax_query']['relation'] = 'AND';

            //add first, type
            $args['tax_query'][] =
                array(
                    'taxonomy' => 'article_type',
                    'field'    => 'term_id',
                    'terms'    => $term_id_to_filter
                );
            //add second, category
            $args['tax_query'][] = $tax_sub_query;

            //this should work, but it doesn't work in ajax backend, and would have needed tax query args
            //add_filter( 'relevanssi_modify_wp_query', 'post_search_tax_query');

        } else if ($post_type !== 'people') {
            /* category not given, add only article_type (uutiset, neuvonnan-artikkelit, lausunnot) into tax query: */
            if ($term_id_to_filter < 2) {
                error_log('Given term_id_to_filter parameter was incorrect no int from term. (e:43533)');
                error_log(print_r($post_type, true));
                echo '<p class="error">Virhe 33: Annetulla sisältötyypillä ei löytynyt sisältöä.</p>';
                die();
            }

            if (isset($args['tax_query']['relation'])) unset($args['tax_query']['relation']);
            $args['tax_query'] = array(
                array(
                    'taxonomy' => 'article_type',
                    'field'    => 'term_id',
                    'terms'    => $term_id_to_filter
                )
            );
        }

        //person search
        if ($people_id > 0) {
            //echo '<div class="writer_selected">';
            //echo '<p class="search-results-info">Henkilön '.get_writer_name_by_id($people_id).' kirjoittamat sisällöt:</p>';
            //echo '<p><a href="#" class="remove_selected_writer">Tyhjennä kirjoittajan valinta</a> </p>';
            //echo '</div>';

            $writer_args_arr = array(
                'meta_key'  => 'writer_object',
                'meta_value' => $people_id
            );
            $args = array_merge($args, $writer_args_arr);
        }

        if (isset($year_sub_query)) {
            $args = array_merge($args, $year_sub_query);
        }
    }
    /* post type post, end */

    // The Query
    if (function_exists('relevanssi_do_query')) {
        $the_query = new WP_Query();
        $the_query->parse_query($args);
        //$the_query->set( 'tax_query', $tax_query );
        relevanssi_do_query($the_query);

        //$the_query = new WP_Query($args);
        //echo '<pre>';
        //print_r($the_query);
        //echo '</pre>';
        //die();

    } else {
        die('relevanssi not enabled!');
        $the_query = new WP_Query($args);
    }


    if ($people_id > 0) {
        //echo '<div class="writer_selected">';
        //echo '<p class="search-results-info">Henkilön '.get_writer_name_by_id($people_id).' kirjoittamat sisällöt:</p>';
        //echo '<p><a href="#" class="remove_selected_writer">Tyhjennä kirjoittajan valinta</a> </p>';
        //echo '</div>';
    }

    //ob_start( );
    echo '<div class="search-results-posts-pages">';
    if ($the_query->have_posts()) {
        //if(isset($search_key) && strlen($search_key) > 2) {
        //  echo '<p class="search-results-info">Tulokset hakusanalle &quot;'.sanitize_text_field($search_key).'&quot;</p>';
        //}
        $post_counter = $the_query->found_posts;

        while ($the_query->have_posts()) {
            $the_query->the_post();
            teaser_search_result();
        }

        echo '<!-- <p>Tuloksia yhteensä: ' . $post_counter . '</p> -->';
        echo '<!-- <p>Sivu nyt: ' . $post_counter . '</p> -->';
        //make post_category variable to string

        $add_args = array('post_type' => $post_type);

        $add_fragment = '';
        if (is_array($post_categories) && !empty($post_categories)) {
            $add_fragment = '&post_category[]=' . implode("&post_category[]=", $post_categories);
        }

        $wp_home_url = esc_url(home_url('/'));
        //esc_url( get_pagenum_link( 999999999 ) )
        echo '<div class="search-pagination"><!-- <p> current page: ' . $paged . '</p> -->';
        echo paginate_links(array(
            'base'         => str_replace(999999999, '%#%',  $wp_home_url . '?s=' . sanitize_text_field($search_key) . '&paged=999999999'),
            'total'        => $the_query->max_num_pages,
            'current'      => max(1, $paged),
            'format'       => '?paged=%#%',
            'show_all'     => false,
            'type'         => 'plain',
            'end_size'     => 2,
            'mid_size'     => 1,
            'prev_next'    => true,
            'prev_text'    => sprintf('<i></i> %1$s', 'Edellinen'),
            'next_text'    => sprintf('%1$s <i></i>', 'Seuraava'),
            'add_args'     => $add_args,
            'add_fragment' => $add_fragment,
        ));
        echo '</div>';
    } else {
        echo '<div class="search-results-none">';
        if ($lang == "fi" || empty($lang)) {
            echo '<p>Hakusi ei tuottanut tuloksia, kokeilethan muuttaa hakutermejä.</p>';
        } else {
            echo '<p>No results. Please try some different search terms!</p>';
        }
        echo '</div>';
    }
    echo '</div>';
    //$return['content'] = ob_get_clean( );
    //$return['pageCount'] = ceil($post_counter / 5);
    //$return['searchPostCount'] = $post_counter;
    //$return['args'] = $args;
    //
    //echo json_encode($return);
    wp_die();
}







add_action('wp_ajax_hskk_search', 'hskk_search');
add_action('wp_ajax_nopriv_hskk_search', 'hskk_search');

add_action('wp_ajax_hskk_search_en', function () {
    hskk_search("en");
});
add_action('wp_ajax_nopriv_hskk_search_en', function () {
    hskk_search("en");
});
