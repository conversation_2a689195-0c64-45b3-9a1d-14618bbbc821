<?php
/**
 * Job listing
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'jobs-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-jobs';
if( !empty($block['className']) ) {
  $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
  $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$title = get_field("title");
$apply_title = get_field("apply_title");
$show_btn = get_field("apply_btn");
$btn_link = get_field("apply_btn_link");
$background_col = get_field("background_col");

// Getting the posts
$args = array(
    'post_type' => 'job',
    'posts_per_page' => -1,
);
$query = new WP_Query($args);
$jobItems = $query->get_posts();

?>

<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?> <?php echo $background_col.'-background'; ?>">
    <div class="block-jobs__inner-container">
        <h2 class="block-jobs__title"><?php echo $title; ?></h2>

        <div class="block-jobs__list">
            <?php foreach($jobItems as $key => $item) : ?>
                <?php 
                    $postEndDate = strtotime($item->dates['end_date']);
                    $currentDate = strtotime(date('d.m.Y'));

                    // Dont show posts that are older than post->dates['end_date'] + 1 day

                    if($currentDate < $postEndDate + 86400 || empty($postEndDate)) : ?>
            
                    <?php $cats = get_the_terms($item->ID, 'job_category'); ?>

                    <a href="<?php echo get_permalink($item); ?>" class="job-link">
                        <div class="job-item">
                            <div class="job-item__img">
                                <img src="<?php if($key % 2 != 0) echo get_stylesheet_directory_uri() . '/assets/images/HSKK.png'; else echo get_stylesheet_directory_uri() . '/assets/images/ko-ratas.svg'; ?>" alt="Kuvituskuva">
                            </div>
                            
                            <div class="job-item__body">
                                <div class="content">
                                    <h3 class="job-title"><?php echo $item->post_title; ?></h3>
                                    <p class="job-description"><?php if( ! empty($item->description)) echo $item->description; else echo ''; ?></p>
                                </div>
                                <?php if( !empty($cats) ) :?>
                                    <div class="job-categories"> <!-- Get post categories -->
                                        <?php foreach($cats as $cat) : ?>
                                            <span class="category-pill"><?php echo $cat->name; ?></span>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="job-item__dates">
                                <span class="job-location"><strong><?php if( ! empty($item->locations['city'])) echo $item->locations['city']; else echo '-'; ?></strong></span>
                                <span class="date-released">Julkaistu <?php if( ! empty($item->dates['publish_date'])) echo $item->dates['publish_date']; else echo '-'; ?></span>
                                <span class="date-closing">Haku päättyy <?php if( ! empty($item->dates['end_date'])) echo $item->dates['end_date']; else echo '-'; ?></span>
                            </div>
                        </div>
                    </a>

                <?php endif; ?>

            <?php endforeach; ?>
        </div>

        <div class="block-jobs__apply">
            <h3 class="text"><?php echo $apply_title; ?></h3>
            <a class="apply-btn" target="_blank" href="<?php echo $btn_link['url']; ?>">Jätä avoin hakemus</a>
        </div>
    </div>
</div>