<?php
/**
 * Template part: Hero
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package kauppakamari
 */

// extra classes
$class = array();



// background
$image_align = get_field('image_align');
if($image_align == '' || $image_align == 'not_set') $image_align = 'center';
$image = '';
if (is_singular() && has_post_thumbnail()) {
  $image = kauppakamari_get_image(get_post_thumbnail_id(), 'hero');
}

?>

<div class="heronew page <?php echo !is_front_page() ?: "page-front "; ?> <?php echo esc_attr(implode(' ', $class)); ?>">

  <div class="heronew__inner">

    <div class="contents">
        <h1 class="heronew__title"><?php echo get_the_title();?></h1>
        <?php if ($content = get_field('content')) : ?>
          <p class="heronew__description"><?php echo $content; ?></p>
        <?php endif; ?>

        <?php if ($link = get_field('link')) : ?>
          <a href="<?php echo $link['url']; ?>"><?php echo $link['title']; ?></a>
        <?php endif; ?>
    </div>
    
    <div class="image-container">
      <div class="image-wrapper" style="background: url(<?php echo kauppakamari_get_image_url(get_post_thumbnail_id(), 'hero'); ?>)">
        <img src="<?php echo get_template_directory_uri() . '/dist/images/ko-ratas.svg'; ?>" alt="" class="addition-graphic">
      </div>
    </div>
  </div>

</div>
<?php get_template_part('partials/content/breadcrumb'); ?>
