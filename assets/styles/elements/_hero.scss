/* ==========================================================================
 # Hero
========================================================================== */

// new hero (koulutusonline.fi) with small adjustments
.heronew {
	padding-bottom: 5rem;
  margin-bottom: 8rem;
  background-color: $light-blue-25;
	&__title {
		// color: $blue;
		line-height: 1.5;
		margin: 1.5rem 0 2.5rem;
	}
	&__inner {
		background: $light-blue-25;
		padding: 2rem 1.5rem;
    @include breakpoint($m) {
      padding: 2rem 3rem;
    }
	}
  .contents {
    margin-bottom: 1rem;
  }
	.image-container {
		position: relative;
		height: 40px;
		.addition-graphic {
      display: block !important;
			position: absolute;
			bottom: 50px;
			left: -50px;
			width: 80px;
			z-index: 30;
		}
		.image-wrapper {
			position: absolute;
			width: calc(100% - 45px);
			max-width: 550px;
			height: 230px;
			top: 0;
			right: 0;
			left: auto;
			margin: auto;
			background-size: cover !important;
			background-position: center !important;
		}
	}
}

@media screen and (min-width: 1120px) {
	.heronew {
		// margin-bottom: 8rem;
    margin-bottom: 2rem;
    margin-left: calc((100% - 100vw)/2);
    margin-right: calc((100% - 100vw)/2);
    padding: 2.5rem;
    padding-left: calc((120vw - 100%)/2);
    padding-right: calc((120vw - 100%)/2);
    // background: #eff6fa;

		&__inner {
			display: grid;
			// padding: 0;
			gap: .5rem;
			grid-template-columns: 1fr 1fr;
			align-items: center;
		}
    &__description {
      font-size: 18px;
    }
    .contents {
      max-width: 100%;
    }
		.image-container {
			min-height: 280px;
			.addition-graphic {
				top: 70px;
				left: -80px;
				width: 160px;
			}
			.image-wrapper {
				position: absolute;
				width: calc(100% - 100px);
				height: 350px;
				top: auto;
				bottom: -70px;
			}
		}
	}
}

@media screen and (min-width: $l) {
	.heronew {
		&__title {
			font-size: 38px;
		}
		&__inner {
			padding: 0;
			grid-template-columns: 1fr 1fr;
		}
    .contents {
      margin-left: 1.25rem;
    }
	}	
}
// new hero end --



.hero {
  align-items: center;
  // background: $primary;
  background: $light-blue-25;
  display: flex;
  position: relative;

  &__container {
    @include wrap;
    max-width: 640px;
    padding-top: 4rem;
    padding-bottom: 4rem;
    position: relative;
    text-align: center;
    z-index: 4;
  }

  &__title {
    margin: 0;
    //font-size: 1.75rem;
    //font-weight: 700;

    @include breakpoint($hero-s) {
      //font-size: 2rem;
    }

    @include breakpoint($hero-m) {
      //font-size: 38px;
    }

    @include breakpoint($hero-l) {
     // font-size: 38px;
    }
  }

  &__meta {
    margin-top: $gap-small;
  }

  &__description {
    margin: 1rem 0 0 0;
    //font-size: .875rem;
    color: $primary;
    //font-size: 1.125rem;

    @include breakpoint($hero-s) {
      //font-size: 1.125rem;
    }

    @include breakpoint($hero-m) {
      //font-size: 1.125rem;
    }

    @media screen and (max-width: 480px) {
      width: 75%;
    }
  }

  /* No background image
  ----------------------------------------------- */

  &--no-background {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  /* Has background image
  ----------------------------------------------- */

  &--has-background {

    overflow: hidden;

    .hero__background,
    .hero__background__image {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
    }

    // min-height as aspect ratio but grow with the content
    &:before {
      content: '';
      float: left;

      // 8:5 for smaller screens
      @include aspect-ratio(8, 5);

      // 8:3 for middle size
      @include breakpoint($hero-m) {
        @include aspect-ratio(8, 3);
      }

      // 3:1 for bigger screens
      @include breakpoint($hero-l) {
        @include aspect-ratio(3, 1);
      }

    }

    &:after {
      clear: left;
      content: ' ';
      display: table;
    }

    h1,
    p,
    span,
    a {
      color: $primary;

      //text-shadow: 0 0 1rem rgba(0,0,0,.6);
      @include breakpoint($hero-s) {
        //text-shadow: 0 0 1rem rgba(0,0,0,.4);
      }
    }
  }

  &__background {
    &__image {
      //@include wrap;
      line-height: 0;
      overflow: hidden;

      img {
        display: block;
        line-height: 0;
        z-index: 2;
        position: absolute;

        // proper way to cover
        object-fit: cover;

        // close enough cover for older browsers
        /*
        min-width: 100%;
        min-height: 100%;
        transform: translate(-50%, -50%);
        left: 50%;
        top: 50%;
        */

        width: 100%;
        height: auto;

        /*should be on mobile */
        @media screen and (max-width: 1380px) {
          min-height: 100%;
        }

        // scale preload image
        &.lazyload-preload {
          transform: scale(1.1);
          left: 0px;
          top: 0px;
        }
      }

      &.center img {
        @media screen and (min-width: 967px) {
          vertical-align: middle;
          height: 100%;
          top: 50%;
          -ms-transform: translateY(-50%);
          transform: translateY(-50%);

        }
      }

      &.top img {
        top: 0px;

      }

      &.bottom img {
        bottom: 0px;
      }

      &.left img {
        top: -50%;
        left: 0px;
      }

      &.right img {
        top: -50%;
        right: 0px;
      }

    }

    &__dimming {
      display: block;
      position: absolute;
      line-height: 0;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      z-index: 3;
      /*background: rgba($black, .25);*/
      /*background: linear-gradient(rgba($black, .15), rgba($black, .3)); */
      background: linear-gradient(0deg, rgba(0, 38, 99, 1) 0%, rgba(0, 38, 99, 0) 90%);

    }
  }

}

.hero {
  &.page {
    &.hero--has-background:before {
      padding-bottom: 0;
    }

    &.hero--has-background .hero__container {
      @include wrap;
      max-width: 91.07rem;
      height: 100%;
      padding-bottom: 3rem;
      height: 480px;
      display: flex;
      align-items: flex-end;
      justify-content: flex-start;

      /* mobile vertical */
      @media screen and (max-width: 780px) and (max-height: 1280px) {
        height: 520px;
        height: auto;
        padding-top: 2rem;
      }

      /* mobile vertical long screen */
      @media screen and (max-width: 780px) and (min-height: 1280px) {
        height: 880px;
        height: auto;
        padding-top: 2rem;
      }

      /* mobile horizontal */
      @media screen and (min-width: 780px) and (max-height: 680px) {
        height: 280px;
        height: auto;
        padding-top: 2rem;
      }

      /* ipad mini vertical */
      @media screen and (min-width: 700px) and (max-width: 800px) and (min-height: 880px) {
        height: 420px;
        height: auto;
        padding-top: 2rem;
      }

      @include breakpoint($l) {
        align-items: center;
        height: calc(60vh - 113px);
        min-height: 420px;
        padding-left: 6rem;
        justify-content: flex-start;
      }

      &--wrap {
        position: relative;
        max-width: 400px;
        text-align: left;
        z-index: 20;
        color: white;

        .hero__title {
          z-index: 20;
          font-size: 38px;
          margin-bottom: 3rem;

          @include breakpoint($l) {
            margin-bottom: 2rem;
          }

          @include breakpoint($sub-m) {
            font-size:30px;
          }
        }

        a {
          @include button;
          background-color: $brand;
          border: 1px solid $brand;
          color: white;
          margin-top: 1.5rem;
          width: fit-content;
          font-size: 1rem;
          display: block;
          text-align: center;

          &:hover {
            background-color: transparent;
            color: white;
            border: 1px solid $brand;
          }

          &.external {
            background-color: #002663;
            border: 1px solid #002663;
            color: white;
            padding: 0.75rem 4.5rem 0.75rem 3rem;

            &:after {
              position: absolute;
              background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23ffffff' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
              background-position: center;
              background-size: 84%;
              width: 1.1rem;
              height: 1rem;
              display: inline-block;
              content: " ";
              margin-left: 8px;
              color: transparent;
              background-repeat: no-repeat;
            }

            &:hover {
              background-color: white;
              color: #002663;

              &:after {
                background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23002663' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
              }
            }
          }

          &:hover {
            background-color: white;
            color: $primary;
          }
        }
      }
    }
  }

  &.hero--has-background.page-front {
    .hero__container {
      //height: calc(100vh - 175px);
      max-width: 110rem;

      @include breakpoint($l) {
        //height: calc(100vh - 112px);
      }
    }
  }
}

/* page */
.hero.page:not(.page-front).hero--has-background .hero__container {

  /* mobile vertical */
  // @media screen and (max-width: 780px) and (max-height: 1280px) {
  //   // height: 240px;
  //   height: auto;
  //   padding-top: 10rem;
  // }

  /* mobile vertical long screen */
  @media screen and (max-width: 780px) and (min-height: 1280px) {
    // height: 240px;
    height: auto;
    padding-top: 10rem;
  }
}


.Koulutus .hero--has-background.page-front {
  .hero__container {
    height: calc(100vh - 175px);
    max-width: 110rem;

    @include breakpoint($l) {
      height: calc(100vh - 112px);
    }
  }
}

.single .hero {
  @include wrap;
  @include narrow-content;
  margin-top: $gap;

  .hero__container {
    height: calc(100vh - 112px);

    @include breakpoint($l) {
      height: calc(60vh - 112px);
    }
  }
}

// HERO SIDE
.hero_side {
  background: url(../../dist/images/kaaritausta.png);
  z-index: 12;
  height: 100%;
  color: white;
  width: 60%;
  right: -3%;
  position: absolute;
  background-size: cover;
  background-position: 14% center;
  display: none;

  @include breakpoint($l) {
    display: block;
  }

  &_wrap {
    width: 50%;
    float: right;
    padding-top: 7vh;

    img {
      //height: 150px;
      width: 80%;
      max-width: 500px;
    }
  }

  &__content {
    color: $primary;
    margin-top: 5vh;
    width: 300px;

    p {
      color: $brand;
      text-shadow: none;
      font-size: 1.2rem;
    }
  }

  a {
    @include button;
    background-color: $brand;
    border: 1px solid $brand;
    color: white;
    margin-top: 3.5rem;
    width: fit-content;
    display: block;
    text-align: center;

    &:hover {
      background-color: transparent;
      color: white;
      border: 1px solid $brand;
    }

    &.external {
      background-color: #002663;
      border: 1px solid #002663;
      color: white;
      padding: 0.75rem 4.5rem 0.75rem 3rem;

      &:after {
        position: absolute;
        background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23ffffff' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-position: center;
        background-size: 84%;
        width: 1.1rem;
        height: 1rem;
        display: inline-block;
        content: " ";
        margin-left: 8px;
        color: transparent;
        background-repeat: no-repeat;
      }

      &:hover {
        background-color: white;
        color: #002663;

        &:after {
          background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23002663' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
      }
    }

    &:hover {
      background-color: white;
      color: $primary;
    }
  }
}


// HERO SIDE
.hero_side_after {
  @include wrap;
  background-color: #fff4fa;
  padding: 3rem 2rem;
  display: block;
  width: 100%;
  text-align: center;

  .hero_side__content {
    margin: 2rem auto;
    font-size: 1.2rem;
  }

  img {
    padding-top: 2.5rem;
  }

  a {
    @include button;
    background-color: $brand;
    border: 1px solid $brand;
    color: white;
    margin: 1.5rem auto;
    width: fit-content;
    display: block;
    text-align: center;

    &:hover {
      background-color: transparent;
      color: white;
      border: 1px solid $brand;
    }

    &.external {
      background-color: #002663;
      border: 1px solid #002663;
      color: white;
      padding: 0.75rem 4.5rem 0.75rem 3rem;

      &:after {
        position: absolute;
        background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23ffffff' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-position: center;
        background-size: 84%;
        width: 1.1rem;
        height: 1rem;
        display: inline-block;
        content: " ";
        margin-left: 8px;
        color: transparent;
        background-repeat: no-repeat;
      }

      &:hover {
        background-color: white;
        color: #002663;

        &:after {
          background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23002663' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
      }
    }

    &:hover {
      background-color: white;
      color: $primary;
    }
  }

  @include breakpoint($l) {
    display: none;
  }
}

body.Kauppakamari {
  .hero--has-background.page-front {
    .hero__container {
      height: calc(100vh - 175px);
      max-width: 110rem;

      @include breakpoint($l) {
        height: calc(60vh - 112px);
      }
    }
  }
}


/*overwrite for ipad*/
@media screen and (min-width: 980px) and (max-width: 1025px) and (min-height: 800px) {
  .hero:not(.hero--search) {

    .hero__container,
    .hero.page.hero--has-background .hero__container {
      height: calc(40vh);
    }
  }
}


.hero.hero-archive {
  align-items: normal;

  .hero__container {
    margin-left: 0px;
    margin-right: auto;
    color: #fff;

    &--wrap {
      text-align: left;

      .hero__title {
        color: #fff;
      }
    }
  }
}


.hero-only-breadcrumb {
  padding-top: 1px;
  .breadcrumb-container {
    margin-top: 10px;
    margin-bottom: 0px;
    padding: 0 1.5rem;
    margin: auto;
    p {
      margin-bottom: 0px;
      margin-left: 0px;
    }
  }
}
