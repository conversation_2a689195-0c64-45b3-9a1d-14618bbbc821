<?php
/**
 * Template part: Introduction
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package kauppakamari
 */

// $class = array();

/*if (is_singular() && has_post_thumbnail()) {
  $image = kauppakamari_get_image(get_post_thumbnail_id(), 'hero', ['lazyload' => 'animated']);
}*/
?>

<div id="introduction-home" class="introduction block-introduction">
    <div class="image-container">
        <div class="image-wrapper">
            <?php if ($image = get_field("introduction-image")) {?>
                <img class="introduction__image" src="<?php echo $image['url'];?>" alt="<?php echo $image['alt'];?>">
            <?php } ?>
        </div>
        <img src="<?php echo get_template_directory_uri() . '/dist/images/ko-ratas.svg'; ?>" class="addition-graphic">
    </div>
    <div class="contents">
        <?php if($title = get_field('introduction-title')) : ?>
            <h1 class="introduction__title"><?php echo $title; ?></h1>
        <?php else : ?>
            <h1><?php echo get_the_title(); ?></h1>
        <?php endif; ?>

        <div class="introduction__body">
         <?php if ($content = get_field('introduction-content')) : ?>
            <p><?php echo $content; ?></p>
         <?php endif; ?>
        </div>

        <div class="button-wrapper">
            <?php if ($link = get_field('introduction-link')) : ?>
                <a class="introduction__button" href="<?php echo $link['url']; ?>"><?php echo $link['title']; ?></a>
            <?php endif; ?>
        </div>
    </div>
</div>