{"key": "group_5da1a0765e93a", "title": "Block: Color-teaser", "fields": [{"key": "field_5da1a07668c87", "label": "Nostot", "name": "color-teasers", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_5da1a0766db34", "label": "Nosto", "name": "teaser", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_5da1acd051003", "label": "<PERSON><PERSON><PERSON>", "name": "color", "type": "select", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"green": "Vihreä", "pink": "<PERSON><PERSON>", "orange": "Orans<PERSON>", "lily": "Li<PERSON>", "yellow": "<PERSON><PERSON><PERSON>"}, "default_value": [], "allow_null": 0, "multiple": 0, "ui": 1, "ajax": 0, "return_format": "value", "placeholder": ""}, {"key": "field_5da1a076726d7", "label": "<PERSON><PERSON>", "name": "img", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_5da1a0767301b", "label": "<PERSON><PERSON><PERSON><PERSON>", "name": "title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5da1aaf5e1775", "label": "<PERSON><PERSON>", "name": "name", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5da1a0767365b", "label": "Sisältö", "name": "content", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": "", "new_lines": ""}, {"key": "field_5da1a07673726", "label": "Koulutus/Kokoelma", "name": "product_group", "type": "group", "instructions": "Magentosta noudettava kokoelma tuo<PERSON><PERSON> kate<PERSON>/attribuutin mukaan.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_5da1a07686456", "label": "ID", "name": "id", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5da1a07687ebf", "label": "<PERSON><PERSON><PERSON><PERSON>", "name": "title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5da1a07689378", "label": "<PERSON><PERSON><PERSON>", "name": "content", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "basic", "media_upload": 1, "delay": 0}, {"key": "field_5da1a0768941b", "label": "<PERSON><PERSON><PERSON>", "name": "icon", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_5da1a07689447", "label": "<PERSON><PERSON>", "name": "link", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}]}]}, {"key": "field_5da1afdc04248", "label": "Nosto", "name": "teaser_2", "type": "clone", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "clone": ["field_5da1a0766db34"], "display": "seamless", "layout": "block", "prefix_label": 0, "prefix_name": 1}, {"key": "field_5da1b00804249", "label": "Nosto", "name": "teaser_3", "type": "clone", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "clone": ["field_5da1a0766db34"], "display": "seamless", "layout": "block", "prefix_label": 0, "prefix_name": 1}]}], "location": [[{"param": "block", "operator": "==", "value": "acf/color-teasers"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": 1, "description": "", "modified": 1570877309}