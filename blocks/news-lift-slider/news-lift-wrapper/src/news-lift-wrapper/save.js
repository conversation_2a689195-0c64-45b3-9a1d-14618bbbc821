import { InnerBlocks } from "@wordpress/block-editor";

export default function save({ attributes }) {
	const { title, linkText, linkUrl, backgroundColor } = attributes;
	const DEFAULT_BACKGROUND = "#fff";

	return (
		<div
			className="news-lift-slider-wrapper alignfull"
			style={{
				backgroundColor: backgroundColor || DEFAULT_BACKGROUND,
				paddingTop: "var(--wp--preset--spacing--50)",
				paddingBottom: "var(--wp--preset--spacing--50)",
			}}
		>
			<div className="alignwide">
				<div className="news-lift-heading">
					<h2 className="wp-block-heading has-main-color has-text-color has-link-color">
						{title}
					</h2>
					{linkText && linkUrl && (
						<p
							className="has-main-color has-text-color has-link-color"
							style={{ textTransform: "uppercase" }}
						>
							<a href={linkUrl}>{linkText}</a>
						</p>
					)}
				</div>
				<div className="news-lift-slider">
					<InnerBlocks.Content />
				</div>
			</div>
		</div>
	);
}
