/* ==========================================================================
 # Mixins for different custom inputs
========================================================================== */

@mixin textinput {
    height: 48px;
    padding: 10px 10px;
    border: 2px solid $light-blue;
    font-weight: $ws-regular;
    letter-spacing: 0.2px;
    &:focus {
        outline: 2px solid $link-use;
        outline-offset: -2px;
        border-radius: 0%;
    }
    &::placeholder {
        letter-spacing: 0.2px;
        font-weight: $ws-regular;
        color: $gray-text;
    }
}


@mixin checkbox { // Requires input to be inside a label
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    outline: none;
    margin: auto;
    width: 20px;
    min-width: 20px;
    height: 20px;
    background: #FFF;
    border:2px solid $light-blue;
    position: relative;
    cursor: pointer;

    &::before {
        content: "\f00c";
        cursor: pointer;
        position: absolute;
        display: block;
        inset: 0;
        font-family: 'Font Awesome 6 Sharp';
        font-weight: 900;
        text-align: center;
        opacity: 0;
    }
    &:checked::before {
        opacity: 1;
    }
    &:focus, &:hover {
        outline: 1px solid $light-blue;
    }
}