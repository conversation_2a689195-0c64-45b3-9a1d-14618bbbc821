/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./src/rss-feed/style.scss ***!
  \************************************************************************************************************************************************************************************************************************************************/
/**
 * The following styles get applied both on the front of your site
 * and in the editor.
 *
 * Replace them with your own styles or remove the file completely.
 */
.rss-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 3rem 2.25rem;
  padding-top: var(--wp--preset--spacing--70);
  padding-bottom: var(--wp--preset--spacing--70);
  max-width: 85.07rem;
  margin: auto;
}

.rss-wrapper h3 {
  line-height: 28px !important;
  font-weight: 600 !important;
  margin: 0 !important;
}

.rss-wrapper h3 a {
  font-size: 22px !important;
  color: #002663 !important;
  text-decoration: none;
}

.rss-wrapper h3 a:hover {
  text-decoration: underline;
}

.meta {
  font-size: 16px !important;
  line-height: 24px !important;
  color: #212121 !important;
  margin-top: 0 !important;
  margin-bottom: 0.5rem !important;
}

.separator {
  margin-right: 7px;
  margin-left: 7px;
}

.rss-hr {
  margin-top: 0rem !important;
  margin-bottom: 2.5rem !important;
  margin-right: 0;
  margin-left: 0;
}

@media (min-width: 601px) and (max-width: 1060px) {
  .rss-wrapper {
    grid-template-columns: 1fr 1fr;
  }
}
@media (max-width: 600px) {
  .rss-wrapper {
    display: block !important;
    padding-top: var(--wp--preset--spacing--60);
    padding-bottom: var(--wp--preset--spacing--20);
  }
  .rss-wrapper .wp-block-column {
    margin-bottom: var(--wp--preset--spacing--50);
  }
  .meta {
    margin-bottom: 0rem !important;
  }
  .rss-wrapper h3 {
    line-height: 22px !important;
  }
  .rss-wrapper h3 a {
    font-size: 18px !important;
  }
  .rss-hr {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }
}
