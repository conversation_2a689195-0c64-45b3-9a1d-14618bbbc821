/* Highlight-1*/

.highlight-info {

	opacity: 0;
	// mobile = flex, reverse column
	display: flex;
	flex-direction: column-reverse;
	&.background-lightblue {
		background: $light-blue-25;
	}
	&.background-coral {
		background: $coral-25;
	}
	&.background-white {
		background: transparent;
		border: 2px solid $light-blue-25;
	}
	&__title {
		font-size: 22px;
		font-weight: $ws-semibold;
		color: $primary;
	}
	&__body {
		margin: 0;
		color: $primary;
		* {
			// font-size: 18px !important;
		}
		ul {
			padding: 0;
		}
		.is-front-page & {
			p {
				//font-size: 18px !important;
			}
		}
	}
	&__content {
		position: relative;
		padding: 2rem;
		padding-top: 4rem;
		display: grid;
		grid-template-rows: auto auto auto;
		align-content: center;
	}
	&__image-wrapper {
		position: relative;
		background-size: cover;
		background-position: center;
		min-height: 300px;
	}
	.addition-graphic {
		position: absolute;
		width: 92px;
		right: 16px;
		top: -45px;
		&.is-hidden {
			display: none !important
		}
	}
	/*& .listed-links {
		li {
			margin: 0;
		}
		.inner-link {
			@include textlink;
			font-weight: $ws-medium;
			text-decoration: underline;
			&:after {
				content:''; // disable after on bullet links
			}
		}
	}*/
	& .button-wrapper {
		margin-top: 1rem;
	}
	&__button {
		&.link_text {
			@include textlink;
			&:after {
				top: 1px;
			}
		}
        &.link_button {
			@include newbutton;
			padding-left: 1.5rem;
			padding-right: 1.5rem; 
		}
    }
}
@media (min-width: $m) {
	.highlight-info {
		display: grid;
		&__title {
			font-size: 28px;
		}
		&.img-left { // if image is on left
			grid-template-columns: 1fr 1.5fr;
			& .highlight-info__content {
				grid-row: 1 / 1;
				grid-column: 2 / 2;
			}
			& .highlight-info__image-wrapper {
				grid-row: 1 / 1;
				grid-column: 1 / 1;
				//min-height: 400px;
			}
			& .addition-graphic {
				display: block;
				position: absolute;
				top: -64px;
				width: 120px;
				left: -50px;
				&.is-hidden {
					display: none !important;
				}
			}
		}

		// image on right as default
		// grid-template-columns: 1.5fr 1fr;
		&__content {
			grid-row: 1 / 1;
			grid-column: 1 / 1;
		}
		&__image-wrapper {
			grid-row: 1 / 1;
			grid-column: 2 / 2;
			// min-height: 400px;
		}	
		& .addition-graphic {
			display: block;
			position: absolute;
			top: -56px;
			right: -70px;
			width: 120px;
			&.is-hidden {
				display: none !important;
			}
		}
	}
}

@media screen and (min-width: $l) {
	.highlight-info {
		margin: 3rem auto !important;
		.is-front-page & {
			margin: 8rem auto !important;
		}
		display: grid;
		// grid-template-columns: 1fr 1fr !important;

		&__content {
			padding: 3.5rem;
		}
	}
}

.page-template-default {
	.highlight-info {
		grid-template-columns: 2fr 1fr !important;
	}
}