<?php

add_action('init', function() {
  register_post_type('release', [
    'label' => 'Tiedotteet',
    'public' => true,
    'show_in_rest'          => true,
    'capability_type' => 'post',
    'taxonomies' => array('post_tag','category', 'article_type'),
    'supports'              => array( 'title', 'editor', 'thumbnail', 'page-attributes' ),
  ]);
  }
);

//debug, otettu pois haun kehityksessä, ei pitäsi enää olla tarpeellinen

//add_action('pre_get_posts', 'query_post_type');

function kk_is_blog () {
  $post_type = get_post_type( $post );
  return ( $post_type === 'post' ) && ( is_home() || is_archive() || is_single() );
}

function query_post_type($query) {
  global $typenow;
  if( in_array($typenow, array('post','release')) && is_admin() ){
    return;
  }
  if ( is_archive() && (is_category() || is_tag()) && ( $query->is_main_query() ) ||  is_home() || kk_is_blog() &&  $query->is_main_query()) {
    $query->set( 'post_type', array('post', 'release') );
  }
}

function generate_featured_image( $file, $post_id, $desc ){

  require_once(ABSPATH . 'wp-admin' . '/includes/image.php');
  require_once(ABSPATH . 'wp-admin' . '/includes/file.php');
  require_once(ABSPATH . 'wp-admin' . '/includes/media.php');

  // Set variables for storage, fix file filename for query strings.
  preg_match( '/[^\?]+\.(jpe?g|jpe|gif|png)\b/i', $file, $matches );
  if ( ! $matches ) {
       return new WP_Error( 'image_sideload_failed', __( 'Invalid image URL' ) );
  }

  $file_array = array();
  $file_array['name'] = basename( $matches[0] );

  // Download file to temp location.
  $file_array['tmp_name'] = download_url( $file );

  // If error storing temporarily, return the error.
  if ( is_wp_error( $file_array['tmp_name'] ) ) {
      return $file_array['tmp_name'];
  }

  // Do the validation and storage stuff.
  $id = media_handle_sideload( $file_array, $post_id, $desc );

  // If error storing permanently, unlink.
  if ( is_wp_error( $id ) ) {
      @unlink( $file_array['tmp_name'] );
      return $id;
  }
  return set_post_thumbnail( $post_id, $id );

}

function kauppakamari_get_releases_from_stt() {

  $response = wp_remote_get('https://www.sttinfo.fi/json/v2/releases?publisher=26487429&channels=68491897');
  $releases = json_decode( wp_remote_retrieve_body( $response ), true );

  if(isset($releases['releases']) && (is_array($releases['releases']) || is_object($releases['releases']))) {

    foreach ($releases['releases'] as $release) {

      //vd($release);

      $existing_releases = get_page_by_path(sanitize_title($release['title']), OBJECT, 'release');

      if ($existing_releases === null) {

         $post_id = wp_insert_post([
          'post_title' => $release['title'],
          'post_slug' => sanitize_title($release['title']),
          'post_type' => 'release',
          'post_excerpt' => $release['leadtext'],
          'post_content' => $release['body'] . "\n " . $release['contacts']['text'],
          'post_date' => $release['published'],
          'post_status' => 'publish',
         ]);

        wp_set_object_terms( $post_id, 'tiedotteet', 'article_type' );
        //wp_set_object_terms( $post_id, 'tiedote', 'category' ); //no category in 2022
        pll_set_post_language( $post_id, 'fi' );

        update_field('field_5e13589ca4867', $release['leadtext'], $post_id);

        if (!empty($release['mainImage']['url'])) {
          generate_featured_image($release['mainImage']['url'], $post_id, $release['mainImage']['caption']);
        } else {
          set_post_thumbnail($post_id, 7279);
        }
      }
    }
  }
}

if ( ! wp_next_scheduled('kauppakamari_update_releases_list')) {
  wp_schedule_event(time(), '5min', 'kauppakamari_update_releases_list');
}

add_action('kauppakamari_update_releases_list', 'kauppakamari_get_releases_from_stt');

//add_action('init', 'kauppakamari_get_releases_from_stt');
