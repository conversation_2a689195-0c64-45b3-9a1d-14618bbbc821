.block-jobs {
    &.primary-background {
        background: $light-blue-25; 
        .job-link {
            &:hover, &:focus {
                .job-item{border: 4px solid $light-blue-50;}
            }
        }
    }
    &.coral-background {
        background: $coral-25; 
        .job-link {
            &:hover, &:focus {
                .job-item{border: 4px solid $coral-50;}
            }
        }
    }

    &__apply {
        max-width: 75%;
        margin: auto;
        padding: 0 10%;
        text-align: center;

        .apply-btn {
            @include button;
            padding-left: 2rem;
            padding-right: 2rem;
            margin: auto;
        }
    }

    &__inner-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin: auto;
        max-width: 85.07rem;
        padding: 3rem 1.5rem;
    }

    &__list {
        width: 100%;
    }
    
    .job-link { // override styles caused by <a> wrap
        color: $primary !important;
        text-decoration: none !important;

        &:hover, &:focus {
            .job-title {
                @include elementhover;
            }
        }
    }
    
    .job-item {
        background: $white;
        display: flex;
        justify-content: space-between;
        gap: 1rem;
        margin: 2.5rem 0;
        padding: 2.5rem 1.5rem;
        transition: border 0.25s;
        border: 4px solid $white;

        &__img {
            flex-basis: 25%;
            max-width: 150px;
            margin: 0 2rem;

            img {
                object-fit: contain;
                object-position: 0 15%;
                aspect-ratio: 1 / 1;
                vertical-align: middle;
            }
        }

        &__body {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex-basis: 100%;

            .job-link {
                color: $primary;
                text-decoration: none;
                &:hover, &:focus {
                    text-decoration: underline;
                }
            }

            .job-title, .job-description {
                margin: 0;
            }

            .job-description {
                margin: 0.5rem 0 1rem;
            }

            .job-categories {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;

                .category-pill {
                    border-radius: 5rem;
                    display: inline-block;
                    font-size: 12px;
                    font-weight: 600;
                    text-transform: uppercase;
                    padding: 0.25rem 1rem;
                    text-decoration: none;
                    vertical-align: baseline;
                    background-color: #d2e4f3;
                    color: #002662;
                }
            }
        }

        &__dates {
            flex-basis: 25%;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            text-align: end;
            white-space: nowrap;
            gap: 0.75rem;

            span {
                font-size: 16px;
                margin-bottom: 0;
            }
        }
    }

    @include breakpoint($sub-xm) { // mobile
        &__apply {
            max-width: 100%;
            padding: 0;
        }

        .job-item {
            flex-direction: column;

            &__img {
                max-width: 175px;
                margin: 0;
                img {
                    aspect-ratio: 2 / 1;
                }
            }

            &__body {
                margin: 0.75rem 0;

                .job-description {
                    margin: 1rem 0;
                }
            }
            &__dates {
                text-align: start;
            }
        }
    }
}