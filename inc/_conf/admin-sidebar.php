<?php

// Then, reorder the remaining menu items
function reorder_admin_menu($menu_order)
{
    if (!$menu_order) return true;

    return array(
        // Dashboard
        'index.php',

        // Media
        'upload.php',

        // Sivulistaus
        'edit.php?post_type=page',

        // Jakaja 1
        'separator1',

        // Artikkelit ja CPT:t
        'edit.php',
        'edit.php?post_type=release',
        'edit.php?post_type=event',
        'edit.php?post_type=event-presentation',
        'edit.php?post_type=people',
        'edit.php?post_type=k3_person_gallery',
        'edit.php?post_type=job',

        // Jakaja 2
        'separator2',

        // Asetussivut
        'admin.php?page=general-settings',
        'admin.php?page=acf-options-tavallinen',

        // Ylläpitovalikot
        'themes.php',
        'plugins.php',
        'users.php',
        'tools.php',
        'options-general.php',

        // Jakaja 3
        'separator3',

        // Lisäosat
        'edit.php?post_type=acf-field-group',
        'admin.php?page=sbi-feed-builder',
        'admin.php?page=gf_edit_forms',
        'admin.php?page=leadin',
        'admin.php?page=es_dashboard',
        'admin.php?page=mlang',
        'admin.php?page=wpseo_dashboard',
        'admin.php?page=wp_stream',
        'admin.php?page=googlesitekit-splash',
    );
}

add_action('admin_menu', function () {
    add_filter('custom_menu_order', '__return_true');
    add_filter('menu_order', 'reorder_admin_menu');
}, 999);

// Add dividers to the admin menu
function add_admin_menu_dividers()
{
    global $menu;

    $menu[] = array('', 'read', 'separator1', '', 'wp-menu-separator');
    $menu[] = array('', 'read', 'separator2', '', 'wp-menu-separator');
    $menu[] = array('', 'read', 'separator3', '', 'wp-menu-separator');

    // Add custom styling for the dividers
    add_action('admin_head', function () {
        echo '<style>
            #adminmenu li.wp-menu-separator {
                background: #32373c;
                border-top: 1px solid #555;
                height: 5px;
                margin: 5px 0;
            }
        </style>';
    });
}
add_action('admin_menu', 'add_admin_menu_dividers', 999);
