<?php

$labels = array(
    'name'                  => _x('Tapahtumat/koulutukset', 'Post Type General Name', 'kauppakamari'),
    'singular_name'         => _x('Tapahtuma/koulutus', 'Post Type Singular Name', 'kauppakamari'),
    'menu_name'             => __('Tapahtumat/koulutukset', 'kauppakamari'),
    'name_admin_bar'        => __('Tapahtumat/koulutukset', 'kauppakamari'),
    'archives'              => __('Item Archives', 'kauppakamari'),
    'attributes'            => __('Item Attributes', 'kauppakamari'),
    'parent_item_colon'     => __('Parent Item:', 'kauppakamari'),
    'all_items'             => __('Kaikki', 'kauppakamari'),
    'add_new_item'          => __('-', 'kauppakamari'),
    'add_new'               => __('-', 'kauppakamari'),
    'new_item'              => __('Uusi', 'kauppakamari'),
    'edit_item'             => __('Muokkaa', 'kauppakamari'),
    'update_item'           => __('Päivitä', 'kauppakamari'),
    'view_item'             => __('Katso', 'kauppakamari'),
    'view_items'            => __('Katso', 'kauppakamari'),
    'search_items'          => __('Etsi', 'kauppakamari'),
    'not_found'             => __('Ei löytynyt', 'kauppakamari'),
    'not_found_in_trash'    => __('Not found in Trash', 'kauppakamari'),
    'featured_image'        => __('Featured Image', 'kauppakamari'),
    'set_featured_image'    => __('Set featured image', 'kauppakamari'),
    'remove_featured_image' => __('Remove featured image', 'kauppakamari'),
    'use_featured_image'    => __('Use as featured image', 'kauppakamari'),
    'insert_into_item'      => __('Insert into item', 'kauppakamari'),
    'uploaded_to_this_item' => __('Uploaded to this item', 'kauppakamari'),
    'items_list'            => __('Items list', 'kauppakamari'),
    'items_list_navigation' => __('Items list navigation', 'kauppakamari'),
    'filter_items_list'     => __('Filter items list', 'kauppakamari'),
);

$rewrite = array(
    'slug'                  => 'tapahtuma',
    'with_front'            => false,
    'pages'                 => true,
    'feeds'                 => true,
);

$args = array(
    'label'                 => __('Tapahtuma/koulutus', 'kauppakamari'),
    'description'           => __('Post Type Description', 'kauppakamari'),
    'labels'                => $labels,
    'supports'              => array( 'title', 'editor', 'thumbnail', 'page-attributes' ),
    'taxonomies'            => array( ),
    'hierarchical'          => true,
    'public'                => true,
    'show_ui'               => true,
    'show_in_menu'          => true,
    'menu_position'         => 5,
    'menu_icon'             => 'dashicons-media-text',
    'show_in_admin_bar'     => true,
    'show_in_nav_menus'     => true,
    'can_export'            => true,
    'has_archive'           => false,
    'exclude_from_search'   => true,
    'publicly_queryable'    => true,
    'rewrite'               => $rewrite,
    'capability_type'       => 'page',
    'show_in_rest'          => true,
);

register_post_type('event', $args);

// CUSTOM TAXONOMY

//create a custom taxonomy name it "type" for your posts
function create_event_taxonomy() {
    //Events categories
    $labels = array(
        'name' => _x( 'Tapahtumakategoriat', 'taxonomy general name' ),
        'singular_name' => _x( 'Tapahtumakategoria', 'taxonomy singular name' ),
        'search_items' =>  __( 'Etsi Tapahtumakategorioista' ),
        'all_items' => __( 'Kaikki Tapahtumakategoriat' ),
        'parent_item' => __( 'Isäntä' ),
        'parent_item_colon' => __( 'Isäntä:' ),
        'edit_item' => __( 'Muokkaa' ), 
        'update_item' => __( 'Päivitä' ),
        'add_new_item' => __( 'Lisää uusi' ),
        'new_item_name' => __( 'Uuden nimi' ),
        'menu_name' => __( 'Tapahtumakategoriat' ),
    );    

    register_taxonomy('event_category', 'event', array(
        'hierarchical' => true,
        'labels' => $labels,
        'show_ui' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array( 'slug' => 'event-category' ),
    ));

    //Workshop categories
    $labels = array(
        'name' => _x( 'Koulutuskategoriat', 'taxonomy general name' ),
        'singular_name' => _x( 'Koulutuskategoria', 'taxonomy singular name' ),
        'search_items' =>  __( 'Etsi Koulutuskategorioista' ),
        'all_items' => __( 'Kaikki Koulutuskategoriat' ),
        'parent_item' => __( 'Isäntä' ),
        'parent_item_colon' => __( 'Isäntä:' ),
        'edit_item' => __( 'Muokkaa' ), 
        'update_item' => __( 'Päivitä' ),
        'add_new_item' => __( 'Lisää uusi' ),
        'new_item_name' => __( 'Uuden nimi' ),
        'menu_name' => __( 'Koulutuskategoriat' ),
    );

    register_taxonomy('workshop_category', 'event', array(
        'hierarchical' => true,
        'labels' => $labels,
        'show_ui' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array( 'slug' => 'workshop-category' ),
    ));
}

add_action( 'init', 'create_event_taxonomy', 0 );

function validateArray($candidate) {
    return is_array($candidate) && count($candidate) > 0;
}

function after_loop_data($data, $wp_query, $args) {
    // Getting meta fields
    $id = $wp_query->post->ID;
    $meta = array();

    // Getting event type (event / workshop)
    $eventType = 'workshop';
    foreach($args['meta_query'] as $query) {
        if (!is_array($query) || !array_key_exists('key', $query) || $query['key'] !== 'event_type') continue;
        $eventType = $query['value'];
        break;
    }

    // Getting categories
    $categories = wp_get_object_terms($id, $eventType . '_category');
    $meta['categories'] = array_map(function ($item) { return $item->name; }, $categories);

    // Getting post meta data
    $method = get_post_meta($id, $eventType . '_method');
    $meta['method'] = validateArray($method) ? $method[0] : null;

    $month = get_post_meta($id, $eventType . '_month');
    $meta['month'] = validateArray($month) ? $month[0] : null;
    
    $start = get_post_meta($id, 'start_date');
    $meta['start_date'] = validateArray($start) ? $start[0] : null;
    
    $end = get_post_meta($id, 'end_date');
    $meta['end_date'] = validateArray($end) ? $end[0] : null;

    // Getting link-handle
    $handle = get_post_meta($id, 'handle');
    $meta['handle'] = validateArray($handle) ? $handle[0] : null;

    // Store meta to own folder in $data (response data)
    $data['meta'][$id] = $meta;

    return $data;
}
add_filter( 'wp_query_route_to_rest_api_after_loop_data', 'after_loop_data', 10, 3 );

// Increase max post size to unlimited
function my_max_posts_per_page($max) {
    return -1; // Default 50
}
// add_filter( 'wp_query_route_to_rest_api_max_posts_per_page', 'my_max_posts_per_page' );

add_action('rest_api_init', 'init_shopify_events');
function init_shopify_events()
{
    register_rest_route('api/v1', '/cron/shopify_eventdata/', array(
        'methods' => 'GET',
        'callback' => 'cron_job_shopify_eventdata_callback',
        'permission_callback' => '__return_true'
    ));

    register_rest_route('api/v1', '/cron/shopify_eventdata_json/', array(
        'methods' => 'GET',
        'callback' => 'cron_job_shopify_eventdata_json_callback',
        'permission_callback' => '__return_true'
    ));

}

function cron_job_shopify_eventdata_callback() {
        $upload_dir_tmp = wp_upload_dir();
        $full_upload_dir_tmp = $upload_dir_tmp['basedir'].'/shopify';

        $workshopsFile = $full_upload_dir_tmp . "/265924051093.json";
        $eventsFile = $full_upload_dir_tmp . "/265924247701.json";

        $eventsData = file_get_contents($eventsFile);
        $eventsData = json_decode($eventsData);

        $workshopsData = file_get_contents($workshopsFile);
        $workshopsData = json_decode($workshopsData);

        $items = [];

        foreach ($workshopsData as $event) {
            $event->type = "workshop";
            $items[] = $event;
        }

        foreach ($eventsData as $event) {
            $event->type = "event";
            $items[] = $event;
        }

        if (count($items) < 25) die("Not enough event data available");

        //remove old events
        $removablePosts = get_posts(array(
            'numberposts'   => -1,
            'post_type'     => 'event',
            'post_status'   => 'any',
        )); 

        foreach ($removablePosts as $removable) {
            wp_delete_post($removable->ID, true);    
        }

        $counter = 0;

        //Old events removed, add new ones
        foreach ($items as $item) {

            $tags = $item->tags;
            $tags = explode(", ", $tags);

            $categories = [];
            $method = "";
            $eventType = "";
            $month = "";

            $inHKI = true;
            $hasOrganization = false;
            $orgDebug = "";

            foreach ($tags as $tag) {
                $tagInfo = explode(":", $tag);

                if (count($tagInfo) == 1) continue;

                $tagKey = $tagInfo[0];
                $tagValue = $tagInfo[1];

                if ($tagKey == "Aihealue") {
                    if ($item->type == "event") {
                        $termInsert = wp_insert_term($tagValue, "event_category");
                    } else {
                        $termInsert = wp_insert_term($tagValue, "workshop_category");
                    }

                    $termID = null;

                    //Term exists already
                    if (is_a($termInsert, "WP_Error") && isset($termInsert->error_data['term_exists'])) {
                        $termID = $termInsert->error_data['term_exists'];
                    }
                    //Term doesn't exists, let's create it 
                    else if (is_array($termInsert) && isset($termInsert['term_id'])) {
                        $termID = $termInsert['term_id'];
                    } 

                    if (!empty($termID)) $categories[] = $termID;
                }

                if ($tagKey == "Organisaatio") {
                    $hasOrganization = true;
                    if (!stristr($tagValue, "Helsingin")) {
                        $inHKI = false;
                    }
                    $orgDebug = $tagValue;
                }

                if ($tagKey == "Toteutusmuoto") {
                    $method = $tagValue;
                }
                
                if ($tagKey == "Tilaisuuden tyyppi") {
                    $eventType = $tagValue;
                }
                
                if ($tagKey == "Ajankohta") {
                    $month = $tagValue;
                }
            }

            //Add event categories
            // if ($item->type == "event") {
            //     foreach ($categories as $cat) {
            //         $catData = wp_insert_term($cat, "event_category");
            //     }
            // } else {
            //     foreach ($categories as $cat) {
            //         $catData = wp_insert_term($cat, "workshop_category");
            //         var_dump($catData);
            //     }
            // }

            if (!$inHKI || !$hasOrganization) {
                continue;
            }

            $item->categories = $categories;
            $item->method = $method;
            $item->event_type = $eventType;
            $item->month = $month;

            $data = array(
                'post_content' => $item->body_html,
                'post_title' => $item->title,
                'post_type' => 'event',
                'post_status' => 'publish', // Make events' status published
                'meta_input' => array(
                    'shopify_id' => $item->id,
                    'event_type' => $item->type,
                    'start_date' => isset($item->alkaa) ? $item->alkaa : null,
                    'end_date' => isset($item->paattyy) ? $item->paattyy : null,
                    ($item->type == "event" ? 'event_method' : 'workshop_method') => $item->method,
                    'categories' => $item->categories,
                    ($item->type == "event" ? 'event_month' : 'workshop_month') => $item->month,
                    'private_or_public' => $item->event_type,
                    'handle' => $item->handle,
                )
            );

            $new = wp_insert_post($data);

            if ($item->type == "event") {
                wp_set_post_terms($new, $item->categories, "event_category");
            } else {
                wp_set_post_terms($new, $item->categories, "workshop_category");
            }

            $counter++;
        }
    }

    function cron_job_shopify_eventdata_json_callback() {
        global $wpdb;

        $monthNames = [
            "tammikuu" => "01",
            "helmikuu" => "02",
            "maaliskuu" => "03",
            "huhtikuu" => "04",
            "toukokuu" => "05",
            "kesäkuu" => "06",
            "heinäkuu" => "07",
            "elokuu" => "08",
            "syyskuu" => "09",
            "lokakuu" => "10",
            "marraskuu" => "11",
            "joulukuu" => "12",
        ];
        //Return all categories of posts with type = "workshop"

        $workshopCategories = get_terms( array(
            'taxonomy' => 'workshop_category',
            'hide_empty' => false,
        ) );

        $eventCategories = get_terms( array(
            'taxonomy' => 'event_category',
            'hide_empty' => false,
        ) );

        $json = [];
        $json['workshop_categories'] = $workshopCategories;
        $json['event_categories'] = $eventCategories;

        //Get all event methods (lähi, etä, hybridi etc)
        $r = $wpdb->get_col( $wpdb->prepare( 
            "
            SELECT DISTINCT pm.meta_value FROM {$wpdb->postmeta} pm
            JOIN {$wpdb->posts} p ON p.ID = pm.post_id
            WHERE pm.meta_key = 'event_method' 
        ") );
        $json['event_methods'] = $r;

        //Get all workshop methods (lähi, etä, hybridi etc)
        $r = $wpdb->get_col( $wpdb->prepare( 
            "
            SELECT DISTINCT pm.meta_value FROM {$wpdb->postmeta} pm
            JOIN {$wpdb->posts} p ON p.ID = pm.post_id
            WHERE pm.meta_key = 'workshop_method' 
        ") );
        $json['workshop_methods'] = $r;

        //Get all event types (private or public)
        $r = $wpdb->get_col( $wpdb->prepare( 
            "
            SELECT DISTINCT pm.meta_value FROM {$wpdb->postmeta} pm
            JOIN {$wpdb->posts} p ON p.ID = pm.post_id
            WHERE pm.meta_key = 'private_or_public' 
        ") );

        $r = array_filter($r);

        $json['private_or_public'] = array_values($r);

        //Get all event months
        $r = $wpdb->get_col( $wpdb->prepare( 
            "
            SELECT DISTINCT pm.meta_value FROM {$wpdb->postmeta} pm
            JOIN {$wpdb->posts} p ON p.ID = pm.post_id
            WHERE pm.meta_key = 'event_month' 
        ") );

        $monthData = [];
        foreach ($r as $row) {
            if (empty($row)) continue;

            $split = explode(" ", $row);
            $tmp = ["title" => $row, "timestamp" => $split[1] . $monthNames[$split[0]]];
            $monthData[] = $tmp;
         }

        usort($monthData, function($a, $b) {
            return $a['timestamp'] > $b['timestamp'];
        });

        $json['event_months'] = $monthData;

        //Get all workshop months
        $r = $wpdb->get_col( $wpdb->prepare( 
            "
            SELECT DISTINCT pm.meta_value FROM {$wpdb->postmeta} pm
            JOIN {$wpdb->posts} p ON p.ID = pm.post_id
            WHERE pm.meta_key = 'workshop_month' 
        ") );
         
        $monthData = [];
        foreach ($r as $row) {
            if (empty($row)) continue;

            $split = explode(" ", $row);
            $tmp = ["title" => $row, "timestamp" => $split[1] . $monthNames[$split[0]]];
            $monthData[] = $tmp;
        }

        usort($monthData, function($a, $b) {
            return $a['timestamp'] > $b['timestamp'];
        });

        $json['workshop_months'] = $monthData;

        return $json;
    }

if (class_exists("WP_CLI")) {
    WP_CLI::add_command( "update_shopify_events", "cron_job_shopify_eventdata_callback" );
    WP_CLI::add_command( "update_shopify_events_json", "cron_job_shopify_eventdata_json_callback" );
}