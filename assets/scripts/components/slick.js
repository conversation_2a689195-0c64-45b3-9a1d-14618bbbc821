jQuery(document).ready(function () {
  jQuery(".product__wrap").slick({
    infinite: true,
    slidesToShow: 4,
    slidesToScroll: 1,
    prevArrow:
      "<img alt='edellinen' class='a-left control-c prev slick-prev' src='/wp-content/themes/kauppakamari/dist/images/chevron_left.png'>",
    nextArrow:
      "<img alt='seuraava' class='a-right control-c next slick-next' src='/wp-content/themes/kauppakamari/dist/images/chevron_right.png'>",
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3,
          infinite: true,
          dots: true,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
      // You can unslick at a given breakpoint now by adding:
      // settings: "unslick"
      // instead of a settings object
    ],
  });
  jQuery(".block-trainer-list").slick({
    infinite: true,
    slidesToShow: 4,
    slidesToScroll: 1,
    prevArrow:
      "<img alt='edellinen' class='a-left control-c prev slick-prev' src='/wp-content/themes/kauppakamari/dist/images/chevron_left.png'>",
    nextArrow:
      "<img alt='seuraava' class='a-right control-c next slick-next' src='/wp-content/themes/kauppakamari/dist/images/chevron_right.png'>",
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3,
          infinite: true,
          dots: true,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
      // You can unslick at a given breakpoint now by adding:
      // settings: "unslick"
      // instead of a settings object
    ],
  });

  //shopify carousel:
  jQuery(".shopify-scroller-inner.carousel-slick").slick({
    dots: false,
    infinite: false,
    speed: 300,
    slidesToShow: 4,
    adaptiveHeight: true,
    autoplay: false,
    prevArrow:
      "<img alt='edellinen' class='a-left control-c prev slick-prev' src='/wp-content/themes/kauppakamari/dist/images/chevron_left_c.png'>",
    nextArrow:
      "<img alt='seuraava' class='a-right control-c next slick-next' src='/wp-content/themes/kauppakamari/dist/images/chevron_right_c.png'>",
    responsive: [
      {
        breakpoint: 900,
        settings: {
          slidesToShow: 2,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          centerPadding: "50px",
        },
      },
    ],
  });

  jQuery(".block-memberstories").slick({
    dots: false,
    infinite: true,
    speed: 300,
    slidesToShow: 1,
    adaptiveHeight: true,
    prevArrow:
      "<img alt='edellinen' class='a-left control-c prev slick-prev' src='/wp-content/themes/kauppakamari/dist/images/chevron_left_c.png'>",
    nextArrow:
      "<img alt='seuraava' class='a-right control-c next slick-next' src='/wp-content/themes/kauppakamari/dist/images/chevron_right_c.png'>",
  });

  jQuery(".block-new_members").slick({
    dots: false,
    centerMode: true,
    centerPadding: "120px",
    slidesToShow: 3,
    infinite: true,
    autoplay: false,
    //adaptiveHeight: true,
    prevArrow:
      "<img alt='edellinen' class='a-left control-c prev slick-prev' src='/wp-content/themes/kauppakamari/dist/images/chevron_left_c.png'>",
    nextArrow:
      "<img alt='seuraava' class='a-right control-c next slick-next' src='/wp-content/themes/kauppakamari/dist/images/chevron_right_c.png'>",
    responsive: [
      {
        breakpoint: 1199,
        settings: {
          slidesToShow: 2,
        },
      },
      {
        breakpoint: 767,
        settings: {
          slidesToShow: 1,
          centerPadding: 0,
        },
      },
    ],
  });
});
