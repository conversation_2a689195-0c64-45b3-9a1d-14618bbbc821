<?php

function kauppakamari_get_products_list($keyword) {
  return null; //not working anymore
//  $response = wp_remote_get('https://kauppakamarikoulutus.koulutusonline.fi/wp-content/products.json');
  $response = wp_remote_get('https://helsinki.chamber.fi/wp-content/products.json');

  $header = $response['headers']; // array of http header lines
  $body = json_decode($response['body']); // use the content

  $selection = null;

  foreach( $body as $item ) {
    if($item->magentoid === $keyword) {
      $selection = $item;
      break;
    }
  }
  if ( is_array( $response ) ) {
    return $selection->products;
  }
}

function kauppakamari_showproducts($keyword) {
  if($products = kauppakamari_get_products_list($keyword)) : ?>
  <div class="product__wrap">
    <?php foreach($products as $product) :
      kauppakamari_get_product_card($product);
    endforeach; ?>
    </div>
  <?php endif;
}

function kauppakamari_get_product_card($product) { ?>
  <div class="product">
    <div class="product--image">
      <img src="<?php echo $product->image; ?>" />
    </div>
    <span class="product--name"><?php echo $product->name; ?></span>
    <a class="product--link" href="<?php echo $product->url; ?>" target="_blank">Lue lisää Kaupassa</a>
  </div>
<?php }
