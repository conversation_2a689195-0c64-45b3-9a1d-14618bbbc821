@media screen and (max-width: $l) {
	.block-accordion {
		padding: 20px;
	}
}

.accordion {
    &__container {
        max-width: 860px;
        margin: 2rem auto;
    }
    &__texts {
        @include breakpoint($m) {
            padding-bottom: 3rem;
        }
        //border-bottom: 1px solid $light-blue-50;
    }
    &__items {
        padding: 3rem 0;
        border-top: 1px solid $light-blue-50;
        border-bottom: 1px solid $light-blue-50;
    }
    &__item {
        background: $light-blue-25;
        margin-bottom: 0.5rem;
        .dropdown-icon {
            height: 20px;
            transition: transform 200ms;
            align-self: center;
        }
        &[open] {
            .dropdown-icon {
                transform: rotate(180deg);
            }
        }
        * {
            color: $primary;
        }
        .item-title {
            padding: 1.5rem 2rem;
            font-size: 18px;
            display: block;
            font-weight: $ws-semibold;
            display: flex;
            justify-content: space-between;
            cursor: pointer;
        }
        &:focus {
            outline: 2px solid $primary;
        }
        .item-content {
            padding: 0.5rem 2rem;
            padding-top: 0;
            p,li,a {
                font-size: 16px !important;
                font-weight: $ws-medium;
                line-height: 24px;
                a {
                    color: $link-use;
                    font-weight: $ws-semibold;
                }
            }
        }
        summary {list-style: none}
		summary::-webkit-details-marker { display: none }
    }
}