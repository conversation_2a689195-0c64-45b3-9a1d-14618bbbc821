<?php

/**
 * Featuredloop used atleast in template-trainers.php and template-experiences.php
 */

function kaup<PERSON><PERSON><PERSON>i_featured_loop($posts) { ?>
      <div class="featured-loop">
        <?php
        $i = 1;
        foreach ($posts as $post) {
          setup_postdata($post); ?>
          <div class="loop_item item_<?php echo $i; ?>" style="background-image:url(<?php echo get_the_post_thumbnail_url($post->ID, 'large') ?>);" >
            <div class="loop_item__content">
              <?php
                if(get_field('title-company', $post->ID)) {
                  echo '<span class="name">' . get_the_title($post->ID) . " &bull; " . get_field('title-company', $post->ID) . '</span>';
                } else {
                  echo '<span class="name">' . get_the_title($post->ID) . '</span>';
                }
                if($terms = get_the_terms( $post->ID, 'training_type' )) :
                  echo '<span class="term">' . $terms[0]->name . '</span>';
                endif;
                echo '<span class="excerpt">' . get_field('description', $post->ID) . '</span>';
              ?>
            </div>
            <div class="dimming dimming__bottom_l"></div>
            <a href="<?php echo get_permalink($post->ID); ?>"></a>
          </div>
          <?php $i++;
          if($i === 10) {
            break;
          }
        } ?>
      </div>
<?php }
