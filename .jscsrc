{"requireCurlyBraces": ["if", "else", "for", "while", "do", "try", "catch"], "requireOperatorBeforeLineBreak": true, "requireCamelCaseOrUpperCaseIdentifiers": true, "maximumLineLength": {"value": 80, "allowComments": true, "allowRegex": true}, "validateIndentation": 2, "validateQuoteMarks": "'", "disallowMultipleLineStrings": true, "disallowMixedSpacesAndTabs": true, "disallowTrailingWhitespace": true, "disallowSpaceAfterPrefixUnaryOperators": true, "disallowMultipleVarDecl": true, "disallowKeywordsOnNewLine": ["else"], "requireSpaceAfterKeywords": ["if", "else", "for", "while", "do", "switch", "return", "try", "catch"], "requireSpaceBeforeBinaryOperators": ["=", "+=", "-=", "*=", "/=", "%=", "<<=", ">>=", ">>>=", "&=", "|=", "^=", "+=", "+", "-", "*", "/", "%", "<<", ">>", ">>>", "&", "|", "^", "&&", "||", "===", "==", ">=", "<=", "<", ">", "!=", "!=="], "requireSpaceAfterBinaryOperators": true, "requireSpacesInConditionalExpression": true, "requireSpaceBeforeBlockStatements": true, "requireSpacesInForStatement": true, "requireLineFeedAtFileEnd": true, "requireSpacesInFunctionExpression": {"beforeOpeningCurlyBrace": true}, "disallowSpacesInAnonymousFunctionExpression": {"beforeOpeningRoundBrace": true}, "disallowSpacesInsideObjectBrackets": "all", "disallowSpacesInsideArrayBrackets": "all", "disallowSpacesInsideParentheses": true, "jsDoc": {"checkParamNames": true, "requireParamTypes": true}, "disallowMultipleLineBreaks": true, "disallowNewlineBeforeBlockStatements": true}