export const monthIndex = {
  tammikuu: "01",
  helmikuu: "02",
  maaliskuu: "03",
  huh<PERSON><PERSON><PERSON>: "04",
  toukokuu: "05",
  kes<PERSON><PERSON><PERSON>: "06",
  hein<PERSON><PERSON><PERSON>: "07",
  elokuu: "08",
  syyskuu: "09",
  lokakuu: "10",
  marraskuu: "11",
  jouluk<PERSON>u: "12",
};

export const dayIndex = {
  0: "Sunnuntai",
  1: "Maananta<PERSON>",
  2: "Tiista<PERSON>",
  3: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  4: "<PERSON><PERSON><PERSON>",
  5: "<PERSON><PERSON><PERSON>",
  6: "<PERSON><PERSON><PERSON>",
};

const dateNow = new Date();
const dateLimit = new Date();
const dateStart = new Date();
dateLimit.setMonth(dateNow.getMonth() + 6);
dateStart.setMonth(dateNow.getMonth() + 1);

const parseDateString = (date) =>
  date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate();

const limitMonthStr = String(dateLimit.getMonth());
const startMonthStr = String(dateStart.getMonth());
console.log("limit month string", limitMonthStr);
const limitMonthTwoDigit =
  limitMonthStr.length === 1 ? "0" + limitMonthStr : limitMonthStr;
const startMonthTwoDigit =
  startMonthStr.length === 1 ? "0" + startMonthStr : startMonthStr;

export const dates = {
  now: parseDateString(dateNow),
  limit: parseDateString(dateLimit),
  timeStampLimit: dateLimit.getFullYear() + limitMonthTwoDigit,
  timeStampStart: dateStart.getFullYear() + startMonthTwoDigit,
};
