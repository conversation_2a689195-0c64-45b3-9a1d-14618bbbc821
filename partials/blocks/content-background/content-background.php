<?php

/**
 * Articleteaser
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-content-background-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-content-background';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$content = get_field('article');

?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">

  <div class="block-content-background--wrap">

    <span class="block-content-background--title h2"><?php the_field('title'); ?></span>

    <div class="block-content-background--content">
      <?php the_field('content'); ?>
    </div>

    <div class="block-content-background--list">

    <?php
    if( have_rows('list') ):
      $i = 1;
      // loop through the rows of data
       while ( have_rows('list') ) : the_row(); ?>
        <div class="block-content-background--listitem">
          <h3><span><?php echo $i; ?>. </span><?php the_sub_field('title'); ?></h3>
          <?php the_sub_field('content'); ?>
        </div>
       <?php
       $i++;
       endwhile; ?>
      <div class="block-content-background--listitem hidden"></div>
   <?php else :
   endif;?>
    </div>

  </div>

</div>
