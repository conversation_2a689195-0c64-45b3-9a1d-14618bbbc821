<?php
/**
 * small lift 2 items
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'accordion-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'accordion';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$accordion_items = get_field("items");

?>

<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">
    <div class="accordion__container">
        <div class="accordion__texts">
        <?php if(get_field('title')) { ?>
            <h2 class="accordion__title"><?php echo get_field('title'); ?></h2>
        <?php } ?>
        <?php if(get_field('subtitle')) {?>
            <span class="accordion__subtitle">
                <?php echo get_field('subtitle'); ?>
            </span>
        <?php } ?>
        </div>
        <div class="accordion__items">
            <?php foreach($accordion_items as $index => $item) { ?>
                <details class="accordion__item" <?= get_field('open_first') && $index == array_key_first($accordion_items) ? "open" : "" ?>>
                    <summary class="item-title">
                        <?php echo $item['label']; ?>
                        <span class="fa-sharp fa-solid fa-chevron-down dropdown-icon"></span>
                    </summary>
                    <div class="item-content">
                        <?php echo $item['content']; ?>
                    </div>
                </details>
            <?php } ?>
        </div>
    </div>
</div>