<?php
/**
 * events calendar frontpage
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'event-calendar-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'event-calendar';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
// $item = get_field();

?>

<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>" data-amount="<?php echo get_field('events_amount_hidden'); ?>">

    <?php if(get_field('title')) {
        echo '<h2 class="event-calendar__title">' . get_field('title') . '</h2>';
    } ?>

    <div class="event-calendar__container">
        <div class="addition-image-container">
            <img alt="" class="addition_1" src="<?php echo get_template_directory_uri() . '/dist/images/calendar_1.svg'; ?>"/>
            <img alt="" class="addition_2" src="<?php echo get_template_directory_uri() . '/dist/images/calendar_2.svg'; ?>"/>
            <img alt="" class="addition_3" src="<?php echo get_template_directory_uri() . '/dist/images/calendar_3.svg'; ?>"/>
            <img alt="" class="addition_4" src="<?php echo get_template_directory_uri() . '/dist/images/calendar_4.svg'; ?>"/>
            <img alt="" class="addition_5" src="<?php echo get_template_directory_uri() . '/dist/images/calendar_5.svg'; ?>"/>
        </div>

        <div class="event-calendar__main">
            <!-- EVENTS START -->
            <div class="event-calendar__events">
            
            <script type="module" crossorigin src="/wp-content/themes/kauppakamari/vue-event-calendar/dist/assets/index.js"></script>
            <!--<link rel="stylesheet" href="/wp-content/themes/kauppakamari/vue-event-calendar/dist/assets/index.css">-->
            <script type="module">try{import.meta.url;import("_").catch(()=>1);}catch(e){}window.__vite_is_modern_browser=true;</script>
            <script type="module">!function(){if(window.__vite_is_modern_browser)return;console.warn("vite: loading legacy build because dynamic import or import.meta.url is unsupported, syntax error above should be ignored");var e=document.getElementById("vite-legacy-polyfill"),n=document.createElement("script");n.src=e.src,n.onload=function(){System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))},document.body.appendChild(n)}();</script>

            <div id="event-calendar-wp-app"></div>
        </div>  
           
            <script nomodule>!function(){var e=document,t=e.createElement("script");if(!("noModule"in t)&&"onbeforeload"in t){var n=!1;e.addEventListener("beforeload",(function(e){if(e.target===t)n=!0;else if(!e.target.hasAttribute("nomodule")||!n)return;e.preventDefault()}),!0),t.type="module",t.src=".",e.head.appendChild(t),t.remove()}}();</script>
            <script nomodule crossorigin id="vite-legacy-polyfill" src="/wp-content/themes/kauppakamari/vue-event-calendar/dist/assets/polyfills-legacy.js"></script>
            <script nomodule crossorigin id="vite-legacy-entry" data-src="/wp-content/themes/kauppakamari/vue-event-calendar/dist/assets/index-legacy.js">System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))</script>
             
            <!-- EVENTS END -->
            <div class="event-calendar__link-container">
                <?php if(get_field('link')) {
                echo '<a href="'. get_field('link')['url'] .'" class="event-calendar__link">'. get_field('link')['title'] .'</a>';
                } ?>
            </div>
        </div>
    </div>
</div>