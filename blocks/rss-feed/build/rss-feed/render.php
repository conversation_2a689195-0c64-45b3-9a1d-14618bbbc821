<?php
/**
 * Server-side render callback for the RSS news feed block.
 *
 * This function is used by WordPress to generate the block's front-end HTML
 * when the page is rendered. The actual dynamic content (RSS feed) will be 
 * rendered on the client side via JavaScript.
 *
 * @param array $attributes Block attributes passed from the block editor.
 * @return string HTML output.
 */

// Log a message to the PHP error log to confirm the file is being loaded.
error_log('Render callback loaded!');

// Define the render callback only if it hasn't been defined already.
if ( ! function_exists( 'rss_feed_render_callback' ) ) {
	function rss_feed_render_callback( $attributes ) {
		ob_start();
		?>
		<!-- Placeholder container where the JavaScript app will mount -->
		<div
			id="rss-feed-root"
			data-attributes='<?php echo esc_attr(json_encode($attributes)); ?>'
		>
			Ladataan RSS-syötettä...
		</div>
		<?php
		return ob_get_clean();
	}
}