@media screen and (max-width: 549px) {
	.block-slider {
		padding: 1rem;

		.slide-container {
			display: flex !important;
			flex-direction: column !important;
			gap: 1rem !important;
			margin-right: 0 !important;

			.slide-image-wrapper {
				height: 200px !important;
				width: 200px !important;
			}
		}
	}
}

.block-slider {
	margin: 2rem 0 3rem;

	padding: 2rem 0;
	border-top: 3px solid $separatorblue;
	border-bottom: 3px solid $separatorblue;

	&__title {
		text-align: center;
		margin-bottom: 3rem;
	}
	.slide-container {
		display: grid;
		grid-template-columns: 175px auto;
		gap: 3rem;
		margin-right: 2rem;

		.slide-image-wrapper {
			background-color: #d2d2d2;
			background-size: cover;
			background-position: center;
			height: 175px;
			border-radius: 50%;
		}
		.slide-content {
			margin: 1.5rem 0;

			.slide-quote {
				color: $primary;
				font-weight: $ws-semibold;
				font-size: 22px;
				margin-bottom: 1rem;
			}
			.slide-author, .slide-status {
				// font-size: 15px;
				font-weight: $ws-semibold;
				color: $black-text;
			}
			.slide-author {
				text-transform: uppercase;
				margin-bottom: 1rem;
			}
			a {
				@include textlink;
				margin-top: 2rem;
				display: block;
				width: auto;
				width: fit-content;
			}
		}
	}
	&-arrows {
		display: flex;
		justify-content: center;
		gap: 20px;
		.slider-arrow-container {
      border: none;
			background: $primary;
			width: 46px;
			height: 46px;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 50%;
			color: white;
			transition: all 200ms;
			cursor: pointer;
			&:hover {
				background: lighten($primary, 10%);
			}
		}
	}
  .slick-list {
    width: 100%;
  }

	@media (min-width: 786px) {
		// margin: 2rem 0 3rem;
		margin: 2rem auto 3rem;
		.slide-container .slide-content {
			.slide-quote {
				font-size: 20px;
			}
		}
	}

  @media screen and (min-width: 550px) {
		// padding: 4rem;
		// $marginNega: calc((100% - 100vw) / 2);
		// $marginPosi: calc((100vw - 100%) / 2);
		// margin-left: $marginNega;
		// margin-right: $marginNega;
		// padding-left: $marginPosi;
		// padding-right: $marginPosi;
		.slide-container {
			// display: grid;
			// grid-template-columns: 175px auto;
			// gap: 3rem;
		}
	}
}
