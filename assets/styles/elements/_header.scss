/* ==========================================================================
 # NEW Header
========================================================================== */

#wpadminbar {
  //display: none;
}
.logged-in .site-header {
  top: 2rem;
}

.site-header {
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 100;
  font-weight: $ws-medium;
  @include breakpoint($menu-visible) {
    align-items: center;
    display: flex;
    padding-top: 0;
  }
  &__container,
  &__top {
    @include breakpoint($menu-visible) {
      @include wrap;
      max-width: 100%;
      padding: 0;
      align-items: center;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
  }
  &__top {
    padding-left: 1.25rem;
    background-color: $primary;
    color: #fff;
    display: flex;
    height: 44px;
    max-height: 44px;
    justify-content: space-between;
    align-items: center;
    a {
      font-size: 15px;
      letter-spacing: normal;
    }
    .header_sites,
    .header_links {
      height: 44px;
      max-height: 44px;
    }
  }
  &__bottom {
    @include breakpoint($menu-visible) {
      @include wrap;
      padding-right: 1rem;
      padding-left: 1rem;
      max-width: 100%;
      align-items: center;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      background: #fff;
      height: 74px;
      max-height: 74px;
    }
    @include breakpoint(min-width 1750px) {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
    height: 66px;
    max-height: 66px;
  }

  .header_lang {
    // language selection
    display: flex;
    list-style: none;
    align-items: center;
    // padding : 0.7171rem 0;
    text-transform: uppercase;
    a {
      text-decoration: none;
      letter-spacing: 0.6px;
      padding: 0 1rem;
      color: #fff;
      &:hover {
        @include elementhover;
        text-decoration-thickness: 1px;
      }
    }
    .lang-item-first a {
      border-right: 1px solid $coral;
    }
    .lang-svg {
      span {
        vertical-align: -0.1em; //override Font awesome vertical align
      }
    }
  }

  .header_sites a::after {
    display: none;
  }

  // External links and search on Top bar
  .header_sites,
  .header_links {
    display: flex;
    list-style: none;
    // max-height: 44px;
    // height: 44px;
    .header-site {
      background: $primary;
      color: #fff;
      padding: 0 1.25rem;
      display: flex;
      align-items: center;
      justify-content: center;
      text-decoration: none;
      &:hover,
      &:focus {
        @include elementhover;
        text-decoration-color: $white;
        text-decoration-thickness: 1px;
      }
    }
    .header-site {
      border-right: 1px solid $coral;
    }
    .header-link-svg {
      margin-left: $gap-tiny;
      vertical-align: 0.1em;
    }
  }
  .header_sites {
    @include breakpoint($sub-xl) {
      display: none;
    }
  }

  //Header logo and menu bar
  .branding {
    padding-left: $gap-tiny;
    padding-right: 2rem;
    background: #fff;
    a {
      text-decoration: none;
      font-size: 0;
    }
    img {
      height: 66px;
      width: auto;
      margin: 0;
      @media screen and (min-width: $menu-visible) {
        // height:4.934rem;
        // margin-left: 1.25rem;
      }
    }

    &__logo {
      background-color: $primary;
      padding: 5px 8px;
      color: white;
      font-weight: 600;
      margin-left: 1rem;
      text-transform: uppercase;
      font-size: 1.2rem;
    }
    &__name {
      color: $primary;
      padding: 5px 4px;
      font-weight: 600;
      text-transform: uppercase;
      border-left: 3px solid $brand;
      margin-left: 2px;
      font-size: 1.2rem;
    }
    @include breakpoint($menu-visible) {
      padding-left: 0px;
      padding-right: 0px;
    }
    @include breakpoint($menu-hidden) {
      align-items: center;
      display: flex;
      justify-content: space-between;
      width: 100%;
      &__logo {
        width: 5rem;
      }
    }

    &__title {
      display: block;
      a {
        display: block;
      }
    }
  }
}
