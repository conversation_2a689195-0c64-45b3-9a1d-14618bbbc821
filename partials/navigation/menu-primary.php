<?php
/**
 * Template part: Primary menu
 *
 * @package kauppakamari
 */

?>

<div class="branding">
  <span class="branding__title">
    <a href="<?php echo esc_url(home_url('/')); ?>" rel="home" itemprop="headline">
      <span class="screen-reader-text">Kauppakamari<?php bloginfo('name'); ?></span>
      <?php  if (get_current_blog_id() === kauppakamari_get_hardcoded_id('koulutus')) { ?>
        <img src="<?php echo get_template_directory_uri() . '/dist/images/HSKK_Koulutus_logo.png'; ?>" alt="Koulutus logo" />
      <?php } else { ?>
        <img src="<?php echo get_template_directory_uri() . '/dist/images/HelsinginSeudun_Kauppakamari.svg'; ?>" alt="Helsingin seudun kauppakamari logo" />
      <?php } ?>
    </a>
  </span>

  <div class="menu-secondary">
      <div class="menu-join-button">
          <div id="header-join-now-copy"></div>
      </div>
    <div class="menu-buttons-wrapper header__toggle">
      <?php kauppakamari_menu_toggle_btn('menu-toggle'); ?>
    </div>
  </div>
  <!--
  font awesome -ikoni versio ?
  -->

</div><!-- .site-branding -->

<nav id="primary-navigation" class="primary-navigation" aria-label="<?php ask_e('Menu: Primary Menu'); ?>" itemscope itemtype="http://schema.org/SiteNavigationElement">
  <?php
  wp_nav_menu(array(
    'theme_location' => 'primary',
    'container'      => '',
    'menu_id'        => 'primary-navigation__items',
    'menu_class'     => 'primary-navigation__items',
    'link_before'    => '',
    'link_after'     => '',
    'fallback_cb'    => '',
    'walker' => new Air_Light_Navwalker(),
  )); ?>
</nav><!-- #primary-navigation -->
