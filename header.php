<?php

/**
 * Header
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package kauppakamari
 */
$isProduction = true;
if (wp_get_environment_type() == 'staging' || wp_get_environment_type() == 'development') {
    $isProduction = false;
}
if (isset($_SERVER) && isset($_SERVER['WP_ENV'])) {
    if ($_SERVER['WP_ENV'] == 'staging' || $_SERVER['WP_ENV'] == 'development') {
        $isProduction = false;
    }  /* put .env file with wp-config.php in seravo */
}
$siteUrl = get_site_url();
if (stristr($siteUrl, '.local') !== false) {
    $isProduction = false;
}
?>
<!DOCTYPE html>
<html <?php language_attributes(); ?> class="no-js">

<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <link rel="stylesheet" href="https://use.typekit.net/scs4xyo.css">

    <?php
    // Fix for all-filter in articles
    $url = $_SERVER['REQUEST_URI'];
    if (str_contains($url, '/artikkelityyppi/kaikki/')) {
        echo '<title>Kaikki sisältö | Helsingin seudun kauppakamari</title>';
    }
    ?>
    <?php wp_head(); ?>
    <script src="https://code.jquery.com/jquery-3.4.1.js" integrity="sha256-WpOohJOqMqqyKL9FccASB9O0KwACQJpFTUBLTYOVvVU=" crossorigin="anonymous"></script>

    <!-- Slick slider -->
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css" />
    <script src="<?php echo get_template_directory_uri() . '/node_modules/slick-slider/slick/slick.min.js'; ?>"></script>

    <!-- chartjs -->
    <script src="<?php echo get_template_directory_uri() . '/node_modules/chart.js/dist/chart.umd.js'; ?>"></script>
    <!-- waypoints -->
    <script src="<?php echo get_template_directory_uri() . '/node_modules/waypoints/lib/noframework.waypoints.min.js'; ?>"></script>

    <?php if (!is_koulutus() && $isProduction): ?>
        <meta name="facebook-domain-verification" content="3146pbpqskusrqen5g53xkz2f7gv07" />
        <!-- Start of HubSpot Embed Code -->
        <script id="hs-script-loader" async defer src="//js.hs-scripts.com/4840223.js"></script>
        <!-- End of HubSpot Embed Code -->
        <!-- Hotjar Tracking Code for https://www.helsinki.chamber.fi -->
        <script>
            (function(h, o, t, j, a, r) {
                h.hj = h.hj || function() {
                    (h.hj.q = h.hj.q || []).push(arguments)
                };
                h._hjSettings = {
                    hjid: 272034,
                    hjsv: 6
                };
                a = o.getElementsByTagName('head')[0];
                r = o.createElement('script');
                r.async = 1;
                r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
                a.appendChild(r);
            })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
        </script>
        <!-- Facebook Pixel Code -->
        <script>
            ! function(f, b, e, v, n, t, s) {
                if (f.fbq) return;
                n = f.fbq = function() {
                    n.callMethod ?
                        n.callMethod.apply(n, arguments) : n.queue.push(arguments)
                };
                if (!f._fbq) f._fbq = n;
                n.push = n;
                n.loaded = !0;
                n.version = '2.0';
                n.queue = [];
                t = b.createElement(e);
                t.async = !0;
                t.src = v;
                s = b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t, s)
            }(window, document, 'script',
                'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '1692273584360212');
            fbq('track', 'PageView');
        </script>
        <!-- End Facebook Pixel Code -->

        <!-- Global site tag (gtag.js) - Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=UA-6972593-21"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            <?php
            $writer = get_field('writer_object', get_queried_object());
            if (!empty($writer)):
                $writer = get_post($writer);
            ?>
                dataLayer.push({
                    'writer': '<?php echo $writer->post_title; ?>',
                });
            <?php endif; ?>

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());
            gtag('config', 'UA-6972593-21');
        </script>

        <meta name="google-site-verification" content="BSblFkLRli9CnnBMsh7AhPe2v-AGF8WN4QwG6CuUGOw" />
        <script id="Cookiebot" src="https://consent.cookiebot.com/uc.js" data-cbid="50b05be1-ebea-40c1-8c29-6ab334fea4bf" data-blockingmode="auto"></script>
        <script id="CookieDeclaration" src="https://consent.cookiebot.com/50b05be1-ebea-40c1-8c29-6ab334fea4bf/cd.js" async></script>
    <?php elseif ($isProduction): ?>

        <!-- Global site tag (gtag.js) - Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=UA-6972593-39"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());
            gtag('config', 'UA-6972593-39');
        </script>
        <script id="Cookiebot" src="https://consent.cookiebot.com/uc.js" data-cbid="df5e01e6-fbbd-499f-aff3-5be867c57eea" data-blockingmode="auto"></script>
        <script id="CookieDeclaration" src="https://consent.cookiebot.com/df5e01e6-fbbd-499f-aff3-5be867c57eea/cd.js" async></script>
        <!-- Facebook Pixel Code -->
        <script>
            ! function(f, b, e, v, n, t, s) {
                if (f.fbq) return;
                n = f.fbq = function() {
                    n.callMethod ?
                        n.callMethod.apply(n, arguments) : n.queue.push(arguments)
                };
                if (!f._fbq) f._fbq = n;
                n.push = n;
                n.loaded = !0;
                n.version = '2.0';
                n.queue = [];
                t = b.createElement(e);
                t.async = !0;
                t.src = v;
                s = b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t, s)
            }(window, document, 'script',
                'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '1692273584360212');
            fbq('track', 'PageView');
        </script>
        <!-- End Facebook Pixel Code -->
    <?php endif; ?>


    <?php if ($isProduction): ?>
        <!-- giosg tag -->
        <script>
            (function(w, t, f) {
                var s = 'script',
                    o = '_giosg',
                    h = 'https://service.giosg.com',
                    e, n;
                e = t.createElement(s);
                e.async = 1;
                e.src = h + '/live2/' + f;
                w[o] = w[o] || function() {
                    (w[o]._e = w[o]._e || []).push(arguments)
                };
                w[o]._c = f;
                w[o]._h = h;
                n = t.getElementsByTagName(s)[0];
                n.parentNode.insertBefore(e, n);
            })(window, document, "4250efa2-2579-11ea-af34-0242ac110025");
        </script>
        <!-- giosg tag -->
    <?php endif; ?>


</head>

<?php
global $blog_id;

$blogname = null;
if (function_exists('get_blog_details')) {
    $current_blog_details = get_blog_details(array('blog_id' => $blog_id));
    $blogname = $current_blog_details->blogname;
}
$isProductionClass = $isProduction ? ' is-production' : ' is-development';

?>

<body <?php body_class((is_front_page() ? 'is-front-page' : '') . ' main-body ' . $blogname . $isProductionClass); ?> itemscope itemtype="http://schema.org/WebPage">

    <?php
    if ($isProduction):
    ?>
        <noscript>
            <img height="1" width="1" style="display:none;" alt="fb" src="https://www.facebook.com/tr?id=1692273584360212&ev=PageView&noscript=1">
        </noscript>
    <?php
    endif;
    ?>
    <div id="page" class="site">
        <a class="skip-to-content screen-reader-text" href="#content"><?php ask_e('Accessibility: Skip to content'); ?></a>

        <header id="masthead" class="site-header" itemscope itemtype="http://schema.org/WPHeader">

            <div class="site-header__container">

                <div class="site-header__top">

                    <div class="header_lang">

                        <div class="lang-svg">
                            <span class="fa-sharp fa-solid fa-globe fa-lg"></span>
                        </div>

                        <?php if (function_exists('pll_the_languages')) {
                            pll_the_languages(
                                array(
                                    'display_names_as' => 'slug',
                                    'hide_current' => false,
                                )
                            );
                        } ?>
                    </div>


                    <div class="header_links">
                        <div class="header_sites">
                            <?php
                            function headerCmp($a, $b)
                            {
                                return strcmp($a['header_order'], $b['header_order']);
                            }

                            $sites = kauppakamari_get_sites();
                            if (!empty($sites)) {
                                // change order and remove 0
                                foreach ($sites as $s => $site) {
                                    if ($site['header_order'] == 0) {
                                        unset($sites[$s]);  // remove if header_order is 0
                                    }
                                }

                                usort($sites, 'headerCmp');
                                reset($sites);

                                foreach ($sites as $site) {
                                    $target_tag = $site['link']['target'] ? 'target="' . $site['link']['target'] . '' : '';
                                    echo '<a class="header-site" href="' . $site['link']['url'] . '" ' . $target_tag . '">';
                                    echo '<span class="site">' . $site['link']['title'] . '<span class="fa-sharp fa-solid fa-up-right-from-square fa-xs header-link-svg"></span></span>';
                                    echo '</a>';
                                }
                            }
                            ?>
                        </div>
                        <?php
                        if (1 or empty(get_search_query())):  // DEBUG SHOW
                        ?>
                            <div class="search-top-container">
                                <a href="#" aria-label="<?php echo pll__('Avaa haku'); ?>" class="search-menu-button"><?php echo pll__('Haku'); ?></a>
                                <div class="search-top-container--inner">
                                    <div class="search-top-container--inner--container">

                                        <span class="search-top-title"><?php pll_e("Hae sivustolta") ?></span>
                                        <?php
                                        $action = '/';
                                        $lang = pll_current_language();
                                        if (!empty($lang) && $lang !== 'fi') {
                                            $action = $action . $lang . '/';
                                        }
                                        ?>
                                        <div class="search-top-container--inner--container--form-container">
                                            <form class="search-top-container-form" action="<?php echo $action; ?>" method="get">
                                                <fieldset>
                                                    <legend class="screen-reader-text" id="search-top-container-form-label2"><?php pll_e("Hae sivustolta") ?></legend>
                                                    <input type="text" class="top-search-input" aria-labelledby="search-top-container-form-label2" name="s" placeholder="<?php pll_e('Etsi uutisia, sisältöjä, yhteystietoja') ?>" autocomplete="off">
                                                    <span class="top-search-input-clear" aria-label="<?php pll_e('Tyhjää hakukenttä') ?>"></span>
                                                    <button type="submit"><?php pll_e("Hae") ?></button>
                                                </fieldset>
                                            </form>
                                            <div class="helper-links">
                                                <?php if (pll_current_language() == "fi") : ?>
                                                    <a class="helper-link" href="/yhteystiedot">Kaikki yhteystiedot</a>
                                                <?php else : ?>
                                                    <a class="helper-link" href="/en/contact-info">All contact information</a>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="site-header__bottom">
                    <?php echo get_template_part('partials/navigation/menu-primary') ?>
                </div>
            </div><!-- /container -->

        </header><!-- #masthead -->

        <div id="content" class="site-content" role="main" itemscope itemprop="mainContentOfPage">