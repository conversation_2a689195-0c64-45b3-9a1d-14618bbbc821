/* ==========================================================================
 # Button mixins
========================================================================== */

// basic button styles
@mixin buttonold {
  @include button-reset;
  @include transition;
  background: $brand;
  border: 1px solid $brand;
  border-radius: 5rem;
  color: white;
  display: inline-block;
  font-size: .875rem;
  @include breakpoint($s) {
    font-size: 1rem;
  }
  font-weight: 400;
  line-height: 1.25;
  padding: $gap-small $gap;
  text-decoration: none;
  vertical-align: baseline;
  &:hover, &:focus, &:active {
    background: darken($brand, 5);
    //transform: translateY(-.125rem);
  }
}

// New button 2023 (blue)
@mixin button {
  @include newbutton;
  // margin: auto;
  background: $primary;
  color: $white;
  &:hover, &:focus {
    background: $link-use;
  }
}


// new button 2023
@mixin newbutton {
  @include button-reset;
  @include transition;
  &.external::after {
    font-family: "Font Awesome 6 Sharp";
    content: "\f35d";
    display: inline-block;
    margin-left: 8px;
    @include breakpoint($sub-m) {
      margin-left: 10px
    }
  }
  &.coral {
    background: $coral !important;
    color: $primary !important;
    &:hover {
      color: $primary !important;
      background: $coral-75 !important;
    } 
  }
  &.royalblue {
    background: $primary !important;
    color: $white !important;
    &:hover {
      color: $white !important;
      background: $link-use !important;
    }
  }
  // relative for :after icons
  background: $coral; // coral button as default
  color: $primary;

  position: relative;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 14px;
  min-height: 42px;
  width: 100%;
  letter-spacing: 0.5px;
  @include breakpoint($m) {
    font-size: 16px;
    min-height: 56px;
    width: fit-content;
  }
  font-weight: $ws-semibold;
  text-transform: uppercase;
  padding: 0.75rem;
  text-decoration: none;
  vertical-align: baseline;
  &:hover, &:focus {
    text-decoration: none !important;
    background: $coral-75;
  }
}

// new text link 2023
@mixin textlink {
  position: relative;
  color: $primary;
  text-decoration: none;
  font-weight: $ws-semibold;
  letter-spacing: 0.5px;
  font-size: 16px;
  cursor: pointer;
  @include breakpoint($m) {
    font-size: 18px;
  }
  &:after { // chevron
    content: "\f054";
    font-family: 'Font Awesome 6 Sharp';
    display: inline-block;
    margin-left: 8px;
    font-size: 14px;
    transition: transform .3s cubic-bezier(0.2, 0.8, 0.2, 1);
  }
  &.external span:after {
    content: "\f35d";
    font-family: 'Font Awesome 6 Sharp';
    display: inline-block;
    margin: 0 4px;
    font-size: 14px;
  }
  &:hover {
    @include elementhover;
    &:after { // chevron animation
      transform: translateX(4px);
    }
  }
}

//bigger textlink (underline, uppercase etc.)
@mixin bigtextlink {
  @include textlink;
  color: $primary;
  text-transform: uppercase;
  font-size: 16px;
  @include breakpoint($m) {
    font-size: 18px;
  }

  text-underline-offset: 8px;
  text-decoration-thickness: 2px;
  text-decoration: underline solid;
  text-decoration-color: $light-blue-75;
  transition: text-decoration 0.1s ease-in-out;

  &:after { // fix chevron on big links
    font-size: 16px;
  }
  &:hover { // override small link underline settings
    color: $primary !important;
    text-underline-offset: 6px;
    text-decoration-thickness: 3px;
    text-decoration-color: $primary;
  }
}



// Underline hover effect
@mixin elementhover {
  text-decoration: underline;
  text-underline-offset: 2px;
  text-decoration-thickness: 2px;
}

// button variaton: big
@mixin button--big {
  padding: $gap-small $gap;
}
