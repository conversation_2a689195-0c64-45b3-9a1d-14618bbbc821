import { useBlockProps } from "@wordpress/block-editor";

export default function save({ attributes }) {
	const {
		mode,
		manualTitle,
		manualText,
		manualDate,
		manualCategory,
		manualImageUrl,
		manualLinkText,
		manualLinkUrl,
		postTitle,
		postText,
		postDate,
		postCategory,
		postImageUrl,
		postUrl,
	} = attributes;

	const isManual = mode === "manual";

	// Determine which content to use based on mode
	const title = isManual ? manualTitle : postTitle;
	const text = isManual ? manualText : postText;
	const date = isManual ? manualDate : postDate;
	const category = isManual ? manualCategory : postCategory;
	const imageUrl = isManual
		? manualImageUrl
		: attributes.overrideImageUrl || postImageUrl;

	// Return nothing if required fields are missing (in manual mode only)
	if (
		isManual &&
		(!manualTitle || !manualText || !manualCategory || !manualImageUrl)
	) {
		return null;
	}

	// Format ISO date string to Finnish locale (returns empty string if invalid)
	const formatDate = (iso) => {
		const date = new Date(iso);
		if (!iso || isNaN(date)) return "";
		return date.toLocaleDateString("fi-FI");
	};

	return (
		<div
			{...useBlockProps.save()}
			className="wp-block-group news-lift-slider-article"
		>
			{/* Image section with optional post link */}
			{imageUrl && (
				<figure className="wp-block-image size-large news-lift-slider-img">
					{postUrl ? (
						<a href={postUrl}>
							<img
								src={imageUrl}
								alt=""
								style={{
									width: "380px",
									height: "250px",
									objectFit: "cover",
								}}
							/>
						</a>
					) : (
						<img
							src={imageUrl}
							alt=""
							style={{
								width: "380px",
								height: "250px",
								objectFit: "cover",
							}}
						/>
					)}
				</figure>
			)}

			{/* Text content section */}
			<div className="wp-block-group news-lift-slider-body">
				<div className="wp-block-group news-lift-slider-meta">
					{/* Render date if it's valid */}
					{!!date && !isNaN(new Date(date)) && (
						<p className="meta-data-date has-very-dark-gray-color has-text-color has-link-color">
							{formatDate(date)}
						</p>
					)}

					{/* Always render category (required field) */}
					<p
						className="meta-data-category has-main-color has-text-color has-link-color"
						style={{ textTransform: "uppercase" }}
					>
						{category}
					</p>
				</div>

				{/* Title with optional post link */}
				<h3 className="wp-block-heading news-lift-slider-heading has-main-color has-text-color has-link-color">
					{postUrl ? <a href={postUrl}>{title}</a> : title}
				</h3>

				{/* Main content/description text */}
				<p className="news-lift-slider-text">{text}</p>

				{/* Optional manual mode link (only shown if both fields exist) */}
				{isManual && manualLinkText && manualLinkUrl && (
					<p>
						<a
							href={manualLinkUrl}
							className="news-lift-slider-link"
							rel="noopener noreferrer"
						>
							{manualLinkText}
						</a>
					</p>
				)}
			</div>
		</div>
	);
}
