<?php
/**
 * Poistettu template name NYT
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package kauppakamari
 */

get_header();
$hide_h1_title = get_field('hide_h1_title');
?>

  <?php get_template_part('partials/content/hero-page'); ?>


  <div id="primary" class="primary primary--page has-sidebar kauppakamarinyt">

  <?php kauppakamari_get_writers(); ?>

    <main id="main" class="main">
      <!-- For h1 and ingress -->
      <article id="post-<?php the_ID(); ?>" <?php post_class('entry entry--page nyt-main'); ?>>

        <div class="entry__content wysiwyg">
          <?php if(!$hide_h1_title): ?>
          <h1><?php the_title(); ?></h1>
          <?php endif; ?>

          <?php the_content(); ?>
        </div>

      </article>

    <div class="kauppakamarinyt_header">
      <span class="kauppakamarinyt_filter_title">Su<PERSON>ta sisältöjä</span>
      <div class="kauppakamarinyt_select_wrap">
        <div class="kauppakamarinyt_select_column">
          <span>Kirjoittaja:</span>
          <div class="kauppakamarinyt_select writer">
            <select id="styledSelect" class="">
            <option value=""><?php echo "Valitse"; ?></option>
              <?php
                $writers = kauppakamari_get_writers();
                $i = 0;
                foreach ( $writers as $writer ) { ?>
                    <?php
                    $writer = get_post($writer);
                    if ($writer) : ?>
                      <option value="<?php echo $writer->ID; ?>"><?php echo $writer->post_title; ?></option>
                      <?php
                    endif;
                  $i++;
                } ?>
            </select>
          </div>
        </div>
        <div class="kauppakamarinyt_select_column">
          <span>Teema:</span>
          <div class="kauppakamarinyt_select">
            <select id="styledSelect" class="">
            <option value=""><?php echo "Valitse"; ?></option>
              <?php
                $categories = get_categories( array(
                    'orderby' => 'name',
                    'order'   => 'ASC',
                    'taxonomy' => 'category'
                ) );
                $i = 0;
                foreach ($categories as $category) { ?>
                    <option value="<?php echo $category->slug; ?>"><?php echo $category->name; ?></option>
                  <?php
                  $i++;
                } ?>
                <option value="international-topics">International topics</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <div class="nyt__checkboxes">
      <div class="nyt__checkboxes--wrap">
      <?php
        $categories = get_categories( array(
            'orderby' => 'name',
            'order'   => 'ASC',
            'taxonomy' => 'article_type',
        ) );
        $i = 0;
        /*pre select on get: */
        if (isset($_GET['kategoria']) && !empty($_GET['kategoria'])) {
          $pre_cats = sanitize_text_field($_GET['kategoria']);
          $pre_cats_array = explode(',', $pre_cats);
        }
        if(isset($pre_cats_array) && sizeof($pre_cats_array) > 0) {

        }


        foreach( $categories as $category ) {
          if(is_array($pre_cats_array) && in_array($category->slug, $pre_cats_array)) {
            $checked = 'checked="checked"';
          }
          else {
            $checked = '';
          }
          ?>
            <input id="ch_<?php echo $i; ?>" type="checkbox" name="field112[]" class="nyt__checkboxes--box hidden" value="<?php echo $category->slug; ?>" <?php echo $checked;?>>
              <label for="ch_<?php echo $i; ?>"><?php echo $category->name; ?></label>
          <?php
          $i++;
        } ?>
      </div>
    </div>



    <div class="kauppakamarinyt_list_wrap">
      <?php

        $tax_query = array('relation' => 'AND');

        if (isset($_GET['kategoria']) && !empty($_GET['kategoria'])) {
          $cats = sanitize_text_field($_GET['kategoria']);

          $cats_array = explode(',', $cats);

          if(sizeof($cats_array) > 1) {
            $tax_query[] = array(
              'taxonomy' => 'article_type',
              'field' => 'slug',
              'terms' => $cats_array
            );
          }
          else {
            $tax_query[] = array(
              'taxonomy' => 'article_type',
              'field' => 'slug',
              'terms' => $cats
            );
          }


        }

        if (isset($_GET['teema']) && !empty($_GET['teema'])) {
          $tax_query[] = array(
            'taxonomy' => 'category',
            'field' => 'slug',
            'terms' => sanitize_text_field($_GET['teema'])
          );
        }

        $args = array(
          'post_type'              => array( 'post', 'release' ),
          //'paged'                  => 1,
          'posts_per_page'         => 10,
          'tax_query'              => $tax_query,
        );

        if (isset($_GET['kirjoittaja']) && !empty($_GET['kirjoittaja'])) {
          $args['meta_key'] = 'writer_object';
          $args['meta_value'] = (int)$_GET['kirjoittaja'];
          $args['order'] = 'DESC';
        }

        if (isset($_GET['kirjoittaja']) && !empty($_GET['kirjoittaja']) ||
            isset($_GET['teema']) && !empty($_GET['teema']) ||
            isset($_GET['kategoria']) && !empty($_GET['kategoria'])) {
          $args['ignore_sticky_posts'] = '1';
        }

        if (isset($_GET['teema']) && !empty($_GET['teema'])) {
          if ($_GET['teema'] == 'international-topics') {
            $args['lang'] = 'en';
          }
        }

        //var_dump($args);

        $query = new WP_Query( $args );
      ?>
      <div
        class="kauppakamarinyt_list"
        data-page="<?php echo get_query_var('paged') ? get_query_var('paged') : 1; ?>"
        data-max="<?php echo $query->max_num_pages; ?>">

        <?php
        $i = 0;

        if ( $query->have_posts() ) {
          while ( $query->have_posts() ) {
            $query->the_post();
            if ($i===0) {
              get_template_part('partials/content/teaser', 'large');
            } elseif ($i===1 || $i===2) {
              get_template_part('partials/content/teaser', 'medium');
            } elseif ($i >= 3 ) {
              echo '<div class="clearfix"></div>';
              get_template_part('partials/content/teaser');
            }
            $i++;
          }
          ?>

          <?php
        } else {}

        wp_reset_postdata();
        ?>
      </div>
      <?php
        $current_page = get_query_var('paged') ? get_query_var('paged') : 1;
      ?>
      <?php if ($current_page < $query->max_num_pages) : ?>
        <a class="btn load-more">Näytä lisää</a>
      <?php endif; ?>

      <div class="kauppakamarinyt_list_links">

      <a href="/arkisto" class="archive-link">Lue kaikki artikkelimme</a>

      <?php
      if (isset( $_GET['teema']) && !empty($_GET['teema'])) {
        $term = get_category_by_slug($_GET['teema']); ?>
        <a href="<?php echo get_category_link($term); ?>">
          Lue lisää aiheesta <b><?php echo $term->name; ?></b>
        </a>
      <?php } ?>

      </div>

    </div>

    </main><!-- #main -->

    <?php get_sidebar('nyt'); ?>

    <?php
    //TODO make better sidebar
    //get_template_part('partials/content/hero');
    ?>

  </div><!-- #primary -->

<?php
get_footer();
