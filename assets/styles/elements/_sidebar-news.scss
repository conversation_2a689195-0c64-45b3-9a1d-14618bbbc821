.sidebar-news-heading {
  font-size: 20px;
  text-transform: uppercase;
  margin-top: 40px !important;
  font-weight: 600;
}

.sidebar-news-thumbnail {
  width: 100%;
  height: 188px;
  overflow: hidden;
}

.sidebar-news-thumbnail img {
  border-radius: 8px;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.sidebar-news-meta {
  margin-top: 20px;
  gap: 20px;
  display: flex;
  align-items: baseline;
}

.sidebar-news-meta p {
  margin: 0 !important;
  font-size: 12px !important;
  font-weight: 600 !important;
}

.sidebar-meta-date {
  color: #636363 !important;
}

.sidebar-meta-category {
  color: #002662;
}

.sidebar-news-article-heading {
  font-size: 16px !important;
  color: #002662;
  font-weight: 600;
  line-height: 22px !important;
  margin-top: 6px;
  margin-bottom: 25px;
}

.sidebar-news-article-heading a {
  text-decoration: none;
  color: #002662;
}

.sidebar-news-article-heading a:hover {
  text-decoration: underline;
}

.sidebar-news-hr {
  border-top: 2px solid #e4eff8;
  margin-bottom: 25px;
}

.sidebar-news-link {
  color: #002662;
  font-size: 16px !important;
  font-weight: 600;
  position: relative;
  text-decoration: none;
}

.sidebar-news-link:hover {
  text-decoration: underline;
}

.sidebar-news-link::after {
  content: "";
  position: absolute;
  top: 6px;
  width: 6px;
  height: 10px;
  background-image: url("../images/arrow.svg");
  background-size: contain;
  background-repeat: no-repeat;
  margin-left: 8px;
}
