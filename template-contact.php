<?php
/**
 * Template name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package kauppakamari
 */

get_header(); ?>

  <?php if(is_front_page()) { ?>
    <?php get_template_part('partials/content/hero-frontpage'); ?>
  <?php } else { ?>
    <?php get_template_part('partials/content/hero-page'); ?>
  <?php } ?>

  <div id="primary" class="primary primary--page contact">

    <main id="main" class="main">

    <?php while (have_posts()) : the_post(); ?>

    <article id="post-<?php the_ID(); ?>" <?php post_class('entry entry--page'); ?>>

      <div class="entry__content wysiwyg">
        <?php the_content(); ?>
      </div>

    </article>

    <?php endwhile; ?>

    </main><!-- #main -->

    <div class="contact__people">

      <div class="contact__header">
        <h2><PERSON><PERSON><PERSON> ka<PERSON>t</h2>
        <p><i>Henkilökuntamme sähköpostiosoitteet ovat muotoa: <EMAIL> </i></p>
      </div>

      <div class="contact__checkboxes">
        <fieldset>
        <legend class="contacts-filter">Suodata tuloksia</legend>
        <?php
          $categories = get_categories( array(
              'orderby' => 'name',
              'order'   => 'ASC',
              'taxonomy' => 'people-cat',
          ) );
          $i = 0;
          foreach( $categories as $category ) { ?>
            <div class="contact__checkbox">
              <label for="contact-ch_<?php echo $i; ?>">
                <input id="contact-ch_<?php echo $i; ?>" type="checkbox" name="field112[]" class="contact__checkboxes--box" value="<?php echo $category->slug; ?>">
                <span class="custom-checkbox"></span>
                <?php echo $category->name;?>
              </label>
            </div>
            <?php
            $i++;
          } ?>
          </fieldset>
      </div>



      <div class="contact_list_wrap">
        <?php

          $tax_query = array('relation' => 'AND');

          if(isset($_GET['kategoria']) && !empty($_GET['kategoria'])) {
            $cats = $_GET['kategoria'];
            $tax_query[] = array(
                'taxonomy' => 'people-cat',
                'field' => 'slug',
                'terms' => $cats
            );
          }

          $args = array(
            'post_type'              => array( 'people' ),
            'paged'                  => 1,
            'posts_per_page'         => '-1',
            'order'                  => 'ASC',
            'orderby'                => 'title',
            'tax_query'              => $tax_query,
          );

          //vd($args);

          $query = new WP_Query( $args );
        ?>
        <div
          class="contact__list"
          data-page="<?php echo get_query_var('paged') ? get_query_var('paged') : 1; ?>"
          data-max="<?php echo $query->max_num_pages; ?>">

          <?php

          $staff_list = [];

          if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
              $query->the_post(); ?>
              <div class="contact__item">
              <?php if (get_the_post_thumbnail_url(get_the_ID(),'medium')) : ?>
              <div class="contact__item__img" style="background-image: url(<?php echo get_the_post_thumbnail_url(get_the_ID(),'medium'); ?>)"></div>
              <?php else : ?>
              <div class="contact__item__img" style="background-image: url(<?php echo get_template_directory_uri() . '/dist/images/person-placeholder.png'; ?>)"></div>
              <?php endif; ?>
                <div class="contact__item__cat">
                  <?php
                   $peoplecat = get_the_terms(get_the_ID(),'people-cat');
                   $i = 0;
                   foreach($peoplecat as $people) {
                     if($i > 0) {
                       echo ', ';
                     }
                     echo $people->name;
                     $i++;
                   }
                  ?>
                </div>
                <div class="contact__item__name">
                  <?php the_title(); ?>
                </div>
                <?php if(!empty(get_field('title'))) : ?>
                  <div class="contact__item__info">
                  <?php if(get_locale() === 'fi') {
                    echo get_field('title', $post->ID);
                  } else {
                    echo get_field('title_eng', $post->ID);
                  } ?>
                  </div>
                <?php endif; ?>
                <?php if(!empty(get_field('puh'))) : ?>
                  <div class="contact__item__info">
                    <?php echo get_field('puh'); ?>
                  </div>
                <?php endif; ?>
                <?php if(!empty(get_field('info'))) : ?>
                  <div class="contact__item__info">
                  <?php if(get_locale() === 'fi') {
                      echo get_field('info', $post->ID);
                    } else {
                      echo get_field('info_eng', $post->ID);
                    } ?>
                  </div>
                <?php endif; ?>
              </div>
              <?php array_push($staff_list, get_the_title()); ?>
            <?php }
            echo '<div class="contact__item filler">';
          } else {}

          wp_reset_postdata(); ?>
        </div>
        <?php update_field('field_606b2a51aa5d3', implode(', ', $staff_list), get_the_ID()); ?>
        <button class="btn load-more" style="display: none;">Lataa lisää</button>

      </div>

    </div>

  </div>

<?php
get_footer();
