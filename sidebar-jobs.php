<?php
/**
 * Sidebar for job information
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package kauppakamari
 */

// Setup the post
global $post;
global $pretend_id;

if (!empty($pretend_id) && is_numeric($pretend_id)) {
    $post = get_post($pretend_id);
    setup_postdata($post);
}

// Getting the job categories
$job_cats = get_the_terms($post->ID, 'job_category');
?>

<aside id="job-info" class="job-info-sidebar">

    <section class="job-information-short">

        <h3 class="job-title">Avoin työpaikka</h3>

        <span class="location"><strong>Sijainti: </strong><?php if(!empty($post->locations['city'])) echo $post->locations['city']; else echo '-'; ?></span>
        <span class="publish-date"><strong>Julkaistu: </strong><?php if(!empty($post->dates['publish_date'])) echo $post->dates['publish_date']; else echo '-'; ?></span>
        <span class="ends-date"><strong>Haku päättyy: </strong><?php if(!empty($post->dates['end_date'])) echo $post->dates['end_date']; else echo '-'; ?></span>

        <?php if( !empty($job_cats) ) :?>
            
            <div class="job-categories">
                <?php foreach($job_cats as $cat) : ?>
                    <span class="category-pill"><?php echo $cat->name; ?></span>
                <?php endforeach; ?>
            </div>

        <?php endif; ?>
    </section> <!-- .job-information end -->

    <section class="job-information-more">

        <a class="apply-btn" href="<?php echo $post->origin_link; ?>" target="_blank" rel="noreferrer noopener">Hae työpaikkaa</a>

        <div class="depth-info">
            <span class="location">
                <strong>Sijainti: </strong>
                <?= $post->locations['full_address']; ?>
                <?php echo !empty($post->locations) ? $post->locations['address'] . ', ' . $post->locations['postal_code'] . ' ' . $post->locations['city'] : '-';?>
            </span>
            <span class="publish-date">
                <strong>Julkaistu:</strong><br>
                <?php echo !empty($post->dates['publish_date']) ? $post->dates['publish_date'] : '-'; ?>
            </span>
            <span class="ends-date">
                <strong>Haku päättyy:</strong><br>
                <?php echo !empty($post->dates['end_date']) ? $post->dates['end_date'] : '-';?>
            </span>
            <?php if(!empty(get_field('job_expertise_side'))) : ?>
                <span class="expertise">
                    <strong>Osaamisalue:</strong><br>
                    <?php echo get_field('job_expertise_side'); ?>
                </span>
            <?php endif; ?>
            <?php if(!empty($post->type)) : ?>
                <span class="job-type">
                    <strong>Työsuhteen tyyppi:</strong><br>
                    <?php echo !empty($post->type) ? $post->type : '-'; ?>
                </span>
            <?php endif; ?>
            <?php if(!empty($post->job_time)) : ?>
                <span class="working-hours">
                    <strong>Työaika:</strong><br>
                    <?php echo !empty($post->job_time) ? $post->job_time : '-'; ?>
                </span>
            <?php endif; ?>
        </div>

    </section> <!-- In-depth .job-information end -->
     
</aside>