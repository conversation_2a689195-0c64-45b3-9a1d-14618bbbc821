@font-face {
  font-family: "icomoon";
  src: url("./icomoon.woff?mr8c4r") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"],
[class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "icomoon" !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-arrow-left:before {
  content: "\e909";
}
.icon-arrow-right:before {
  content: "\e90a";
}
.icon-minus:before {
  content: "\e907";
}
.icon-plus:before {
  content: "\e908";
}
.icon-angle-down:before {
  content: "\e905";
}
.icon-angle-up:before {
  content: "\e906";
}
.icon-external:before {
  content: "\e904";
}
.icon-up-right-from-square-solid:before {
  content: "\e903";
}
.icon-checkmark:before {
  content: "\e900";
}
.icon-cancel:before {
  content: "\e901";
}
.icon-grid:before {
  content: "\e902";
}
.icon-chevron-left:before {
  content: "\f053";
}
.icon-chevron-right:before {
  content: "\f054";
}
.icon-external-link:before {
  content: "\f08e";
}
.icon-facebook:before {
  content: "\ea90";
}
.icon-facebook2:before {
  content: "\ea91";
}
.icon-instagram:before {
  content: "\ea92";
}
.icon-twitter:before {
  content: "\ea96";
}
.icon-flickr:before {
  content: "\eaa5";
}
.icon-linkedin:before {
  content: "\eac9";
}
.icon-linkedin2:before {
  content: "\eaca";
}
