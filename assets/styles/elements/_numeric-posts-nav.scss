/* ==========================================================================
 # Numeric posts navigation
========================================================================== */

.numeric-navigation {
  display: block;
  text-align: center;
  margin: 1.5rem 0;
  ul {
    @include list-reset;
  }

  &__item {
    display: inline-block;
    margin: .5rem .125rem;
    font-size: 16px;
    a {
      @include transition;
      background: #D2E4F3;
      color: $primary;
      display: inline-block;
      line-height: 1;
      min-width: 2rem;
      padding: .5rem;
      text-decoration: none;
      border-radius:1rem;
      font-size: 16px;
      &:hover, &:focus, &:active {
        background: #002663;
        color: white;
      }
    }

    &--previous {
      svg {
        // turn caret othwer way around
        transform: rotate(180deg);
      }
      a, a:hover, a:active, a:focus {
        background: transparent;
        color: #002663!important;
      }
      a:hover {
        text-decoration: underline;
      }
      a:after {
        content: "Edellinen";
        background: transparent;
      }
    }

    &--next {
      a, a:hover, a:active, a:focus {
        background: transparent;
        color: #002663!important;
      }
      a:hover {
        text-decoration: underline;
      }
      a:before {
        content: "Seuraava";
        background: transparent;
      }
    }

    &--pagenum {

    }

    &--active {

      a {
        background: $primary;
        color: $white;
        &:hover, &:focus, &:active {
          background: $primary;
        }
      }

    }

    &--separator {

    }

  }

}
