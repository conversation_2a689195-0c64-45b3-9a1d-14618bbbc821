/* ==========================================================================
 # Article filter 2022
========================================================================== */


.article-filter-container {

}


.article-filter {

  &--post-types {
  }

  .mobile-only {
    display: none !important;

    @include breakpoint($sub-s) {
      display: block !important;
    }
  }

  label {
    &.filter-label {
      margin-bottom: 8px;
    }
  }

  .filter-label {
    display: block;
    width: 100%;
    text-align: center;
    font-weight: $ws-semibold;
    font-size: 18px;
    color: $primary;
    margin-top: 8px;
    margin-bottom: 20px;
    @include breakpoint($sub-s) {
      text-align: left;
    }
  }
  @include breakpoint($sub-s) {
    p.filter-label {
      display: none;
    }
  }

  .article-filter--post-types > p.filter-label {
    margin-bottom: 20px;
  }

  .article-filter--select {
    width: 100%;
    border: none;
    border: 2px solid $light-blue;
    font-weight: 600;
    font-size: 18px;
    text-transform: uppercase;
    color: #002662;
    margin-bottom: 28px;
  }

  &--tabs {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    list-style: none;
    list-style-type: none;
    margin-block-start: 0px;
    margin-block-end: 0px;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    padding-inline-start: 0px;
    margin-left: 10%;
    margin-right: 10%;
    margin-bottom: 48px;
    @include breakpoint($sub-s) {
      display: none;
    }

    li {
      flex-grow: 0;
      max-height: 30px;
      margin-left: 0;
      border-bottom: 3px solid #E1E1E1;
      margin-top: 8px;

      &.active {
        border-bottom: 3px solid $primary;
        a {
          color: $primary;
        }
      }
      &:hover, &:focus {
        a {
          color: $primary;
        }
      }

      a {
        text-transform: uppercase;
        text-decoration: none;
        font-weight: 600;
        font-size: 19px;
        color: #6D6D6D;
        padding-right: 15px;
        padding-left: 15px;
        &:focus {
          color: $primary;
        }
      }
      /*
      &:not(:first-child) a {
      }
      */
    }

  }

  &--category {

  }
  &--pills {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    list-style: none;
    list-style-type: none;
    margin-block-start: 0px;
    margin-block-end: 0px;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    padding-inline-start: 0px;
    margin-left: 10%;
    margin-right: 10%;
    margin-bottom: 40px;
    @include breakpoint($sub-s) {
      display: none;
    }

    li {
      /*display: inline-block;*/
      margin-left: 10px;
      margin-bottom: 18px;

      a {
        border-radius: 5rem;
        display: inline-block;
        font-size: 14px;
        font-weight: 600;
        line-height: 1.25;
        padding: $gap-small $gap;
        text-decoration: none;
        vertical-align: baseline;
        background-color: #D2E4F3;
        color: #002662;

      }
      &.active a, &.active:hover a  {
        color: #fff;
        background-color: #002662;

      }
      &:hover a {
        background-color: $blue-bg-hover; /* #B0C8E7; */
        color: #002662;
      }
    }
  }

  .article-filter--category.post-type-tiedotteet {
    display: none;
  }

}
/*article-filter */
