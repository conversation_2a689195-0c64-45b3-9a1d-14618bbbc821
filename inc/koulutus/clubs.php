<?php

// Register Custom Post Type
function kauppakamari_clubs_post_type() {

	$labels = array(
		'name'                  => _x( 'Klubit', 'Post Type General Name', 'kauppakamari' ),
		'singular_name'         => _x( 'Klubi', 'Post Type Singular Name', 'kauppakamari' ),
		'menu_name'             => __( 'Klubit', 'kauppakamari' ),
		'name_admin_bar'        => __( 'klubi', 'kauppakamari' ),
		'archives'              => __( 'Item Archives', 'kauppakamari' ),
		'attributes'            => __( 'Item Attributes', 'kauppakamari' ),
		'parent_item_colon'     => __( 'Parent Item:', 'kauppakamari' ),
		'all_items'             => __( 'Kaikki', 'kauppakamari' ),
		'add_new_item'          => __( 'Lisää uusi', 'kauppakamari' ),
		'add_new'               => __( 'Lisää uusi', 'kauppakamari' ),
		'new_item'              => __( 'Uusi', 'kauppakamari' ),
		'edit_item'             => __( 'Muokkaa', 'kauppakamari' ),
		'update_item'           => __( 'Päivitä', 'kauppakamari' ),
		'view_item'             => __( 'Katso', 'kauppakamari' ),
		'view_items'            => __( 'Katso', 'kauppakamari' ),
		'search_items'          => __( 'Etsi', 'kauppakamari' ),
		'not_found'             => __( 'Ei löytynyt', 'kauppakamari' ),
		'not_found_in_trash'    => __( 'Not found in Trash', 'kauppakamari' ),
		'featured_image'        => __( 'Featured Image', 'kauppakamari' ),
		'set_featured_image'    => __( 'Set featured image', 'kauppakamari' ),
		'remove_featured_image' => __( 'Remove featured image', 'kauppakamari' ),
		'use_featured_image'    => __( 'Use as featured image', 'kauppakamari' ),
		'insert_into_item'      => __( 'Insert into item', 'kauppakamari' ),
		'uploaded_to_this_item' => __( 'Uploaded to this item', 'kauppakamari' ),
		'items_list'            => __( 'Items list', 'kauppakamari' ),
		'items_list_navigation' => __( 'Items list navigation', 'kauppakamari' ),
		'filter_items_list'     => __( 'Filter items list', 'kauppakamari' ),
	);
	$rewrite = array(
		'slug'                  => 'klubi',
		'with_front'            => true,
		'pages'                 => true,
		'feeds'                 => true,
	);
	$args = array(
		'label'                 => __( 'Kouluttaja', 'kauppakamari' ),
		'description'           => __( 'Post Type Description', 'kauppakamari' ),
		'labels'                => $labels,
		'supports'              => array( 'title', 'editor', 'thumbnail' ),
		'taxonomies'            => array( 'category' ),
		'hierarchical'          => false,
		'public'                => false,
		'show_ui'               => true,
		'show_in_menu'          => true,
		'menu_position'         => 5,
		'menu_icon'             => 'dashicons-businessperson',
		'show_in_admin_bar'     => true,
		'show_in_nav_menus'     => true,
		'can_export'            => true,
		'has_archive'           => false,
		'exclude_from_search'   => true,
		'publicly_queryable'    => true,
		'rewrite'               => $rewrite,
		'capability_type'       => 'page',
		'show_in_rest'          => false,
	);
	register_post_type( 'club', $args );

}
add_action( 'init', 'kauppakamari_clubs_post_type', 0 );
