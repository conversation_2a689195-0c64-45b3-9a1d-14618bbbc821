<?php

/**
 * new_members - no need for ACF fields
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */


$new_members_cache_dir = wp_upload_dir();
$new_members_cache_file_path = $new_members_cache_dir['basedir'].'/new_members.htm';

if (!defined('NEW_MEMBERS_CACHE_FILE_PATH')) {
  define('NEW_MEMBERS_CACHE_FILE_PATH', $new_members_cache_file_path);
}


if(!function_exists('DOMinnerHTML')) {
  function DOMinnerHTML(DOMNode $element)
  {
    $innerHTML = "";
    $children = $element->childNodes;
    foreach ($children as $child)
    {
      $innerHTML .= $element->ownerDocument->saveHTML($child);
    }
    return $innerHTML;
  }
}

if(!function_exists('getElContentsByTagClass')) {
  function getElContentsByTagClass($html,$tag,$class)
  {
    $doc = new DOMDocument();
      @$doc->loadHTML($html);//Turn the $html string into a DOM document
      $els = $doc->getElementsByTagName($tag); //Find the elements matching our tag name ("div" in this example)
      foreach($els as $el)
      {
        //for each element, get the class, and if it matches return it's contents
        $classAttr = $el->getAttribute("class");
        if(preg_match('#\b'.$class.'\b#',$classAttr) > 0) return DOMinnerHTML($el);
      }
    }
  }

  if(!function_exists('members_data_needs_update')) {
    function members_data_needs_update() {
      $file_path = NEW_MEMBERS_CACHE_FILE_PATH;
      $time_now = time();
      $offset = 60 * 60 * 16; //last number is hours
      $t = filemtime($file_path);
      //if updated inside offset
      if($t  > $time_now - $offset) {
        return false;
      }
      return true;
    }
  }

  if(!function_exists('save_new_members_data')) {
    function save_new_members_data($members_html_data) {
      if(strlen($members_html_data) > 20) {
        $file_path = NEW_MEMBERS_CACHE_FILE_PATH;
        file_put_contents($file_path, $members_html_data);
      }
    }
  }

  if(!function_exists('fetch_new_members_html')) {
    function fetch_new_members_html() {
      $base_url ='https://www.kauppakamariverkosto.fi/';
      $full_url = $base_url;

      $curl = curl_init($full_url);
      curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
      curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
      curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
      curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
      //curl_setopt($ch, CURLOPT_HEADER, false);
      curl_setopt($curl, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US) AppleWebKit/534.10 (KHTML, like Gecko) Chrome/8.0.552.224 Safari/534.10');
      $html = curl_exec($curl);
      curl_close($curl);


      $dom = new DOMDocument();
      @$dom->loadHTML($html);
      $xpath = new DOMXPath($dom);

      $scroller_inner_html = getElContentsByTagClass($html,'div','uudet-jasenet-carousel');

      //save also
      save_new_members_data($scroller_inner_html);

      return $scroller_inner_html;
    }
  }


  /* gets the product data from single collection */
  if(!function_exists('get_new_members_html')) {
    function get_new_members_html() {
      $file_path = NEW_MEMBERS_CACHE_FILE_PATH;

    //DEBUG 0
      if(file_exists($file_path)) {
        if(!members_data_needs_update()) {
          error_log('New members data does not need update ');
          $file_content = file_get_contents($file_path);
          if ( is_string( $file_content ) && $file_content !== '' ) {
            return $file_content;
          }
          else {
            error_log('New members data was empty');
            return '';
          }
        }
      }
      error_log('New members data NEEDS update ');

    //fetch first time or update:
    return fetch_new_members_html(); //returns string
  }
}






// Create id attribute allowing for custom "anchor" value.
$id = 'block-new_members-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-new_members';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$new_members_block_settings = get_field('new_members');
$new_members_title = $new_members_block_settings['title'] ?? 'Uudet jäsenet';
echo '<div class="block-new_members-container">';
//if(!empty($new_members_title)) echo '<h2>'.$new_members_title.'</h2>';
?>

  <div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">
    <?php
      echo get_new_members_html();
    ?>

  </div>
</div>

<style type="text/css">

  .block-new_members-container {
    display: block;
    padding-top: 10px;
    padding-bottom: 60px;
  }

  .block-new_members-container h2 {
    display: block;
    text-align: center;
    width: 100%;
  }


  .block-new_members {
    padding-top: 10px;
    padding-bottom: 30px;
    /*overflow: hidden;*/
  }

  .block-new_members .slick-initialized .slick-slide {
    display: block;
    flex-wrap: unset;
    padding-bottom: 1rem;
  }


  .block-new_members .border-shadow {
    box-shadow: 0 1px 4px 0px rgba(0,0,0,0.2);
    min-height: 100px;
    margin-top:  6px;
  }

  .block-new_members .py-xs-100,
  .block-new_members .mb-xs-100 {
    padding-top: 1rem !important;
    padding-bottom: 0.5rem !important;
    margin-right: 1rem !important;
    margin-left: 1rem !important;
    display: block;
    width: 340px;
  }

  .block-new_members .col-sm-6 {
      -webkit-box-flex: 0;
      flex: 0 0 50%;
      max-width: 50%;
  }

  .block-new_members a {
    min-height: 60px;
    min-width: 120px;

    color: #000;
  }
  .block-new_members > a:hover,
  .block-new_members a:hover strong {
    text-decoration: underline;
  }

  .block-new_members .slick-list {
    overflow-x: hidden;
    min-height: 108px;
  }


  .block-new_members a .slide div div strong:after {
    position: absolute;
    background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23000000' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-position: center;
    background-size: 82%;
    width: 1em;
    height: 1.3em;
    display: inline-block;
    content: " ";
    margin-left: 6px;
    color: transparent;
    background-repeat: no-repeat;
  }


  @media (max-width: 575px) {
    .col-xs-12 {
        float: left;
        width: 100%;
    }

  }

  @media (min-width: 576px) {
    .col-sm-6 {
        -webkit-box-flex: 0;
        flex: 0 0 50%;
        max-width: 50%;
    }
  }
  @media (min-width: 544px) {
    .col-sm-6 {
        float: left;
        width: 50%;
    }
  }

  .block-new_members .slick-next {
    margin-right: 32px;
    opacity: 0.4;
    background-color: transparent!important;
  }

  .block-new_members .slick-prev {
    margin-left: 32px;
    opacity: 0.4;
    background-color: transparent!important;
  }
  .block-new_members .control-c:hover {
    opacity: 1;
  }

  @media (max-width: 766px) {
    .block-new_members .slide strong {
      margin-left: 40px;
      display: block;
    }
  }



  /* admin */

  .acf-block-preview .block-new_members-container {
    overflow: hidden;
    width: 98%;
  }
  .acf-block-preview .block-new_members {
    overflow-x: hidden;
    width: 9000px;
  }

  .acf-block-preview .block-new_members > * {
    float: left;
    display: inline-block;
  }
</style>
