.block-presentations {
  &-header {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  &-item {
    margin-bottom: 2rem;
  }

  &-date {
    align-self: flex-end;
    color: $grey-darker;
    font-weight: 600;
  }

  &-recording {
    background-color: $orange;
    border-radius: 25px;
    padding: 10px 20px;
    color: white;
    display: flex;
    align-items: center;
    text-decoration: none;
    svg {
      margin-right: 1rem;
    }
  }
  &-title {
    margin-top: 0;
    padding-top: 0;
    font-weight: bold;
    width: 100%;
  }
  &-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    &-title {
      border-bottom: 2px solid $grey-darker;
      width: 100%;
      font-weight: bold;
      text-transform: uppercase;
      margin-bottom: 1rem;
      padding-bottom: 1rem;
    }
    &-item {
      display: flex;
      align-items: center;
      width: 100%;
      color: $base-font-color;
      text-decoration: none;
      border-bottom: 2px solid $grey-light;
      margin-bottom: 1rem;
      padding-bottom: 1rem;
      @include breakpoint($m) {
        width: calc((100%/2) - 1rem);
      }
      svg {
        width: 1.5rem;
        height: 1.5rem;
        margin-right: 2rem;
        fill: $grey-dark;
        color: $grey-darker;
        transition: .1s ease-in-out;
      }
      &-title {
        font-weight: 600;
        margin-bottom: .5rem;
      }
      &-presenter-name {
        display: inline-block;
        color: $grey-darker;
        font-weight: 600;
        border-right: 2px solid $grey-darker;
        margin-right: 1rem;
        padding-right: 1rem;
        line-height: 0.8rem;
      }
      &-presenter-title {
        display: inline-block;
        color: $grey-darker;
        font-weight: 400;
      }
      &:hover {
        svg {
          color: black;
        }
      }
    }
  }
}
