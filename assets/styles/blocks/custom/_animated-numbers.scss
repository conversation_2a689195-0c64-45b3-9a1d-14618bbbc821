.block-animated-numbers {
    display: flex;
    flex-direction: column;
    position: relative;

    &.has-bg {
        padding: 3rem 0;
        &:before {
            content: " ";
            position: absolute;
            z-index: -1;
            top: 0;
            bottom: 0;
            left: -100%;
            right: -100%;
            background: $light-blue-25;
        }
    }

    &__title-wrapper {
        text-align: left;
    }
    &__subtitle {
        font-size: 18px;
        font-weight: $ws-medium;
    }
    &__link-wrapper {
        text-align: center;
        padding: 1rem 0;
    }
    &__link {
        @include bigtextlink;
        width: fit-content;
    }
    &__items {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        width: 100%;
        margin: 3rem 0;
    }
    &__item {
        display: flex;
        justify-content: center;
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 2rem;
        margin-top: 2rem;
        .item__body {
            max-width: 260px;
            width: 260px;
            margin: 0;
            text-align: center;
            span {
                font-size: 3rem;
                font-weight: $ws-bold;
            }
            img {
                //max-width: 150px;
                width: 100%;
                max-height: 150px;
                margin-bottom: 1.5rem;
            }
            p {
                width: 100%;
                font-size: 18px !important;
                font-weight: $ws-medium;
                color: $primary;
                margin: 0;
            }
            // Animated number styles
            .number-wrapper {
                display: flex;
                justify-content: center;
                /*.animated-number {
                    position: relative;
                    display: flex;
                    justify-content: space-between;
                    width: 0;
                    max-width: max-content;
                    overflow: hidden;
                    height: 48px;
                    margin: .5rem 0;
                    @include breakpoint($m) {
                        margin: 1rem 0;
                    }
                    //transition: opacity .25s linear;
                    > span {
                        z-index: -1;
                        display: flex;
                        // text-align: center;
                        flex-direction: column;
                        opacity: 0;
                        flex-shrink: 2;
                        width: 1px;
                        font-size: 2.5rem;
                        font-weight: $ws-bold;
                        position: absolute;
                        right: 0;
                        line-height: 48px;
                        transition: transform 1.5s ease;
                        @include breakpoint($m) {
                            font-size: 3rem;
                        }
                    }
                    > span.visible {
                        z-index: 1;
                        position: relative;
                        // width: 28px;
                        width: max-content;
                        opacity: 1;
                        flex-shrink: 1;
                    }
                }*/
            }
        }
    }
    & .mobile-separator {
        border-bottom: 2px solid $coral;
        width: 85%;
        margin: auto;
    }
}

@include breakpoint($s) {
    .block-animated-numbers {
        &__item {
            justify-content: center;
            gap: 3rem;
            .item__body {
                p {
                    font-size: 20px !important;
                }
            }
        }
    }
}

@include breakpoint($m) {
    .block-animated-numbers {
        &__title-wrapper {
            text-align: start;
        }
        &__subtitle {
            font-size: 22px;
        }
        &__item {
            flex: 0 0 50%;
            max-width: 50%;
            justify-content: space-evenly;
            border-bottom: none;
            //border-right: 2px solid $coral;
            &:last-of-type {
                //border-right: 2px solid transparent;
            }
        }
        & .mobile-separator {
            display: none;
        }
    }
}

@include breakpoint($l) {
    .block-animated-numbers {
        &__item {
            flex: 0 0 33.33%;
            max-width: 33.33%;
            gap: 0.5rem;
        }
    }
}

@include breakpoint($xl) {
    .block-animated-numbers {
        &__item {
            .item__body {
                span {
                    font-size: 3rem;
                }
            }
        }
    }
}