<?php

/**
 * Teaser blocks
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-teaser-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-teaser';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}
?>

<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">

  <?php
    if($teasers = get_field('teasers')) :
      foreach($teasers as $teaser) : ?>
        <div class="block-teaser__item">
          <?php if($teaser['img']) {
              echo '<img src="' . $teaser['img']['sizes']['hero_sm'] . '" />';
            }
          ?>
          <?php if($teaser['title']) {
              echo '<h3 class="block-teaser--title h3">' . $teaser['title'] . '</h3>';
            }
          ?>
          <?php if($teaser['content']) {
              echo '<div class="block-teaser--content">' . $teaser['content'] . '</div>';
            }
          ?>
          <?php if($teaser['link']) {
              echo '<a href="' . $teaser['link']['url'] . '" >' . $teaser['link']['title'] . '</a>';
            }
          ?>
        </div>

      <?php endforeach;
    endif;
  ?>

</div>
