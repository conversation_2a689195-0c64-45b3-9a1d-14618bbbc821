<?php

/**
 * highlight 1
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'highlight-info-' . $block['id'];
if (!empty($block['anchor'])) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'highlight-info';
if (!empty($block['className'])) {
    $className .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$item = get_field("group");

?>

<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?> <?php // Background color
                                                                                    if ($item['background-color'] == "Vaaleansininen") echo 'background-lightblue';
                                                                                    elseif ($item['background-color'] == "Roosa") echo 'background-coral';
                                                                                    else {
                                                                                        echo 'background-white';
                                                                                    } ?> <?php
                                            if ($item['img_side'] == "Oikea") echo 'img-right';
                                            elseif ($item['img_side'] == "Vasen") echo 'img-left'; ?>"
    <?php // Image width
    if ($item['img_side'] == "Oikea") echo 'style="grid-template-columns: auto ' . $item['img_width'] . '% !important"';
    else echo 'style="grid-template-columns:' . $item['img_width'] . '% auto !important"'; ?>>
    <div
        class="highlight-info__image-wrapper image-wrapper"
        style="background-image: url(<?php echo $item['image']['url']; ?>)">
    </div>
    <div class="highlight-info__content block-content">
        <img
            class="addition-graphic <?php
                                    if ($item['show_icon'] == false) {
                                        echo 'is-hidden';
                                    } ?>"
            src="<?php echo get_template_directory_uri() . '/dist/images/text-image-ratas.svg'; ?>"
            alt="" />
        <span class="highlight-info__title" <?php if (!$item['body']) echo 'style="margin-bottom: 1.5rem;"' ?>>
            <?php echo $item['title'] ?: "Otsikko"; ?>
        </span>
        <?php if ($item['body']) { ?>
            <div class="highlight-info__body">
                <p>
                    <?php echo $item['body']; ?>
                </p>
            </div>
        <?php } ?>

        <?php if ($item['link']) { ?>
            <div class="button-wrapper">
                <a class="highlight-info__button <?php
                                                    if ($item['link_type'] == 'Tekstilinkki') echo 'link_text';
                                                    else echo 'link_button'; ?> 
			<?php
            if ($item['button_link_color'] == 'koralli') echo 'coral';
            else echo 'royalblue'; ?>
			<?php
            if (!empty($item['is_external'])) echo 'external'; ?>" href="<?php echo $item['link']['url']; ?>">
                    <span><?php echo $item['link']['title']; ?></span></a>
            </div>
        <?php } ?>
    </div>

    <style>
        /* purkka */
        #<?php echo esc_attr($id); ?> {
            <?php echo 'margin-top:' . $item['margin_top'] . 'px !important;'; ?>
        }
    </style>
</div>