.price-text {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;


  &__item {
    width: 100%;
    margin-bottom: 4rem;
    text-align: left;
    display: flex;
    justify-content: flex-start;
    @include breakpoint($l) {


      width: calc(50% - 1.5rem);
    }
    .title {
      font-weight: $ws-semibold;
      display: block;
      font-size: 22px;
      margin-top: 1.5rem;
      margin-bottom: 1.5rem;
      @include breakpoint($l) {
        margin-top: 0;
      }
    }
  }
  img {
    width: auto;
    max-height: 100%;
  }
  &__number {
    font-weight: bold;
    font-size: 1.5rem;
  }
  &__price {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding-top: 1rem;
    background: transparent linear-gradient(180deg, $coral-25 0%, $coral-25 100%) 0% 0% no-repeat padding-box;
    border: 1px solid #EFEFEF;
    position: relative;
    align-items: center;
    margin-right: 1.5rem;
    max-width: 185px;
    min-width: 185px;
    min-height: 325px;
    .info-wrapper {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }
    .price-text__type {
      margin-top: 0;
      margin-bottom: 0;
      font-weight: $ws-medium;
      font-size: 16px;
      text-transform: uppercase;
    }
    //@include breakpoint($l) {
      display: flex;
    //}
    &:before {
      bottom: 0px;
      border: 92px solid transparent;
      border-top: 0;
      border-bottom: 40px solid #EFEFEF;
      content: "";
      position: absolute;
      display: block;
      width: 0;
      right: 0;
    }
    &:after {
      bottom: -1px;
      border: 92px solid transparent;
      border-top: 0;
      border-bottom: 40px solid #fff;
      content: "";
      position: absolute;
      display: block;
      width: 0;
      right: 0;
      //transform: translate(-50%,calc(-100% - 5px));
  }
  }
  &__img {

    max-height: 120px;
    min-height: 120px;
    height: 100%;
    // padding: 1rem;
    margin-bottom: .5rem;

    @include breakpoint($l) {
      max-height: 143px;
      min-height: 143px;

    }
  }
  a {
    @include textlink;
    font-size: 16px !important;
  }
  &__content {
     p{font-size: 16px;}
    a {
      @include textlink;
    }
    @include breakpoint($l) {

    }
  }
}

// mobile
@include breakpoint($sub-s) {
  .price-text {
    &__item {
      border-bottom: 2px solid $light-blue-50;
      margin-bottom: 6rem;
      flex-direction: column;
    }
    &__price {
      background: $coral-25;
      // flex-wrap: wrap;
      min-width: 100%;
      max-width: 100%;
      min-height: 150px;
      margin: 0;
      gap: 0;
      padding: 1.5rem 0.75rem;
      border: none;
      justify-content: center;
      flex-direction: row;
      .info-wrapper {
        display: flex;
        flex-direction: column;
        width: 60%;
        gap: 1rem;
      }
      &:after, &:before {
        display: none;
      }
    }
    &__type, &__number, &__link, &__img {
      //width: 100%;
      text-align: center;
    }
    &__img {
      width: 40%;
      margin: 0;
      order: 2;
      max-height: 100px;
      min-height: 100px;
      height: 100%;
    }
    &__content {
      .title {
        font-size: 22px;
        font-weight: $ws-semibold;
        margin: 0 0 1rem;
      }
      text-align: start;
      padding: 0.75rem 1rem 1rem;
      border-top: 0;
    }
  }
}
