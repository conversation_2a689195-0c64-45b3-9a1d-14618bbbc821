.block-teaser {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  &__item {
    min-width: calc((100% / 3) - 1.9rem);
    max-width: calc((100% / 3) - 1.9rem);
    width: 100%;
    margin-bottom: 2rem;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    @include breakpoint($sub-l) {
      min-width: 100%;
    }
  }
  img {
    margin-bottom: 2rem;
  }
  &--title {
    font-weight: 600;
    margin-bottom: 1.5rem;
    margin-top: 0px;
  }
  img + &--title {
    font-size: 22px;
    font-weight: 600;
  }
  &--content {
    font-size: 15px;
    margin-bottom: 1.5rem;
  }
  a {
    align-self: flex-start;
    @include textlink;
    width: 100%;
    text-align: center;
    border: 1px solid $primary;
    color: white;
    background: $primary;
    padding: 0.75rem;
    &:hover {
      background: #1d397f !important;
      border: 1px solid #1d397f !important;
      text-decoration: none;
    }
    &.external {
      background-color: $primary;
      border: 1px solid $primary;
      color: white;
      padding: 0.75rem 4.5rem 0.75rem 3rem;
      &:after {
        position: absolute;
        background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23ffffff' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E") !important;
        background-position: center;
        width: 1.1rem !important;
        height: 1rem !important;
        display: inline-block;
        content: " ";
        margin-left: 8px !important;
        margin-top: 0px !important;
        color: transparent;
        background-repeat: no-repeat;
      }
      &:hover {
        background-color: white;
        color: #002663;
        &:after {
          background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23002663' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E") !important;
        }
      }
    }
  }
}
