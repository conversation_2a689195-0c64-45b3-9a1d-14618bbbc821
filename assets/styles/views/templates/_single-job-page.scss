.single-job {
    .entry__content {
        &.wysiwyg {
            padding: 0 1.5rem;

            >* {
                max-width: $max-width-l;
            }

            &:after {
                display: none; // Disable weird hr on jobpost
            }
        }
    }

    .entry__header {
        max-width: $max-width-l;
        padding: 0 1.5rem;
    }

    .entry__footer {
        // Social share
        border-top: 3px solid $separatorblue;
        border-bottom: 3px solid $separatorblue;
        max-width: $max-width-l;
        width: auto;
        padding: 0;
        margin: 0 1.5rem;

        .social-share {
            display: flex;
            align-items: center;
            margin: 0.75rem 0;

            &__title {
                margin-bottom: 0 !important;
            }

            &__link {
                background: transparent;
                margin-bottom: 0;

                svg {
                    fill: $primary;
                    width: 1.5rem;
                    height: 1.5rem;
                }
            }
        }
    }
}

.job-template-default {
    .breadcrumb-container {
        margin-top: 3rem;
    }

    #primary {
        @include wrap();
        display: flex;
        margin-top: 1rem;
        padding-bottom: 3rem !important;

        aside {
            width: 25%;
            order: 0;

            .job-information-short {
                // Info with border
                border: 3px solid $light-blue-50;
                display: flex;
                flex-direction: column;
                padding: 3rem 2rem;
                font-size: 16px;

                >span {
                    margin-bottom: 1rem;
                }

                .job-title {
                    margin-top: 0;
                }

                .location,
                .publish-date,
                .ends-date {
                    strong {
                        text-transform: uppercase;
                    }
                }

                .job-categories {
                    display: flex;
                    flex-direction: column;
                    row-gap: 1rem;
                    margin-top: 1rem;

                    .category-pill {
                        border-radius: 5rem;
                        display: inline-block;
                        font-size: 12px;
                        font-weight: 600;
                        text-transform: uppercase;
                        padding: 0.25rem 1rem;
                        text-decoration: none;
                        vertical-align: baseline;
                        background-color: #d2e4f3;
                        color: #002662;
                        width: max-content;
                    }
                }
            }

            // Info with border end

            .job-information-more {
                margin: 2rem;

                .apply-btn {
                    @include button;
                    // Overriding some mixin styles
                    width: 100% !important;
                }

                .depth-info {
                    display: flex;
                    flex-direction: column;
                    font-size: 16px;
                    margin-top: 4rem;

                    span {
                        strong {
                            text-transform: uppercase;
                            margin-bottom: 0.5rem;
                        }

                        margin-bottom: 1rem;
                    }
                }
            }
        }

        main {
            // main
            width: 75%;
            order: 1;
            padding-left: 3rem;

            .job__content-acf {
                background: $light-blue-25;
                display: flex;
                flex-direction: column;
                row-gap: 1rem;
                padding: 1.5rem 2.5rem;
                margin-top: 3rem;
                font-size: 16px;

                &:empty {
                    display: none;
                }
            }

            .apply-btn {
                @include button;
                margin: 3rem 0;
                padding-left: 3rem;
                padding-right: 3rem;
            }
        }

        // main end


    }

    #secondary {
        // Stuff below main content
        @include wrap();
        margin-bottom: 3rem;

        .secondary__columns {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .column {
                text-align: center;

                .col-btn {
                    @include button;
                    margin: auto;
                    padding-left: 2rem;
                    padding-right: 2rem;
                }
            }
        }
    }

    @include breakpoint($sub-xm) {
        #secondary {
            .secondary__columns {
                flex-direction: column;
                row-gap: 3rem;
            }
        }
    }

    @include breakpoint($sub-l) {

        // ipad
        #primary {
            flex-direction: column;

            main {
                width: 100%;
                padding-left: 0;
            }

            aside {
                width: 80%; // iso ?
                margin: auto;
            }

        }
    }

    @include breakpoint($sub-m) {

        // mobile
        #primary {
            aside {
                width: 100%;

                .job-information-more {
                    margin-left: 0;
                    margin-right: 0;

                    .depth-info {
                        margin-top: 2rem;
                    }
                }
            }

            .entry {
                &__header {
                    padding: 0;
                }

                &__content {
                    padding: 0;
                }
            }
        }
    }
}