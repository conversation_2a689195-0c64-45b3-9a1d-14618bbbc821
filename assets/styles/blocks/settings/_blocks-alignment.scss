/* ==========================================================================
 # Block alignment
========================================================================== */

/* Only front-end
----------------------------------------------- */

.wysiwyg {
  padding-left: $gap;
  padding-right: $gap;
  @include clearfix;
  @include child-margin-reset;

  // each block takes care of its own width
  & > *:not(.wp-block-hskk-hero-carousel) {
    margin-left: auto;
    margin-right: auto;
    max-width: $content-default-width;
  }

  .wp-block-hskk-hero-carousel {
    margin-left: -1.5rem;
    margin-right: -1.5rem;
  }

  & > .alignfull {
    margin-top: $gap-big;
    margin-bottom: $gap-big;
    margin-left: -$gap;
    margin-right: -$gap;
    max-width: none;
  }

  & > .alignwide {
    margin-top: $gap-big;
    margin-bottom: $gap-big;
    max-width: $max-width-l;
  }

  // float left
  .alignleft {
    @include breakpoint($content-fits) {
      float: left;
      margin-top: 0;
      margin-bottom: $gap;
      margin-right: $gap;
      max-width: calc(50% - $gap-small);
    }
  }

  // float right
  .alignright {
    @include breakpoint($content-fits) {
      float: right;
      margin-top: 0;
      margin-bottom: $gap;
      margin-left: $gap;
      max-width: calc(50% - $gap-small);
    }
  }
}
.wysiwyg .gform_wrapper {
  margin-left: auto;
  margin-right: auto;
  max-width: $content-default-width;
}

.single .wysiwyg {
  & > * {
    max-width: $max-width-m;
  }
}

.single-job .wysiwyg {
  & > * {
    max-width: $max-width-l;
  }
}

.are-vertically-aligned-center {
  align-items: center;
}
.is-vertically-aligned-center {
  align-self: center;
}

// Backend
.post-type-post {
  .editor-styles-wrapper {
    .wp-block {
      max-width: #{$max-width-m + $gap-big};
    }
  }
}
.post-type-page {
  .editor-styles-wrapper {
    .wp-block {
      max-width: $content-default-width;
    }
  }
}
