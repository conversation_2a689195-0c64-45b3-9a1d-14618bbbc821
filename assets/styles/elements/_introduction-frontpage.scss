// KoulutusOnline etusivu hero pienillä muokkauksilla

@media screen and (max-width: $l) {
	.introduction {
		padding: 20px;
	}
}

.introduction {
	// margin-bottom: 8rem;
	background: $light-blue-25;
	padding: 20px;
	//overflow-x: hidden;
	position: relative;
	&__title {
		font-size: 38px !important;
	}
	&__body {
		margin: 1rem 0 1.5rem;
		p {
			font-weight: $ws-medium !important;
			margin: 0;
		}
	}
	&:after {
		content: '';
		display: block;
		position: absolute;
		bottom: -13.5rem;
		right: -1rem;
		background-image: url('../images/HERO-ratas.svg');
		background-size: contain;
		background-position: right bottom;
		background-repeat: no-repeat;
		height: 54rem;
		width: 30rem;
        //height: 57rem;
        //width: 30rem;
		z-index: 1;
		@media (max-width: 900px) {
			top: -1rem;
			right: 0;
			height: 30rem;
			width: 11.25rem;
		}
		@media (max-width: $s) {
			top: 10rem;
			right: -0.5rem;
			height: 20rem;
			width: 11.25rem;
		}
	}
	.contents {
		display: grid;
		grid-template-rows: auto auto auto;
		align-content: center;
		z-index: 3;
		position: relative;
		margin: 2rem 0 2rem 0;
	}
	.contents > * {
		color: $primary;
	}
	.image-container {
		position: relative;
		overflow: visible;
		min-height: calc(360px + 2rem);
		z-index: 2;
		.addition-graphic {
			position: absolute;
			width: 120px;
			top: 5px;
			right: auto;
			left: 120px;
			z-index: 1000;
		}
		.image-wrapper {
			position: absolute;
			top: 0;
			right: auto;
			bottom: 0;
			left: -100px;
			margin: auto;
			// background: $primary;
			width: 360px;
			height: 360px;
			overflow: hidden;
			border-radius: 50%;
		}
	}
	&__image {
		min-width: 100%;
		min-height: 100%;
	}
	&__button {
		@include newbutton;
		padding-left: 3rem;
		padding-right: 3rem;
	}
	
	@media (min-width: $m) {
		// margin-bottom: 8rem;
	}
}

@media screen and (min-width: 900px) {
	.introduction {
		&__title {
			font-size: 42px !important;
			margin: 0 !important;
		}
		&__body {
			margin: 2rem 0 2.5rem;
			p {
				font-size: 20px !important;
			}
		}
		$marginNega: calc((100% - 100vw) / 2);
		$marginPosi: calc((100vw - 100%) / 2);
		margin-left: $marginNega;
		margin-right: $marginNega;
		padding: 35px;
		display: flex;
		justify-content: flex-start;
		// gap: 1rem;
		//	grid-template-columns: 1fr 1.5fr;

		.image-container {
			min-height: calc(480px + 2rem);
			width: 420px;
			flex-shrink: 0;
			.addition-graphic {
				width: 190px;
				top: 10px;
				right: 30px;
				left: auto;
			}	
			.image-wrapper {
				right: auto;
				left: -120px;
				width: 480px;
				height: 480px;
			}
		}
		.contents {
			margin-right: 2rem;
		}
	}
}

@media screen and (min-width: 1260px) {
	.introduction {
		grid-template-columns: 1fr 1fr;
		&__title {
			font-size: 72px !important;
		}
		.image-container {
			min-height: calc(600px + 2rem);
			width: 600px;
			.addition-graphic {
				width: 236px;
				top: 40px;
				right: 60px;
				left: auto;
			}	
			.image-wrapper {
				right: 8.5rem;
				left: auto;
				width: 600px;
				height: 600px;
				margin: 0;
			}
		}
		&__body {
			width: 65%;
		}
		.contents {
			max-width: 840px;
			// max-width: 960px;
			//margin-right: 2rem;
		}
	}
}