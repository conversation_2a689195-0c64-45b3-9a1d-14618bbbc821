/* ==========================================================================
  main.js
========================================================================== */
var searchTimeout = 0;
var hashChangeTimeout = 0;
var search_url;

var paramsString;
var searchParams;

function getQueryStringParam(paramName) {
  const params = new URLSearchParams(window.location.search);
  return params.get(paramName);
}

function getQueryStringArrayParam(paramName) {
  const params = new URLSearchParams(window.location.search);
  /*
  for (const key in params) {
    if (params.hasOwnProperty(key)) {
      console.log('Looping, ', key);
      console.log('val: ', params.key);
    }
  }
  */
  var post_category_array = [];

  for (var pair of params.entries()) {
    //console.log(pair[0]+ ', '+ pair[1]);
    if (pair[0] === "post_category[]") {
      post_category_array.push(pair[1]);
    }
  }

  //return params.get(paramName);
  return post_category_array;
}

function setQueryStringParam(paramName, val) {
  const params = new URLSearchParams(window.location.search);
  params.set(paramName, val);
  window.history.pushState({}, "", location.pathname + "?" + params);
  window.dispatchEvent(new Event("popstate"));
}

function setQueryStringArrayParam(paramName, val) {
  const params = new URLSearchParams(window.location.search);
  params.append(paramName, val);
  window.history.pushState({}, "", location.pathname + "?" + params);
  window.dispatchEvent(new Event("popstate"));
}

function setQueryStringArray(paramName, paramArray) {
  const params = new URLSearchParams(window.location.search);
  params.delete(paramName);
  for (var newVal of paramArray) {
    params.append(paramName, newVal);
    //console.log('added to paramArray', newVal, paramArray);
  }
  window.history.pushState({}, "", location.pathname + "?" + params);
  window.dispatchEvent(new Event("popstate"));
}

function getHashQueryStringParam(paramName) {
  const params = new URLSearchParams(window.location.hash.substr(1));
  return params.get(paramName);
}

window.addEventListener("popstate", function () {
  //console.log("URL CHANGED! popstate. hashChangeTimeout: ", hashChangeTimeout);
  //searchInit();
  if (hashChangeTimeout === 0) {
    clearTimeout(hashChangeTimeout);
    hashChangeTimeout = setTimeout(function () {
      //always wait small time
      searchInit();
    }, 100);
  } else {
    clearTimeout(hashChangeTimeout);
    hashChangeTimeout = setTimeout(function () {
      searchInit();
    }, 800);
  }
});

function emptyResults() {
  var height = $(".main.search-results-container").css("height");
  $(".main.search-results-container").html(
    '<div class="loader loader--big-margins">Ladataan...</div>'
  );
  $(".main.search-results-container").css("height", height);
}

function searchInit() {
  var params = new URLSearchParams(document.location.search);
  //params.get(paramName)
  emptyResults();

  //var s_php = getQueryStringParam('s');
  var s_php = params.get("s");
  //var s_js = getHashQueryStringParam('s');
  var s_js = getHashQueryStringParam("s");
  var people_id = params.get("people_id");

  var s_form_hidden = $("#search_key").val();
  var s_form_input = $(".main-search-input").val();
  var people_id_form_input = $("#people_id").val();
  s_js = s_php;

  if (s_form_hidden !== s_js) {
    $("#search_key").val(s_js);
  }
  if (s_form_input !== s_js) {
    $(".main-search-input").val(s_js);
  }

  if (people_id === null) {
    people_id = "";
  }
  if (people_id_form_input !== people_id) {
    $("#people_id").val(people_id);
  }

  //post_type
  var post_type = params.get("post_type");

  //select post type var

  $(".post_category_checkbox").prop("checked", false); //uncheck all checkboxes
  $(".main-category-list").addClass("hidden");
  //?$('input[name="post_category[]"]').prop('checked', false);

  if (typeof post_type == "string" && post_type !== "") {
    $(".main-category-list[data-slug=" + post_type + "]").removeClass("hidden");
    $('input[name="post_type"]').prop("checked", false);
    $('input[name="post_type"][value=' + post_type + "]")
      .first()
      .prop("checked", true);
  } else {
    //select "All" the first
    $('input[name="post_type"]').prop("checked", false);
    $('input[name="post_type"]').first().prop("checked", true); //eg. All
  }

  //post category checkboxes:

  //gets only one:
  //var post_category = params.get('post_category[]');
  var post_category = getQueryStringArrayParam("post_category[]");

  if (Array.isArray(post_category) && post_category[0] !== "") {
    //alert('Oli array osoiterivillä array of post_category');
    $('input[name="post_category[]"]').prop("checked", false);
    for (var post_category_slug of post_category) {
      $('input[name="post_category[]"][value=' + post_category_slug + "]")
        .first()
        .prop("checked", true);
    }
  } else {
    if (typeof post_category === "string" && post_category !== "") {
      $('input[name="post_category[]"]').prop("checked", false);
      $('input[name="post_category[]"][value=' + post_category + "]")
        .first()
        .prop("checked", true);
      // bug, what if same in multiple parents? or maybe cannot be because wordpress prevents
    } else {
      $('input[name="post_category[]"]').prop("checked", false);
      //$('input[name="post_category[]"]').first().prop('checked', true); //eg. All
    }
  }

  //0 DEBUG DEBUG
  if (0 && post_type !== "people" && people_id < 1 && s_js.length < 3) {
    noSearchMessage();
  } else {
    triggerSearch();
  }
}

function noSearchMessage() {
  $(".search-results-container").html(
    '<p class="no-search-made">Anna hakusana, vähimmäispituus 3 merkkiä</p>'
  );
}

function triggerSearch() {
  if (searchTimeout === 0) {
    //$('#main_search_form').submit();
    getSearchResults();
  } else {
    searchTimeout = setTimeout(function () {
      searchTimeout = 0;
      //$('#main_search_form').submit();
      getSearchResults();
    }, 1000);
  }
}

/**
 * Navigation
 */
aucor_navigation(document.getElementById("primary-navigation"), {
  desktop_min_width: 890, // min width in pixels
  menu_toggle: "#menu-toggle", // selector for toggle
});

/**
 * Responsive videos
 */
fitvids();

/**
 * Polyfill object-fit for lazyloaded
 */
if (typeof objectFitPolyfill === "function") {
  document.addEventListener("lazybeforeunveil", function (e) {
    // current <img> element
    var el = e.target;

    objectFitPolyfill();
    el.addEventListener("load", function () {
      objectFitPolyfill();
    });
  });
}

// External link handler - add icon
jQuery("a")
  .filter(function () {
    return this.hostname && this.hostname !== location.hostname;
  })
  .addClass("external")
  .attr("target", "_blank")
  .attr("aria-description", "ulkoinen linkki")
  .filter(function () {
    return $(this).has("img").length;
  })
  .addClass("external-img")
  .removeClass("external");

// Slick slider
jQuery(function () {
  // console.log('slicki', jQuery().slick)
  jQuery(".block-slides-container").slick({
    slidesToShow: 2,
    slidesToScroll: 1,
    appendArrows: ".block-slider-arrows",
    nextArrow:
      '<button class="slider-arrow-container" aria-label="Seuraava uratarina"><i class="fa-sharp fa-solid fa-angle-right"></i></button>',
    prevArrow:
      '<button class="slider-arrow-container" aria-label="Edellinen uratarina"><i class="fa-sharp fa-solid fa-angle-left"></i></button>',
    responsive: [
      {
        breakpoint: 1000,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  });
});

function getSearchResults() {
  //e.preventDefault();
  var search_key = $(".main-search-input").val(); //S
  $("#search_key").val(search_key); //copy to form

  window.dataLayer = window.dataLayer || [];
  window.dataLayer.push({
    event: "search",
    searchTerm: search_key,
  });

  var form_data = $("#search-filter-primary").serialize();
  var pageNum = getQueryStringParam("paged");
  if (pageNum < 2) {
    pageNum = 1;
  }
  form_data = form_data + "&paged=" + pageNum;

  //search_options = search_options.concat(form);

  //Object.assign(
  //search_options = {
  //  ...search_options,
  //  ...form
  //};

  //var data = {};
  ///$("#search-filter-primary").serializeArray().map(function(x){search_options[x.name] = x.value;});
  //console.debug(form_data);

  $.ajax({
    type: "POST",
    url: search_url,
    data: form_data,
    success: (res) => {
      //console.log(res);
      $(".search-results-container").html(res);
      $(".main.search-results-container").css("height", "");
      setTimeout(function () {
        if (window.scrollToTop && window.scrollToTop === true) {
          window.scrollToTop = false;
          /*$('html, body').scrollTop($("#content").offset().top);*/
          $("html, body").scrollTop(0);
        }
      }, 40);
    },
  });

  return false;
}

/* 2023 new js elements
-----------------------------------*/

// front page charts 2023
function createCharts() {
  // observer for the map 'chart'
  const mapobserver = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add("animate");
      } else {
        entry.target.classList.remove("animate");
      }
    });
  });
  const map = document.querySelector(".block-membership__map");
  if (map) {
    mapobserver.observe(map);
  }
  // map end --

  // observer for doughnuts
  const charts = document.querySelectorAll(".is-chart");
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        // Dynamic chart data from acf value
        let chartValue = entry.target.dataset.textvalue;
        let chartLeftover = 100 - chartValue;
        let chartRotation = chartValue * 2;

        // Get the chart from DOM
        const chartId = entry.target.dataset.chartId;
        const chartContainer = document.getElementById(chartId);

        // Full chart configuration with dynamic data
        const chartConfig = {
          type: "doughnut",
          data: {
            datasets: [
              {
                data: [chartLeftover, chartValue],
                backgroundColor: ["#A5C9E7", "#FD9180"],
              },
            ],
          },
          options: {
            tooltips: { enabled: false },
            hover: { mode: null },
            events: [],
            rotation: chartRotation,
            borderWidth: 0,
            animation: {
              delay: 250,
            },
          },
        };
        if (window.chartInstances.has(chartContainer)) {
          window.chartInstances.get(chartContainer).destroy();
        }
        const chart = new Chart(chartContainer, chartConfig);
        window.chartInstances.set(chartContainer, chart);
      } else {
        const chartContainer = entry.target.querySelector("is-chart");
        if (window.chartInstances.has(chartContainer)) {
          window.chartInstances.get(chartContainer).destroy();
        }
      }
    });
  });
  charts.forEach((chart) => {
    observer.observe(chart);
  });
  window.chartInstances = new WeakMap();
}
// front page charts end --

/* Number countup animation (not used) */
/* Count numbers up on  */
/*if ( $( ".count-js" ).length ) {
  var $window = $(window);
  var $elem = $(".count-js")
  var itemsCounted = true;

  function isScrolledIntoView($elem, $window) {
      var docViewTop = $window.scrollTop();
      var docViewBottom = docViewTop + $window.height();

      var elemTop = $elem.offset().top;
      var elemBottom = elemTop + $elem.height();

      return ((elemBottom <= docViewBottom) && (elemTop >= docViewTop));
  }

  $(document).on("scroll", function () {
      if (isScrolledIntoView($elem, $window)) {
        if (itemsCounted) {
         counterCount();
         itemsCounted = false;
       }
      }
  });

function counterCount(){
$('.count-js').each(function () {
    $(this).prop('Counter',0).animate({
        Counter: $(this).text()
    }, {
        duration: 3000,
        easing: 'swing',
        step: function (now) {
            $(this).text(Math.ceil(now));
        }
      });
    });
  }
}*/
// --

// 2023 Scrolling number load
function scrollingNumbers(item, destination) {
  let currNum;
  let fresh = true;

  function scrollNumber(digits) {
    element.querySelectorAll("span[data-value]").forEach((tick, i) => {
      tick.style.transform = `translateY(-${100 * parseInt(digits[i])}%)`;
    });
    element.style.width = "max-content";
  }

  function addDigit(digit, fresh) {
    const possibleNumbers = Array(10)
      .join(0)
      .split(0)
      .map((x, j) => `<span>${j}</span>`)
      .join("");

    element.insertAdjacentHTML(
      "beforeend",
      `<span style="transform: translateY(-1000%)" data-value="${digit}">
          ${possibleNumbers}
          </span>`
    );
    const firstDigit = element.lastElementChild;

    setTimeout(
      () => {
        firstDigit.className = "visible";
      },
      fresh ? 0 : 2000
    );
  }

  function setup(startNum) {
    const digits = startNum.toString().split("");
    for (let i = 0; i < digits.length; i++) {
      addDigit("0", true);
    }
    setTimeout(() => scrollNumber(digits), 200);
    currNum = startNum;
  }
  const element = item;
  setup(destination);
}
// number scroll end

// Fade in animations for chosen blocks 2023
function checkExistence() {
  let membership__charts = document.querySelectorAll(
    ".block-membership__charts"
  );
  if (membership__charts) {
    // initializing the charts here
    createCharts();
  }

  let animation_numbers = document.querySelectorAll(".block-animated-numbers");
  if (animation_numbers) {
    /*let animationTriggered = false;

    const observer = new IntersectionObserver((entries)=>{ // observer to load animations when scrolled on
      entries.forEach((entry) => {
        if (entry.isIntersecting && !animationTriggered) {
          nmbr.forEach((animatedElement) => {
            animatedElement.classList.add('animate');
            if(animatedElement.classList.contains('animate')) {
              let destinationValue = animatedElement.dataset.destination;
              scrollingNumbers(animatedElement, destinationValue); // Number animation
            }
          });
          animationTriggered = true;
        }
      });
    })
    let nmbr = document.querySelectorAll('.animated-number');

    nmbr.forEach(animatedElement => {
      observer.observe(animatedElement);
    });*/
  }

  let small__lift2 = document.querySelectorAll(".small-lift-2");
  if (small__lift2) {
    let small__lift2Arr = Array.from(small__lift2);
    fadeIn(small__lift2Arr, small__lift2, 0, 110);
  }

  let highlight1 = document.querySelectorAll(".highlight-info");
  if (highlight1) {
    let highlight1Arr = Array.from(highlight1);
    fadeIn(highlight1Arr, highlight1, 0, 150);
  }
  let text__image = document.querySelectorAll(".block-text-image");
  if (text__image) {
    let text__imageArr = Array.from(text__image);
    fadeIn(text__imageArr, text__image, 0, 150);
  }

  function fadeIn(array, element, delay, offset) {
    array.forEach(function (element) {
      var waypoint = new Waypoint({
        element: element,
        handler: function () {
          element.style.animation = "fadeInElement 1s ease 1";
          element.style.animationDelay = delay + "s";
          element.style.opacity = "1";
        },
        offset: offset + "%",
      });
    });
  }
}
checkExistence();
// fade in end--

// Footer 'scroll to top' function 2023
function scrollFunction() {
  const scrollToTopButton = document.getElementById("scroll-up-btn");
  const scrollFunc = () => {
    let y = window.scrollY;

    if (y > 1500) {
      scrollToTopButton.className = "top-link show";
    } else {
      scrollToTopButton.className = "top-link hide";
    }
  };
  window.addEventListener("scroll", scrollFunc);
  const scrollToTop = () => {
    const c = document.documentElement.scrollTop || document.body.scrollTop;
    if (c > 0) {
      window.requestAnimationFrame(scrollToTop);
      window.scrollTo(0, c - c / 7);
    }
  };
  scrollToTopButton.onclick = function (e) {
    e.preventDefault();
    scrollToTop();
  };
}
scrollFunction();
// scroll to top end--

/* //2023 new js elements
-----------------------------------*/

const searchText = document.documentElement.lang == "fi" ? "Haku" : "Search";

const closeText =
  document.documentElement.lang == "fi" ? "Sulje haku" : "Close search";

const minthree =
  document.documentElement.lang == "fi"
    ? "Kirjoita vähintään kolme kirjainta"
    : "Write at least three letters";
const minthree2 =
  document.documentElement.lang == "fi"
    ? "Kirjoita vähintään kolme kirjainta hakukenttään"
    : "Write at least three letters to the search form";

jQuery(document).ready(function ($) {
  search_url = frontendajax.ajaxurl; /* from header */
  /*on page load, variables update */

  /* top search modal */
  $(".search-menu-button").on("click", function (e) {
    e.preventDefault();
    $(this).parent().toggleClass("search-open");
    if ($(this).parent().hasClass("search-open")) {
      $(this).text(closeText);
      $(".top-search-input").first().focus();
    } else {
      $(this).text(searchText);
    }
  });
  /* add search val from modal search to # */
  $(".search-top-container .search-top-container-form").on(
    "submit",
    function (e) {
      if ($(".top-search-input").val().length < 3) {
        if ($(".top-search-input").val() === "") {
          $(".top-search-input").prop("placeholder", minthree);
        } else {
          alert(minthree2);
        }
        e.preventDefault();
        return false;
      }
      var s = $(".top-search-input").val();
      var action = $(".search-top-container-form").attr("action");
      action = action; /* + '#!s='+s; */
      $(".search-top-container-form").attr("action", action);
    }
  );

  // Top search clear input
  $(".top-search-input-clear").on("click", function () {
    $(".top-search-input").val("").focus();
  });

  /* top search modal end */

  /* main search function 2022 */

  //auto submit search page form on load:
  //if($('#search_key').val() !== '' && typeof($('#search_key').val()) !== 'undefined') {
  //  setTimeout(function() {
  //    //$('#main_search_form').submit();
  //    //getSearchResults();
  //    //window.dispatchEvent(new Event("popstate"));
  //    //do nothing
  //  }, 30);
  //}

  $("#main_search_form").on("submit", function (e) {
    e.preventDefault();
    var s = $(".main-search-input").val();
    if (s === "undefined" || s === undefined) {
      s = "";
    }
    //?? $('#search_key').val(s);
    setQueryStringParam("s", s);
    setQueryStringParam("paged", "1");
    return false;
  });

  /* main search function 2022 end */

  /* main search filters 2022 */

  if ($("body.search").length > 0) {
    searchInit();

    //window.addEventListener("hashchange", (e) => {
    //window.addEventListener("popstate", (e) => {
    //    console.log("HASH CHANGED:", location.hash);
    //    searchInit();
    //});

    $(".main-search-input-clear").on("click", function () {
      $(".main-search-input").val("").focus();
    });

    $("#primary").on("click", "#show-more-people-link", function (e) {
      e.preventDefault();
      setQueryStringParam("paged", "1");
      setQueryStringParam("post_type", "people");
      //$('input[name="post_type"]').prop('checked', false); //uncheck all checkboxes
      //$('input[name="post_type"][value=people]').click();
      return false;
    });

    //remove selected writer
    $("#primary").on("click", ".remove_selected_writer", function (e) {
      e.preventDefault();
      setQueryStringParam("paged", "1");
      setQueryStringParam("people_id", "");
      return false;
    });

    /* pagination click, get pagenum from clicked link and set to url */
    $("#primary").on("click", "a.page-numbers", function (e) {
      e.preventDefault();
      var hr = $(this).attr("href");
      var urlPagedParams = new URLSearchParams(hr);
      var urlPaged = urlPagedParams.get("paged");
      if (urlPaged !== "undefined" && urlPaged !== undefined) {
        setQueryStringParam("paged", urlPaged);
      }
      window.scrollToTop = true;
      return false;
    });

    /* post type change, change sub-filter list eg. sisältötyypit */
    $('input[name="post_type"]').on("click", function () {
      var post_type = $(this).val();
      setQueryStringParam("post_type", post_type);
      setQueryStringParam("post_category[]", ""); //remove all post_category[]
      setQueryStringParam("paged", "1");

      /* TODO MOVE TO INIT:
      $('.post_category_checkbox').prop('checked', false); //uncheck all checkboxes
      console.log(post_type);
      $('.main-category-list').addClass('hidden');
      if(typeof(post_type) == 'string' && post_type != '') {
        $('.main-category-list[data-slug='+post_type+']').removeClass('hidden');
      }
      console.log('@@todo trigger search');
      //triggerSearch();
      */
    });

    /* post category change, update array of teemat */
    $(".search-filters").on("click", ".post_category_checkbox", function (e) {
      var post_category_this = $(this).val();
      //if($(this).is(':checked')) {
      //  setVal
      //}
      setQueryStringParam("paged", "1");
      var post_category_arr = [];
      $(".post_category_checkbox:checked").each(function () {
        post_category_arr.push($(this).val());
      });

      setQueryStringArray("post_category[]", post_category_arr);
      //setQueryStringArrayParam('post_category[]', post_category_this);
    });

    //search by writer link click / people_id / person_id:
    $("#primary").on("click", ".search_by_writer", function (e) {
      e.preventDefault();
      setQueryStringParam("paged", "1");
      var people_id = $(this).attr("data-id");
      if (people_id) {
        /*
        $('#people_id').val(people_id);
        $("input[name=post_type][value=uutiset]").attr('checked', 'checked');
        $('.main-search-input').val('');
        $('#search_key').val('');
        triggerSearch();
        */
        setQueryStringParam("s", ""); //need to empty search word
        setQueryStringParam("people_id", people_id);
        setQueryStringParam("post_type", "uutiset");
      }
      return false;
    });
  }
  //if end

  /* main search filters 2022 end */

  $("label.btn").on("click", "input", function (e) {
    e.stopPropagation();
    $(this).attr("checked", !$(this).attr("checked"));
    $(e.target).closest("label").toggleClass("btn-flat");
  });

  $(".btn_checkbox").on("click keypress", function (e) {
    e.preventDefault();
    if (
      $("#ch_1").is(":checked") ||
      $("#ch_2").is(":checked") ||
      $("#ch_3").is(":checked") ||
      $("#ch_4").is(":checked") ||
      $("#ch_5").is(":checked") ||
      $("#ch_6").is(":checked")
    ) {
      $(".newsletter__checkboxes").get(0).style.display = "none";
      $(".newsletter__info").get(0).style.display = "block";
    } else {
      $(".newsletter__error").get(0).style.display = "block";
    }
  });

  function formAjaxHandler() {
    // form selector (CHANGE to match <form> in the HTML)
    const $form = $(".newsletter");

    function init() {
      // bind form submit listener
      $form.on("submit", handleFormSubmit);
    }

    function handleFormSubmit(event) {
      // prevent default submit event because it will be handled as ajax request
      event.preventDefault();

      // use rf=json (request format = json)
      const formUrl = "https://www.kauppakamarinuutiskirjeet.fi/?rf=json";

      // send form to the given URL as XHR POST
      return $.ajax({
        data: $form.serialize(),
        success: handleRequestSuccess,
        type: "POST",
        url: formUrl,
      });
    }

    // do something with the form response
    function handleRequestSuccess(response) {
      $(".newsletter__info").get(0).style.display = "none";
      $(".newsletter__success").get(0).style.display = "block";
      //console.log(response);
    }

    return init();
  }

  // initialize form ajax handler on Document Ready
  $(formAjaxHandler);

  $(".subpages-toggle").on("click", function (event) {
    this.parentNode.classList.toggle("open");
    this.setAttribute(
      "aria-expanded",
      this.getAttribute("aria-expanded") === "false"
    );
  });

  $("#newsletter__back").on("click", function (event) {
    event.preventDefault();
    $(".newsletter__info").get(0).style.display = "none";
    $(".newsletter__checkboxes").get(0).style.display = "block";
  });

  //new filter 2022 articles mobile
  if ($(".article-filter--select").length > 0) {
    $(".article-filter--select").on("change", function () {
      var link = $(this).val();
      document.location.href = link;
    });
  }

  function rearrangeIcegram() {
    // accessibility (to get rid of reversed order)
    let rearrangeItems = $("form.es_subscription_form > div");

    rearrangeItems.each(function () {
      if (!$(this).attr("class")) {
        $(this).addClass("es-extra-field");
        $("form.es_subscription_form").prepend($(this));
      }
    });
  }

  $(rearrangeIcegram);

  function icegramDescriptions() {
    // move descriptions below the corresponding checkbox
    let listTable = $(".ig-es-form-list-selection td");

    listTable.each(function (index) {
      let descToAppend = $(`.subscribe .description-${index}`);
      $(this).append(descToAppend);
    });
  }

  $(icegramDescriptions);

  // Header join now button fix
  function fixJoinnow() {
    let original = document.querySelector(".header-join-now");
    let clone = original.cloneNode(true);
    clone.removeAttribute("id");
    document.getElementById("header-join-now-copy").innerHTML = clone.innerHTML;
  }
  $(fixJoinnow);
});

// Make Icregram Express list selection checkboxes checked (not possible in plugin dashboard)
function icegramListSelection() {
  let $inputs = $(
    '.subscribe .ig-es-form-list-selection input[type="checkbox"]'
  );

  console.log($inputs.length);

  if ($inputs.length) {
    $inputs.each(function () {
      $(this).prop("checked", true);
    });
  }
}
$(icegramListSelection);

// Enable placeholders on top of CookieBot-blocked content
((d, i, m) => {
  let ct = (t) => d.createTextNode(t);
  let ce = (e) => d.createElement(e);
  d.querySelectorAll(i).forEach((e) => {
    const a = ce("a"),
      div = ce("div"),
      p = ce("p"),
      s = e.dataset.cookieblockSrc,
      sp = /google\.com\/maps\/embed/.test(s)
        ? "Google Maps"
        : /player\.vimeo\.com\/video\//.test(s)
        ? "Vimeo"
        : /youtube(-nocookie)?\.com\/embed\//.test(s)
        ? "YouTube"
        : /w\.soundcloud\.com\/player\//.test(s)
        ? "SoundCloud"
        : undefined;
    if (!sp) return;
    div.innerHTML =
      `<div style="background-color:#CCC;display:inline-` +
      `block;height:${e.height}px;position:relative;width:${e.width}px;"><div style=` +
      '"background-color:#848484;border-radius:15px;height:50%;position:absolute;' +
      'transform:translate(50%,50%);width:50%;"><p style="color:#FFF;font-size:7.5em;' +
      "position:relative;top:50%;left:50%;margin:0;text-align:center;transform:translate" +
      '(-50%,-50%);">&ctdot;</p></div>';
    div.classList.add(`cookieconsent-optout-${m}`);
    a.textContent = `ja hyväksy kaikki evästeet`;
    a.href = "javascript:Cookiebot.renew()";
    p.append(ct("Ole hyvä "), a, ct(` nähdäksesi ${sp} -sisällön.`));
    div.append(p);
    e.parentNode.insertBefore(div, e);
  });
})(document, "iframe[data-cookieblock-src]", "marketing");
