<?php
/**
 * Poistettu template name Ver<PERSON><PERSON>
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package kauppakamari
 */

get_header();

?>

  <script src="//cdnjs.cloudflare.com/ajax/libs/list.js/1.5.0/list.min.js"></script>

  <?php
    $company_array = kauppakamari_get_verkkoproducts_list();
    $type_list = array_unique(array_map(function ($i) { return $i['type']; }, $company_array));
    $location_list = array_unique(array_map(function ($i) { return $i['location']; }, $company_array));
    $chamber_list = array_unique(array_map(function ($i) { return $i['chamber']; }, $company_array));
    asort($type_list);
    asort($location_list);
    asort($chamber_list);
  ?>

  <div id="company-filter" class="company-filter">
    <div class="company-filter__header hero" style="background-image: url(<?php echo get_the_post_thumbnail_url(); ?>);">
      <div class="company-filter__header--wrap">
        <h1 class="hero__title"><?php echo get_field('title'); ?></h1>
        <input class="search" placeholder="Hae tästä kumppania…" />
      </div>
      <div class="dimming dimming__bottom_l"></div>
    </div>

    <div class="company-filter__primary">

      <div class="company-filter__aside">

        <div class="company-filter__aside--header">
          <span>Suodata tuloksia</span>
          <a class="reset-filter">X</a>
        </div>

        <select name="type" id="filter-type" class="select-filter">
          <option selected="selected" value="all">Valitse toimiala</option>
          <?php foreach ($type_list as $type) { ?>
            <option value="<?php echo $type; ?>"><?php echo $type; ?></option>
          <?php } ?>
        </select>

        <select name="location" id="filter-location" class="select-filter">
          <option selected="selected" value="all">Valitse sijainti</option>
          <?php foreach ($location_list as $location) { ?>
            <option value="<?php echo $location; ?>"><?php echo $location; ?></option>
          <?php } ?>
        </select>

        <select name="chamber" id="filter-chamber" class="select-filter">
          <option selected="selected" value="all">Valitse Kauppakamari</option>
          <?php foreach ($chamber_list as $chamber) { ?>
            <option value="<?php echo $chamber; ?>"><?php echo $chamber; ?></option>
          <?php } ?>
        </select>
        <h4>Järjestä tuloksia</h4>
        <a class="sort" data-sort="name">Nimen mukaan</a>
        <a class="sort" data-sort="location">Sijainnin mukaan</a>
        <a class="sort" data-sort="type">Toimialan mukaan</a>

      </div>

      <div class="company-filter__main">
        <div class="record-count-total">
          <span id="record-count-total"></span> hakutulosta
        </div>
        <ul class="company-filter__list list">
        <?php
          foreach ($company_array as $company) { ?>
            <li class="company__item">
              <a href="https://www.kauppakamariverkko.fi<?php echo $company['links']['FI'][0]; ?>" target="_blank">
                <h5 class="name"><?php echo $company['name']['FI']; ?></h5>
                <span class="type"><?php echo $company['type']; ?></span>
                <span class="location"><?php echo '  |  ' . $company['location']; ?></span>
                <span class="chamber"><?php echo  '  |  ' . $company['chamber']; ?></span>
              </a>
            </li>
          <?php } ?>
        </ul>

        <ul class="pagination"></ul>

      </div>



    </div>

  </div>

  <div id="primary" class="primary primary--page">

    <main id="main" class="main">

      <?php while (have_posts()) : the_post(); ?>

        <article id="post-<?php the_ID(); ?>" <?php post_class('entry entry--page'); ?>>

          <div class="entry__content wysiwyg">
            <?php the_content(); ?>
          </div>

        </article>

      <?php endwhile; ?>

    </main><!-- #main -->

  </div><!-- #primary -->

<?php
get_footer();
