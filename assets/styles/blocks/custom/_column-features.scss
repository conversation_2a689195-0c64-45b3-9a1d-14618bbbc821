.block-column-features {
  background-color: $primary;
  box-shadow: inset 0 -75px white;
  &__wrap {
    @include wrap();
    h3 {
      font-size: 22px;
      color: white;
      text-align: center;
      font-weight: 600;
      margin: 0;
      margin-bottom: 2rem;
    }
  }
  &__subtitle {
    color: white;
    text-transform: uppercase;
    text-align: center;
    display: block;
    padding-top: 2rem;
  }
  &__title {
    color: white;
  }
  &__content_wrap {
    display: flex;
    justify-content: space-between;
    text-align: center;
    @include breakpoint($sub-l) {
      flex-wrap: wrap;
    }
  }
  &__item {
    width: calc((100% / 3) - 1.5rem);
    @include breakpoint($sub-l) {
      width: 100%;
      margin-bottom: 1.5rem;
    }
    box-shadow: 0 0 13px -5px rgba(0, 0, 0, 0.4);
    background-color: white;
    padding: 1.5rem 4rem 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    &--title {
      color: $primary;
      font-weight: 600;
      font-size: 18px;
      margin-bottom: 1.5rem;
    }
    a {
      @include button;
      margin: 1.5rem auto;
      display: block;
      width: auto;
      min-width: 150px;
      text-align: center;
      border: 1px solid $primary;
      color: white;
      background: $primary;
      width: fit-content;
      min-width: 220px;
      padding-top: 1rem !important;
      &:hover {
        color: white;
        background: #1d397f !important;
        border: 1px solid #1d397f !important;
      }
      &.external {
        background-color: #002663;
        border: 1px solid #002663;
        color: white;
        padding: 0.75rem 4.5rem 0.75rem 3rem !important;
        &:after {
          position: absolute;
          margin-top: -1px !important;
          background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23ffffff' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E") !important;
          background-position: center;
          background-size: 84%;
          width: 1.1rem;
          height: 1rem;
          display: inline-block;
          content: " ";
          margin-left: 8px;
          color: transparent;
          background-repeat: no-repeat;
        }
        &:hover {
          background-color: white;
          color: #002663;
          &:after {
            background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23002663' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E") !important;
          }
        }
      }
      &:hover {
        background-color: $primary;
        border-color: $primary;
        color: white;
      }
    }
    img {
      width: 225px;
      margin: 0 auto;
    }
  }
}
