/**
 * open and closes sites tab
 */

const offCanvas = document.querySelector(".header_sites"),
  menuToggle = document.querySelector(".header__toggle"),
  main = document.querySelector(".main-body");

const closingElements = [menuToggle, main];
const toggleElements = [offCanvas, main];

function openElements(...elements) {
  const elementsToOpen = [...elements];
  elementsToOpen.forEach((elementToOpen) =>
    elementToOpen.classList.toggle("open")
  );
}

function closeElements(...elements) {
  const elementsToClose = [...elements];
  elementsToClose.forEach((elementToClose) =>
    elementToClose.classList.remove("open")
  );
}

menuToggle.addEventListener("click", (e) => {
  e.preventDefault();
  e.stopImmediatePropagation();
  openElements(...toggleElements);
});

closingElements.forEach((closingElem) => {
  closingElem.addEventListener("click", (e) => {
    //e.preventDefault();
    if (
      main.classList.contains("open") &&
      !e.target.classList.contains("menu-toggle")
    ) {
      closeElements(...toggleElements);
    }
  });
});

/**
 * Copy sitenav to mobilemenu
 */

//const menu = document.querySelector(".header_sites__sites");

/* adding external sites to mobile nav */

const menu = document.querySelector(".header_sites");
const cloned_menu = menu.cloneNode(true);
if (document.getElementById("primary-navigation__items") !== undefined) {
  document.getElementById("primary-navigation__items").appendChild(cloned_menu);
}

/**
 * Menu scrolling behaviout
 */

var lastScrollTop = 65;

window.addEventListener(
  "scroll",
  function () {
    var st = window.pageYOffset || document.documentElement.scrollTop;
    if (st > lastScrollTop && st > 150) {
      document.getElementById("primary-navigation").classList.add("hide");
    } else {
      document.getElementById("primary-navigation").classList.remove("hide");
    }
    lastScrollTop = st;
  },
  false
);

/**
 * Height of menu
 */

// var elmnt = document.getElementById("masthead");
// document.getElementById("content").style.marginTop = elmnt.offsetHeight + "px";
