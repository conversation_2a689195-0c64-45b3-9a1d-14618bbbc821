.block-people-select {
  // box-shadow: 0 0 13px -5px rgba(0, 0, 0, .4);
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 1.25rem 6.67rem;

  @include breakpoint($sub-m) {
    padding: 1.25rem;
  }

  h2 {
    width: 100%;
    text-align: center;
    color: $primary;
    margin-bottom: 4rem;
    padding-bottom: 2rem;
  }

  h3 {
    width: 100%;
    text-align: center;
    color: $primary;
    margin-bottom: 3rem;
    padding-bottom: 1rem;
  }

  &__item {
    width: 100%;
    align-items: center;
    display: block;
    flex-wrap: wrap;
    font-size: 15px;
    //justify-content: center;
    margin-bottom: 2rem;
    text-align: center;

    @include breakpoint($m) {
      display: flex;
      width: calc(50% - 1.5rem);
      flex-wrap: nowrap;
      text-align: left;
    }

    .name {
      font-size: 16px;
      font-weight: 700;
      padding: 0 0 0.5rem;
      display: block;
    }

    .title {
      color: $grey-aa;
      display: block;
      margin-bottom: 0.5rem;
    }

    .content {
      font-size: 15px;

      .content p,
      p {
        font-size: 15px;
        margin: 0 0 0.5rem
      }

      a.external {
        @include external-link;
        padding-right: 24px;
      }
    }
  }

  img {
    width: auto;
    max-height: 100%;
  }

  &__img {
    border: 2px solid $light-blue-50;
    max-width: 143px;
    min-width: 143px;
    max-height: 143px;
    min-height: 143px;
    border-radius: 50%;
    background-size: cover;
    background-position: center center;
    height: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;

    @include breakpoint($m) {
      margin-right: 1.5rem;
      margin-bottom: 0;
    }

    @include breakpoint($sub-m) {
      margin-top: 15px;
      margin-right: auto;
      margin-left: auto;
    }
  }

  &__content {
    @include breakpoint($m) {
      //padding-right: 6rem;
    }
  }
}

.page-template-default {
  .block-people-select {
    padding: 1.25rem 1.67rem;

    .block-people__content {
      padding: 1rem;
      width: 85%;
    }

    .block-people__item {
      flex-direction: column;
      border-bottom: 1px solid $grey;
      text-align: center;
    }

    .block-people__img {
      margin-right: 0;
      margin-bottom: 2rem;
      max-width: 173px;
      min-width: 173px;
      max-height: 173px;
      min-height: 173px;
    }

    .name {
      padding: 0;
    }

    p {
      font-size: 1rem !important;
    }
  }

}
