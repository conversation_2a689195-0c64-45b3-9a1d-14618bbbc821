.breadcrumb-container {
  @include wrap;
  display: flex;
  max-width: 100%;
  margin-top: 28px;
  margin-bottom: 28px;
  @include breakpoint($m) {
    padding-left: 3rem;
    padding-right: 3rem;
  }
  @include breakpoint(min-width 1120px) {
    margin-top: 1rem;
    margin-left: calc((100% - 100vw) / 2);
    margin-right: calc((100% - 100vw) / 2);
    padding: 0;
    padding-left: calc((120vw - 100%) / 2);
    padding-right: calc((120vw - 100%) / 2);
    max-width: 65%;
  }
}

.breadcrumb-container p {
  color: #444;
  @include breakpoint($l) {
    margin-left: 1.25rem;
  }
}
.breadcrumb-container p,
.breadcrumb-container a {
  font-size: 14px;
}

.breadcrumb-container p a {
  color: #002663;
  text-decoration: underline;
}
.breadcrumb-container p a:hover {
}

.breadcrumb-container p span.separator {
  text-indent: -9999px;
  display: inline-block;
  overflow: hidden;
  height: 15px;
  width: 11px;
  margin-bottom: -3.5px;
  transform: scale(0.5);
  background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8' standalone='no'%3F%3E%3Csvg xmlns:dc='http://purl.org/dc/elements/1.1/' xmlns:cc='http://creativecommons.org/ns%23' xmlns:rdf='http://www.w3.org/1999/02/22-rdf-syntax-ns%23' xmlns:svg='http://www.w3.org/2000/svg' xmlns='http://www.w3.org/2000/svg' width='8.461318' height='13.999997' viewBox='0 0 2.2387237 3.7041658' version='1.1' id='svg974'%3E%3Cdefs id='defs968' /%3E%3Cmetadata id='metadata971'%3E%3Crdf:RDF%3E%3Ccc:Work rdf:about=''%3E%3Cdc:format%3Eimage/svg+xml%3C/dc:format%3E%3Cdc:type rdf:resource='http://purl.org/dc/dcmitype/StillImage' /%3E%3Cdc:title%3E%3C/dc:title%3E%3C/cc:Work%3E%3C/rdf:RDF%3E%3C/metadata%3E%3Cg id='layer1' transform='translate(-123.75901,-60.872806)'%3E%3Cpath d='m 123.95187,61.065661 1.66005,1.660085 -1.66005,1.658266' style='fill:none;stroke:%233a75c4;stroke-width:0.545481;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1' id='path957' /%3E%3C/g%3E%3C/svg%3E%0A");
  background-repeat: no-repeat;
}

body.single-release .breadcrumb-container,
body.single-post .breadcrumb-container {
  padding-top: 38px;
  margin-bottom: 0px;
  @include breakpoint($sub-m) {
    padding-top: 18px;
  }
}

/* same margin in archive and single post */
.archive .breadcrumb-container {
  padding-top: 31px;
  @include breakpoint($sub-m) {
    padding-top: 8px;
  }
}
