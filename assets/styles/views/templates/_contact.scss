.contact {
  &__people {
    @include wrap;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 4.6rem;
    @include breakpoint($sub-l) {
      display: block;
    }
  }
  &__header {
   width: 100%;
   border-bottom: 1px solid $separatorblue;
   margin-bottom: 3rem;
   padding-bottom: 2rem;
   h1 {
     margin: 0;
   }
  }
  &__checkboxes {
    width: 20%;
    @include breakpoint($sub-l) {
      width: 100%;
      margin-bottom: 3rem;
    }
    .contacts-filter {
      font-size: 16px;
      font-weight: $ws-semibold;
    }
  }
  &_list_wrap {
    width: 78%;
    @include breakpoint($sub-l) {
      width: 100%;
    }
  }
  &__checkbox {
    display: block;
    margin-bottom: 0.5rem;
    margin-right: 0.5rem;
    display: flex;
    align-items: center;

    label {
      display: flex;
      align-items: center;
      text-align: left;
      width: 100%;
      gap: 0.5rem;
      transition: all .1s ease-in-out;
      cursor: pointer;
      &:focus, &.focus {
        outline: 2px solid white;
        outline-offset: 1px;
      }
    }
    input {
      position: absolute;
      opacity: 0;
      cursor: pointer;
      height: 0;
      width: 0;
    }
    .custom-checkbox {
      top: 0;
      left: 0;
      height: 20px;
      width: 20px;
      background-color: #fff;
      border: 2px solid $light-blue;
    }
    input:checked ~ .custom-checkbox:after{
      display: block;
      // custom 'checked' icon
      content:"\f00c";
      font-family: 'Font Awesome 6 Sharp';
      font-weight: 900;
      text-align: center;
      line-height: 1.25;
    }
    input:focus ~ .custom-checkbox {
      outline: 1px solid $light-blue;
      //outline-offset: 0.5px;
    }
    @include breakpoint($sub-l) {
      display: inline-block;
    }
  }
  &__list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  &__item {
    padding: 2.5rem;
    width: calc((100% / 3) - 0.93rem);
    margin-bottom: 0.93rem;
    box-shadow: 0 0px 2.2px rgba(0, 0, 0, 0.057),
    0 3.7px 5.3px rgba(0, 0, 0, 0.065);
    @include breakpoint($sub-l) {
      width: calc((100% / 2) - 0.93rem);
    }
    @include breakpoint($sub-m) {
      width: 260px;
      max-width: 100%;
      margin-left: auto;
      margin-right: auto;
    }
    &__img {
      border: 2px solid $light-blue-25;
      width: 143px;
      height: 143px;
      border-radius: 50%;
      background-position: center center;
      background-size: cover;
      margin: 0rem auto;
      margin-bottom: 2.5rem;
      display: block;
      clear: both;
    }
    &__info {
      border-bottom: 1px solid $separatorblue;
      padding: .5rem 0;
      p {
        font-size: 15px;
        margin-bottom: 0px;
      }
      a.external {
        @include external-link;
        padding-right: 24px;
      }
    }
    &__name {
      font-size: 16px;
      font-weight: 700;
      border-bottom: 1px solid $separatorblue;
      padding: 0 0 .5rem;
    }
    &__cat {
      color: $gray-text;
      text-transform: uppercase;
      font-weight: 500;
    }
    &.filler {
      box-shadow: none;
    }
  }
}
