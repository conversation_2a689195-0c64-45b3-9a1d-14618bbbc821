<?php

/**
 * Events external
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-events-external' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-events-external';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
// $content = get_field('trainers-list');

?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">

  <?php
  $events = kauppakamari_get_products_list('24');
  ?>

  <div class="block-events-external__wrap">

    <div class="block-events-external__title">
      <h5>Tulevia tapahtumia</h5>
    </div>

    <div class="block-events-external__ajax">

    <?php if ( !empty($events) ) : ?>

      <?php
        $i = 0;
        foreach ($events as $event) { ?>
          <div class="block-events-external__item">
            <div class="block-events-external__item--content">
              <span class="date"><?php echo $event->date; ?></span>
              <a href="<?php echo $event->url; ?>" target="_blank"><?php echo $event->name; ?></a>
            </div>
          </div>
          <?php
          $i++;
          if($i >= 5) {
            break;
          } ?>
        <?php }
        ?>
    <?php endif; ?>
    </div>
  </div>

  <div class="block-events-external__wrap featured">

  <?php $featured_event = get_field('featured_event'); ?>

  <div class="teaser__content">

      <div class="teaser__content-wrap">
        <div class="teaser__meta">
        <?php echo $featured_event['date']; ?> | <?php echo $featured_event['location']; ?>
        </div>

        <a href="<?php echo $featured_event['link']['url']; ?>" rel="bookmark" class="teaser__title">
          <h2>
            <?php echo $featured_event['title']; ?>
          </h2>
        </a>

        <div class="teaser__summary">
          <?php echo $featured_event['content']; ?>
        </div>
      </div>

    <?php if($featured_event['link']['url']) {
        echo '<a class="teaser__link" href="' . $featured_event['link']['url'] . '" >' . $featured_event['link']['title'] . '</a>';
    } ?>

  </div>

  <?php if (!empty($featured_event['img'])) : ?>
    <a class="teaser__thumbnail" href="<?php echo $featured_event['link']['url']; ?>" style="background-image: url(<?php echo $featured_event['img']['sizes']['wide_xl']; ?>;">
    </a>
  <?php endif; ?>

  </div>

</div>
