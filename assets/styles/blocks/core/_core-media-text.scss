.wp-block-media-text {
	display: grid;
  grid-template-rows: auto;
  box-shadow: 0 0 13px -5px rgba(0,0,0,.4);
	grid-template-columns: 55% 1fr;
	&.has-media-on-the-right {
		grid-template-columns: 1fr 55%;
    @media screen and (max-width: 600px) {
      grid-template-columns: 100%;
      grid-template-rows: auto;
  }
  }
  p {
    font-size: 15px !important;
  }
}

.wp-block-media-text.is-vertically-aligned-top {
	.wp-block-media-text__content,
	.wp-block-media-text__media {
		align-self: start;
	}
}
.wp-block-media-text,
.wp-block-media-text.is-vertically-aligned-center {
	.wp-block-media-text__content,
	.wp-block-media-text__media {
		align-self: center;
	}
}

.wp-block-media-text.is-vertically-aligned-bottom {
	.wp-block-media-text__content,
	.wp-block-media-text__media {
		align-self: end;
	}
}

.wp-block-media-text .wp-block-media-text__media {
	grid-column: 1;
	grid-row: 1;
	margin: 0;
}

.wp-block-media-text .wp-block-media-text__content {
	grid-column: 2;
	grid-row: 1;
	word-break: break-word;
  padding: 0 8% 0 8%;
  padding: 2rem 2rem;
}

.wp-block-media-text.has-media-on-the-right .wp-block-media-text__media {
	grid-column: 2;
	grid-row: 1;
  @media screen and (max-width: 600px) {
    grid-column: 1;
    grid-row: 1;
  }
}

.wp-block-media-text.has-media-on-the-right .wp-block-media-text__content {
	grid-column: 1;
  grid-row: 1;
  padding: 2rem 2rem;
  @media screen and (max-width: 600px) {
    grid-column: 1;
    grid-row: 2;
  }
}

.page-template-template-landingpage {
  .wp-block-media-text.has-media-on-the-right .wp-block-media-text__content {
    padding: 2rem 5rem;
    @media screen and (max-width: 800px) {
      padding: 2rem 2rem;
    }
  }
  .wp-block-media-text .wp-block-media-text__content {
    padding: 2rem 5rem;
    @media screen and (max-width: 800px) {
      padding: 2rem 2rem;
    }
  }
}

.wp-block-media-text > figure > img,
.wp-block-media-text > figure > video {
	max-width: unset;
	width: 100%;
	vertical-align: middle;
}

.wp-block-media-text.is-image-fill figure.wp-block-media-text__media {
	height: 100%;
	min-height: 250px;
	background-size: cover;
}

.wp-block-media-text.is-image-fill figure.wp-block-media-text__media > img {
	// The image is visually hidden but accessible to assistive technologies.
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	border: 0;
}
/*
* Here we here not able to use a mobile first CSS approach.
* Custom widths are set using inline styles, and on mobile,
* we need 100% width, so we use important to overwrite the inline style.
* If the style were set on mobile first, on desktop styles,
* we would have no way of setting the style again to the inline style.
*/
@media (max-width: #{ ($sub-m) }) {
	.wp-block-media-text.is-stacked-on-mobile {
    outline: 3px solid red;
		grid-template-columns: 100% !important;
    grid-template-rows: auto;
    grid-auto-rows: auto;
    grid-auto-flow: row;

		.wp-block-media-text__media {
			grid-column: 1;
			grid-row: 1;
		}
		.wp-block-media-text__content {
			grid-column: 1;
			grid-row: 2;
		}
	}

	.wp-block-media-text.is-stacked-on-mobile.has-media-on-the-right {
    outline: 3px solid blue;
		.wp-block-media-text__media {
			grid-column: 1;
			grid-row: 2;
		}
		.wp-block-media-text__content {
			grid-column: 1;
			grid-row: 1;
		}
	}
}
