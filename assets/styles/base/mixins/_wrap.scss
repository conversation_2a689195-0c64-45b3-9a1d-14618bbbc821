/* ==========================================================================
 # Wrap mixins
========================================================================== */

// wrap outer containers (width, padding, center)
@mixin wrap {
  max-width: #{$max-width-l + $gap-big};
  padding-left:  $gap;
  padding-right: $gap;
  margin-left:  auto;
  margin-right: auto;
  width: 100%;
}

// narrow content
@mixin narrow-content {
  max-width: #{$max-width-m + $gap-big};
}

// extra-narrow content (search results wrapper)
@mixin extra-narrow-content-left {
  max-width: #{$max-width-s + $gap-big};
  margin-left: 0px;
}
