/* ==========================================================================
 # List block
=============================================================================

Notice: Lists without custom styles won't have identifiable classes on front-end.
Be careful with lists styles as they will be applied by other blocks as well.

*/

/* Front-end and back-end
----------------------------------------------- */

.editor-styles-wrapper ul.editor-rich-text__tinymce,
.editor-styles-wrapper ol.editor-rich-text__tinymce,
.wysiwyg ul,
.wysiwyg ol, {
  margin: 0 auto $gap auto;
  padding-left: $gap;

  li {
    margin: .25rem 1rem;
    padding: 0;
  }

  // inner lists
  ul, ol {
    margin: $gap-tiny 0;
    .has-background {
      padding-left: 2.375em; /* same as wp */
    }
  }

}

/* Only front-end
----------------------------------------------- */

.wysiwyg {

  ul, ol {

  }

}

/* Only back-end
----------------------------------------------- */

.editor-styles-wrapper ul.editor-rich-text__tinymce,
.editor-styles-wrapper ol.editor-rich-text__tinymce {

}


.single.single-post, .single.single-release {
  .wysiwyg, .entry__side_content {
    ul, ol {
      li {
        // font-size: 1rem;
        font-size: 19px;
      }
    }
  }
}
