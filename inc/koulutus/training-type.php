<?php

// Register Custom Taxonomy
function kauppakamari_training_type() {

	$labels = array(
		'name'                       => _x( 'Koulutustyypit', 'Taxonomy General Name', 'kauppakamari' ),
		'singular_name'              => _x( 'Koulutustyyppi', 'Taxonomy Singular Name', 'kauppakamari' ),
		'menu_name'                  => __( 'Koulutustyypit', 'kauppakamari' ),
		'all_items'                  => __( 'Kaikki', 'kauppakamari' ),
		'parent_item'                => __( 'Parent Item', 'kauppakamari' ),
		'parent_item_colon'          => __( 'Parent Item:', 'kauppakamari' ),
		'new_item_name'              => __( 'Uusi', 'kauppakamari' ),
		'add_new_item'               => __( 'Lisää uusi', 'kauppakamari' ),
		'edit_item'                  => __( 'Muokkaa', 'kauppakamari' ),
		'update_item'                => __( 'Päivitä', 'kauppakamari' ),
		'view_item'                  => __( 'Katso', 'kauppakamari' ),
		'separate_items_with_commas' => __( 'Separate items with commas', 'kauppakamari' ),
		'add_or_remove_items'        => __( 'Add or remove items', 'kauppakamari' ),
		'choose_from_most_used'      => __( 'Choose from the most used', 'kauppakamari' ),
		'popular_items'              => __( 'Popular Items', 'kauppakamari' ),
		'search_items'               => __( 'Search Items', 'kauppakamari' ),
		'not_found'                  => __( 'Ei löytynyt', 'kauppakamari' ),
		'no_terms'                   => __( 'Ei löytynyt', 'kauppakamari' ),
		'items_list'                 => __( 'Items list', 'kauppakamari' ),
		'items_list_navigation'      => __( 'Items list navigation', 'kauppakamari' ),
	);
	$rewrite = array(
		'slug'                       => 'koulutustyyppi',
		'with_front'                 => true,
		'hierarchical'               => false,
	);
	$args = array(
		'labels'                     => $labels,
		'hierarchical'               => false,
		'public'                     => true,
		'show_ui'                    => true,
		'show_admin_column'          => true,
		'show_in_nav_menus'          => true,
		'show_tagcloud'              => true,
		'rewrite'                    => $rewrite,
	);
	register_taxonomy( 'training_type', array( 'experiences' ), $args );

}
add_action( 'init', 'kauppakamari_training_type', 0 );
