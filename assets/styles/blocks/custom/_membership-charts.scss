.block-membership {
    display: flex;
    flex-direction: column;
    //margin-top: -6rem !important;

    &__title-wrapper {
        text-align: left;
    } 
    &__subtitle {
        font-size: 18px;
        font-weight: $ws-medium;
    }
    &__link-wrapper {
        text-align: center;
        padding: 1rem 0;
    }
    &__link {
        @include bigtextlink;
        width: fit-content;
    }
    &__charts {
        display:flex;
        flex-wrap: wrap;
        justify-content: space-between;
        width: 100%;
        margin: 3rem 0 1rem;
    }
    &__chart {
        display: flex;
        justify-content: space-between;
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 2rem;
        margin-top: 2rem;
        canvas {
            // padding: 2rem;
            margin: auto 0;
            margin-top: 0;
            width: 140px !important;
            height: 140px !important;
        }
        &-text {
            max-width: 170px;
            width: 170px;
            margin: auto 0;
            p {
                font-size: 15px !important;
                font-weight: $ws-medium;
                color: $primary;
                margin: 0;
            }
            span {
                white-space: nowrap;
                font-size: 2rem;
                display: inline;
                max-height: 100px;
                color: $primary;
                font-weight: $ws-bold;
            }
        }
    }
    & .mobile-separator {
        border-bottom: 2px solid $coral;
        width: 85%;
        margin: auto;
        margin-bottom: 2rem;
    }
    // fin map
    &__map {
        position: relative;
        max-height: 222px;
        padding-left: 1.75rem;
        padding-right: 1rem;
        margin: auto 0;
        // transition: fill_full 1.5s ease-in;
        img, &:after {
            height: 222px !important;
            min-width: 108px !important;
        }
        &:after {
            content: "";
            background-image: url('../images/fin_map.svg');
            position: absolute;
            display: block;
            bottom: 0;
            z-index: 1;
            opacity: 0;
        }

        // Animations for the map
        &.animate.fill-helsinki:after {
            animation: helsinki 1s 0.25s 1 forwards;
        }
        &.animate.less-20:after {
            animation: fill_20 1s 0.25s 1 forwards;
        }
        &.animate.less-40:after {
            animation: fill_40 1s 0.25s 1 forwards;
        }
        &.animate.less-60:after {
            animation: fill_60 1s 0.25s 1 forwards;
        }
        &.animate.less-80:after {
            animation: fill_80 1s 0.25s 1 forwards;
        }
        &.animate.less-100:after {
            animation: fill_100 1s 0.25s 1 forwards;
        }
        &.animate.is-100:after {
            animation: fill_full 1s 0.25s 1 forwards;
        }
    }
}

@include breakpoint(min-width 500px) {
    .block-membership {
        &__chart {
            justify-content: center;
            gap: 3rem;
            &-text {
                p {
                    font-size: 16px !important;
                }
            }
        }
        &__map {
            padding-left: 1rem;
        }
    }
}

@include breakpoint(min-width 850px) {
    .block-membership {
        &__title-wrapper {
            text-align: start;
        }
        &__subtitle {
            font-size: 22px;
        }
        &__chart {
            flex: 0 0 50%;
            max-width: 50%;
            justify-content: space-evenly;
            border-bottom: none;
            border-right: 2px solid $coral;
            &:last-of-type {
                border-right: 2px solid transparent;
            }
            canvas {
                margin: auto 0;
            }
            &-text {
                max-width: 180px;
                width: 180px;
            }
        }
        & .mobile-separator {
            display: none;
        }
    }
}

@include breakpoint(min-width 1200px) {
    .block-membership {
        &__chart {
            flex: 0 0 33.33%;
            max-width: 33.33%;
            justify-content: start;
            gap: 0.5rem;
            // padding: 1.5rem 0;
            canvas {
                width: 200px !important;
                height: 200px !important;
                padding: 1rem;
                margin-top: 0;
            }
        }
        &__map {
            padding-left: 2rem;
            padding-right: 2rem;
        }
    }
}

@include breakpoint(min-width 1300px) {
    .block-membership {
        &__chart {
            &-text {
                max-width: 200px;
                width: 200px;
                p {
                    // font-size: 18px !important;
                    font-weight: $ws-medium;
                }
                span {
                    font-size: 3rem !important;
                }
            }

        }
    }
}