.page-template-default {
  #primary {
    @include wrap();
    display: flex;
    margin-top: 1rem;
    aside {
      width: 25%;
      order: 0;
      .sub-pages__title {
        display: none;
      }
      .sub-pages {
        >ul {
          &>:first-child { // First child as "title" for navigation
            // color: red !important;
            border-bottom: none;
            >a {
              padding: 0 0 1rem;
              text-transform: uppercase;
              font-weight: $ws-semibold;
              font-size: 18px;
              border-bottom: none;
              background: $white;
              &:hover {
                background: $white;
                color: $primary;
                @include elementhover;
              }
            }
          }
          &>:last-child {
            border: 0;
          }
        }
        ul {
          list-style: none;
          padding-left: 0;
          li {
            background-color: #F2F8FC;
            transition: all .1s linear;
            border-bottom: 1px solid $light-blue-75;
            a:hover {
              background-color: #C9DFF1;
              color: $primary;
              &+button {
                background-color: #C9DFF1;
                fill: $link-use;
              }
            }
          }
          a {
            font-weight: $ws-medium;
            color: $link-use;
            font-size: 16px;
            text-decoration: none;
            padding-left: 1.5rem;
          }
        }
        ul ul {
          padding-left: 0;
          a {
            // font-weight: 600;
            padding-left: 2.3rem;
          }
          li.page_item_has_children > a {
            font-weight: $ws-medium;
            border-bottom: none;
          }
        }
        ul ul ul a {
          font-weight: $ws-medium;
        }
        ul ul ul>li>a {
          padding-left: 3rem;
        }
        ul ul ul ul>li>a {
          padding-left: 3.2rem;
        }
        a {
          padding: 0.75rem;
          display: block;
        }
        .page_item_has_children {
          display: flex;
          flex-wrap: wrap;
          > a {
            width: 80%;
            border-bottom: none;
          }
          > button {
            width: 20%;
          }
          .children {
            width: 100%;
            a { 
              color: $link-use; border-bottom: none;
              font-size: 15px; 
              padding-top: 0.5rem;
              padding-bottom: 0.5rem;
            }
            li {
              border: none;
            }
            a:hover { 
              color: $primary;
            }
            .current_page_item > a {
              color: $primary;
            }
          }
          button {
            border: 0;
            background-color: transparent;
            .navigation-arrow {
              transition: transform 0.2s;
            }
          }
          &>.children {
            display: none;
          }
          &.open {
            &>button>.navigation-arrow {
              transform: rotate(180deg);
            }
            &>.children {
              display: block;
              margin-bottom: 0;
            }
          }
        }
        .page_item_has_children:not(.current_page_item) {
          &>button>svg { fill: black; }
        }
        .page_item_has_children:not(.current_page_item):hover {
          button>svg { fill: black; }
        }
        .page_item_has_children.current_page_ancestor:hover {
          button>svg { fill: black; }
        }
        .page_item_has_children:not(.current_page_item):hover {
          &>button:hover>svg { fill: black; }
        }
        .page_item_has_children.current_page_item:hover {
          &>button:hover>svg { fill: white; }
        }
        .page_item_has_children.current_page_item:hover {
          &>button>svg { fill: white; }
        }
        .page_item_has_children.current_page_ancestor:hover {
          .page_item_has_children.current_page_item>button>svg { fill: white; }
        }

        .current_page_item {
          color: $primary;
          svg { fill: white; }
          > a {
            font-weight: $ws-semibold;
            background-color: #C9DFF1;
            color: $link-use;
          }
          &>button {
            background-color: #C9DFF1;
          }
          .children {
            width: 100%;
            margin: 0;
            a { color: $link-use; }
          }
        }
      }
      @include breakpoint($sub-l) {
        display: none;
      }
    }
    main {
      width: 75%;
      order: 1;
      padding-left: 3rem;
      @include breakpoint($sub-l) {
        width: 100%;
        padding-left: 0;
      }
      figcaption {
        font-size: 1rem;
      }

      .block-content-background--title {
        font-weight: 700;
        border-bottom: 1px solid $grey-dark;
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
      }
      &>p, &>article>p, &>article>div>p {
        font-size: 18px;
        a.external {
          @include external-link;
          padding-right: 24px;
        }
      }
      &>p.has-background.has-brand-background-color,
      &>p.has-background.has-main-background-color,
      &>p.has-background.has-very-dark-gray-background-color,
      &>article>p.has-background.has-brand-background-color,
      &>article>p.has-background.has-main-background-color,
      &>article>p.has-background.has-very-dark-gray-background-color,
      &>article>div>p.has-background.has-brand-background-color,
      &>article>div>p.has-background.has-main-background-color,
      &>article>div>p.has-background.has-very-dark-gray-background-color {
       a.external {
         @include external-link-white;
         padding-right: 24px;
       }
      }
    }
  }

}
