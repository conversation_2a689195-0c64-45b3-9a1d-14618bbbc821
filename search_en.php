<?php

/**
 * The template for displaying search results pages - empty for 2022 search
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#search-result
 *
 * @package kauppakamari
 */


/**
 * Recursively sort an array of taxonomy terms hierarchically. Child categories will be
 * placed under a 'children' member of their parent term.
 * @param Array   $cats     taxonomy term objects to sort
 * @param Array   $into     result array to put them in
 * @param integer $parentId the current parent ID to put them in
 */
function sort_terms_hierarchically(array &$cats, array &$into, $parentId = 0)
{
    foreach ($cats as $i => $cat) {
        if ($cat->parent == $parentId) {
            $into[$cat->term_id] = $cat;
            unset($cats[$i]);
        }
    }

    foreach ($into as $topCat) {
        $topCat->children = array();
        sort_terms_hierarchically($cats, $topCat->children, $topCat->term_id);
    }
}



/**
 * get_all_category_terms
 */

function get_all_category_terms()
{

    $category_terms = get_terms(array(
        'taxonomy' => 'category',
        'hide_empty' => false,
    ));

    return $category_terms;
}

?>

<?php get_template_part('partials/content/hero-search'); ?>

<div id="primary" class="primary primary--search primary--search-fi primary--page">

    <main id="main" class="main search-results-container">

        <p>Loading results...</p>
        <pre style="max-width: 500px;overflow: hidden">

    </pre>

    </main><!-- #main -->
    <aside class="search-nav">
        <div class="search-nav-box box-primary">
            <h2 class="box-title">Filter your search</h2>


            <h3>Content type</h3>
            <?php
            $article_type_terms = get_terms(array(
                'taxonomy' => 'article_type',
                'lang' => 'en',
                'hide_empty' => false,
                'meta_key'  => 'show_article_type_in_filters',
                'meta_value' => true,
            ));


            //wp_dropdown_categories( array( 'name' => 'category', 'taxonomy' => 'category' ) );
            //wp_dropdown_categories( array( 'name' => 'article_type', 'taxonomy' => 'article_type' ) );

            ?>

            <form id="search-filter-primary" class="search-filters">
                <fieldset>
                    <legend class="screen-reader-text">Filter your search</legend>

                    <input type="hidden" name="action" value="hskk_search_en" />
                    <input type="hidden" id="search_key" name="search_key" value="<?php echo $s; ?>" />
                    <input type="hidden" name="lang" value="" />
                    <input type="hidden" id="people_id" name="people_id" value="" />

                    <label class="control control-radio">
                        <input type="radio" name="post_type" value="" checked="checked" /><span>All</span></label>

                    <label class="control control-radio">
                        <input type="radio" name="post_type" value="page" /><span>Pages</span></label>


                    <?php
                    /*
          WP_Term Object
          (
              [term_id] => 27
              [name] => Uutiset
              [slug] => uutiset
              [term_group] => 0
              [term_taxonomy_id] => 27
              [taxonomy] => article_type
              [description] => Muun muassa lakimiesten ja asiantuntijoiden kirjoittamat artikkelit.
              [parent] => 0
              [count] => 1078
              [filter] => raw
              [term_order] => 1
          )
          */

                    foreach ($article_type_terms as $article_type_term) {

                        echo "\n";
                        $term_link = $article_type_term->slug;
                        if ($term_link === 'kaikki') {
                            echo ''; // Empty because the article type 'kaikki' is not used widely enough on the sites posts.
                            // Maybe changes in the future?
                        } else {
                            echo '<label class="control control-radio">';
                            echo '<input type="radio" name="post_type" value="' . $term_link . '" />';
                            echo '<span>' . $article_type_term->name . '</span>';
                            echo '</label>';
                        }
                    }

                    ?>

                    <label class="control control-radio">
                        <input type="radio" name="post_type" value="people" /><span>Contacts</span></label>



                    <?php
                    //teemat / category_terms

                    $category_terms = get_all_category_terms();
                    //echo '<pre>';
                    //print_r($category_terms);
                    //echo '</pre>';

                    //echo '<br>TREE MODEL:<br><br>';

                    $categories = get_terms('category', array('hide_empty' => false));
                    $categoryHierarchy = array();
                    sort_terms_hierarchically($categories, $categoryHierarchy);

                    //echo '<pre>';
                    //var_dump($categoryHierarchy);
                    //echo '</pre><hr>END END <br>';


                    foreach ($categoryHierarchy as $cat) {
                        //do not show release sublist:
                        if ($cat->slug == 'tiedotteet') {
                            //to prevent js errors:
                            echo '<div id="cat-' . $cat->slug . '" class="main-category-list main-category-list--empty hidden" data-slug="' . $cat->slug . '">';
                            echo '</div>';
                            continue;
                        } else if ($cat->slug == 'lausunnot') {
                            //to prevent js errors:
                            echo '<div id="cat-' . $cat->slug . '" class="main-category-list hidden" data-slug="' . $cat->slug . '">';
                            echo '<h3>Vuosi</h3>';
                            $years_arr = array();
                            for ($i = 2021; $i <= date('Y'); $i++) {
                                $years_arr[] = $i;
                                $term_link = $i;
                                echo '<label class="control control-radio">';
                                echo '<input type="checkbox" class="post_category_checkbox" name="post_category[]" value="' . $i . '" />';
                                echo '<span class="custom-checkbox"></span>';
                                echo $i;
                                echo '</label>';
                            }

                            echo '</div>';
                            continue;
                        }

                        echo '<div id="cat-' . $cat->slug . '" class="main-category-list hidden" data-slug="' . $cat->slug . '">';
                        echo '<h3>Teema</h3>';
                        echo '<!--' . $cat->slug . '-->';

                        if ($cat->children && sizeof($cat->children) > 0) {
                            foreach ($cat->children as $child_cat) {
                                $term_link = $child_cat->slug;
                                echo '<label class="control control-radio">';
                                echo '<input type="checkbox" class="post_category_checkbox" name="post_category[]" value="' . $term_link . '" />';
                                echo '<span class="custom-checkbox"></span>';
                                echo $child_cat->name;
                                echo '</label>';
                            }
                        }

                        echo '</div>';
                    }

                    //echo '<hr>END2 END2 <br>';
                    /*
          foreach ($category_terms as $category_term) {
            $active = ($category_term->slug == $article_category_parameter ? 'active' : '' );

            $term_link = get_term_link($category_term->term_id);

            echo '<li class="'.$active.'">';
            echo '<a href="'.$term_link.'" class="post-type-term-tab">'.$category_term->name .'</a>';
            echo '</li>';
          }
          */
                    ?>
                </fieldset>
            </form>

        </div>







        <div class="search-nav-box box-guide">
            <?php
            echo get_field('search_wysiwyg_fi', 'options');
            ?>
        </div>

    </aside>
</div><!-- #primary -->