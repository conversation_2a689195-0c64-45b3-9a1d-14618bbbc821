<?php

add_action('wp_ajax_nopriv_kauppakamari_filter_clubs', 'kauppakamari_filter_clubs');
add_action('wp_ajax_kauppakamari_filter_clubs', 'kauppakamari_filter_clubs');

function kauppakamari_filter_clubs() {


  $args = array(
    'post_type'       => 'club',
    'posts_per_page'  => 5,
    'meta_key'        => 'date',
    'orderby'         => 'meta_value',
    'order'           => 'asc'
  );

  $today = new \DateTime();

  $args['meta_query'] = [
    [
        //'key' => 'txtbox_start_date',
        'key' => 'date',
        'type' => 'DATETIME',
        'compare' => '>=',
        'value' => $today->format('Y-m-d H:i:s'),
    ]
  ];

  if( isset( $_POST['next_page'] ) ) {
    $next_page = $_POST['next_page'];
    $args['paged'] = $next_page;
  }

  $clubs = new WP_Query($args);

  if ( $clubs->have_posts() ) : ?>

  <?php while ( $clubs->have_posts() ) : $clubs->the_post();
      global $post; ?>
      <div class="block-clubs__item">
        <div class="block-clubs__item--content <?php echo get_field('content', $post->ID) ? 'accordion-toggle' : ''; ?>">
          <h5><?php the_title(); ?></h5>
          <span class="date"><?php echo date_i18n( 'l j.m.Y', strtotime( get_field('date', $post->ID) ) ); ?></span>
          <span class="time"><?php echo get_field('time', $post->ID) ? "<span class='separator'>I</span>" . get_field('time', $post->ID) : ""; ?></span>
          <span class="location"><?php echo get_field('location', $post->ID) ? "<span class='separator'>I</span>" . get_field('location', $post->ID) : ""; ?></span>
          <span class="icon-wrap"><?php echo get_field('content', $post->ID) ? kauppakamari_get_svg('chevron_right') . ' Lisää tietoa': ''; ?></span>
        </div>
        <?php if(get_field('link', $post->ID)) : ?>
          <a href="<?php echo get_field('link', $post->ID); ?>">Ilmoittaudu mukaan</a>
        <?php endif; ?>

        <?php if(get_field('content', $post->ID)) : ?>
          <div class="block-clubs__item--hidden">
            <?php echo get_field('content', $post->ID); ?>
          </div>
        <?php endif; ?>

      </div>

  <?php endwhile; ?>

  <?php wp_reset_postdata(); ?>

  <?php else : ?>
      <p><?php _e( 'Sorry, no posts matched your criteria.' ); ?></p>
  <?php endif;

  wp_send_json_success(ob_get_clean());

  wp_die();
}
