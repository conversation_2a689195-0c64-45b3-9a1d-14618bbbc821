{"version": 3, "file": "news-lift-wrapper/index.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAqC;AAMJ;AACsC;AACd;AACT;AAAA;AAEhD,MAAMiB,cAAc,GAAG,CAAC,qBAAqB,CAAC;AAC9C,MAAMC,SAAS,GAAG,EAAE;AACpB,MAAMC,kBAAkB,GAAG,MAAM;AAElB,SAASC,IAAIA,CAAC;EAAEC,UAAU;EAAEC,aAAa;EAAEC;AAAS,CAAC,EAAE;EACrE,MAAM;IAAEC,eAAe;IAAEC,KAAK;IAAEC,QAAQ;IAAEC;EAAQ,CAAC,GAAGN,UAAU;EAEhE,MAAMO,WAAW,GAAGpB,0DAAS,CAC3BqB,MAAM,IAAKA,MAAM,CAAC,mBAAmB,CAAC,CAACC,SAAS,CAACP,QAAQ,CAAC,EAC3D,CAACA,QAAQ,CACV,CAAC;EAED,MAAMQ,UAAU,GAAG9B,sEAAa,CAAC;IAChC+B,KAAK,EAAE;MACNR,eAAe,EAAEA,eAAe,IAAIL,kBAAkB;MACtDc,UAAU,EAAE,gCAAgC;MAC5CC,aAAa,EAAE;IAChB,CAAC;IACDC,SAAS,EAAE;EACZ,CAAC,CAAC;EAEF,MAAMC,cAAc,GAAGA,CAAC;IAAEC;EAAW,CAAC,KAAK;IAC1C,MAAM;MAAEC;IAAY,CAAC,GAAG7B,4DAAW,CAAC,mBAAmB,CAAC;IAExD,oBACCG,sDAAA,CAACL,yDAAM;MACNgC,OAAO,EAAC,SAAS;MACjBJ,SAAS,EAAC,YAAY;MACtBK,QAAQ,EAAEH,UAAW;MACrBI,OAAO,EAAEA,CAAA,KAAM;QACd,IAAI,CAACJ,UAAU,EAAE;UAChB,MAAMK,QAAQ,GAAGhC,8DAAW,CAAC,qBAAqB,CAAC;UACnD4B,WAAW,CAACI,QAAQ,EAAEC,SAAS,EAAEpB,QAAQ,CAAC;QAC3C;MACD,CAAE;MAAAqB,QAAA,EAEDP,UAAU,GAAG,yBAAyB,GAAG;IAAa,CAChD,CAAC;EAEX,CAAC;EAED,MAAMQ,gBAAgB,GAAG3C,4EAAmB,CAC3C;IACCiC,SAAS,EAAE;EACZ,CAAC,EACD;IACCW,aAAa,EAAE7B,cAAc;IAC7B8B,WAAW,EAAE,YAAY;IACzBC,YAAY,EAAE,KAAK;IACnBC,cAAc,EAAE;EACjB,CACD,CAAC;EAED,oBACCnC,uDAAA,CAAAE,uDAAA;IAAA4B,QAAA,gBACChC,sDAAA,CAACT,sEAAiB;MAAAyC,QAAA,eACjB9B,uDAAA,CAACT,4DAAS;QAACoB,KAAK,EAAEzB,mDAAE,CAAC,WAAW,EAAE,mBAAmB,CAAE;QAAA4C,QAAA,gBACtDhC,sDAAA,CAACN,8DAAW;UACX4C,KAAK,EAAElD,mDAAE,CAAC,SAAS,EAAE,mBAAmB,CAAE;UAC1CmD,KAAK,EAAE1B,KAAM;UACb2B,QAAQ,EAAGC,GAAG,IAAK/B,aAAa,CAAC;YAAEG,KAAK,EAAE4B;UAAI,CAAC;QAAE,CACjD,CAAC,eACFzC,sDAAA,CAACN,8DAAW;UACX4C,KAAK,EAAElD,mDAAE,CAAC,cAAc,EAAE,mBAAmB,CAAE;UAC/CmD,KAAK,EAAEzB,QAAS;UAChB0B,QAAQ,EAAGC,GAAG,IAAK/B,aAAa,CAAC;YAAEI,QAAQ,EAAE2B;UAAI,CAAC;QAAE,CACpD,CAAC,eACFzC,sDAAA,CAACN,8DAAW;UACX4C,KAAK,EAAElD,mDAAE,CAAC,YAAY,EAAE,mBAAmB,CAAE;UAC7CmD,KAAK,EAAExB,OAAQ;UACfyB,QAAQ,EAAGC,GAAG,IAAK/B,aAAa,CAAC;YAAEK,OAAO,EAAE0B;UAAI,CAAC;QAAE,CACnD,CAAC,eACFzC,sDAAA;UAAAgC,QAAA,EAAI5C,mDAAE,CAAC,YAAY,EAAE,mBAAmB;QAAC,CAAI,CAAC,eAC9CY,sDAAA,CAACR,iEAAY;UACZkD,MAAM,EAAE,CACP;YAAEC,IAAI,EAAE,eAAe;YAAEC,KAAK,EAAE;UAAU,CAAC,EAC3C;YAAED,IAAI,EAAE,cAAc;YAAEC,KAAK,EAAE;UAAU,CAAC,EAC1C;YAAED,IAAI,EAAE,sBAAsB;YAAEC,KAAK,EAAE;UAAU,CAAC,EAClD;YAAED,IAAI,EAAE,eAAe;YAAEC,KAAK,EAAE;UAAU,CAAC,EAC3C;YAAED,IAAI,EAAE,gBAAgB;YAAEC,KAAK,EAAE;UAAU,CAAC,EAC5C;YAAED,IAAI,EAAE,SAAS;YAAEC,KAAK,EAAE;UAAU,CAAC,CACpC;UACFL,KAAK,EAAE3B,eAAgB;UACvB4B,QAAQ,EAAGI,KAAK,IACflC,aAAa,CAAC;YAAEE,eAAe,EAAEgC,KAAK,IAAIrC;UAAmB,CAAC;QAC9D,CACD,CAAC;MAAA,CACQ;IAAC,CACM,CAAC,eAEpBL,uDAAA;MAAA,GAASiB,UAAU;MAAAa,QAAA,gBAClB9B,uDAAA;QAAKqB,SAAS,EAAC,WAAW;QAAAS,QAAA,gBACzB9B,uDAAA;UAAKqB,SAAS,EAAC,mBAAmB;UAAAS,QAAA,GAChCnB,KAAK,iBAAIb,sDAAA;YAAIuB,SAAS,EAAC,iBAAiB;YAAAS,QAAA,EAAEnB;UAAK,CAAK,CAAC,EACrDC,QAAQ,IAAIC,OAAO,iBACnBf,sDAAA;YAAGuB,SAAS,EAAC,gBAAgB;YAAAS,QAAA,eAC5BhC,sDAAA;cAAG6C,IAAI,EAAE9B,OAAQ;cAAAiB,QAAA,EAAElB;YAAQ,CAAI;UAAC,CAC9B,CACH;QAAA,CACG,CAAC,eACNd,sDAAA;UAAA,GAASiC;QAAgB,CAAG,CAAC;MAAA,CACzB,CAAC,eACNjC,sDAAA,CAACwB,cAAc;QAACC,UAAU,EAAET,WAAW,CAAC8B,MAAM,IAAIxC;MAAU,CAAE,CAAC;IAAA,CAC3D,CAAC;EAAA,CACL,CAAC;AAEL;;;;;;;;;;;;;;;;;ACrHA;AACA;AACA;AACA;AACA;AACsD;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACsB;;AAEtB;AACA;AACA;AAC0B;AACA;AACU;;AAEpC;AACA;AACA;AACA;AACA;AACAyC,oEAAiB,CAAEE,6CAAa,EAAE;EACjC;AACD;AACA;EACCC,IAAI,EAAE1C,6CAAI;EAEV;AACD;AACA;EACCwC,IAAIA,+CAAAA;AACL,CAAE,CAAC;;;;;;;;;;;;;;;;;;ACtCmD;AAAA;AAEvC,SAASA,IAAIA,CAAC;EAAEvC;AAAW,CAAC,EAAE;EAC5C,MAAM;IAAEI,KAAK;IAAEC,QAAQ;IAAEC,OAAO;IAAEH;EAAgB,CAAC,GAAGH,UAAU;EAChE,MAAMF,kBAAkB,GAAG,MAAM;EAEjC,oBACCP,sDAAA;IACCuB,SAAS,EAAC,oCAAoC;IAC9CH,KAAK,EAAE;MACNR,eAAe,EAAEA,eAAe,IAAIL,kBAAkB;MACtDc,UAAU,EAAE,gCAAgC;MAC5CC,aAAa,EAAE;IAChB,CAAE;IAAAU,QAAA,eAEF9B,uDAAA;MAAKqB,SAAS,EAAC,WAAW;MAAAS,QAAA,gBACzB9B,uDAAA;QAAKqB,SAAS,EAAC,mBAAmB;QAAAS,QAAA,gBACjChC,sDAAA;UAAIuB,SAAS,EAAC,+DAA+D;UAAAS,QAAA,EAC3EnB;QAAK,CACH,CAAC,EACJC,QAAQ,IAAIC,OAAO,iBACnBf,sDAAA;UACCuB,SAAS,EAAC,8CAA8C;UACxDH,KAAK,EAAE;YAAEgC,aAAa,EAAE;UAAY,CAAE;UAAApB,QAAA,eAEtChC,sDAAA;YAAG6C,IAAI,EAAE9B,OAAQ;YAAAiB,QAAA,EAAElB;UAAQ,CAAI;QAAC,CAC9B,CACH;MAAA,CACG,CAAC,eACNd,sDAAA;QAAKuB,SAAS,EAAC,kBAAkB;QAAAS,QAAA,eAChChC,sDAAA,CAACmD,gEAAW,CAACE,OAAO,IAAE;MAAC,CACnB,CAAC;IAAA,CACF;EAAC,CACF,CAAC;AAER;;;;;;;;;;;ACnCA;;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;UAEA;UACA;;;;;WCzBA;WACA;WACA;WACA;WACA,+BAA+B,wCAAwC;WACvE;WACA;WACA;WACA;WACA,iBAAiB,qBAAqB;WACtC;WACA;WACA,kBAAkB,qBAAqB;WACvC;WACA;WACA,KAAK;WACL;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;;;;WC3BA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;WCNA;;WAEA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,MAAM,qBAAqB;WAC3B;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;WACA;WACA;;;;;UEjDA;UACA;UACA;UACA;UACA", "sources": ["webpack://news-lift-wrapper/./src/news-lift-wrapper/edit.js", "webpack://news-lift-wrapper/./src/news-lift-wrapper/index.js", "webpack://news-lift-wrapper/./src/news-lift-wrapper/save.js", "webpack://news-lift-wrapper/./src/news-lift-wrapper/style.scss?fb52", "webpack://news-lift-wrapper/external window [\"wp\",\"blockEditor\"]", "webpack://news-lift-wrapper/external window [\"wp\",\"blocks\"]", "webpack://news-lift-wrapper/external window [\"wp\",\"components\"]", "webpack://news-lift-wrapper/external window [\"wp\",\"data\"]", "webpack://news-lift-wrapper/external window [\"wp\",\"i18n\"]", "webpack://news-lift-wrapper/external window \"ReactJSXRuntime\"", "webpack://news-lift-wrapper/webpack/bootstrap", "webpack://news-lift-wrapper/webpack/runtime/chunk loaded", "webpack://news-lift-wrapper/webpack/runtime/compat get default export", "webpack://news-lift-wrapper/webpack/runtime/define property getters", "webpack://news-lift-wrapper/webpack/runtime/hasOwnProperty shorthand", "webpack://news-lift-wrapper/webpack/runtime/make namespace object", "webpack://news-lift-wrapper/webpack/runtime/jsonp chunk loading", "webpack://news-lift-wrapper/webpack/before-startup", "webpack://news-lift-wrapper/webpack/startup", "webpack://news-lift-wrapper/webpack/after-startup"], "sourcesContent": ["import { __ } from \"@wordpress/i18n\";\nimport {\n\tuseBlockProps,\n\tuseInnerBlocksProps,\n\tInspectorControls,\n\tColorPalette,\n} from \"@wordpress/block-editor\";\nimport { PanelBody, TextControl, Button } from \"@wordpress/components\";\nimport { useSelect, useDispatch } from \"@wordpress/data\";\nimport { createBlock } from \"@wordpress/blocks\";\n\nconst ALLOWED_BLOCKS = [\"hskk/news-lift-item\"];\nconst MAX_ITEMS = 10;\nconst DEFAULT_BACKGROUND = \"#fff\";\n\nexport default function Edit({ attributes, setAttributes, clientId }) {\n\tconst { backgroundColor, title, linkText, linkUrl } = attributes;\n\n\tconst innerBlocks = useSelect(\n\t\t(select) => select(\"core/block-editor\").getBlocks(clientId),\n\t\t[clientId],\n\t);\n\n\tconst blockProps = useBlockProps({\n\t\tstyle: {\n\t\t\tbackgroundColor: backgroundColor || DEFAULT_BACKGROUND,\n\t\t\tpaddingTop: \"var(--wp--preset--spacing--50)\",\n\t\t\tpaddingBottom: \"var(--wp--preset--spacing--50)\",\n\t\t},\n\t\tclassName: \"news-lift-slider-wrapper alignwide\",\n\t});\n\n\tconst CustomAppender = ({ isDisabled }) => {\n\t\tconst { insertBlock } = useDispatch(\"core/block-editor\");\n\n\t\treturn (\n\t\t\t<Button\n\t\t\t\tvariant=\"primary\"\n\t\t\t\tclassName=\"submit-btn\"\n\t\t\t\tdisabled={isDisabled}\n\t\t\t\tonClick={() => {\n\t\t\t\t\tif (!isDisabled) {\n\t\t\t\t\t\tconst newBlock = createBlock(\"hskk/news-lift-item\");\n\t\t\t\t\t\tinsertBlock(newBlock, undefined, clientId);\n\t\t\t\t\t}\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t{isDisabled ? \"Maksimimäärä saavutettu\" : \"Lisää nosto\"}\n\t\t\t</Button>\n\t\t);\n\t};\n\n\tconst innerBlocksProps = useInnerBlocksProps(\n\t\t{\n\t\t\tclassName: \"news-lift-slider\",\n\t\t},\n\t\t{\n\t\t\tallowedBlocks: ALLOWED_BLOCKS,\n\t\t\torientation: \"horizontal\",\n\t\t\ttemplateLock: false,\n\t\t\trenderAppender: false,\n\t\t},\n\t);\n\n\treturn (\n\t\t<>\n\t\t\t<InspectorControls>\n\t\t\t\t<PanelBody title={__(\"Asetukset\", \"news-lift-wrapper\")}>\n\t\t\t\t\t<TextControl\n\t\t\t\t\t\tlabel={__(\"Otsikko\", \"news-lift-wrapper\")}\n\t\t\t\t\t\tvalue={title}\n\t\t\t\t\t\tonChange={(val) => setAttributes({ title: val })}\n\t\t\t\t\t/>\n\t\t\t\t\t<TextControl\n\t\t\t\t\t\tlabel={__(\"Linkkiteksti\", \"news-lift-wrapper\")}\n\t\t\t\t\t\tvalue={linkText}\n\t\t\t\t\t\tonChange={(val) => setAttributes({ linkText: val })}\n\t\t\t\t\t/>\n\t\t\t\t\t<TextControl\n\t\t\t\t\t\tlabel={__(\"Linkin URL\", \"news-lift-wrapper\")}\n\t\t\t\t\t\tvalue={linkUrl}\n\t\t\t\t\t\tonChange={(val) => setAttributes({ linkUrl: val })}\n\t\t\t\t\t/>\n\t\t\t\t\t<p>{__(\"Taustaväri\", \"news-lift-wrapper\")}</p>\n\t\t\t\t\t<ColorPalette\n\t\t\t\t\t\tcolors={[\n\t\t\t\t\t\t\t{ name: \"Koralli Light\", color: \"#FFE4E0\" },\n\t\t\t\t\t\t\t{ name: \"Beige Medium\", color: \"#F3F0EA\" },\n\t\t\t\t\t\t\t{ name: \"Vaaleansininen Light\", color: \"#E8F1F9\" },\n\t\t\t\t\t\t\t{ name: \"Sininen Light\", color: \"#D5DAE7\" },\n\t\t\t\t\t\t\t{ name: \"Vaaleansininen\", color: \"#A5C9E7\" },\n\t\t\t\t\t\t\t{ name: \"Koralli\", color: \"#FD9180\" },\n\t\t\t\t\t\t]}\n\t\t\t\t\t\tvalue={backgroundColor}\n\t\t\t\t\t\tonChange={(color) =>\n\t\t\t\t\t\t\tsetAttributes({ backgroundColor: color || DEFAULT_BACKGROUND })\n\t\t\t\t\t\t}\n\t\t\t\t\t/>\n\t\t\t\t</PanelBody>\n\t\t\t</InspectorControls>\n\n\t\t\t<div {...blockProps}>\n\t\t\t\t<div className=\"alignwide\">\n\t\t\t\t\t<div className=\"news-lift-heading\">\n\t\t\t\t\t\t{title && <h2 className=\"news-lift-title\">{title}</h2>}\n\t\t\t\t\t\t{linkText && linkUrl && (\n\t\t\t\t\t\t\t<p className=\"news-lift-link\">\n\t\t\t\t\t\t\t\t<a href={linkUrl}>{linkText}</a>\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t)}\n\t\t\t\t\t</div>\n\t\t\t\t\t<div {...innerBlocksProps} />\n\t\t\t\t</div>\n\t\t\t\t<CustomAppender isDisabled={innerBlocks.length >= MAX_ITEMS} />\n\t\t\t</div>\n\t\t</>\n\t);\n}\n", "/**\n * Registers a new block provided a unique name and an object defining its behavior.\n *\n * @see https://developer.wordpress.org/block-editor/reference-guides/block-api/block-registration/\n */\nimport { registerBlockType } from '@wordpress/blocks';\n\n/**\n * Lets webpack process CSS, SASS or SCSS files referenced in JavaScript files.\n * All files containing `style` keyword are bundled together. The code used\n * gets applied both to the front of your site and to the editor.\n *\n * @see https://www.npmjs.com/package/@wordpress/scripts#using-css\n */\nimport './style.scss';\n\n/**\n * Internal dependencies\n */\nimport Edit from './edit';\nimport save from './save';\nimport metadata from './block.json';\n\n/**\n * Every block starts by registering a new block type definition.\n *\n * @see https://developer.wordpress.org/block-editor/reference-guides/block-api/block-registration/\n */\nregisterBlockType( metadata.name, {\n\t/**\n\t * @see ./edit.js\n\t */\n\tedit: Edit,\n\n\t/**\n\t * @see ./save.js\n\t */\n\tsave,\n} );\n", "import { InnerBlocks } from \"@wordpress/block-editor\";\n\nexport default function save({ attributes }) {\n\tconst { title, linkText, linkUrl, backgroundColor } = attributes;\n\tconst DEFAULT_BACKGROUND = \"#fff\";\n\n\treturn (\n\t\t<div\n\t\t\tclassName=\"news-lift-slider-wrapper alignfull\"\n\t\t\tstyle={{\n\t\t\t\tbackgroundColor: backgroundColor || DEFAULT_BACKGROUND,\n\t\t\t\tpaddingTop: \"var(--wp--preset--spacing--50)\",\n\t\t\t\tpaddingBottom: \"var(--wp--preset--spacing--50)\",\n\t\t\t}}\n\t\t>\n\t\t\t<div className=\"alignwide\">\n\t\t\t\t<div className=\"news-lift-heading\">\n\t\t\t\t\t<h2 className=\"wp-block-heading has-main-color has-text-color has-link-color\">\n\t\t\t\t\t\t{title}\n\t\t\t\t\t</h2>\n\t\t\t\t\t{linkText && linkUrl && (\n\t\t\t\t\t\t<p\n\t\t\t\t\t\t\tclassName=\"has-main-color has-text-color has-link-color\"\n\t\t\t\t\t\t\tstyle={{ textTransform: \"uppercase\" }}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<a href={linkUrl}>{linkText}</a>\n\t\t\t\t\t\t</p>\n\t\t\t\t\t)}\n\t\t\t\t</div>\n\t\t\t\t<div className=\"news-lift-slider\">\n\t\t\t\t\t<InnerBlocks.Content />\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n", "// extracted by mini-css-extract-plugin\nexport {};", "module.exports = window[\"wp\"][\"blockEditor\"];", "module.exports = window[\"wp\"][\"blocks\"];", "module.exports = window[\"wp\"][\"components\"];", "module.exports = window[\"wp\"][\"data\"];", "module.exports = window[\"wp\"][\"i18n\"];", "module.exports = window[\"ReactJSXRuntime\"];", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = (result, chunkIds, fn, priority) => {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar [chunkIds, fn, priority] = deferred[i];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t\"news-lift-wrapper/index\": 0,\n\t\"news-lift-wrapper/style-index\": 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = globalThis[\"webpackChunknews_lift_wrapper\"] = globalThis[\"webpackChunknews_lift_wrapper\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [\"news-lift-wrapper/style-index\"], () => (__webpack_require__(\"./src/news-lift-wrapper/index.js\")))\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n", ""], "names": ["__", "useBlockProps", "useInnerBlocksProps", "InspectorCont<PERSON><PERSON>", "ColorPalette", "PanelBody", "TextControl", "<PERSON><PERSON>", "useSelect", "useDispatch", "createBlock", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ALLOWED_BLOCKS", "MAX_ITEMS", "DEFAULT_BACKGROUND", "Edit", "attributes", "setAttributes", "clientId", "backgroundColor", "title", "linkText", "linkUrl", "innerBlocks", "select", "getBlocks", "blockProps", "style", "paddingTop", "paddingBottom", "className", "CustomAppender", "isDisabled", "insertBlock", "variant", "disabled", "onClick", "newBlock", "undefined", "children", "innerBlocksProps", "allowedBlocks", "orientation", "templateLock", "renderAppender", "label", "value", "onChange", "val", "colors", "name", "color", "href", "length", "registerBlockType", "save", "metadata", "edit", "InnerBlocks", "textTransform", "Content"], "sourceRoot": ""}