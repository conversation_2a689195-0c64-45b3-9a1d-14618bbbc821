<?php
/**
 * Cache hooks and functions
 *
 * @package kauppakamari
 */

/**
 * Get last-edited timestamp
 *
 * @global array $kauppakamari_timestamps cached timestamp values
 *
 * @param string $asset ID of asset type
 *
 * @return int UNIX timestamp
 */
function kauppakamari_last_edited($asset = 'css') {

  global $kauppakamari_timestamps;

  // save timestamps to cache in global variable for this request
  if (empty($kauppakamari_timestamps)) {

    $filepath = get_template_directory() . '/assets/last-edited.json';

    if (file_exists($filepath)) {
      $json = file_get_contents($filepath);
      $kauppakamari_timestamps = json_decode($json, true);
    }

  }

  // use cached value from global variable
  if (isset($kauppakamari_timestamps[$asset])) {
    return absint($kauppakamari_timestamps[$asset]);
  }

  return 0;

}
