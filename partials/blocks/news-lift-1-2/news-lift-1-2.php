<?php

/**
 * News list
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-news-lift-1-2' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-news-lift-1-2';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$post_object = get_field('featured_article');
$block_title = get_field('title');
$block_link = get_field('link');

?>
<div class="block-news-lift-1-2-outer">
<?php
if(!empty($block_title)) {
  echo '<h2><a href="/artikkelityyppi/uutiset/">' . $block_title . '</a></h2>';
}
?>

<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">

<?php
    $args = array(
      'post_type'       => array('post'),
      'posts_per_page'  => 1,
      'orderby'         => 'date',
      'order'           => 'desc',
      'tax_query' => array(
              array (
                  'taxonomy' => 'article_type',
                  'field' => 'slug',
                  'terms' => 'uutiset',
              )
          ),
      //'paged'           => $paged
    );

    $latest = new WP_Query($args);
  ?>

  <?php if(!empty($post_object)) { ?>
    <div class="block-news-lift-1-2__wrap">
      <?php
      global $post;
      $post = $post_object;
      setup_postdata( $post );
      $not_in = $post->ID;
      ?>


      <?php get_template_part('partials/content/teaser-front-new', 'front-new'); ?>
      <?php wp_reset_postdata(); ?>
    </div>
  <?php } else { ?>
    <div class="block-news-lift-1-2__wrap">
      <?php if ( $latest->have_posts() ) : ?>

          <?php while ( $latest->have_posts() ) : $latest->the_post();
              global $post;
              $not_in = $post->ID;
              ?>
            <?php get_template_part('partials/content/teaser', 'front-new'); ?>
            <?php break; ?>
          <?php endwhile; ?>

          <?php wp_reset_postdata();
          wp_reset_query(); ?>

      <?php else : ?>
          <p><?php _e( 'Sorry, no posts matched your criteria.' ); ?></p>
      <?php endif; ?>
    </div>
  <?php } ?>





  <div class="block-news-lift-1-2__wrap">

    <?php
      $args = array(
        'post_type'       => array('post'),
        'posts_per_page'  => 2,
        'orderby'         => 'date',
        'order'           => 'desc',
        'post__not_in'    => array($not_in),
        'tax_query' => array(
                array (
                    'taxonomy' => 'article_type',
                    'field' => 'slug',
                    'terms' => 'uutiset',
                )
            ),
        //'paged'           => $paged
      );
      wp_reset_query();
      wp_reset_postdata();
      $news = new WP_Query($args);
    ?>

    <div class="block-news-lift-1-2__ajax">

    <?php $lift_n = 0;
    if ( $news->have_posts() ) : ?>

        <?php while ( $news->have_posts() && $lift_n < 2 ) : $news->the_post();
            $lift_n++;
            global $post; ?>
            <div class="block-news-lift-1-2__item">
              <div class="block-news-lift-1-2__item--teaser__thumbnail">
                <a href="<?php the_permalink(); ?>">
                  <?php echo kauppakamari_get_image(get_post_thumbnail_id(), 'article'); ?>
                </a>
                <?php echo kauppakamari_get_post_meta_new(); ?>
              </div>

              <div class="block-news-lift-1-2__item--content">
              <a href="<?php echo get_the_permalink(); ?>">
                <h3 class="title"><?php the_title(); ?></h3>
              </a>
                <?php
                if (get_field('ingress', $post->ID)) {
                  echo excerpt(16, get_field('ingress', $post->ID));
                } else {
                  //echo the_excerpt();
                  echo excerpt(16);
                }
                ?>
              </div>
            </div>

        <?php endwhile; ?>

        <?php
        wp_reset_query();
        wp_reset_postdata();
        ?>

    <?php else : ?>
        <p><?php _e( 'Sorry, no posts matched your criteria.' ); ?></p>
    <?php endif; ?>



    </div>



  </div>

</div>

<?php
if(!empty($block_link)) {
  echo '<p class="block-news-lift-1-2-bottom"><a href="'.$block_link['url'].'" '.($block_link['target'] ? 'target="_blank"' : '' ).'>'. $block_link['title'] .'</a></p>';
}
?>

</div>
<!-- outer end -->

