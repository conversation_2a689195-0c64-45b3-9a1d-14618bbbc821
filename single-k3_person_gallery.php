<?php
/**
 * Default template for pages.
 * THIS FILE IS IN THE THEME FOLDER, BELONGS TO k3-person-gallery -plugin.
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package kauppakamari
 */
$isProduction = true;
$k3_person_template_page_id = 41061; //production

if(wp_get_environment_type() == 'staging' || wp_get_environment_type() == 'development') {
  $k3_person_template_page_id = 37760; //staging
  $isProduction = false;
}
if(isset($_SERVER) && isset($_SERVER['WP_ENV'])) {
  if($_SERVER['WP_ENV'] == 'staging' || $_SERVER['WP_ENV'] == 'development') {
    $isProduction = false;
    $k3_person_template_page_id = 37760; //staging
  }
  /* put .env file with wp-config.php in seravo */
}
$siteUrl = get_site_url();
if (stristr($siteUrl, '.local') !== false) {
  $isProduction = false;
  $k3_person_template_page_id = 935637; //dev local
}



/* for hero, need to remove single class */
add_filter('body_class', function (array $classes) {
    if (in_array('single', $classes)) {
      unset( $classes[array_search('single', $classes)] );
    }
    if (!in_array('page-template-default', $classes)) {
      return array_merge( $classes, array('page-template-default'));
    }
  return $classes;
});

/*
  Override Yoast SEO Breadcrumb Trail
*/

add_filter( 'wpseo_breadcrumb_links', 'k3_chamber_override_yoast_breadcrumb_trail' );
  function k3_chamber_override_yoast_breadcrumb_trail( $links ) {
      //global $post;
      global $k3_person_template_page_id;
      //$oldpost = $post;

      $ppId = wp_get_post_parent_id($k3_person_template_page_id);
      $bc = array(array('url' => get_home_url(), 'text' => 'Etusivu'));
      while($ppId) {
        $bc[] = array(
            'url' => get_permalink( $ppId ),
            'text' => get_the_title( $ppId ),
          );
        $ppId = wp_get_post_parent_id($ppId);

      }


      //$post = get_post( $k3_person_template_page_id, OBJECT );
      //setup_postdata( $post );

          $bc[] = array(
            'url' => get_permalink( $k3_person_template_page_id ),
            'text' => get_the_title( $k3_person_template_page_id ),
          );
          array_splice( $links, 0, -1, $bc );
      return $links;
  }

get_header();

$old_id = $id;

$post = get_post( $k3_person_template_page_id, OBJECT );
setup_postdata( $post );


$hide_h1_title = get_field('hide_h1_title');
get_template_part('partials/content/hero-page');
wp_reset_postdata();
$post = get_post( $old_id, OBJECT );
?>
<style>
    .single-k3-person main{display:-ms-flexbox;display:flex;padding-left:1.5rem;padding-right:1.5rem;margin-bottom:4rem;}
    .single-k3-person main figure{-ms-flex:4;flex:4;margin:0;max-width:380px;height:auto;}.single-k3-person main article{-ms-flex:7;flex:7}.single-k3-person main article label{text-transform:uppercase;font-size:12px;color:#737373;letter-spacing:1px;line-height:22.5px;font-weight:600;}.single-k3-person main article h1,.single-k3-person main article p{margin-left:0}.single-k3-person main article a{color:#3a75c4}
        .single-k3-person main .k3pg-pill{display:inline-block;padding:2px 10px;background:#DFE9F6;margin-right:6px;color:#002662;margin-bottom:6px;}.single-k3-person main .k3pg-icon{display:inline-block;width:20px;height:20px;background-size:contain;background-repeat:no-repeat;background-position:50%;position:relative;top:5px;text-align:center}.single-k3-person main .button--back{color:#fff}.k3pg-results{background:#f1f1f1;padding:1px 1rem;display:block}.k3pg-results .kp3g-person-card-container{display:grid;grid-template-columns:1fr 1fr 1fr 1fr;grid-gap:1rem}.k3pg-results .k3pg-person-card{margin-bottom:2rem;padding-bottom:60px;position:relative}.k3pg-results .k3pg-person-card figure{width:100%;height:0;padding-bottom:100%;background-size:cover;background-repeat:no-repeat;background-position:50% 10%}.k3pg-results .k3pg-person-card h3{font-size:16px;color:#002f40;letter-spacing:.12px;line-height:24.5px;margin-bottom:0}.k3pg-results .k3pg-person-card .k3pg-nomargin{margin-bottom:5px}.k3pg-results .k3pg-person-card .k3pg-location{text-transform:uppercase;font-size:12px;color:#3a75c4;letter-spacing:.9px;line-height:22.5px}.k3pg-results .k3pg-person-card .button{position:absolute;bottom:1rem}

    @media (max-width: 768px) {
        .single-k3-person main{display:block;}
        .single-k3-person main h1 {
          white-space: break-spaces;
          max-width: 95vw;
        }
        .single-k3-person main figure img {
          max-width: 70%;
        }
        .single-k3-person main .k3pg-icon {
            display: inline;
        }
        .single-k3-person main p > a.external {
          word-break: break-all;
          max-width: 95vw;
        }
    }

    .k3_person_gallery label {
      color: #737373;
    }
    </style>


    <div id="primary" class="primary primary--page single-k3-person">

      <main id="main" class="main">

         <?php while (have_posts()) : the_post(); ?>

          <?php
          /*
          if(!$post->post_parent) {
            wp_update_post(
                array(
                    'ID' => $post->ID,
                    'post_parent' => $k3_person_template_page_id
                )
            );
          }
          */


          $imgID = get_post_meta($post->ID, "image", true);
          $img = wp_get_attachment_image($imgID, "large");

          $uploadedImage = get_post_meta($post->ID, "uploaded_image", true);

          if (isset($uploadedImage)) {
            $uploadedImage = "<img src='$uploadedImage' alt='" . get_the_title() . "'>";
          }

          if (!empty($img)) {
            $uploadedImage = $img;
          }

          ?>
          <article id="post-<?php the_ID();?>" <?php post_class('entry entry--page');?>>
            <div class="entry__content wysiwyg">
              <h1><?php the_title();?></h1>

              <figure class="img-responsive k3-person-image">
                <?php echo $uploadedImage ?>
              </figure>

              <label>Tittelit</label>
              <p><?php echo get_post_meta($post->ID, "titles", true) ?></p>

              <label>Yritykset</label>
              <p><?php echo get_post_meta($post->ID, "companies", true) ?></p>

              <label>Koulutustaso/oppiarvo</label>
              <p><?php echo get_post_meta($post->ID, "studies", true) ?></p>

              <label>Yhteystiedot</label>
              <p>
                <span class="k3pg-icon k3pg-icon--map"><svg width="20" height="20" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><path d="M0 0h20v20H0z"/><circle stroke="#000" stroke-width="1.28" stroke-linecap="round" stroke-linejoin="round" cx="10" cy="8.125" r="2.5"/><path d="M16.25 8.125c0 5.625-6.25 10-6.25 10s-6.25-4.375-6.25-10a6.25 6.25 0 1112.5 0z" stroke="#000" stroke-width="1.28" stroke-linecap="round" stroke-linejoin="round"/></g></svg></span>
                <?php echo get_post_meta($post->ID, "city", true) ?>,
                <?php echo get_post_meta($post->ID, "country", true) ?>
                <br>
                <span class="k3pg-icon k3pg-icon--email"><svg width="16" height="16" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><path d="M0 0h16v16H0z"/><circle stroke="#000" stroke-width=".96" stroke-linecap="round" stroke-linejoin="round" cx="8" cy="8" r="2.5"/><path d="M11.318 13A6 6 0 1114 8c0 1.38-.5 2.5-1.75 2.5S10.5 9.38 10.5 8V5.5" stroke="#000" stroke-width=".96" stroke-linecap="round" stroke-linejoin="round"/></g></svg></span>
                <?php echo get_post_meta($post->ID, "email", true) ?>
                <br>
                <span class="k3pg-icon k3pg-icon--phone"><svg width="16" height="16" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><path d="M0 0h16v16H0z"/><path d="M5.78 7.801a5.272 5.272 0 002.44 2.43c.159.075.345.06.49-.037l1.565-1.044a.5.5 0 01.474-.043l2.927 1.254a.498.498 0 01.3.519A3 3 0 0111 13.5 8.5 8.5 0 012.5 5a3 3 0 012.62-2.976.498.498 0 01.519.3l1.255 2.93a.5.5 0 01-.04.47l-1.04 1.59a.496.496 0 00-.034.487z" stroke="#000" stroke-width=".96" stroke-linecap="round" stroke-linejoin="round"/></g></svg></span>
                <?php echo get_post_meta($post->ID, "phone", true) ?>
              </p>

              <label>Kielitaito</label>
              <?php
              $langs = get_post_meta($post->ID, "languages", true);
              $langs = explode(",", $langs);
              echo "<p>";
              foreach ($langs as $lang) {
                echo "<span class='k3pg-pill'>" . $lang . "</span>";
              }
              echo "</p>";
              ?>

              <label>Toimiala</label>
              <?php
              $sectors = get_post_meta($post->ID, "sectors", true);
              $sectors = explode(",", $sectors);
              echo "<p>";
              foreach ($sectors as $sector) {
                echo "<span class='k3pg-pill'>" . $sector . "</span>";
              }
              echo "</p>";
              ?>

              <label>Osaamisalueet</label>
              <?php
              $tags = get_post_meta($post->ID, "tags", true);
              $tags = explode(",", $tags);
              echo "<p>";
              foreach ($tags as $tag) {
                echo "<span class='k3pg-pill'>" . $tag . "</span>";
              }
              echo "</p>";
              ?>

              <label>Minusta</label>

              <?php the_content();?>

              <hr>
              <?php

              $link1 = get_post_meta($post->ID, "link_1", true);
              $link2 = get_post_meta($post->ID, "link_2", true);
              $link3 = get_post_meta($post->ID, "link_3", true);
              $video = get_post_meta($post->ID, "video_url", true);

              if (!empty($link1)) {
                echo "<label>Linkkejä</label>";
                echo "<p><a href='$link1'>$link1</a>";
              }
              if (!empty($link2)) {
                echo "<br><a href='$link2'>$link2</a>";
              }
              if (!empty($link3)) {
                echo "<br><a href='$link2'>$link2</a>";
              }
              if (!empty($link1)) {
                echo "</p>";
              }

              if (!empty($video)) {
                echo "<label>Video</label>";
                echo "<p><a href='$video'>$video</a></p>";
              }

              /*
              <a class="button button--back" href="<?php echo site_url() ?>/hab-hyvaksytty-advisory-boardin-jasen/"> < Palaa HAB-verkostoon</a>
              */
              ?>


            </div>
          </article>

         <?php endwhile; ?>

      </main><!-- #main -->

      <aside>
        <?php
        //$post = get_post( $k3_person_template_page_id, OBJECT );
        //setup_postdata( $post );
        //get_sidebar();
        global $pretend_id;
        $pretend_id = $k3_person_template_page_id;
        kauppakamari_sub_pages_navigation();
        ?>
      </aside>

    </div><!-- #primary -->

    <?php
    get_footer();
