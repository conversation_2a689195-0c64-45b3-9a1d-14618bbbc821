<?php



function register_acf_block_types() {

  $block_svg = '<svg xmlns="http://www.w3.org/2000/svg" width="44" height="44" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="Layer_1" x="0px" y="0px" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512;" xml:space="preserve"> <style type="text/css"> .st0{fill:#002663;} .st1{fill:#FFFFFF;} </style> <title>Artboard 1</title> <polygon class="st0" points="0,512 512,512 512,0 0,0 "/> <polygon class="st1" points="284.7,341.3 325.8,341.3 262.7,252 318.3,171 280.3,171 232.6,246.3 225.6,246.4 225.6,171 192.2,171 192.2,341.3 225.6,341.3 225.6,268.5 234.3,268.5 "/> </svg>';
  /**
   * Icon Text
   */

  acf_register_block_type(array(
    'name'              => 'icon-text',
    'title'             => __('Ikoni & teksti -kolumnit'),
    'description'       => __('Kaksi vierekkäistä laatikkoa ikonilla ja tekstillä'),
    'render_template'   => 'partials/blocks/icon-text/icon-text.php',
    'category'          => 'forkauppakamari-blocksmatting',
    'icon'              => $block_svg,
    'keywords'          => array( 'icon', 'text' ),
    'align'             => 'wide',
  ));

  /**
   * Icon Text
   */

  acf_register_block_type(array(
    'name'              => 'text-image',
    'title'             => __('Kaksi Teksti & Kuva'),
    'description'       => __('Kaksi vierekkäistä laatikkoa kuvalla ja tekstillä'),
    'render_template'   => 'partials/blocks/text-image/text-image.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'image', 'text' ),
    'align'             => 'wide',
  ));

  /**
   * Yksi iso text-image 2023
   */

   acf_register_block_type(array(
    'name'              => 'highlight-1',
    'title'             => __('Teksti & Kuva'),
    'description'       => __('Yksi suurempi laatikko kuvalla ja tekstillä'),
    'render_template'   => 'partials/blocks/highlight-1/highlight-1.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * Accordion 'haitari' 2023
   */

   acf_register_block_type(array(
    'name'              => 'accordion',
    'title'             => __('Haitari'),
    'description'       => __('Haitarielementti'),
    'render_template'   => 'partials/blocks/accordion/accordion.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * Slider 'slaideri' 2023
   */

   acf_register_block_type(array(
    'name'              => 'slider',
    'title'             => __('Slider'),
    'description'       => __('Sliderelementti'),
    'render_template'   => 'partials/blocks/slider/slider.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * Job-listing (avoimet työpaikat) 2023
   */

   acf_register_block_type(array(
    'name'              => 'job-listing',
    'title'             => __('Työpaikat'),
    'description'       => __('Avoimien työpaikkojen listaus'),
    'render_template'   => 'partials/blocks/job-listing/job-listing.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'full',
  ));

  /**
   * Kaksi pientä nostoa 2023
   */

   acf_register_block_type(array(
    'name'              => 'small-lift-2',
    'title'             => __('Kaksi värinostoa'),
    'description'       => __('Kaksi vierekkäistä taustavärillistä "kortti" nostoa'),
    'render_template'   => 'partials/blocks/small-lift-2/small-lift-2.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * Jäsenyys diagrammit
   */

   acf_register_block_type(array(
    'name'              => 'membership-charts',
    'title'             => __('Jäsenyys -diagrammit'),
    'description'       => __('Jäsenyys -diagrammit (suunniteltu etusivulle)'),
    'render_template'   => 'partials/blocks/membership-charts/membership-charts.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * Animoidut luvut
   */

   acf_register_block_type(array(
    'name'              => 'animated-numbers',
    'title'             => __('Jäsenyyslukuja'),
    'description'       => __('Tietoa jäsenyydestä animoiduilla numeroilla'),
    'render_template'   => 'partials/blocks/animated-numbers/animated-numbers.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * Tapahtumat lista (etusivu)
   */

   acf_register_block_type(array(
    'name'              => 'event-calendar-fp',
    'title'             => __('Tapahtumakalenteri (etusivu)'),
    'description'       => __('Tapahtuma- ja koulutuskalenteri josta karsittu muutamia elementtejä'),
    'render_template'   => 'partials/blocks/event-calendar-fp/event-calendar-fp.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * Trainer
   */

  acf_register_block_type(array(
    'name'              => 'trainer',
    'title'             => __('Kouluttaja'),
    'description'       => __('Näytä yksi kouluttaja ja hänen tiedoja'),
    'render_template'   => 'partials/blocks/trainer/trainer.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * Tabs
   */

  acf_register_block_type(array(
    'name'              => 'tabs',
    'title'             => __('Välilehdet'),
    'description'       => __('Lisää sisältöä välilehtiin'),
    'render_template'   => 'partials/blocks/tabs/tabs.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * Featured
   */

  acf_register_block_type(array(
    'name'              => 'teaser',
    'title'             => __('Nostot'),
    'description'       => __('Nostolaatikot'),
    'render_template'   => 'partials/blocks/teaser/teaser.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * Clubs
   */

  acf_register_block_type(array(
    'name'              => 'clubs',
    'title'             => __('Klubit'),
    'description'       => __('Klubit'),
    'render_template'   => 'partials/blocks/clubs/clubs.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * Lomake
   */

  acf_register_block_type(array(
    'name'              => 'gravityforms',
    'title'             => __('Lomake'),
    'description'       => __('Lomake'),
    'render_template'   => 'partials/blocks/gravityforms/gravityforms.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * product-teasers
   */

  acf_register_block_type(array(
    'name'              => 'product-teasers',
    'title'             => __('Koulutusnostot'),
    'description'       => __('Koulutusnostot'),
    'render_template'   => 'partials/blocks/product-teasers/product-teasers.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * trainer-list
   */

  acf_register_block_type(array(
    'name'              => 'trainer-list',
    'title'             => __('Klubivetäjät'),
    'description'       => __('Klubivetäjät / Kouluttajat'),
    'render_template'   => 'partials/blocks/trainer-list/trainer-list.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * people
   */

  acf_register_block_type(array(
    'name'              => 'people',
    'title'             => __('Henkilöstö'),
    'description'       => __('Henkilöstö'),
    'render_template'   => 'partials/blocks/people/people.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * Color teasers
   */

  acf_register_block_type(array(
    'name'              => 'color-teasers',
    'title'             => __('Värilliset nostot'),
    'description'       => __('Värilliset nostot, 3 kpl'),
    'render_template'   => 'partials/blocks/color-teasers/color-teasers.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * news-list
   */

  acf_register_block_type(array(
    'name'              => 'news-list',
    'title'             => __('Uutiset'),
    'description'       => __('Uusimmat uutiset'),
    'render_template'   => 'partials/blocks/news-list/news-list.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * news-lift-1-2 2022-01
   */

  acf_register_block_type(array(
    'name'              => 'news-lift-1-2',
    'title'             => __('Uutiset 1-2'),
    'description'       => __('Nosto ja 2 uutista'),
    'render_template'   => 'partials/blocks/news-lift-1-2/news-lift-1-2.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * news-lift-3 2022-01
   */

  acf_register_block_type(array(
    'name'              => 'news-lift-3',
    'title'             => __('Uutiset 3'),
    'description'       => __('Nosto uutisista 3 saraketta'),
    'render_template'   => 'partials/blocks/news-lift-3/news-lift-3.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));
  /**
   * read-more-3 2022-01 - Note! uses the same php template as news-lift-3
   */

  acf_register_block_type(array(
    'name'              => 'read-more-3',
    'title'             => __('Lue lisää 3'),
    'description'       => __('Relevanssin nosto uutisista, 3 saraketta'),
    'render_template'   => 'partials/blocks/news-lift-3/news-lift-3.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'full',
  ));

  /**
   * post-columns
   */

  acf_register_block_type(array(
    'name'              => 'post-columns',
    'title'             => __('Nostokolumnit'),
    'description'       => __('Vierekkäiset nostot kirjoittajan kuvalla'),
    'render_template'   => 'partials/blocks/post-columns/post-columns.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));


  /**
   * video-modal
   */

  acf_register_block_type(array(
    'name'              => 'video-modal',
    'title'             => __('Video Modal'),
    'description'       => __('Videolohko'),
    'render_template'   => 'partials/blocks/video-modal/video-modal.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'full',
  ));

  /**
   * events (external events)
   */

  acf_register_block_type(array(
    'name'              => 'events-external',
    'title'             => __('Tapahtumat (API)'),
    'description'       => __('Tapahtumat ulkoisesta API:sta'),
    'render_template'   => 'partials/blocks/events-external/events-external.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * column-features
   */

  acf_register_block_type(array(
    'name'              => 'column-features',
    'title'             => __('Nostokolumnit taustalla'),
    'description'       => __('Nostokolumnit taustalla'),
    'render_template'   => 'partials/blocks/column-features/column-features.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'full',
  ));

  /**
   * hubspot
   */

  acf_register_block_type(array(
    'name'              => 'hubspot',
    'title'             => __('Hubspot cta / lomake'),
    'description'       => __('Hubspot cta / lomake'),
    'render_template'   => 'partials/blocks/hubspot/hubspot.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * jasentarinat
   */

  acf_register_block_type(array(
    'name'              => 'memberstories',
    'title'             => __('Jäsentarinat'),
    'description'       => __('Jäsentarinat'),
    'render_template'   => 'partials/blocks/memberstories/memberstories.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * article teaser
   */

  acf_register_block_type(array(
    'name'              => 'article-teaser',
    'title'             => __('Artikkelinosto'),
    'description'       => __('Artikkelinosto'),
    'render_template'   => 'partials/blocks/article-teaser/article-teaser.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * article teaser
   */

  acf_register_block_type(array(
    'name'              => 'content-background',
    'title'             => __('Sisältö taustalla'),
    'description'       => __('Sisältö harmaalla taustalla ja numeroidulla listalla.'),
    'render_template'   => 'partials/blocks/content-background/content-background.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'full',
  ));

  /**
   * article teaser
   */

  acf_register_block_type(array(
    'name'              => 'price-text',
    'title'             => __('Hinnasto'),
    'description'       => __('Hinnasto, iconilla, tekstillä'),
    'render_template'   => 'partials/blocks/price-text/price-text.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * people select
   */

  acf_register_block_type(array(
    'name'              => 'people-select',
    'title'             => __('Henkilöstövalinta'),
    'description'       => __('Henkilöstö valinnalla'),
    'render_template'   => 'partials/blocks/people-select/people-select.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * Presentations
   */

  acf_register_block_type(array(
    'name'              => 'Presentations',
    'title'             => __('Tapahtumien esitykset'),
    'description'       => __('Tapahtumien esitykset'),
    'render_template'   => 'partials/blocks/presentations/presentations.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));



  /**
   * Shopify karuselli
   */

  acf_register_block_type(array(
    'name'              => 'shopify-scroller',
    'title'             => __('Shopify karuselli'),
    'description'       => __('Shopify karuselli'),
    'render_template'   => 'partials/blocks/shopify_carousel/shopify_carousel.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * Tapahtuma ja koulutuslistaus
   */

  acf_register_block_type(array(
    'name'              => 'event-calendar',
    'title'             => __('Tapahtuma- ja koulutuslistaus'),
    'description'       => __('Tapahtuma- ja koulutuslistaus'),
    'render_template'   => 'partials/blocks/event-calendar/event_calendar.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

  /**
   * Shopify-koulutus ja tapahtumakalenteri
   */

  acf_register_block_type(array(
    'name'              => 'event-calendar-vue',
    'title'             => __('Tapahtumat ja koulutukset (VUE)'),
    'description'       => __('Tapahtumat ja koulutukset (VUE)'),
    'render_template'   => 'partials/blocks/shopify_events_workshops/shopify_events_workshops.php',
    'category'          => 'forkauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'icon', 'text' ),
    'align'             => 'wide',
  ));


  /**
   * Uudet jäsenet
   */

  acf_register_block_type(array(
    'name'              => 'new-members',
    'title'             => 'Uudet jäsenet',
    'description'       => 'Uudet jäsenet',
    'render_template'   => 'partials/blocks/new_members/new_members.php',
    'category'          => 'kauppakamari-blocks',
    'icon'              => $block_svg,
    'keywords'          => array( 'custom' ),
    'align'             => 'wide',
  ));

}

// Check if function exists and hook into setup.
if ( function_exists('acf_register_block_type') ) {

  add_action('acf/init', 'register_acf_block_types');
}

function kauppakamari_block_categories( $categories, $post ) {
  $block_svg = '<svg xmlns="http://www.w3.org/2000/svg" width="44" height="44" viewBox="5 2 10 15">
  <path fill="#F04E30" fill-rule="evenodd" d="M14.708 8.298c0-.333.3-.407.3-.407 1.748-.501 1.727-2.537 1.727-2.537l-2.064.067A2.708 2.708 0 0 0 12.291 4a2.696 2.696 0 0 0-2.696 2.696c0 .825.422 1.812 1.052 2.529l.125.152S10.063 8.9 8.603 8.9c-1.356 0-1.664.488-2.233.488-.693 0-.962-.417-.962-.417A4.934 4.934 0 0 0 5 10.927c0 1.18.427 2.273 1.131 3.167.24.335.546.644.905.92.351.287 1.18.705 1.18.705a7.81 7.81 0 0 0 3.218.685c3.074 0 5.566-1.787 5.566-3.992 0-1.01-.524-1.93-1.386-2.634.003 0-.906-.816-.906-1.48z"/>
  </svg>';

  return array_merge(
      $categories,
      array(
          array(
              'slug' => 'kauppakamari-blocks',
              'title' => __( 'Kauppakamarin lohkot', 'my-plugin' ),
              'icon'  => $block_svg,
          ),
      )
  );
}
add_filter( 'block_categories', 'kauppakamari_block_categories', 10, 2 );

//2025 new blocks:
require_once get_theme_file_path('/blocks/rss-feed/rss-feed.php');

function register_native_blocks() {
  register_block_type( get_template_directory() . '/blocks/news-lift-slider/news-lift-wrapper/build/news-lift-wrapper' );
    register_block_type( get_template_directory() . '/blocks/news-lift-slider/news-lift-item/build/news-lift-item' );
    register_block_type( get_template_directory() . '/blocks/rss-feed/build/rss-feed' );
}
add_action('init', 'register_native_blocks');