<?php
/**
 * Function for custom search form
 *
 * @package kauppakamari
 */

/**
 * Custom search form
 *
 * @example kauppakamari_search_form('search-header')
 * @example kauppakamari_search_form('search-header', ['submit' => kauppakamari_get_svg('submit')])
 *
 * @param string $id HTML ID tag
 * @param array  $args options for form element
 */
function kauppakamari_search_form($id, $args = array()) {

  // set defaults
  $defaults = array(
    'class'              => 'search-form',
    'action'             => home_url('/'),
    'value'              => get_search_query(),
    'name'               => 's',
    'submit'             => ask__('Search: Submit'), // can be HTML, icons etc
    'placeholder'        => ask__('Search: Placeholder'),
    'screen-reader-text' => ask__('Search: Screen reader label'),
  );

  // parse args
  $args = wp_parse_args($args, $defaults);

  // extend classlist instead of replacing it
  if ($defaults['class'] !== $args['class']) {
    $args['class'] .= ' ' . $defaults['class'];
  }

  // create ID for input
  $input_id = $id . '-input';

  ?>
  <form id="<?php echo esc_attr($id); ?>" role="search" method="get" class="<?php echo esc_attr($args['class']); ?>" action="<?php echo esc_url($args['action']); ?>">

    <label for="<?php echo esc_attr($input_id); ?>" class="search-form__label screen-reader-text"><?php echo esc_attr($args['screen-reader-text']); ?></label>

    <input
      type="search"
      class="search-form__input"
      id="<?php echo esc_attr($input_id); ?>"
      name="<?php echo esc_attr($args['name']); ?>"
      value="<?php echo esc_attr($args['value']); ?>"
      placeholder="<?php echo esc_attr($args['placeholder']); ?>"
    />

    <button type="submit" class="search-form__submit"><?php echo $args['submit']; ?></button>

  </form>
  <?php

}

//test for catch ajax-autocomplete:
function filter_relevanssi_live_search_alter_results($found_posts=null) {
  global $wp_query; // The found posts are stored in the global $wp_query object in $wp_query->posts_found.

  error_log('started filter live juttu');
  error_log(print_r($found_posts, true));
  error_log(print_r('toka:', true));
  error_log(print_r($wp_query->posts_found, true));

}
//add_filter('relevanssi_live_search_alter_results', 'filter_relevanssi_live_search_alter_results', 99);

/**
 * Filter function for 'All' Search 2022
 *
 *
 */
function rlv_pages_top_sorting( $hits ) {
  $people_posts   = array();
  $rest_of_the_posts   = array();
  $pages                = array();
  $posts_from_this_year    = array();
  $rest_of_the_posts    = array();

  foreach ( $hits[0] as $_post ) {
    $post_object = relevanssi_get_an_object( $_post )['object'];

    if ( 'page' === $post_object->post_type ) {
      $pages[] = $_post;
    }
    elseif ( date( 'Y' ) === date( 'Y', strtotime( $post_object->post_date_gmt ) ) ) {
      $posts_from_this_year[] = $_post;
    }

    else {
      $rest_of_the_posts[] = $_post;
    }
  }
  $hits[0] = array_merge( $pages, $posts_from_this_year, $rest_of_the_posts );
  return $hits;
}
//filter will be added in search-functions.php
//add_filter( 'relevanssi_hits_filter', 'rlv_pages_top_sorting' );
