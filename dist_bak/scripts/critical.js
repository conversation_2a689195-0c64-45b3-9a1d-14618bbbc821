"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}!function(e){var t=function(l,k){"use strict";if(!k.getElementsByClassName){return}var D,H,O=k.documentElement,u=l.Date,r=l.HTMLPictureElement,f="addEventListener",P="getAttribute",$=l[f],I=l.setTimeout,c=l.requestAnimationFrame||I,d=l.requestIdleCallback,q=/^picture$/i,n=["load","error","lazyincluded","_lazyloaded"],a={},j=Array.prototype.forEach,G=function e(t,i){if(!a[i]){a[i]=new RegExp("(\\s|^)"+i+"(\\s|$)")}return a[i].test(t[P]("class")||"")&&a[i]},J=function e(t,i){if(!G(t,i)){t.setAttribute("class",(t[P]("class")||"").trim()+" "+i)}},K=function e(t,i){var a;if(a=G(t,i)){t.setAttribute("class",(t[P]("class")||"").replace(a," "))}},Q=function e(t,i,a){var r=a?f:"removeEventListener";if(a){e(t,i)}n.forEach(function(e){t[r](e,i)})},U=function e(t,i,a,r,n){var o=k.createEvent("Event");if(!a){a={}}a.instance=D;o.initEvent(i,!r,!n);o.detail=a;t.dispatchEvent(o);return o},V=function e(t,i){var a;if(!r&&(a=l.picturefill||H.pf)){if(i&&i.src&&!t[P]("srcset")){t.setAttribute("srcset",i.src)}a({reevaluate:true,elements:[t]})}else if(i&&i.src){t.src=i.src}},X=function e(t,i){return(getComputedStyle(t,null)||{})[i]},s=function e(t,i,a){a=a||t.offsetWidth;while(a<H.minSize&&i&&!t._lazysizesWidth){a=i.offsetWidth;i=i.parentNode}return a},Y=function(){var a,r;var i=[];var n=[];var o=i;var s=function e(){var t=o;o=i.length?n:i;a=true;r=false;while(t.length){t.shift()()}a=false};var e=function e(t,i){if(a&&!i){t.apply(this,arguments)}else{o.push(t);if(!r){r=true;(k.hidden?I:c)(s)}}};e._lsFlush=s;return e}(),Z=function e(i,t){return t?function(){Y(i)}:function(){var e=this;var t=arguments;Y(function(){i.apply(e,t)})}},e=function e(t){var i;var a=0;var r=H.throttleDelay;var n=H.ricTimeout;var o=function e(){i=false;a=u.now();t()};var s=d&&n>49?function(){d(o,{timeout:n});if(n!==H.ricTimeout){n=H.ricTimeout}}:Z(function(){I(o)},true);return function(e){var t;if(e=e===true){n=33}if(i){return}i=true;t=r-(u.now()-a);if(t<0){t=0}if(e||t<9){s()}else{I(s,t)}}},ee=function e(t){var i,a;var r=99;var n=function e(){i=null;t()};var o=function e(){var t=u.now()-a;if(t<r){I(e,r-t)}else{(d||n)(n)}};return function(){a=u.now();if(!i){i=I(o,r)}}};(function(){var e;var t={lazyClass:"lazyload",loadedClass:"lazyloaded",loadingClass:"lazyloading",preloadClass:"lazypreload",errorClass:"lazyerror",autosizesClass:"lazyautosizes",srcAttr:"data-src",srcsetAttr:"data-srcset",sizesAttr:"data-sizes",minSize:40,customMedia:{},init:true,expFactor:1.5,hFac:.8,loadMode:2,loadHidden:true,ricTimeout:0,throttleDelay:125};H=l.lazySizesConfig||l.lazysizesConfig||{};for(e in t){if(!(e in H)){H[e]=t[e]}}l.lazySizesConfig=H;I(function(){if(H.init){i()}})})();var t=function(){var y,m,c,z,i;var h,p,g,b,C,A,E;var o=/^img$/i;var d=/^iframe$/i;var _="onscroll"in l&&!/(gle|ing)bot/.test(navigator.userAgent);var w=0;var M=0;var N=0;var S=-1;var v=function e(t){N--;if(!t||N<0||!t.target){N=0}};var x=function e(t){if(E==null){E=X(k.body,"visibility")=="hidden"}return E||X(t.parentNode,"visibility")!="hidden"&&X(t,"visibility")!="hidden"};var L=function e(t,i){var a;var r=t;var n=x(t);g-=i;A+=i;b-=i;C+=i;while(n&&(r=r.offsetParent)&&r!=k.body&&r!=O){n=(X(r,"opacity")||1)>0;if(n&&X(r,"overflow")!="visible"){a=r.getBoundingClientRect();n=C>a.left&&b<a.right&&A>a.top-1&&g<a.bottom+1}}return n};var t=function e(){var t,i,a,r,n,o,s,l,u,f,c,d;var v=D.elements;if((z=H.loadMode)&&N<8&&(t=v.length)){i=0;S++;f=!H.expand||H.expand<1?O.clientHeight>500&&O.clientWidth>500?500:370:H.expand;D._defEx=f;c=f*H.expFactor;d=H.hFac;E=null;if(M<c&&N<1&&S>2&&z>2&&!k.hidden){M=c;S=0}else if(z>1&&S>1&&N<6){M=f}else{M=w}for(;i<t;i++){if(!v[i]||v[i]._lazyRace){continue}if(!_){R(v[i]);continue}if(!(l=v[i][P]("data-expand"))||!(o=l*1)){o=M}if(u!==o){h=innerWidth+o*d;p=innerHeight+o;s=o*-1;u=o}a=v[i].getBoundingClientRect();if((A=a.bottom)>=s&&(g=a.top)<=p&&(C=a.right)>=s*d&&(b=a.left)<=h&&(A||C||b||g)&&(H.loadHidden||x(v[i]))&&(m&&N<3&&!l&&(z<3||S<4)||L(v[i],o))){R(v[i]);n=true;if(N>9){break}}else if(!n&&m&&!r&&N<4&&S<4&&z>2&&(y[0]||H.preloadAfterLoad)&&(y[0]||!l&&(A||C||b||g||v[i][P](H.sizesAttr)!="auto"))){r=y[0]||v[i]}}if(r&&!n){R(r)}}};var a=e(t);var W=function e(t){var i=t.target;if(i._lazyCache){delete i._lazyCache;return}v(t);J(i,H.loadedClass);K(i,H.loadingClass);Q(i,T);U(i,"lazyloaded")};var r=Z(W);var T=function e(t){r({target:t.target})};var B=function e(t,i){try{t.contentWindow.location.replace(i)}catch(e){t.src=i}};var F=function e(t){var i;var a=t[P](H.srcsetAttr);if(i=H.customMedia[t[P]("data-media")||t[P]("media")]){t.setAttribute("media",i)}if(a){t.setAttribute("srcset",a)}};var s=Z(function(t,e,i,a,r){var n,o,s,l,u,f;if(!(u=U(t,"lazybeforeunveil",e)).defaultPrevented){if(a){if(i){J(t,H.autosizesClass)}else{t.setAttribute("sizes",a)}}o=t[P](H.srcsetAttr);n=t[P](H.srcAttr);if(r){s=t.parentNode;l=s&&q.test(s.nodeName||"")}f=e.firesLoad||"src"in t&&(o||n||l);u={target:t};J(t,H.loadingClass);if(f){clearTimeout(c);c=I(v,2500);Q(t,T,true)}if(l){j.call(s.getElementsByTagName("source"),F)}if(o){t.setAttribute("srcset",o)}else if(n&&!l){if(d.test(t.nodeName)){B(t,n)}else{t.src=n}}if(r&&(o||l)){V(t,{src:n})}}if(t._lazyRace){delete t._lazyRace}K(t,H.lazyClass);Y(function(){var e=t.complete&&t.naturalWidth>1;if(!f||e){if(e){J(t,"ls-is-cached")}W(u);t._lazyCache=true;I(function(){if("_lazyCache"in t){delete t._lazyCache}},9)}},true)});var R=function e(t){var i;var a=o.test(t.nodeName);var r=a&&(t[P](H.sizesAttr)||t[P]("sizes"));var n=r=="auto";if((n||!m)&&a&&(t[P]("src")||t.srcset)&&!t.complete&&!G(t,H.errorClass)&&G(t,H.lazyClass)){return}i=U(t,"lazyunveilread").detail;if(n){te.updateElem(t,true,t.offsetWidth)}t._lazyRace=true;N++;s(t,i,n,r,a)};var n=function e(){if(m){return}if(u.now()-i<999){I(e,999);return}var t=ee(function(){H.loadMode=3;a()});m=true;H.loadMode=3;a();$("scroll",function(){if(H.loadMode==3){H.loadMode=2}t()},true)};return{_:function e(){i=u.now();D.elements=k.getElementsByClassName(H.lazyClass);y=k.getElementsByClassName(H.lazyClass+" "+H.preloadClass);$("scroll",a,true);$("resize",a,true);if(l.MutationObserver){new MutationObserver(a).observe(O,{childList:true,subtree:true,attributes:true})}else{O[f]("DOMNodeInserted",a,true);O[f]("DOMAttrModified",a,true);setInterval(a,999)}$("hashchange",a,true);["focus","mouseover","click","load","transitionend","animationend","webkitAnimationEnd"].forEach(function(e){k[f](e,a,true)});if(/d$|^c/.test(k.readyState)){n()}else{$("load",n);k[f]("DOMContentLoaded",a);I(n,2e4)}if(D.elements.length){t();Y._lsFlush()}else{a()}},checkElems:a,unveil:R}}(),te=function(){var a;var o=Z(function(e,t,i,a){var r,n,o;e._lazysizesWidth=a;a+="px";e.setAttribute("sizes",a);if(q.test(t.nodeName||"")){r=t.getElementsByTagName("source");for(n=0,o=r.length;n<o;n++){r[n].setAttribute("sizes",a)}}if(!i.detail.dataAttr){V(e,i.detail)}});var r=function e(t,i,a){var r;var n=t.parentNode;if(n){a=s(t,n,a);r=U(t,"lazybeforesizes",{width:a,dataAttr:!!i});if(!r.defaultPrevented){a=r.detail.width;if(a&&a!==t._lazysizesWidth){o(t,n,r,a)}}}};var e=function e(){var t;var i=a.length;if(i){t=0;for(;t<i;t++){r(a[t])}}};var t=ee(e);return{_:function e(){a=k.getElementsByClassName(H.autosizesClass);$("resize",t)},checkElems:t,updateElem:r}}(),i=function e(){if(!e.i){e.i=true;te._();t._()}};return D={cfg:H,autoSizer:te,loader:t,init:i,uP:V,aC:J,rC:K,hC:G,fire:U,gW:s,rAF:Y}}(e,e.document);e.lazySizes=t,"object"==("undefined"==typeof module?"undefined":_typeof(module))&&module.exports&&(module.exports=t)}(window),document.addEventListener("lazybeforeunveil",function(e){var t=e.target;t.addEventListener("load",function(){var e=t.nextSibling;e&&e!==t&&null!=e&&1===e.nodeType&&(void 0===e.classList.contains||e.classList.contains("lazyload-preload"))&&e.classList.add("lazyload-preload--ready")})});