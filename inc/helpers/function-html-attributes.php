<?php
/**
 * Function for image attributes
 *
 * @package kauppakamari
 */

/**
 * Build html attributes from key-value array
 *
 * @param array $attr key-value array of attribute names and values
 *
 * @return string attributes for html element
 */
function kauppakamari_build_attributes($attr) {

  $attr_str = '';
  foreach ($attr as $key => $value) {
    if (!empty($value) || is_numeric($value)) {
      $attr_str .= ' ' . esc_attr($key) . '="' . esc_attr($value) . '"';
    } else {
      if($key === 'alt') {
        $attr_str .= ' alt="Artikkelikuva"';
      }
      else {
        $attr_str .= ' ' . esc_attr($key);
      }
    }
  }
  return $attr_str;

}
