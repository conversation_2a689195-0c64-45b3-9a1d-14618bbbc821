/* ==========================================================================
 # Fonts
========================================================================== */

/* Font families
----------------------------------------------- */

$font-text:  "Work Sans", "myriad-pro", "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
$font-title: "Work Sans", "myriad-pro", "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;

/* Font weights
----------------------------------------------- */

$light:   300;
$normal:  400;
$bold:    700;
$heavy:   900;


/* Work Sans font weights*/
$ws-regular:  400;
$ws-medium:   500;
$ws-semibold: 600;
$ws-bold:     700;
/**/

/* Line heights
----------------------------------------------- */

$line-height-small:   1.25;
$line-height:         1.5;
$line-height-big:     1.75;

/* Font faces
----------------------------------------------- */

/* Work Sans */
@font-face {
  font-family: 'Work Sans';
  font-style: normal;
  font-weight: 400;
  src: url('../fonts/WorkSans-Regular.ttf');
  src: local('WorkSans-Regular'), local('WorkSans-Regular'),
       url('../fonts/WorkSans-Regular.ttf')  format('truetype');
}

@font-face {
  font-family: 'Work Sans';
  font-style: normal;
  font-weight: 500;
  src: url('../fonts/WorkSans-Medium.ttf');
  src: local('WorkSans-Medium'), local('WorkSans-Medium'),
       url('../fonts/WorkSans-Medium.ttf')  format('truetype');
}

@font-face {
  font-family: 'Work Sans';
  font-style: normal;
  font-weight: 600;
  src: url('../fonts/WorkSans-SemiBold.ttf');
  src: local('WorkSans-SemiBold'), local('WorkSans-SemiBold'),
       url('../fonts/WorkSans-SemiBold.ttf')  format('truetype');
}

@font-face {
  font-family: 'Work Sans';
  font-style: normal;
  font-weight: 700;
  src: url('../fonts/WorkSans-Bold.ttf');
  src: local('WorkSans-Bold'), local('WorkSans-Bold'),
       url('../fonts/WorkSans-Bold.ttf')  format('truetype');
}
/* --------------- */

/*@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 300;
  src: url('../fonts/montserrat-v12-latin-300.eot');
  src: local('Montserrat Light'), local('Montserrat-Light'),
       url('../fonts/montserrat-v12-latin-300.eot?#iefix')     format('embedded-opentype'),
       url('../fonts/montserrat-v12-latin-300.woff2')          format('woff2'),
       url('../fonts/montserrat-v12-latin-300.woff')           format('woff'),
       url('../fonts/montserrat-v12-latin-300.ttf')            format('truetype'),
       url('../fonts/montserrat-v12-latin-300.svg#Montserrat') format('svg');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 400;
  src: url('../fonts/montserrat-v12-latin-regular.eot');
  src: local('Montserrat Regular'), local('Montserrat-Regular'),
       url('../fonts/montserrat-v12-latin-regular.eot?#iefix')     format('embedded-opentype'),
       url('../fonts/montserrat-v12-latin-regular.woff2')          format('woff2'),
       url('../fonts/montserrat-v12-latin-regular.woff')           format('woff'),
       url('../fonts/montserrat-v12-latin-regular.ttf')            format('truetype'),
       url('../fonts/montserrat-v12-latin-regular.svg#Montserrat') format('svg');
}

@font-face {
  font-family: 'Montserrat';
  font-style: italic;
  font-weight: 400;
  src: url('../fonts/montserrat-v12-latin-italic.eot');
  src: local('Montserrat Italic'), local('Montserrat-Italic'),
       url('../fonts/montserrat-v12-latin-italic.eot?#iefix')     format('embedded-opentype'),
       url('../fonts/montserrat-v12-latin-italic.woff2')          format('woff2'),
       url('../fonts/montserrat-v12-latin-italic.woff')           format('woff'),
       url('../fonts/montserrat-v12-latin-italic.ttf')            format('truetype'),
       url('../fonts/montserrat-v12-latin-italic.svg#Montserrat') format('svg');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 700;
  src: url('../fonts/montserrat-v12-latin-700.eot');
  src: local('Montserrat Bold'), local('Montserrat-Bold'),
       url('../fonts/montserrat-v12-latin-700.eot?#iefix')     format('embedded-opentype'),
       url('../fonts/montserrat-v12-latin-700.woff2')          format('woff2'),
       url('../fonts/montserrat-v12-latin-700.woff')           format('woff'),
       url('../fonts/montserrat-v12-latin-700.ttf')            format('truetype'),
       url('../fonts/montserrat-v12-latin-700.svg#Montserrat') format('svg');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 900;
  src: url('../fonts/montserrat-v12-latin-900.eot');
  src: local('Montserrat Black'), local('Montserrat-Black'),
       url('../fonts/montserrat-v12-latin-900.eot?#iefix')      format('embedded-opentype'),
       url('../fonts/montserrat-v12-latin-900.woff2')           format('woff2'),
       url('../fonts/montserrat-v12-latin-900.woff')            format('woff'),
       url('../fonts/montserrat-v12-latin-900.ttf')             format('truetype'),
       url('../fonts/montserrat-v12-latin-900.svg#Montserrat')  format('svg');
}
*/