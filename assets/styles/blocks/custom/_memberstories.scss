.block-memberstories {
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  padding: 0 1.5rem;
  .slick-prev {
    right: 5.5rem;
    left: auto;
    top: 90% !important;
    bottom: 0px !important;
    background-color: transparent !important;
    width: 2rem !important;
    height: 2rem !important;
    min-width: 2.5rem !important;
    min-height: 2.5rem !important;
    @include breakpoint($sub-l) {
      top: 95% !important;
      right: 5.5rem;
    }
  }
  .slick-next {
    right: 2.5rem;
    top: 90% !important;
    bottom: 0px !important;
    background-color: transparent !important;
    width: 2rem !important;
    height: 2rem !important;
    min-width: 2.5rem !important;
    min-height: 2.5rem !important;
    @include breakpoint($sub-l) {
      top: 95% !important;
      right: 2.5rem;
    }
  }
  &__img {
    width: 34%;
    background-size: cover;
    background-position: center;
    height: 100%;
    height: 300px;

    @include breakpoint($sub-l) {
      width: 100%;
      height: 300px;
      margin-bottom: $gap;
    }
  }
  &__content {
    z-index: 10;
    width: 62%;
    @include breakpoint($sub-l) {
      width: 100%;
    }
  }

  &__item {
    padding: 2rem;
    display: flex;
    align-items: center;
    align-items: inherit !important;
    justify-content: space-between;
    flex-wrap: wrap;
    position: relative;

    img {
      width: 30%;
      padding-bottom: 2rem;

      @include breakpoint($sub-l) {
        width: 100%;
      }
    }

    .quote {
      font-size: 22px;
      font-weight: 600;
      /*text-transform: italic;*/
      margin-bottom: 2rem;
      display: block;
    }
    .name {
      color: black;
      width: 100%;
      display: block;
      font-size: 1rem;
      font-weight: 600;
    }
    .subject {
      color: $grey-aa;
      width: 100%;
      display: block;
      text-transform: uppercase;
      font-size: 15px;
      font-weight: 400;
      letter-spacing: 0.8px;
    }
    a {
      @include button;
      margin-top: 1.5rem;
      display: block;
      width: auto;
      min-width: 150px;
      text-align: center;
      border: 1px solid $primary;
      color: white;
      background: $primary;
      width: fit-content;
      padding-top: 1rem;
      &:hover {
        background: #1d397f !important;
        border: 1px solid #1d397f !important;
      }
      &.external {
        background-color: $primary;
        border: 1px solid $primary;
        color: white;
        padding-left: 30px;
        padding-right: 55px;
        &:hover {
          background-color: white;
          color: #002663;
        }
        &:after {
          position: absolute;
          background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23ffffff' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
          background-position: center;
          background-size: 84%;
          width: 1.1rem;
          height: 1rem;
          display: inline-block;
          content: " ";
          margin-left: 8px;
          color: transparent;
          background-repeat: no-repeat;
        }
      }
      &:hover {
        background-color: $primary;
        border-color: $primary;
        color: white;
      }
    }
  }
}
