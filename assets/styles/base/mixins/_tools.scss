/* ==========================================================================
 # Tool mixins
========================================================================== */

// aspect ratio
@mixin aspect-ratio($width, $height) {
  padding-bottom: percentage(calc($height / $width));
}

// unified transition for theme
@mixin transition($property:all) {
  transition: $property .2s ease-in-out;
}

// fix WP admin bar height on sticky menu
@mixin admin-bar-sticky-fix {
  .admin-bar & {
    top: 46px;
    @media screen and (min-width: 783px) {
      top: 32px;
    }
  }
}

// show text to screen readers only
@mixin visuallyhidden {
  border: 0;
  clip: rect(1px, 1px, 1px, 1px);
  clip-path: inset(50%);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
  word-wrap: normal;
}

// fix float overflow
@mixin clearfix {
  &:after {
    content: '';
    display: table;
    clear: both;
  }
}


@mixin external-link {
  &:after {
    content: "\f35d";
    font-family: "Font Awesome 6 Sharp";
    position: absolute;
    font-size: 14px;
    margin-top: 2px;
    margin-left: 2px;
  }
}

@mixin external-link-dark {
  &:after {
    content: "\f08e";
    font-family: "Font Awesome 6 Sharp";
    position: absolute;
    margin-left: 2px;
    transform: scale(0.8);
  }
}


@mixin external-link-white {
  &:after {
    content: "\f08e";
    color: #ddd;
    font-family: "Font Awesome 6 Sharp";
    position: absolute;
    margin-left: 2px;
    transform: scale(0.8);
  }
}

@mixin external-link-black {
  &:after {
    content: "\f08e";
    color: #000;
    font-family: "Font Awesome 6 Sharp";
    position: absolute;
    margin-left: 2px;
    transform: scale(0.8);
  }
}

/*spinner for search */

.loader,
.loader:before,
.loader:after {
  border-radius: 50%;
  width: 2.5em;
  height: 2.5em;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation: load7 1.8s infinite ease-in-out;
  animation: load7 1.8s infinite ease-in-out;
}
.loader {
  color: #858585;
  font-size: 10px;
  margin: 80px auto;
  position: relative;
  text-indent: -9999em;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}
.loader:before,
.loader:after {
  content: '';
  position: absolute;
  top: 0;
}
.loader:before {
  left: -3.5em;
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}
.loader:after {
  left: 3.5em;
}
@-webkit-keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
@keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}


.loader.loader--big-margins {
  display: block;
  margin-left: auto;
  margin-right: auto;
  margin: 130px auto 800px;
}
