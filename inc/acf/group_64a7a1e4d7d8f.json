{"key": "group_64a7a1e4d7d8f", "title": "<PERSON><PERSON><PERSON>", "fields": [{"key": "field_64a7a1ea5fb1b", "label": "<PERSON><PERSON><PERSON><PERSON>", "name": "newsletter_title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}, {"key": "field_64a7a1f35fb1c", "label": "Ingressi", "name": "newsletter_text", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}, {"key": "field_64a7a388d7525", "label": "Uutiskirje valinnat", "name": "newsletter_checkboxes", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_64a7a40cd752a", "label": "<PERSON><PERSON>", "name": "", "type": "message", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "Tästä voit muokata sivuston yleisen uutiskirjeen valintaruutujen yhteydessä olevia tekstejä.", "new_lines": "wpautop", "esc_html": 0}, {"key": "field_64a7a3bed7527", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "checkboxes_topical", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_64a7a443d752b", "label": "<PERSON><PERSON><PERSON><PERSON>", "name": "topical_label", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}, {"key": "field_64a7a451d752c", "label": "<PERSON><PERSON><PERSON>", "name": "topical_description", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}, {"key": "field_64a7a459d752d", "label": "<PERSON><PERSON><PERSON> usein?", "name": "topical_regularity", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}]}, {"key": "field_64a7a46fd7532", "label": "Jäsentapahtumat", "name": "checkboxes_events", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_64a7a46fd7533", "label": "<PERSON><PERSON><PERSON><PERSON>", "name": "events_label", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}, {"key": "field_64a7a46fd7534", "label": "<PERSON><PERSON><PERSON>", "name": "events_description", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}, {"key": "field_64a7a46fd7535", "label": "<PERSON><PERSON><PERSON> usein?", "name": "events_regularity", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}]}, {"key": "field_64a7a49dd7536", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "checkboxes_verkosto", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "33", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_64a7a49dd7537", "label": "<PERSON><PERSON><PERSON><PERSON>", "name": "verkosto_label", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}, {"key": "field_64a7a49dd7538", "label": "<PERSON><PERSON><PERSON>", "name": "verkosto_description", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}, {"key": "field_64a7a49dd7539", "label": "<PERSON><PERSON><PERSON> usein?", "name": "verkosto_regularity", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}]}]}], "location": [[{"param": "options_page", "operator": "==", "value": "acf-options-tavallinen"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "modified": 1688708367}