{"version": 3, "file": "news-lift-wrapper/style-index.css", "mappings": ";;;AAAA;EACC;EACA;EACA;EACA;EACA;EACA;AACD;;AAEA;EACC;EACA;EACA;EACA;AACD;;AAEA;EACC;EACA;AACD;;AAEA;EACC;EACA;AACD;;AAEA;;EAEC;EACA;AACD;;AAEA;EACC;EACA;AACD;;AAEA;EACC;EACA;EACA;EACA;AACD;;AAEA;EACC;EACA;AACD;;AAEA;;EAEC;EACA;AACD;;AAEA;EACC;EACA;EACA;EACA;AACD;;AAEA;EACC;EACA;EACA;AACD;;AAEA;EACC;EACA;EACA;EACA;EACA;AACD;;AAEA;EACC;AACD;;AAEA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACD;;AAEA;EACC;IACC;EACA;EAED;;IAEC;EAAA;EAGD;IACC;EADA;EAID;;IAEC;EAFA;AACF;AAKA;EACC;IACC;EAHA;AACF;AAMA;EACC;;IAEC;IACA;EAJA;AACF;AAOA;EACC;IACC;EALA;AACF;AAQA;EACC;IACC;IACA;EANA;AACF,C", "sources": ["webpack://news-lift-wrapper/./src/news-lift-wrapper/style.scss"], "sourcesContent": [".news-lift-slider {\n\tdisplay: flex;\n\toverflow-x: scroll;\n\tscroll-snap-type: x mandatory;\n\tgap: 2.5rem;\n\tpadding-bottom: 50px;\n\tmargin-bottom: 20px;\n}\n\n.news-lift-slider::-webkit-scrollbar {\n\t-webkit-appearance: none;\n\theight: 8px !important;\n\tborder-radius: 8px;\n\tbackground-color: rgba(29, 57, 127, 0.1);\n}\n\n.news-lift-slider::-webkit-scrollbar-thumb {\n\tborder-radius: 8px;\n\tbackground-color: rgba(29, 57, 127, 0.4);\n}\n\n.news-lift-slider-wrapper.alignfull {\n\twidth: 100vw;\n\tmargin-left: calc(50% - 50vw);\n}\n\n.page-template-default .news-lift-slider-wrapper.alignfull,\n.page-template-template-NYT .news-lift-slider-wrapper.alignfull {\n\twidth: 100%;\n\tmargin-left: 0;\n}\n\n.news-lift-slider-wrapper {\n\twidth: 100%;\n\tposition: relative;\n}\n\n.submit-btn {\n\tposition: absolute;\n\tbottom: 1.5rem;\n\tleft: 50%;\n\tz-index: 9999;\n}\n\n.alignwide {\n\tmax-width: 98rem;\n\tmargin: auto;\n}\n\n.page-template-default .alignwide,\n.page-template-template-NYT .alignwide {\n\tmax-width: 100%;\n\tmargin: 0 2rem;\n}\n\n.news-lift-heading {\n\tdisplay: flex;\n\tgap: 150px;\n\talign-items: baseline;\n\tmargin: 0 20px 20px 20px;\n}\n\n.news-lift-heading h2 {\n\tfont-size: 38px;\n\tcolor: #002662;\n\tfont-weight: 600;\n}\n\n.news-lift-heading a {\n\tfont-size: 18px;\n\tfont-weight: 600;\n\tcolor: #002662;\n\ttext-decoration: none;\n\tposition: relative;\n}\n\n.news-lift-heading a:hover {\n\ttext-decoration: underline;\n}\n\n.news-lift-heading a::after {\n\tcontent: \"\";\n\tposition: absolute;\n\ttop: 5px;\n\twidth: 8px;\n\theight: 13px;\n\tbackground-image: url(\"../../../assets/arrow.svg\");\n\tbackground-size: contain;\n\tbackground-repeat: no-repeat;\n\tmargin-left: 12px;\n}\n\n@media (max-width: 1520px) {\n\t.news-lift-slider {\n\t\tmargin: 0 20px 20px 20px;\n\t}\n\n\t.page-template-default .news-lift-slider,\n\t.page-template-template-NYT .news-lift-slider {\n\t\tmargin: 0 0 20px 0;\n\t}\n\n\t.news-lift-heading {\n\t\tmargin: 0 40px 20px 40px;\n\t}\n\n\t.page-template-default .news-lift-heading,\n\t.page-template-template-NYT .news-lift-heading {\n\t\tmargin: 0 20px 20px 20px;\n\t}\n}\n\n@media (max-width: 1190px) and (min-width: 1120px) {\n\t.page-template-default .news-lift-heading {\n\t\tdisplay: block;\n\t}\n}\n\n@media (max-width: 1119px) {\n\t.page-template-default .news-lift-slider-wrapper.alignfull,\n\t.page-template-template-NYT .news-lift-slider-wrapper.alignfull {\n\t\twidth: 100vw;\n\t\tmargin-left: -3rem;\n\t}\n}\n\n@media (max-width: 788px) {\n\t.news-lift-heading {\n\t\tdisplay: block;\n\t}\n}\n\n@media (max-width: 640px) {\n\t.alignwide {\n\t\tmax-width: 100%;\n\t\tmargin: auto;\n\t}\n}\n"], "names": [], "sourceRoot": ""}