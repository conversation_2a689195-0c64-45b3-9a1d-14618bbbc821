import { __ } from "@wordpress/i18n";
import { useEffect } from "react";
import { useState, useCallback } from "@wordpress/element";
import { debounce } from "@wordpress/compose";
import {
	useBlockProps,
	InspectorControls,
	MediaUpload,
} from "@wordpress/block-editor";
import {
	PanelBody,
	RadioControl,
	TextControl,
	TextareaControl,
	Button,
	ComboboxControl,
} from "@wordpress/components";
import { useSelect } from "@wordpress/data";

export default function Edit({ attributes, setAttributes, isSelected }) {
	// Destructure all attributes from props
	const {
		mode,
		selectedPost,
		selectedPostType,
		manualTitle,
		manualText,
		manualDate,
		manualCategory,
		manualImageUrl,
		manualLinkText,
		manualLinkUrl,
	} = attributes;

	const blockProps = useBlockProps();
	const MAX_WORDS = 30;

	// Fetch the selected post based on type and ID
	const post = useSelect(
		(select) => {
			if (mode === "post" && selectedPost && selectedPostType) {
				const core = select("core");
				return core.getEntityRecord("postType", selectedPostType, selectedPost);
			}
			return null;
		},
		[mode, selectedPost, selectedPostType],
	);

	// Get featured image for selected post
	const featuredImage = useSelect(
		(select) => {
			if (post && post.featured_media) {
				return select("core").getMedia(post.featured_media);
			}
			return null;
		},
		[post],
	);

	// Fetch taxonomy terms (article types) for the post
	const articleTypes = useSelect(
		(select) => {
			const { getEntityRecords } = select("core");
			return post?.id
				? getEntityRecords("taxonomy", "article_type", {
						post: post.id,
				  }) || []
				: [];
		},
		[post?.id],
	);

	const postUrl = mode === "post" && post ? post.link : null;

	// Determine image to show based on mode
	const imageUrl =
		mode === "manual"
			? manualImageUrl
			: attributes.overrideImageUrl || featuredImage?.source_url || "";

	// Determine title and text content
	const title = mode === "post" ? post?.title?.rendered : manualTitle;

	const [manualTextError, setManualTextError] = useState("");

	// Utility: strip HTML tags
	const stripTags = (html) => html?.replace(/<[^>]+>/g, "") ?? "";

	// Utility: trim text to a specific number of words
	const trimToWords = (input, wordLimit = MAX_WORDS) => {
		if (!input) return "";
		const words = input.trim().split(/\s+/);
		if (words.length <= wordLimit) return input;
		return words.slice(0, wordLimit).join(" ") + "…";
	};

	// Determine excerpt or manual text
	const text =
		mode === "post"
			? trimToWords(stripTags(post?.excerpt?.rendered))
			: manualText;

	// Use custom ACF ingress field if available
	const ingress = post?.acf?.ingress ? trimToWords(post.acf.ingress) : null;

	const postText = ingress || text;

	const date = mode === "post" ? post?.date : manualDate;

	// Determine category/label based on source
	const category =
		mode === "post"
			? articleTypes.length > 0
				? articleTypes[0].name
				: ""
			: manualCategory;

	// When mode is 'post', sync post data into block attributes
	useEffect(() => {
		if (
			mode === "post" &&
			post?.title &&
			featuredImage?.source_url &&
			articleTypes.length > 0
		) {
			const stripTags = (html) => html?.replace(/<[^>]+>/g, "") ?? "";

			const trimToWords = (input, wordLimit = MAX_WORDS) => {
				if (!input) return "";
				const words = input.trim().split(/\s+/);
				if (words.length <= wordLimit) return input;
				return words.slice(0, wordLimit).join(" ") + "…";
			};

			const text =
				mode === "post"
					? trimToWords(stripTags(post?.excerpt?.rendered))
					: manualText;

			const ingress = post?.acf?.ingress ? trimToWords(post.acf.ingress) : null;

			const postText = ingress || text;

			setAttributes({
				postTitle: post.title.rendered,
				postText: postText,
				postDate: post.date,
				postCategory: articleTypes[0].name,
				postImageUrl: attributes.overrideImageUrl || featuredImage.source_url,
				postUrl: post.link,
			});
		}
	}, [mode, post, featuredImage, articleTypes]);

	// Clear conflicting fields when switching between modes
	useEffect(() => {
		if (mode === "manual") {
			setAttributes({
				selectedPost: null,
				postTitle: "",
				postText: "",
				postDate: "",
				postCategory: "",
				postImageUrl: "",
				postUrl: "",
				overrideImageUrl: "",
			});
		} else if (mode === "post") {
			setAttributes({
				manualTitle: "",
				manualText: "",
				manualDate: "",
				manualCategory: "",
				manualImageUrl: "",
				manualLinkText: "",
				manualLinkUrl: "",
			});
		}
	}, [mode]);

	const [search, setSearch] = useState("");
	const [query, setQuery] = useState(""); // debounced query string

	// Fetch matching posts and releases when search query updates
	const posts = useSelect(
		(select) => {
			if (!query) return [];

			const core = select("core");

			const posts =
				core.getEntityRecords("postType", "post", {
					search: query,
					per_page: 10,
				}) || [];

			const releases =
				core.getEntityRecords("postType", "release", {
					search: query,
					per_page: 10,
				}) || [];

			return [...posts, ...releases];
		},
		[query],
	);

	// Debounce user input to limit API calls
	const debouncedSearch = useCallback(
		debounce((val) => {
			if (!val || val.length < 3) return;
			setQuery(val);
		}, 500),
		[],
	);

	// Trigger search when input changes
	useEffect(() => {
		if (!search) {
			setQuery("");
			return;
		}
		debouncedSearch(search);
	}, [search, debouncedSearch]);

	// Format fetched posts into options for ComboboxControl
	const options =
		posts?.map((post) => ({
			value: post.id,
			label: post.title.rendered || "(no title)",
		})) || [];

	// Determine if all required fields for manual mode are filled
	const hasRequiredFields = () => {
		if (mode !== "manual") return true;
		return (
			manualImageUrl &&
			manualCategory?.trim() &&
			manualTitle?.trim() &&
			manualText?.trim()
		);
	};

	// === Render output ===

	// If block is NOT selected in editor, show preview mode
	return !isSelected ? (
		<div {...blockProps}>
			<div className="news-lift-slider-article">
				{imageUrl && (
					<figure className="wp-block-image size-large news-lift-slider-img">
						{postUrl ? (
							<a href={postUrl}>
								<img
									src={imageUrl}
									alt=""
									style={{
										width: "380px",
										height: "250px",
										objectFit: "cover",
									}}
								/>
							</a>
						) : (
							<img
								src={imageUrl}
								alt=""
								style={{
									width: "380px",
									height: "250px",
									objectFit: "cover",
								}}
							/>
						)}
					</figure>
				)}

				<div className="news-lift-slider-body">
					<div className="news-lift-slider-meta">
						{date && (
							<p className="meta-data-date has-very-dark-gray-color has-text-color has-link-color">
								{new Date(date).toLocaleDateString("fi-FI")}
							</p>
						)}
						<p
							className="meta-data-category has-main-color has-text-color has-link-color"
							style={{ textTransform: "uppercase" }}
						>
							{category}
						</p>
					</div>
					<h3 className="wp-block-heading news-lift-slider-heading has-main-color has-text-color has-link-color">
						{postUrl ? <a href={postUrl}>{title}</a> : title}
					</h3>

					<p className="news-lift-slider-text">{postText}</p>
					{manualLinkText && manualLinkUrl && (
						<p className="news-lift-slider-link">
							<a
								href={manualLinkUrl}
								rel="noopener noreferrer"
								style={{ color: "var(--wp--preset--color--main)" }}
							>
								{manualLinkText}
							</a>
						</p>
					)}
				</div>
			</div>
		</div>
	) : (
		// Block is selected in editor: render editing UI
		<>
			{/* Sidebar settings */}
			<InspectorControls>
				<PanelBody title="Tiedon lähde">
					<RadioControl
						label="Näytettävä nosto"
						selected={mode}
						options={[
							{ label: "Syötä käsin", value: "manual" },
							{ label: "Valitse artikkeli tai tiedote", value: "post" },
						]}
						onChange={(newMode) => setAttributes({ mode: newMode })}
					/>

					{/* Post mode controls */}
					{mode === "post" && (
						<>
							<ComboboxControl
								label="Hae nimen perusteella"
								value={attributes.selectedPost}
								options={options}
								onChange={(postId) => {
									const post = posts.find((p) => p.id === parseInt(postId, 10));
									setAttributes({
										selectedPost: post.id,
										selectedPostType: post.type,
									});
								}}
								onFilterValueChange={(val) => {
									setSearch(val);
								}}
							/>
							{/* Current post image */}
							{featuredImage && (
								<div style={{ marginTop: "20px" }}>
									<h3>Artikkelista haettu kuva</h3>
									{featuredImage ? (
										<div style={{ position: "relative" }}>
											<img
												src={featuredImage.source_url}
												alt="Featured"
												style={{
													width: "200px",
													height: "200px",
													objectFit: "cover",
													display: "block",
												}}
											/>
										</div>
									) : (
										<p>Ei kuvaa valittuna.</p>
									)}
								</div>
							)}

							{/* Override image */}
							<div style={{ marginTop: "20px" }}>
								<h3>Valitse vaihtoehtoinen kuva nostoon</h3>
								<MediaUpload
									allowedTypes={["image"]}
									onSelect={(media) =>
										setAttributes({ overrideImageUrl: media.url })
									}
									render={({ open }) => (
										<div>
											{attributes.overrideImageUrl ? (
												<div>
													<Button
														onClick={open}
														isSecondary
														style={{
															marginBottom: "10px",
															marginRight: "10px",
															backgroundColor: "#fff",
														}}
													>
														Vaihda kuva
													</Button>
													<Button
														onClick={() =>
															setAttributes({ overrideImageUrl: "" })
														}
														isDestructive
														style={{
															backgroundColor: "#cc1818",
															color: "#fff",
														}}
													>
														Poista kuva
													</Button>
													<img
														src={attributes.overrideImageUrl}
														alt="Selected Image"
														style={{
															width: "200px",
															height: "200px",
															objectFit: "cover",
															display: "block",
														}}
													/>
												</div>
											) : (
												<Button
													onClick={open}
													isSecondary
													style={{
														width: "200px",
														height: "200px",
														backgroundColor: "#e0e0e0",
														display: "flex",
														alignItems: "center",
														justifyContent: "center",
													}}
												>
													Valitse kuva
												</Button>
											)}
										</div>
									)}
								/>
							</div>
						</>
					)}
				</PanelBody>
			</InspectorControls>

			{/* Main editing content area */}
			<div {...blockProps}>
				{mode === "manual" && (
					<div className="news-lift-slider-article">
						<div className="news-lift-slider-img-wrapper">
							{/* Image picker for manual mode */}
							<MediaUpload
								allowedTypes={["image"]}
								onSelect={(media) =>
									setAttributes({ manualImageUrl: media.url })
								}
								render={({ open }) => (
									<>
										{manualImageUrl ? (
											<div
												className="news-lift-slider-img"
												style={{ position: "relative" }}
											>
												<img
													src={manualImageUrl}
													alt=""
													style={{
														width: "380px",
														height: "250px",
														objectFit: "cover",
														display: "block",
													}}
												/>
												<Button
													onClick={open}
													isSecondary
													style={{
														position: "absolute",
														top: "10px",
														left: "10px",
														zIndex: 1,
														backgroundColor: "#fff",
													}}
												>
													Vaihda kuva
												</Button>
											</div>
										) : (
											<div
												style={{
													width: "380px",
													height: "250px",
													backgroundColor: "#e0e0e0",
													display: "flex",
													alignItems: "center",
													justifyContent: "center",
													border: !manualImageUrl?.trim()
														? "1px solid red"
														: "1px solid transparent",
												}}
											>
												<Button
													onClick={open}
													isSecondary
													style={{ backgroundColor: "#fff" }}
												>
													Valitse kuva
												</Button>
											</div>
										)}
									</>
								)}
							/>
						</div>

						{/* Input fields for manual content */}
						<div>
							<label
								htmlFor="manual-date"
								style={{
									display: "block",
									marginTop: "10px",
									marginBottom: "5px",
									fontSize: "13px",
									lineHeight: "1.4em",
									color: "#444",
									fontWeight: "500",
									fontFamily:
										"BlinkMacSystemFont, Segoe UI, Roboto, Oxygen-Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif",
								}}
							>
								Päivämäärä
							</label>
							<input
								type="date"
								id="manual-date"
								value={manualDate}
								onChange={(e) => setAttributes({ manualDate: e.target.value })}
								style={{
									padding: "6px 12px",
									border: "1px solid #8c8f94",
									borderRadius: "4px",
									width: "auto",
									maxWidth: "200px",
									marginBottom: "10px",
								}}
							/>
							{manualDate && (
								<Button
									isLink
									isDestructive
									style={{ marginTop: "5px", marginLeft: "10px" }}
									onClick={() => setAttributes({ manualDate: "" })}
								>
									Poista päivämäärä
								</Button>
							)}
						</div>

						<label
							htmlFor="manual-category"
							style={{
								marginTop: "10px",
								marginBottom: "10px",
								fontSize: "13px",
								lineHeight: "1.4em",
								color: "#444",
								fontWeight: "500",
								fontFamily:
									"BlinkMacSystemFont, Segoe UI, Roboto, Oxygen-Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif",
							}}
						>
							Aihe / kategoria
						</label>
						<TextControl
							style={{
								fontSize: "16px",
								borderRadius: "4px",
								borderColor: !manualCategory?.trim() ? "red" : "#8c8f94",
							}}
							value={manualCategory}
							onChange={(val) => setAttributes({ manualCategory: val })}
						/>

						<label
							htmlFor="manual-heading"
							style={{
								marginTop: "10px",
								marginBottom: "10px",
								fontSize: "13px",
								lineHeight: "1.4em",
								color: "#444",
								fontWeight: "500",
								fontFamily:
									"BlinkMacSystemFont, Segoe UI, Roboto, Oxygen-Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif",
							}}
						>
							Otsikko
						</label>
						<TextControl
							style={{
								fontSize: "16px",
								borderRadius: "4px",
								borderColor: !manualTitle?.trim() ? "red" : "#8c8f94",
							}}
							value={manualTitle}
							onChange={(val) => setAttributes({ manualTitle: val })}
						/>

						<label
							htmlFor="manual-text"
							style={{
								marginTop: "10px",
								marginBottom: "10px",
								fontSize: "13px",
								lineHeight: "1.4em",
								color: "#444",
								fontWeight: "500",
								fontFamily:
									"BlinkMacSystemFont, Segoe UI, Roboto, Oxygen-Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif",
							}}
						>
							Teksti (max {MAX_WORDS} sanaa)
						</label>

						<TextareaControl
							style={{
								fontSize: "16px",
								borderRadius: "4px",
								borderColor: !manualText?.trim() ? "red" : "#8c8f94",
							}}
							help={
								manualTextError ? (
									<span style={{ color: "red" }}>{manualTextError}</span>
								) : (
									`${
										manualText?.trim().split(/\s+/).length || 0
									} / ${MAX_WORDS} sanaa`
								)
							}
							value={manualText}
							onChange={(val) => {
								const words = val.trim().split(/\s+/);

								if (words.length > MAX_WORDS) {
									const trimmedVal = words.slice(0, MAX_WORDS).join(" ");
									setManualTextError(
										`Teksti saa sisältää enintään ${MAX_WORDS} sanaa.`,
									);
									setAttributes({ manualText: trimmedVal });
								} else {
									setManualTextError("");
									setAttributes({ manualText: val });
								}
							}}
							isInvalid={!!manualTextError}
						/>
						<label
							htmlFor="manual-link-text"
							style={{
								marginTop: "10px",
								marginBottom: "10px",
								fontSize: "13px",
								lineHeight: "1.4em",
								color: "#444",
								fontWeight: "500",
								fontFamily:
									"BlinkMacSystemFont, Segoe UI, Roboto, Oxygen-Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif",
							}}
						>
							Linkkiteksti
						</label>
						<TextControl
							style={{
								border: "1px solid #8c8f94",
								borderRadius: "4px",
							}}
							value={manualLinkText}
							onChange={(val) => setAttributes({ manualLinkText: val })}
						/>

						<label
							htmlFor="manual-link-url"
							style={{
								marginTop: "10px",
								marginBottom: "10px",
								fontSize: "13px",
								lineHeight: "1.4em",
								color: "#444",
								fontWeight: "500",
								fontFamily:
									"BlinkMacSystemFont, Segoe UI, Roboto, Oxygen-Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif",
							}}
						>
							Linkin URL
						</label>
						<TextControl
							style={{
								border: "1px solid #8c8f94",
								borderRadius: "4px",
							}}
							value={manualLinkUrl}
							onChange={(val) => setAttributes({ manualLinkUrl: val })}
						/>
					</div>
				)}

				{/* Add "can't be edited" text when the article is selected and active */}
				{mode === "post" && selectedPost && (
					<p
						style={{
							fontStyle: "italic",
							color: "gray",
							padding: "20px",
							width: "380px",
						}}
					>
						Artikkelin sisältö ei ole muokattavissa tässä näkymässä.
					</p>
				)}
			</div>
		</>
	);
}
