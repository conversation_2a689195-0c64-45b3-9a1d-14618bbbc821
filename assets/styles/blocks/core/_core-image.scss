/* ==========================================================================
 # Image block
========================================================================== */

/* Front-end and back-end
----------------------------------------------- */

.editor-styles-wrapper .wp-block-image,
.wysiwyg .wp-block-image {

  margin-top: $gap;
  margin-bottom: $gap;
  text-align: left;

  img {
    display: inline-block;
    line-height: 0;

    @media screen and (max-width: 1024px) {
      display: block;
      width: 100% !important;

    }

  }

  // wide width
  &.alignwide {

    figure,
    img {
      width: 100%;
    }
  }

  &.alignfull {
    margin-top: $gap-big;
    margin-bottom: $gap-big;

    figcaption {
      padding-left: $gap;
      padding-right: $gap;
      max-width: $content-default-width;
      margin-left: auto;
      margin-right: auto;
    }
  }

  // floated to side
  figure {
    margin-top: 0;
  }

  // caption
  figcaption {
    color: $grey-font-color;
    display: block;
    margin-top: $gap-tiny;
    margin-bottom: $gap;
    font-size: .875rem;
  }

}

/* Only front-end
----------------------------------------------- */

// image block
.wysiwyg .wp-block-image {}

// classic editor images
.wysiwyg .wp-caption {
  @extend .wp-block-image;
}

/* Only back-end
----------------------------------------------- */

.editor-styles-wrapper .wp-block-image {}

@media (max-width: 781px) {
  .home {
    .wp-block-columns:not(.is-not-stacked-on-mobile)>.wp-block-column {
      flex-basis: inherit !important;
    }

    .wp-block-image.aligncenter,
    .wp-block-image .aligncenter,
    .wp-block-image.alignleft,
    .wp-block-image .alignleft,
    .wp-block-image.alignright,
    .wp-block-image .alignright {
      display: block;
    }
  }
}


@media (max-width: 781px) {
  .home {
    .wp-block-columns:not(.is-not-stacked-on-mobile)>.wp-block-column {
      width: 90%;
    }
  }
}

/* fix responsive wp 3 column images not to show 2+1 but 3  */
@media (max-width: 781px) {
  // flex-wrap: nowrap;

  .wp-block-column:nth-child(3n) {
    // margin-left: 0 !important;
  }
}
