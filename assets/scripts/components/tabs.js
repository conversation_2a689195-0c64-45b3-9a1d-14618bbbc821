function displayCurrentTab(current, tabsContents, tabsBtns) {
  for (let i = 0; i < tabsContents.length; i++) {
    if (current === i) {
      tabsContents[i].style.height = "auto";
      if (
        tabsContents[i].parentNode.parentNode.classList.contains("scrollto")
      ) {
        tabsContents[i].scrollIntoView({ block: "start", behavior: "smooth" });
      }
    } else {
      tabsContents[i].style.height = "0";
    }
  }
  for (let i = 0; i < tabsBtns.length; i++) {
    if (current === i) {
      tabsBtns[i].classList.add("on");
    } else {
      tabsBtns[i].classList.remove("on");
    }
  }
}

if (document.querySelector(".tabs")) {
  const tabs = document.querySelectorAll(".tabs");

  for (let i = 0; i < tabs.length; i++) {
    const tabsBtns = tabs[i].querySelectorAll(".tabs__btn");
    const tabsContents = tabs[i].querySelectorAll(".tabs__content");
    const close = tabs[i].querySelectorAll(".tabs__content--close");

    if (document.querySelector(".active_tabs")) {
      displayCurrentTab(0, tabsContents, tabsBtns);
    }

    for (let i = 0; i < close.length; i++) {
      close[i].addEventListener("click", (event) => {
        for (let i = 0; i < tabsContents.length; i++) {
          tabsContents[i].style.height = "0";
        }
      });
    }

    tabs[i].addEventListener("click", (event) => {
      jQuery(".product__wrap").trigger("resize");
      jQuery(".product__wrap").trigger("resize");
      if (
        event.target.classList.contains("tabs__btn") ||
        event.target.parentNode.classList.contains("tabs__btn") ||
        event.target.parentNode.parentNode.classList.contains("tabs__btn")
      ) {
        for (let i = 0; i < tabsBtns.length; i++) {
          if (
            event.target === tabsBtns[i] ||
            event.target.parentNode === tabsBtns[i] ||
            event.target.parentNode.parentNode === tabsBtns[i]
          ) {
            displayCurrentTab(i, tabsContents, tabsBtns);
            break;
          }
        }
      }
    });
  }
}
