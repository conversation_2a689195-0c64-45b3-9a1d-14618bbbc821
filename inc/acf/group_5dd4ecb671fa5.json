{"key": "group_5dd4ecb671fa5", "title": "Lohko: Nostokolumnit taustalla", "fields": [{"key": "field_5dd4ece4e88a2", "label": "Nostokolumnit taustalla", "name": "column-features", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_5dd4ed06e88a3", "label": "Pie<PERSON>tsik<PERSON>", "name": "small_title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5dd4ed27e88a4", "label": "Pääotsikko", "name": "title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5dd4ed3fe88a5", "label": "Nostot", "name": "columns", "type": "repeater", "instructions": "1-3 kpl", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 3, "layout": "table", "button_label": "", "sub_fields": [{"key": "field_5dd4ed5be88a6", "label": "<PERSON><PERSON><PERSON><PERSON>", "name": "title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5dd4ed69e88a7", "label": "<PERSON><PERSON><PERSON>", "name": "kuvaus", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": "", "new_lines": ""}, {"key": "field_5dd4ed72e88a8", "label": "<PERSON><PERSON>", "name": "link", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}, {"key": "field_5dd4ed79e88a9", "label": "<PERSON><PERSON>", "name": "img", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}]}]}], "location": [[{"param": "block", "operator": "==", "value": "acf/column-features"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": 1, "description": "", "modified": 1574768670}