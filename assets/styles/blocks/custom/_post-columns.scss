.block-post-columns {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  @include breakpoint($sub-l) {
    justify-content: center;
  }
  &__separator {
    &-mobile {
      display: none;
    }
    width: 2px;
    background-color: $separatorblue;
    min-height: 100%;
    margin: 2rem;
    @include breakpoint($sub-l) {
      display: none;
      &-mobile {
        display: block;
        width: 50%;
        height: 2px;
        background-color: $separatorblue;
        margin: 1rem;
      }
    }
  }

  &__content {
    margin: 0 0.5rem;
  }
  &__item {
    // width: 45%;
    width: calc(50% - 2.5rem);
    display: flex;
    align-items: center;
    @include breakpoint($sub-l) {
      width: 80%;
      margin: 2rem 0;
    }

    .title {
      color: $primary;
      font-weight: $ws-semibold;
      font-size: 22px;
      margin-bottom: 0;
    }
    .subtitle {
      display: block;
      clear: both;
      font-size: 16px;
      font-weight: $ws-medium;
      color: $black-text;
      margin: 0.75rem 0.75rem 0.75rem 0;
      &.is-quote {
        font-weight: $ws-semibold;
        &:before {
          content: open-quote;
        }
        &:after {
          content: close-quote;
        }
      }
    }
    img {
      // align-self: flex-start;
      align-self: center;
      border-radius: 50%;
      max-width: 175px;
      margin-right: 3rem;
    }
    &:nth-of-type(2) {
      // border-left: 2px solid $grey;
    }
    a {
      @include textlink;
      margin-top: 2rem;
      display: block;
      width: auto;
      width: fit-content;
    }
  }
}

@include breakpoint($sub-s) {
  .block-post-columns {
    &__item {
      width: 100%;
      margin: 1rem 0 2rem;
      .title {
        font-size: 20px;
      }
      .subtitle {
        hyphens: auto;
      }
      img {
        .is-front-page & {
          align-self: flex-start !important;
        }
        margin-right: .75rem;
        max-width: 110px !important;
      }
      a {
        font-size: 16px;
      }
    }
  }
}

.page-template-default {
  .block-post-columns {
    margin-bottom: 4rem;
    &__item {
      width: 100%;
    }
    &__separator {
      border-color: transparent;
      margin: 3rem 0;
    }
    &__content {
      margin-right: 5rem;
    }
  }
  @include breakpoint($sub-m) {
    .block-post-columns {
      &__item {
        flex-direction: column;
        gap: 1rem;
        img {
          //margin-left: 2rem;
          max-width: 125px !important;
        }
      }
      &__content {
        margin: 1.25rem 0;
        .subtitle {
          margin-top: 1.25rem;
        }
      }
    }
  }
}