.dimming {
  position: absolute;
  top:0;
  bottom: 0;
  left: 0;
  z-index: 2;
  right: 0;
  &__bottom {
    background: linear-gradient(0deg, rgba(0,38,99,1) 0%, rgba(0,38,99,0) 25%);
  }
  &__bottom_l {
    background: linear-gradient(0deg, rgba(0,38,99,1) 0%, rgba(0,38,99,0) 90%);
  }
  &__bottom_m {
    /*background: linear-gradient(0deg, rgba(0,38,99,1) 0%, rgba(0,38,99,0) 70%);*/
    background: linear-gradient(90deg, rgba(0,38,99,1) 0%, rgba(0,38,99,0) 80%);
  }
  &__left {
    background: linear-gradient(90deg, rgba(0,38,99,1) 0%, rgba(0,38,99,0) 80%);
  }
  @include breakpoint($l) {
    &__left {
      background: linear-gradient(90deg, rgba(0,38,99,1) 0%, rgba(0,38,99,0) 80%);
    }
  }
}
.single .dimming {
  display: none;
}
