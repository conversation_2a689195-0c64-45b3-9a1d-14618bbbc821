<?php

/**
 * Presentations Block Template.
 *
 * @param array $block The block settings and attributes.
 * @param string $content The block inner HTML (empty).
 * @param bool $is_preview True during AJAX preview.
 * @param (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block--' . $block['id'];
if ( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-presentations';
if ( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if ( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Add class for page template
global $template;
$className .= ' ' . preg_replace('/\\.[^.\\s]{3,4}$/', '', basename($template));

// Load values and assign defaults.
$handpicked = get_field('filter_presentations');

if (!empty($handpicked)) {
  $posts = $handpicked;
} else {
  $args = array(
    'post_type'              => array( 'event_presentation' ),
    'post_status'            => 'publish',
  );
  $posts = get_posts($args);
}

?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">

<?php
foreach ($posts as $post) :
?>
  <div class="block-presentations-item">

      <div class="block-presentations-header">

        <span class="block-presentations-date"><?php echo get_field('date', $post->ID); ?></span>

        <?php if (get_field('recording_link', $post->ID)) : ?>
          <a class="block-presentations-recording" href="<?php echo get_field('recording_link', $post->ID); ?>">
            <?php echo kauppakamari_get_svg('live_tv'); ?>
            <?php echo get_field('recording_title', $post->ID); ?>
          </a>
        <?php endif; ?>

        <h3 class="block-presentations-title"><?php echo get_the_title($post->ID); ?></h3>

      </div>

      <div class="block-presentations-list-title"><?php echo get_field('list_title', $post->ID); ?></div>

      <?php if (have_rows('presentations', $post->ID)) : ?>
        <div class="block-presentations-list">
        <?php while ( have_rows('presentations', $post->ID) ) : the_row(); ?>
          <?php
              if (get_sub_field('file', $post->ID)) :
                $link = get_sub_field('file', $post->ID);
              elseif (get_sub_field('file_link', $post->ID)) :
                $link = get_sub_field('file_link', $post->ID);
              else :
                $link = '';
              endif;
             ?>
          <a class="block-presentations-list-item" href="<?php echo $link; ?>">
            <?php echo kauppakamari_get_svg('file'); ?>
            <div class="block-presentations-list-item-wrap">
              <div class="block-presentations-list-item-title">
                <?php the_sub_field('presentation_name', $post->ID); ?>
              </div>
              <div class="block-presentations-list-item-presenter-name">
              <?php the_sub_field('presenter_name', $post->ID); ?>
              </div>
              <div class="block-presentations-list-item-presenter-title">
                <?php the_sub_field('presenter_title', $post->ID); ?>
              </div>
            </div>
          </a>
        <?php endwhile; ?>
        </div>
      <?php endif; ?>
  </div>
<?php endforeach; ?>

</div>
