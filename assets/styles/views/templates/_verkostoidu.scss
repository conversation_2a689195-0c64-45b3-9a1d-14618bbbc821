.company-filter {
  &__header {
    height: calc(60vh - 113px);
    display: flex;
    align-items: center;
    background-position: center;
    background-size: cover;
    &--wrap {
      @include wrap();
      @include narrow-content();
      max-width: 400px;
      z-index: 10;
      h1 {
        color: white;
        margin-bottom: 1rem;
      }
      input {
        padding: 7px 15px;
        border-radius: 20px;
        border: 0;
        width: 100%;
        background: url(../images/search.svg) white no-repeat 10px 3px;
        background-position: 97% center;
      }
    }
  }
  &__list {
    list-style: none;
  }
  &__primary {
    @include wrap();
    display: block;
    margin-top: 4rem;
    border: 1px solid $grey-darker;
    @include breakpoint($m) {
      display: flex;
    }
  }
  &__aside {
    width: 100%;
    border-right: none;
    &--header {
      padding-top: 2.5rem;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid $grey;
      margin-bottom: 1.5rem;
      padding-bottom: 1rem;
      color: $grey-darker;
      a {
        margin-right: 1rem;
        font-weight: 700;
        margin-right: 10%;
      }
    }
    select {
      padding: 10px 15px;
      margin-bottom: 1rem;
    }
    .select-filter {
      display: block;
      font-size: 15px;
      font-weight: 500;
      color: black;
      line-height: 1.3;
      padding: .6em 1.4em .5em .8em;
      width: 100%;
      max-width: 100%;
      box-sizing: border-box;
      margin: 1rem 0;
      border: 1px solid #aaa;
      box-shadow: 0 1px 0 1px rgba(0,0,0,.04);
      border-radius: .5em;
      -moz-appearance: none;
      -webkit-appearance: none;
      appearance: none;
      background-color: #fff;
      background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23007CB2%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E'),
        linear-gradient(to bottom, #ffffff 0%,#ffffff 100%);
      background-repeat: no-repeat, repeat;
      background-position: right .7em top 50%, 0 0;
      background-size: .65em auto, 100%;
      @include breakpoint($m) {
        width: 90%;
        max-width: 90%;
      }
    }
    .select-filter::-ms-expand {
      display: none;
    }
    .select-filter:hover {
      border-color: #888;
    }
    .select-filter:focus {
      border-color: #aaa;
      box-shadow: 0 0 1px 3px rgba(59, 153, 252, .7);
      box-shadow: 0 0 0 3px -moz-mac-focusring;
      color: #222;
      outline: none;
    }
    .select-filter option {
      font-weight:normal;
    }
    .sort {
      display: block;
      cursor: pointer;
      width: 80%;
      position: relative;
      margin-bottom: .5rem;
      color: $grey-darker;
      &.asc {
        font-weight: 600;
      }
      &.desc {
        font-weight: 600;
      }
      &.asc:before {
        content: "";
        position: absolute;
        width: 20px;
        height: 20px;
        top: 0;
        right: 0;
        background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23007CB2%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E'),
        linear-gradient(to bottom, #ffffff 0%,#ffffff 100%);
        background-repeat: no-repeat, repeat;
        background-position: right .7em top 50%, 0 0;
        background-size: .65em auto, 100%;
      }
      &.desc:before {
        content: "";
        position: absolute;
        width: 20px;
        height: 20px;
        top: 0;
        right: 10px;
        background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23007CB2%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E'),
        linear-gradient(to bottom, #ffffff 0%,#ffffff 100%);
        background-repeat: no-repeat, repeat;
        background-position: right .7em top 50%, 0 0;
        background-size: .65em auto, 100%;
        transform: rotate(180deg);
      }
    }
    @include breakpoint($m) {
      width: 25%;
      border-right: 1px solid $grey;
    }
  }
  &__main {
    width: 100%;
    padding: 2.5rem 0rem;
    .record-count-total {
      color: $brand;
      font-size: 2.5rem;
      font-weight: bold;
      padding-left: 1rem;
      border-bottom: 1px solid $grey-dark;
    }
    .company-filter__list {
      margin: 0;
      padding: 0;
    }
    .pagination {
      margin: 3rem auto;
      list-style: none;
      display: flex;
      max-width: 400px;
      justify-content: space-between;
      li {
        padding: 4px 8px;
        border: 1px solid $brand;
        a {
          text-decoration: none;
        }
        &.active {
          background-color: $brand;
          a { color: white };
        }
        &.disabled {
          border: none;
        }
      }
    }
    @include breakpoint($m) {
      width: 75%;
      padding: 2.5rem 3.5rem;
    }
  }
  .company__item {
    border-bottom: 1px solid $grey;
    position: relative;

    a {
      width: 100%;
      text-decoration:none;
      padding: 1rem;
      display: block;
      &:hover {
        background-color: $grey-light;
      }
    }
    h5 {
      margin: 0;
      padding: 0;
      color: black
    }
    span {
      color: $grey-darker;
      padding: .5rem 0;
    }
    .type {
      font-weight: bold;
    }
    &:before {
      content: "";
      position: absolute;
      width: 20px;
      height: 20px;
      top: 20px;
      right: 10px;
      background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23007CB2%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E');
      background-repeat: no-repeat, repeat;
      background-position: right .7em top 50%, 0 0;
      background-size: .65em auto, 100%;
      transform: rotate(270deg);
    }
  }
}
