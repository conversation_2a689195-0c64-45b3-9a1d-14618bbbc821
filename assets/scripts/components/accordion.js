$(".accordion-toggle").click(function (e) {
  e.preventDefault();

  var $this = $(this);

  if ($this.next().hasClass("show")) {
    $this.toggleClass("active");
    $this.next().removeClass("show");
    $this.next().slideUp(350);
  } else {
    $this.toggleClass("active");
    $this.parent().parent().find("div .inner").removeClass("show");
    $this.parent().parent().find("div .inner").slideUp(350);
    $this.next().toggleClass("show");
    $this.next().slideToggle(350);
  }
});
