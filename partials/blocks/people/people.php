<?php

/**
 * People
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-people-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-people';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$items = get_field('people_rows');

?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">

  <h3> <?php echo get_field('title') ?: "<PERSON><PERSON><PERSON>ko"; ?> </h3>
  <?php
    foreach($items as $item) { ?>
    <?php if(!empty($item['group']['title']) && !empty($item['group']['content'])) { ?>
      <div class="block-people__item">
        <?php
        if($icon = $item['group']['icon']) { ?>
          <div class="block-people__img" style="background-image: url(<?php echo $icon['sizes']['medium_large']; ?>)">
          </div>
        <?php } ?>
        <div class="block-people__content">
          <span class="name">
            <?php echo $item['group']['name'] ?: "Otsikko"; ?>
          </span>
          <span class="title">
            <?php echo $item['group']['title'] ?: "Titteli"; ?>
          </span>
          <span class="content">
            <?php echo $item['group']['content'] ?: "Sisältö"; ?>
          <span>
        </div>
      </div>
      <?php } ?>
      <?php if(!empty($item['group_2']['title']) && !empty($item['group_2']['content'])) { ?>
      <div class="block-people__item">

        <?php
        if($icon = $item['group_2']['icon']) { ?>
          <div class="block-people__img" style="background-image: url(<?php echo $icon['sizes']['medium_large']; ?>)">
          </div>
        <?php } ?>

        <div class="block-people__content">
        <span class="name">
            <?php echo $item['group_2']['name'] ?: "Otsikko"; ?>
          </span>
          <span class="title">
            <?php echo $item['group_2']['title'] ?: "Titteli"; ?>
          </span>
          <span class="content">
            <?php echo $item['group_2']['content'] ?: "Sisältö"; ?>
          <span>
        </div>
      </div>
      <?php } ?>
    <?php
    }
  ?>

</div>
