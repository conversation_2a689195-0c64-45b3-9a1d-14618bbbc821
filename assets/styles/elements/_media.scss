/* ==========================================================================
 # Media
========================================================================== */

/* Lazyload
----------------------------------------------- */

// lazyload from transparent to visible
img.lazyload--fast {
  transition: .15s ease-in-out;
  &.lazyload,
  &.lazyloading {
    opacity: 0;
  }
  &.lazyloaded {
    opacity: 1;
  }
}

// lazyload from blurry to visible
img.lazyload--animated {
  transition: .15s ease-in-out;
  &.lazyload,
  &.lazyloading,
  &.lazyloaded {
    opacity: 1;
  }
  &.lazyload-preload {
  backface-visibility: hidden;
  filter: blur(15px);
  transition: .5s ease-out;
  transform: scale(1.075);
  z-index: 3;
    &.lazyload-preload--ready {
      opacity: 0;
    }
  }
}

// hide js depended images if there's no js
.no-js {
  .lazyload,
  .lazyload-preload {
    display: none !important;
  }
}

/* Images
----------------------------------------------- */

.wp-caption {
  float: none;
  font-size: .875rem;
  margin: 0;
  max-width: 100%;
}

.alignnone {
  height: auto;
  margin-left: auto;
  margin-right: auto;
  max-width: 100%;

}

.aligncenter {
  display: block;
  height: auto;
  margin: 1rem auto;
}

/* Icons
----------------------------------------------- */

.icon {
  display: inline-block;
  fill: currentColor;
  height: 1em; // match current font-size
  vertical-align: middle;
  width:  1em; // match current font-size

}

/* Iframe
----------------------------------------------- */

iframe {
  max-width: 100%;
}

/* Gallery
----------------------------------------------- */

.gallery {
  display: flex;
  flex-wrap: wrap;
  text-align: center;
  img {
    display: block;
    line-height: 0;
  }
}

.gallery-item {
  margin: 0;
  padding: .5rem 0;
  width: 100%;
  .gallery-columns-2 & {
    width: 100%;
    @include breakpoint($s) {
      margin-right: 1rem;
      width: calc(50% - 1rem);
      &:nth-of-type(2n) {
        margin-right: 0;
      }
    }
  }
  .gallery-columns-3 & {
    width: 100%;
    @include breakpoint($s) {
      margin-right: 1rem;
      width: calc(50% - 1rem);
      &:nth-of-type(2n) {
        margin-right: 0;
      }
    }
    @include breakpoint($m) {
      margin-right: 1rem;
      width: calc(33.33% - 1rem);
      &:nth-of-type(2n) {
        margin-right: 1rem;
      }
      &:nth-of-type(3n) {
        margin-right: 0;
      }
    }
  }
  .gallery-columns-4 & {
    width: 100%;
    @include breakpoint($s) {
      margin-right: 1rem;
      width: calc(50% - 1rem);
      &:nth-of-type(2n) {
        margin-right: 0;
      }
    }
    @include breakpoint($m) {
      margin-right: 1rem;
      width: calc(33.33% - 1rem);
      &:nth-of-type(2n) {
        margin-right: 1rem;
      }
      &:nth-of-type(3n) {
        margin-right: 0;
      }
    }
    @include breakpoint($l) {
      margin-right: 1rem;
      width: calc(25% - 1rem);
      &:nth-of-type(2n),
      &:nth-of-type(3n) {
        margin-right: 1rem;
      }
      &:nth-of-type(4n) {
        margin-right: 0;
      }
    }
  }
}
