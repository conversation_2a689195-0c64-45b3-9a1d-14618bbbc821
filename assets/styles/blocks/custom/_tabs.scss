.block-tabs {
  border: 1px solid $grey-dark;
  //box-shadow: 0 0 13px -5px rgba(0,0,0,.4);
  .tabs__btn {
    flex: 1;
    width: 100%;
    text-align: center;
    border-bottom: 1px solid $grey-dark;
    border-right: 1px solid $grey-dark;
    background: $grey;
    padding: 1rem 0;
    font-weight: 700;
    &.on {
      background: white;
      border-bottom: 0;
      //box-shadow: 0 0 13px -5px rgba(0,0,0,.5);
    }
  }
  .tabs__content {
    display: block;
    overflow: hidden;
  }
  .tabs__wrap {
    padding: 2rem 1rem;
    @include breakpoint($l) {
      display: flex;
      padding: 6.25rem 3.25rem;
    }
  }
  .tabs__column {
    padding: 0 3rem;
    min-width: 19.07rem;
    width: 100%;
    padding-bottom: 3rem;
    @include breakpoint($l) {
      padding-bottom: 0;
    }
  }
  h5 {
    margin-top: 0;
    margin-bottom: 1.5rem;
    font-size: 1.13rem;
    font-weight: 700;
  }
  a {
    @include button;
    margin-top: 1.5rem;
    display: block;
    width: auto;
    min-width: 150px;
    text-align: center;
    border: 1px solid $primary;
    color: white;
    background: $primary;
    padding-top: 1rem;
    &:hover {
      color: white;
    }
    &.external {
      background-color: $primary;
      border: 1px solid $primary;
      color: white;
      padding: 0.75rem 4.5rem 0.75rem 3rem;
      &:after {
        position: absolute;
        background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23ffffff' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-position: center;
        background-size: 84%;
        width: 1.1rem;
        height: 1rem;
        display: inline-block;
        content: " ";
        margin-left: 8px;
        color: transparent;
        background-repeat: no-repeat;
      }
      &:hover {
        background-color: white;
        color: $primary;
        &:after {
          background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23002663' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
      }
    }
  }
}

.tabs__list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  width: 100%;
  justify-content: space-around;
}

.tabs__li {
  padding: 5px;
}
