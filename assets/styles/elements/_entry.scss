/* ==========================================================================
 # Entry
========================================================================== */

.entry {

  &__image {
    @include wrap;
    @include narrow-content;
    img {
      width: 100%;
    }
  }
  &__meta {
    @include wrap;
    @include narrow-content;
    display: flex;
    justify-content: space-between;
    margin-top: 1.5rem;
    &__cats {
      color: $grey-aa;
    }
    &__tags {
      a {
        border: 1px solid $separatorblue;
        padding: 3px 12px;
        color: $grey-aa;
        border-radius: 15px;
        margin-left: 3px;
        font-size: 14px;
        text-transform: uppercase;
        text-decoration: none;
        transition: all .15s ease-in-out;
        &:hover {
          background-color: $grey-aa;
          color: white;
        }
      }
    }
  }
  &__author {
    @include wrap;
    @include narrow-content;
    &__inner {
      border-top: 3px solid $separatorblue;
      padding-top: 1.5rem;
      padding-bottom: 1.5rem;
      box-sizing: border-box;
      display: flex;
      align-items: flex-start;
      &__writer {
        width:40%;
      }
      &__photographer {
        width:40%;
      }
      &__content {
        display: block;
        color: $primary;
        font-weight: $ws-semibold;
      }
      &__title {
        color: $gray-text;
        text-transform: uppercase;
        padding-bottom: .25rem;
      }
      &__name {
        color: $primary;
        font-weight: $ws-semibold;
        padding-bottom: .25rem;
      }
      &__info {
        color: $primary;
        font-weight: normal;
        &::first-letter {
          text-transform: uppercase;
        }
      }
      &__img {
        width: 75px;
        height: 75px;
        margin-right: 1.5rem;
        background-position: center center;
        background-size: cover;
        border-radius: 50%;
        border: 1px solid $light-blue-25;
      }
    }
    .writer {
      display: block;
      color: $grey-aa;
      border-top: 1px solid $separatorblue;
      padding-top: 1.5rem;
    }
  }

  &__ingress {
    @include wrap;
    @include narrow-content;
    display: flex;
    flex-wrap: wrap;

    padding-bottom: 2rem;
    font-weight: 600;
    font-size: 1.25rem;
    line-height: 28px;
    @include breakpoint($sub-s) {
      font-size: 1.125rem;
      line-height: 24px
    }
  }

  .entry_published {
    @include wrap;
    @include narrow-content;
    &__inner {
      display: flex;
      align-items: center;
      border-top: 3px solid $separatorblue;
    }
  }

  .entry_published__dateheader {
    text-transform: uppercase;
    font-size: 14px;
    margin-top: 0;
    padding-top: $gap;
    display: block;
    margin-bottom: $gap;
    width: 50%;
  }

  .entry_published__some {
    width: 50%;
    padding-top: 1.5rem;
    margin-bottom: 1.5rem;
    text-align: right;
    .simple {
      display: flex;
      align-items: center;
      text-transform: uppercase;
      justify-content: flex-end;
      margin-top: 0rem;
      .social-share__title {
        font-size: 14px;
        margin-right: 1rem;
        margin-bottom: 0;
      }
      a {
        background-color: transparent;
        color: $primary;
        margin-top: 0;
        margin-bottom: 0;
        padding-top: 0;
        padding-bottom: 0;
      }
    }
  }

  .entry-meta__date {
    text-transform: uppercase;
    font-size: 14px;
  }

  &__header {
    @include wrap;
    @include narrow-content;
    margin-top: $gap;
  }

  &__content {
    //cannot have with images
    strong > a.external,
    p:not(.item-title):not(.shopify-scroller-link-out) > a.external,
    & > ul > li > a.external,
    & > ol > li > a.external {
      @include external-link;
      padding-right: 24px;
    }
  }

  &__side_content {
    @include wrap;
    @include narrow-content;
    margin-top: 7rem;
    &__inner {
      background-color: $light-blue-25;
      padding: 2rem;
      color: $primary;
      a {
        color: $link-base;
        &.external {
          @include external-link;
          padding-right: 24px;
        }
      }

    }
  }

  &__featured {
    @include wrap;
    @include narrow-content;
    &__inner {
      margin: 0rem 0;
      padding: 2rem 0 0;
      &--header {
        font-weight: 600;
        display:block;
        text-transform: uppercase;
        border-bottom: 2px solid $separatorblue;
        margin-bottom: .5rem;
        padding-bottom: .5rem;
      }
      .teaser {
        border-top: 0 !important;
      }
    }
  }

  &__footer {
    @include wrap;
    @include narrow-content;
    margin-top: $gap;
    .cat-links {
      display: block;
    }
    .tag-links {
      display: block;
    }
  }
  &__navigation {
    @include wrap;
    @include narrow-content;
    &__inner {
      border-top: 2px solid $separatorblue;
      border-bottom: 2px solid $separatorblue;
      padding-top: 4rem;
      box-sizing: border-box;
      padding-bottom: 4rem;
      margin-top: 2rem;
      margin-bottom: 2rem;
      display: flex;
      justify-content: space-between;
      @include breakpoint($sub-l) {
        display: block;
      }

      a {
        box-shadow: 0 0 10px -5px rgba(0,0,0,.4);
        width: calc(100% / 2 - $gap-medium);
        text-decoration: none;
        align-items: center;
        display: flex;
        transition: all .2s ease-out;
        border-bottom: 5px solid $primary;
        @include breakpoint($sub-l) {
          width: 100%;
          margin-bottom: $gap;
        }
        &:hover {
          border-bottom: 5px solid $brand;
          box-shadow: 0 0 7px -5px rgba(0,0,0,.4);
        }
        .icon {
          width: 20%;
          height: 3rem;
          fill: $grey-dark;
        }
      }
    }
    &--wrap {
      width: 80%;
      padding: 1rem 0;
      padding-right: 10%;
      span {
        display: block;
      }
      &.second {
        margin-left: 10%;
        width: 70%;
        padding-right:0;
      }
    }

    &--label {
      color: $grey-darker;
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
    }
    &--title {
      color: black;
      text-decoration: none;
      font-weight: 500;
      text-transform: uppercase;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }

  }

  &__rns {
    @include wrap;
    @include narrow-content;
    margin-top: 2rem;
  }

}

.entry-meta {
  display: flex;
  align-items: center;
  margin-bottom: .5rem;
  &__date {
    color: $gray-text;
    font-weight: 600;
    margin-right: .5rem;
  }
  &__article_type {
    a {
      color: $gray-text;
      font-weight: 600;
      font-size: 14px;
    }
    margin-right: .5rem;
  }
  &__term {
    color: $gray-text;
    padding-top: 5px;
    font-weight: $ws-medium;

  }
  &__separator {
    color: $gray-text;
    margin-right: .5rem;
  }

}

.home .block-news-list__wrap .entry-meta__term {
  padding-top: 0px;
}


/* NEW */
.entry-meta.entry-meta-new {
  &>.entry-meta__date.entry-meta__date-new {
    color: $gray-text;
    font-size: 14px;
  }

  &>.entry-meta__term.entry-meta__term-new {
    &>a {
      font-size: 12px;
      font-weight: 600;
      border-radius: 2rem;
      text-decoration: none;
      vertical-align: baseline;
      width: 100%;
      text-align: center;
      border: 1px solid $separatorblue;
      color: #002662;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      // background-color: #D7E3F3;
      background-color: #D2E4F3;
      padding: 0.26rem 1rem;
      margin-right: 8px;
      letter-spacing: 0.38px;
      margin-bottom: 4px;
      display: inline-block;
      width: auto;
      align-items: flex-start;
    }

  &>* {
    align-self: flex-start;
  }


  @include breakpoint($sub-m) {
      display: block;
  }
  }

}

@include breakpoint($sub-l) {

/* limit to 1 in mobile */
  .entry-meta.entry-meta-new>.entry-meta__term.entry-meta__term-new > a:not(:first-child) {
    display: none;
  }
  .entry-meta.entry-meta-new>.entry-meta__term.entry-meta__term-new > a:fist-child {
    display: block!important;
  }
}
