<?php

function kauppaka<PERSON>i_get_verkkoproducts_list() {
  return array();


  if ( '' === ( $verkko_products = get_transient( 'verkkoproducts_transient' ) ) ) {
    //error_log('GETVERKKOPRODUCTS transient not found');
    //$args = array(
    //  'timeout' => '50',
    //);
    //$response = wp_remote_get('https://kauppakamarikoulutus.helsinki.chamber.fi/wp-content/verkko_products.json', $args);
    //$response = json_decode( wp_remote_retrieve_body( $response ), true );
    //$verkko_products = $response;
    $upload_dir_tmp = wp_upload_dir();
    $filepath = $upload_dir_tmp['basedir'].'/verkko_products.json';
    $file_contents = file_get_contents($filepath);

    $verkko_products = json_decode($file_contents, true);
    set_transient( 'verkkoproducts_transient', $verkko_products, 7 * DAY_IN_SECONDS );
  }
  return $verkko_products;
}
