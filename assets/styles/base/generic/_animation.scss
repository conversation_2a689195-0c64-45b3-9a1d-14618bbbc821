/* All @keyframes animations */

// element fade in
@keyframes fadeInElement {
    0% { 
        opacity: 0;
        transform: translateY(80px);
    }
    80% {
    }
    100% { 
        opacity: 1; 
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}

// dropdown animation
@keyframes growDown {
    0% {
        transform: scaleY(0);
    }
    100% {
        transform: scaleY(1);
    }
}

// map filling split to 5 stages
@keyframes helsinki { // only fill "Helsingin seutu"
    0% {
        opacity: 1;
        clip-path: circle(0% at 42% 95%);
    } to {
        opacity: 1;
        clip-path: circle(8% at 42% 97%);
    }
}

@keyframes fill_20 { // use for 'less-20' (0-20)
    0% {
        opacity: 1;
        clip-path: polygon(0 100%, 40% 100%, 61% 100%, 100% 100%, 100% 100%, 0 100%);
    } to {
        opacity: 1;
        clip-path: polygon(0 84%, 40% 84%, 55% 80%, 100% 80%, 100% 100%, 0 100%);
    }
}
@keyframes fill_40 { // use for 'less-40' (20-40%)
    0% {
        opacity: 1;
        clip-path: polygon(0 100%, 39% 100%, 61% 100%, 100% 100%, 100% 100%, 0 100%);
    } to {
        opacity: 1;
        clip-path: polygon(0 70%, 39% 70%, 61% 64%, 100% 64%, 100% 100%, 0 100%);
    }
}
@keyframes fill_60 { // use for 'less-60' (40-60%)
    0% {
        opacity: 1;
        clip-path: polygon(0 100%, 54% 100%, 61% 100%, 100% 100%, 100% 100%, 0 100%);
    } to {
        opacity: 1;
        clip-path: polygon(0 52%, 54% 52%, 61% 50%, 100% 50%, 100% 100%, 0 100%);
    }
}
@keyframes fill_80 { // use for 'less-80' (60-80%)
    0% {
        opacity: 1;
        clip-path: polygon(0 100%, 49% 100%, 61% 100%, 100% 100%, 100% 100%, 0 100%);
    } to {
        opacity: 1;
        clip-path: polygon(0 37%, 49% 37%, 61% 34%, 100% 34%, 100% 100%, 0 100%);
    }
}
@keyframes fill_100 { // use for 'less-100' (80-99%)
    0% {
        opacity: 1;
        clip-path: polygon(0 100%, 49% 100%, 61% 100%, 100% 100%, 100% 100%, 0 100%);
    } to {
        opacity: 1;
        clip-path: polygon(0 22%, 49% 22%, 61% 18%, 100% 18%, 100% 100%, 0 100%);
    }
}
@keyframes fill_full { // use for 'is-100' (100%) (unlikely to be used)
    0% {
        opacity: 1;
        clip-path: polygon(0 100%, 100% 100%, 100% 100%, 0 100%);
    } to {
        opacity: 1;
        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    }
}
//