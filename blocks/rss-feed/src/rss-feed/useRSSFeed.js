import { useState, useEffect } from "@wordpress/element";

// Front-end component for rendering RSS feed
function RSSFeed() {
	const [articles, setArticles] = useState([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		// Fetch RSS articles on initial component mount
		const fetchArticles = async () => {
			setLoading(true);
			try {
				// Retrieve and parse the RSS feed XML
				const response = await fetch(
					"https://www.retriever-info.com/feed/2011171/helsingin-seudun-kauppakamari/index.xml",
				);
				const text = await response.text();
				const parser = new DOMParser();
				const xmlDoc = parser.parseFromString(text, "application/xml");

				// Extract article data from XML items
				const items = Array.from(xmlDoc.querySelectorAll("item"));
				const parsed = items
					.map((item, index) => ({
						id: index,
						title: item.querySelector("title")?.textContent || "Ei otsikkoa",
						date: new Date(
							item.querySelector("pubDate")?.textContent,
						).toLocaleDateString("fi-FI"),
						creator:
							item.getElementsByTagName("dc:creator")[0]?.textContent ||
							"Tuntematon",
						link: item.querySelector("link")?.textContent,
					}))
					// Filter out items with creator as "STT Info" or "Kauppalehti KL Nyt"
					.filter(
						(article) =>
							!["STT Info", "Kauppalehti KL Nyt"].includes(article.creator),
					);

				setArticles(parsed);
			} catch (err) {
				// Log any errors during fetch/parsing
				console.error("Virhe haettaessa RSS-syötettä:", err);
			} finally {
				setLoading(false);
			}
		};

		fetchArticles();
	}, []);

	return (
		<div>
			{loading ? (
				// Show loading message while fetching articles
				<p>Ladataan RSS-syötettä...</p>
			) : (
				// Render the parsed articles
				<div className="rss-wrapper">
					{articles.slice(0, 6).map((article) => (
						<div key={article.id} className="wp-block-column">
							<hr className="wp-block-separator rss-hr"></hr>
							<p className="meta">
								{article.date} <span className="separator">|</span>{" "}
								{article.creator}
							</p>
							<h3>
								<a
									href={article.link}
									target="_blank"
									rel="noopener noreferrer"
								>
									{article.title}
								</a>
							</h3>
						</div>
					))}
				</div>
			)}
		</div>
	);
}

export default RSSFeed;
