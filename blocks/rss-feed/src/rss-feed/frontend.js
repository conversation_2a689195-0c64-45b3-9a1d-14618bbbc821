import { render } from "@wordpress/element";
import RSSFeed from "./useRSSFeed";

// Front-end component for rendering RSS feed
function RSSFeedFrontend() {
	return <RSSFeed />;
}

// Mount the React component to all RSS feed block roots on the page
document.addEventListener("DOMContentLoaded", () => {
	document.querySelectorAll("#rss-feed-root").forEach((el) => {
		const attrs = JSON.parse(el.dataset.attributes || "{}");
		render(<RSSFeedFrontend attributes={attrs} />, el);
	});
});
