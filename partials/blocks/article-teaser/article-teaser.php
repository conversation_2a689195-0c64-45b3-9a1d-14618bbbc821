<?php

/**
 * Articleteaser
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-article-teaser-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-article-teaser';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$content = get_field('article');

?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">

  <div class="block-article-teaser__content">

    <header class="teaser__header">
      <span class="entry-meta__date"><?php echo kauppakamari_get_posted_on($content->ID); ?></span>
      <span class='entry-meta__separator'>I</span>
      <span class="entry-meta__term">
        <?php
        $category = get_the_category($content->ID);
        if(sizeof($category) > 1) {
          foreach($category as $i => $term) {
              if( get_post_meta($content->ID, '_yoast_wpseo_primary_category',true) == $term->term_id ) {
                unset($category[$i]);
                array_unshift($category, $term); //to first
                break;
             }
          }
        }

        if ( $category[0] ) {
            echo $category[0]->cat_name;
        } ?>
      </span>
      <h2 class="teaser__header__title">
        <a href="<?php echo get_permalink($content->ID) ?>" rel="bookmark">
          <?php echo get_the_title($content->ID); ?>
        </a>
      </h2>
    </header>

    <div class="teaser__summary">
      <?php echo get_the_excerpt($content); ?>
    </div>

    <div class="teaser__links">
      <a href="<?php echo get_permalink($content->ID) ?>" class="btn" rel="bookmark">
        <?php ask_e('Block: Read whole story'); ?>
      </a>

      <a href="/artikkelityyppi/kaikki" rel="bookmark">
        <?php ask_e('Block: All articles'); ?>
      </a>
    </div>

  </div>
  <div class="block-article-teaser__image" style="background-image: url('<?php echo get_the_post_thumbnail_url($content->ID); ?>')">

  </div>
</div>
