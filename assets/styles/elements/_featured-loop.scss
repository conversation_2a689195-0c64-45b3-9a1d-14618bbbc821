.listing_description * {
  text-align: center;
  max-width: 900px;
  margin: 2rem auto 4rem;
  font-size: 1.6rem;
  font-weight: 600;
}
.featured-loop {
  @include wrap;
  display: grid;
  grid-gap: 15px;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr 1fr 1fr 1fr 1fr;
  grid-template-areas:
  "item_1"
  "item_2"
  "item_3"
  "item_4"
  "item_5"
  "item_6"
  "item_7"
  "item_8"
  "item_9";
  @include breakpoint($s) {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr 1fr 1fr;
    grid-template-areas:
    "item_1 item_2"
    "item_3 item_4"
    "item_5 item_6"
    "item_7 item_8"
    "item_9 item_9";
  }
  @include breakpoint($l) {
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
    grid-template-areas:
      "item_1 item_1 item_2 item_3"
      "item_1 item_1 item_4 item_5"
      "item_6 item_7 item_8 item_9";
  }
  .item_1 { grid-area: item_1; }

  .item_2 { grid-area: item_2; }

  .item_3 { grid-area: item_3; }

  .item_4 { grid-area: item_4; }

  .item_5 { grid-area: item_5; }

  .item_6 { grid-area: item_6; }

  .item_7 { grid-area: item_7; }

  .item_8 { grid-area: item_8; }

  .item_9 { grid-area: item_9; }

  .loop_item {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
    padding: 2rem;
    min-height: 377px;
    display: flex;
    align-items: flex-end;
    flex-wrap: wrap;
    position: relative;
    transition: all .2s ease-out;
    &__content {
      z-index: 10;
    }
    .name {
      color: white;
      width: 100%;
      display: block;
      text-transform: uppercase;
      font-size: 1rem;
    }
    .term {
      color: white;
      width: 100%;
      display: block;
      text-transform: uppercase;
      font-size: 0.75rem;
      font-weight: 700;
    }
    .excerpt {
      padding-top: 1.25rem;
      color: white;
      font-size: 1.5rem;
      display: block;
      line-height: 1.88rem;
    }
    &.item_1 {
      .excerpt {
        font-weight: 600;
        font-size: 2.13rem;
        line-height: 2.5rem;
      }
    }
    a {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 11;
    }
    &:hover {
      //transform:scale(1.005);
      box-shadow: 0 7px 13px -10px #000;
    }
  }

}

