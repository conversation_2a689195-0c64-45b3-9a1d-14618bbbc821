/**
 * Project specific gulp configuration
 */

module.exports = {
  /**
   * URL for BrowserSync to mirror
   */
  devUrl: function () {
    return "https://helsinki.chamber.local/";
  },

  /**
   * JS files
   *
   * "build-file-name.js": [
   *   "../node_modules/some-module/some-module.js",
   *   "scripts/cool-scripts.js"
   * ]
   */
  js: function () {
    return {
      // main js to be loaded in footer
      "main.js": [
        // polyfill for external x:link svg (https://github.com/Keyamoon/svgxuse)
        "../node_modules/svgxuse/svgxuse.js",

        // polyfill for object-fit
        "../node_modules/objectFitPolyfill/dist/objectFitPolyfill.min.js",

        // vanilla js version of fitvids, that makes iframe videos responsice (https://www.npmjs.com/package/fitvids)
        "../node_modules/fitvids/dist/fitvids.js",

        // project specific js
        "scripts/components/youtube_api.js",

        "scripts/components/navigation.js",
        "scripts/components/markup-enhancements.js",
        "scripts/components/menu-toggle.js",
        "scripts/components/tabs.js",
        "scripts/components/slick.min.js",
        "scripts/components/slick.js",
        "scripts/components/accordion.js",
        "scripts/components/nyt.js",
        "scripts/components/contact.js",
        "scripts/components/clubs.js",
        "scripts/components/video-modal.js",
        "scripts/components/verkkofilter.js",
        "scripts/main.js",
        "scripts/components/skip-to-content.js",
      ],

      // critical js to be loaded in <head>
      "critical.js": [
        // jquery
        "../node_modules/jquery/dist/jquery.min.js",

        // library to lazyload images and iframes that have class "lazyload"
        "../node_modules/lazysizes/lazysizes.js",

        // project specific critical js

        "scripts/critical.js",
      ],

      // gutenberg editor specific js
      "editor-gutenberg.js": [
        "scripts/editor-gutenberg.js",
        "scripts/components/tabs.js",
      ],
    };
  },

  /**
   * CSS files
   *
   * "build-file-name.css": [
   *   "../node_modules/some-module/some-module.css",
   *   "styles/main.scss"
   * ]
   */
  css: function () {
    return {
      "main.css": ["styles/main.scss"],

      "editor-gutenberg.css": ["styles/editor-gutenberg.scss"],

      "editor-classic.css": ["styles/editor-classic.scss"],

      "admin.css": ["styles/admin.scss"],

      "wp-login.css": ["styles/wp-login.scss"],
    };
  },
};
