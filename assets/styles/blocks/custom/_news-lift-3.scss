/*
2022 block 3 columns news lift
*/

/*article page and */

.single .entry__content.wysiwyg:after {
  content: "";
  position: absolute;
  margin: auto;
  right: 0;
  left: 0;
  height: 50px;
  border-bottom: 3px solid $separatorblue;
  width: $max-width-m;
  @include wrap;
  @include narrow-content;
}

/*full with bg */

.block-news-lift-3.alignfull {
  padding-bottom: 30px!important;
  padding-top: 40px!important;
}

.block-news-lift-3.read-more-3.alignfull {
  padding-bottom: 40px!important;
  padding-top: 40px!important;
  margin-top: 140px!important;
  margin-bottom: 0px; /* closer gap to rns block */
}


.main .block-news-lift-3.alignwide .block-news-lift-3__wrap {
  padding-left: 0px;
  padding-right: 0px;
}

/*read more in article */
.entry_featured__new .block-news-lift-3,
.entry_featured__new .block-news-lift-3.read-more-3 {
  margin-top: 60px!important;
  &__wrap {
    .block-news-lift-3__title  {
      font-size: 22px!important;
      font-weight: 500;
      line-height: 28px!important;
    }
  }
}

/* no bg */
.block-news-lift-3.alignwide {
    // padding-bottom: 10px!important;
    // padding-top: 10px!important;
    padding: 10px 1.5rem;
}

/* trying to fix, read-more-3 block inserted into 2 column layout */
/* must avoid using */

@include breakpoint($l) {
  .page-template-default #primary main {
    .wysiwyg>.read-more-3.alignfull,
    .wysiwyg>.block-news-lift-3.alignfull {
      width: 100vw;
      margin: 0 calc(25% - 48vw);
    }
  }
}



/*normal, any */

.block-news-lift-3 {

  .block-news-lift-3-bottom {
    margin-top: 30px;
    margin-bottom: 30px;
    text-align: center;
    @include breakpoint($sub-s) {
      margin-top: 20px;
      margin-bottom: 20px;
    }
    a {
      @include bigtextlink;
    }
  }


  &__wrap {
    @include wrap();
    .title {

    }
  }
  .title {
    /*@include h1-new-bigger;*/
  }
  .block-news-lift-3__title {
    margin-top: 10px;
    margin-bottom: 28px;
    a {
      color: $primary;
      text-decoration: none;
      &:hover, &:focus {
        text-decoration: underline !important;
      }
    }
  }
  .block-news-lift-3__title-margin {
    display: block;
    margin-top: 24px;
  }

  &__content_wrap {
    display: flex;
    justify-content: space-between;
    text-align: left;
    @include breakpoint($sub-l) {
      flex-wrap: wrap;
    }
    @include breakpoint($sub-s) {
      display: block;
    }
  }
  &__item {
    width: calc((100% / 3) - 1.5rem);
    @include breakpoint($sub-l) {
      width: 100%;
      margin-bottom: 1.5rem;
    }
    background-color: transparent;
    padding: 0;
    display: block;
    justify-content: space-between;

    &--content {
      padding-bottom: 16px;
      font-size: 16px;
      p {
        font-size: 16px;
      }
    }

    img {
      width: 100%;
      max-width: 100%;
    }

    &:not(:first-child) {
      img {
        width: 100%;
        max-width: 100%;
        @include breakpoint($sub-l) {
          display: none;
        }
      }
    }
    .entry-meta {
      margin-top: 8px;
      margin-bottom: 2px;
    }
    .title {
      /*@include h2-new-bigger;*/
      padding-top: 0px;
      margin-top: 8px;
      margin-bottom: 0.5rem;
      @include breakpoint($m) {
        min-height: 2rem;
      }
    }

    .date {
      margin: .5rem 0rem;
      display: inline-block;
      color: $grey-darker;
      font-weight: 600;
      text-transform: capitalize;

    }
    .time {
      color: $grey-darker;
    }
    .location {
      color: $grey-darker;
    }
    a {
      color: $primary;
      text-decoration: none;
      &:hover, &:focus {
        text-decoration: underline !important;
      }
    }
  }
}


/* title bigger, but not in home page */
body:not(.home) {
  .block-news-lift-3 {
    .title {
      @include h1-new-bigger;
    }
    &__item {
      .title {
        @include h3-new-bigger;
      }
    }
  }
}

body.home {
  .block-news-lift-3__title.h1 {
      font-size: 1.5rem;
      @include breakpoint($s) {
        font-size: 28px;
      }
      font-weight: 600;
      line-height: 1.25;
  }

  .block-news-lift-3__item .title {
    @include breakpoint($m) {
      min-height: 4rem;
    }
  }

}

body.page-template-template-landingpage {
  .block-news-lift-3__wrap {
    .block-news-lift-3__title {
      // @include h1-new-bigger;
    }
  }
}
