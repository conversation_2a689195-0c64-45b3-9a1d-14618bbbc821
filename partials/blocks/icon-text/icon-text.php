<?php

/**
 * Icon - Text
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'icon-text-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'icon-text';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$items = get_field('block_rows');

?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">

  <?php
    foreach($items as $item) { ?>
      <?php if(!empty($item['group']['title']) && !empty($item['group']['content'])) { ?>
      <div class="icon-text__item">
        <?php
        if($icon = $item['group']['icon']) { ?>
          <div class="icon-text__img">
          <?php echo '<img src="' . $icon['sizes']['medium_large'] . '" alt="' . $item['group']['title'] . '" />'; ?>
          </div>
        <?php } ?>
        <div class="icon-text__content">
          <span class="title">
            <?php echo $item['group']['title'] ?: "Otsikko"; ; ?></span>
          <span class="content">
            <?php echo $item['group']['content'] ?: "Sisältö"; ?>
          <span>
        </div>
      </div>
      <?php } ?>

      <?php if(!empty($item['group_2']['title']) && !empty($item['group_2']['content'])) : ?>
      <div class="icon-text__item">
        <?php
        if($icon = $item['group_2']['icon']) { ?>
          <div class="icon-text__img">
          <?php echo '<img src="' . $icon['sizes']['medium_large'] . '" alt="' . $item['group_2']['title'] . '" />'; ?>
          </div>
        <?php } ?>

        <div class="icon-text__content">
          <span class="title">
            <?php echo $item['group_2']['title'] ?: "Otsikko"; ?></span>
          <span class="content">
            <?php echo $item['group_2']['content'] ?: "Sisältö"; ?>
          <span>
        </div>
      </div>
      <?php endif; ?>
    <?php
    }
  ?>
  <?php if(get_field("link")) : ?>
    <div class="icon-text__link-wrapper">
      <a class="icon-text__link" href="<?php echo get_field('link')['url'] ?>"><?php echo get_field('link')['title'];?></a>
    </div>
  <?php endif; ?>
</div>
