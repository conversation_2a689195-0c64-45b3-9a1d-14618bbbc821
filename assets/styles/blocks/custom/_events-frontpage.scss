/* frontpage events calendar */

.event-calendar {
    // hiding unwanted elements
    .sort-tools, .month-header, .filter-buttons, .calendar-paging, .item-categories-pill {
        display: none !important;
    }
    .events-amount-hidden {
        display: none;
        position: absolute;
        top: -999rem;
        left: -999rem;
        opacity: 0;
        visibility: hidden;
    }
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    text-align: center;
    //background: $light-blue-25;
    
    &__container {
        position: relative;
        background: $light-blue-25;
        width: 100%;
        padding: 45px 0;
        margin-bottom: $gap-big;
        & .addition-image-container {
            img {
                position: absolute;
                display: none;
            }
            & .addition_1 {
                display: block;
                width: 110px;
                top: -16px;
                left: 64px;
            }
            & .addition_3 {
                display: block;
                top: 12px;
                right: 12px;
                width: 80px;
            }
        }
    }
    &__title {
        background: $white;
        width: 100%;
        padding: 2rem 0.25rem;
    }
    &__main {
        position: relative;
        background: $white;
        padding: 0 1.5rem 0 1.5rem;
        z-index: 2;
        .event-type-selector {
            padding: 3rem 0;
            .main-link {
                @include breakpoint($s) {
                    font-size: 20px;
                    padding: 5px 20px;
                }
                font-size: 16px;
                font-weight: $ws-semibold;
                background: none;
                border: none;
                outline: 0;
                text-transform: uppercase;
                display: inline-block;
                // background-color:red;
                padding: 5px 10px;
                color: $gray-text;
                color: #6d6d6d;
                border-bottom: 3px solid $grey-dark;
                &--is-selected {
                    color: $primary;
                    border-color: $primary;
                }
                &:hover {
                    color: $primary;
                }
            }
        }
        a:focus { // Event item focus
            .item-title {
                border: 1px dotted $primary;
                width: fit-content;
            }
        }
        .event-item {
            &:hover {
                .item-title {
                    text-decoration: underline;
                }
            }
            padding: 1rem;
            border-bottom: 2px solid $grey;
            .item-timebox {
              background: $coral-75;
              padding: 1.5rem 1.2rem;
              color: $primary;
              font-size: 18px;
              height: 90px;
              > div {
                font-weight: bold;
              }
            }
            .item-content-wrapper {
                padding: 10px 0;
            }
            .item-top-row {
              display: flex;
              flex-wrap: wrap;
              align-items: center;
              gap: 1rem;
              font-size: 16px;
              font-weight: $ws-medium;
            }
            .item-method {
                color: $black-text;
            }
            .item-categories-pill {
              display: inline-block;
              color: $primary;
              font-size: 12px;
              line-height: 1.5;
              font-weight: 600;
              padding: 0.26rem 1rem;
              border-radius: 14px;
              background-color: $light-blue-50;
            }
            .item-title {
              margin: 10px 0;
              font-size: 18px;
              color: $primary;
              font-weight: $ws-semibold;
              //text-decoration: underline;
              //text-underline-offset: 1px;
              &:after {
                // position: absolute;
                content: "\f35d";
                font-family: "Font Awesome 6 Sharp";
                font-size: 16px;
                display: inline-block;
                margin-left: 8px;
              }
            }
            @include breakpoint($m) {
                display: grid;
                gap: 2rem;
                grid-template-columns: 158px 1fr;
                .item-title {
                  margin: 10px 0;
                  font-size: 20px;
                  text-align: left;
                }
            }
        }
    }
    &__events {
        *:not(.item-title) { // override user agent stylesheet for annoying text decorations
            text-decoration: none !important;
        }
    }
    &__link-container {
        padding: 3.5rem 0;
    }
    &__link {
        @include bigtextlink;
    }

    @include breakpoint($m) {
        &__container {
            //padding: 0 60px;
        }
        &__main {
            padding: 0 5rem 0 5rem;
            & .item-timebox {
                width: 100%;
            }
        }
    }
    @include breakpoint($sub-m) {
        &__main {
            & .event-item {
                text-align: left;
                & .item-timebox {
                    height: fit-content;
                    font-size: 16px;
                }
                & .item-method {
                    font-size: 14px;
                }
            }
        }
    }
    @include breakpoint(900px) {
        margin-top: 3rem;
        &__container {
            padding: 45px 80px;
        }
    }

    @include breakpoint($xl) {
        &__main {
            padding: 0 6rem 0 6rem;
            & .event-item {
                & .item-timebox {
                    //max-width: 148px;
                }
            }
        }
        &__container {
            padding: 0 20%;
            & .addition-image-container {
                img {
                    display: block;
                    z-index: 1;
                    &.addition_1 {
                        width: 226px;
                        top: -32px;
                        left: -66px;
                    }
                    &.addition_2 {
                        width: 67px;
                        left: 50px;
                        top: 240px;
                    }
                    &.addition_3 {
                        width: 111px;
                        right: 48px;
                        top: -32px;
                    }
                    &.addition_4 {
                        width: 342px;
                        right: -170px;
                        top: 220px;
                    }
                    &.addition_5 {
                        bottom: 0;
                        right: calc(15.5% + 0.5rem);
                    }
                }
            }
        }
    }
    @include breakpoint(min-width 1600px) {
        &__container {
            //padding: 0 20%;
        }
    }
}