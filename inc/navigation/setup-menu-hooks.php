<?php

/**
 * Menus
 *
 * @package kauppakamari
 */

/**
 * Dropdown caret for primary menu
 *
 * @param string  $item_output the menu item output
 * @param WP_Post $item menu item object
 * @param int     $depth depth of the menu
 * @param array   $args wp_nav_menu() arguments
 *
 * @return string menu item with possible description
 */
function kauppakamari_dropdown_icon_to_menu_links($item_output, $item, $depth, $args)
{

    if ($args->theme_location == 'primary') {
        foreach ($item->classes as $value) {
            if ($value == 'menu-item-has-children') {
                // add caret button. not focusable as tab navigation is handeled without this button
                //$opts_array = array('title' => 'Toggle menu', 'desc' => 'Toggle dropdown menu');
                $item_output .= '<button tabindex="-1" aria-haspopup="true" aria-label="Avaa pudotusvalikko" class="menu-item__link__caret js-menu-caret"><span class="header-arrow fa-sharp fa-solid fa-sm fa-chevron-down"></span></button>';
                //$item_output .= '<button tabindex="-1" aria-haspopup="true" aria-label="Toggle dropdown menu" class="menu-item__link__caret js-menu-caret">'. kauppakamari_get_svg('caret-down') .'</button>';

            }
        }
    }
    return '<span class="menu-item__link">' . $item_output . '</span>';
}
add_filter('walker_nav_menu_start_el', 'kauppakamari_dropdown_icon_to_menu_links', 10, 4);

/**
 * SVG icons for social menu
 *
 * @param string  $title the title of menu item
 * @param WP_Post $item menu item object
 * @param array   $args wp_nav_menu() arguments
 * @param int     $depth depth of the menu
 *
 * @return string menu item with possible description
 */
function kauppakamari_social_menu_icons($title, $item, $args, $depth)
{

    if ($args->theme_location == 'social') {

        // supported social icons
        $social_icons = array(
            'facebook.com'   => 'facebook',
            'instagram.com'  => 'instagram',
            'linkedin.com'   => 'linkedin',
            'mailto:'        => 'mail',
            'x.com'    => 'x-twitter',
            'youtube.com'    => 'youtube',
            'bsky.app'    => 'bluesky',
            'threads.net'    => 'threads',
        );

        // fallback icon
        $svg = 'external';

        // find matching icon
        foreach ($social_icons as $domain => $value) {
            if (strstr($item->url, $domain)) {
                $svg = $value;
            }
        }

        // replace title with svg and <span> wrapped title

        // $title = kauppakamari_get_svg(esc_attr($svg), array('title' => $title)) . '<span class="social-navigation__item__label social-navigation__item__label-' . sanitize_title($title) . '">' . $title . '</span>';
        $title = "<span class='social-navigation__icon social-navigation__icon-" . sanitize_title($title) . "' style='" . svgToBackgroundImage(get_template_directory() . "/dist/images/" . sanitize_title($title) . ".svg", "#002663") . "'>" . '</span><span class="social-navigation__item__label social-navigation__item__label-' . sanitize_title($title) . '">' . $title . '</span>';
        return $title;
    }

    return $title;
}
add_filter('nav_menu_item_title', 'kauppakamari_social_menu_icons', 10, 4);


function svgToBackgroundImage($svgPath, $fillColor = '#000000')
{
    if (!file_exists($svgPath)) {
        return 'background-image: none;';
    }

    $svgContent = file_get_contents($svgPath);

    // Ensure XML is properly parsed
    $dom = new DOMDocument();
    libxml_use_internal_errors(true);
    $dom->loadXML($svgContent);
    libxml_clear_errors();

    $svgElement = $dom->getElementsByTagName('svg')->item(0);

    if ($svgElement) {
        // Add or modify the fill attribute in the SVG element
        $svgElement->setAttribute('fill', $fillColor);
    }

    $modifiedSvgContent = $dom->saveXML();

    // Encode as base64 for inline usage
    $base64Svg = 'data:image/svg+xml;base64,' . base64_encode($modifiedSvgContent);

    return 'background-image: url("' . $base64Svg . '");';
}

/**
 * Include icon by class name
 *
 * Example: `icon-arrow` includes svg `arrow` from SVG sprite.
 *
 * @param string $title the title of menu item
 * @param WP_Post $item menu item object
 * @param array $args wp_nav_menu() arguments
 * @param int $depth depth of the menu
 *
 * @return string menu item with possible description
 */
function kauppakamari_icons_from_classes($title, $item, $args, $depth)
{

    foreach ($item->classes as $value) {
        if (strpos($value, 'icon-') === 0) {
            $title = kauppakamari_get_svg(str_replace('icon-', '', $value), ['class' => 'icon-from-class']) . $title;
        }
    }
    return $title;
}
add_filter('nav_menu_item_title', 'kauppakamari_icons_from_classes', 10, 4);

/**
 * The core navigation file
 *
 * A custom WordPress nav walker class, using the WordPress built in menu manager.
 *
 * Original GitHub URI: https://github.com/twittem/wp-bootstrap-navwalker
 * Version: 2.0.4
 * Author: Originally Edward McIntyre - @twittem
 * License: GPL-2.0+
 * License URI: http://www.gnu.org/licenses/gpl-2.0.txt
 *
 * @Date: 2019-10-15 12:30:02
 * @Last Modified by: Timi Wahalahti
 * @Last Modified time: 2019-10-15 14:37:37
 * @package air-light
 */
class Air_Light_Navwalker extends Walker_Nav_Menu
{
    private $curItem;
    public function start_lvl(&$output, $depth = 0, $args = array())
    {
        $indent = str_repeat("\t", $depth);
        $output .= "\n$indent<span class=\"sub-menu-wrap\">\n";
        $output .= "\n$indent<ul class=\"sub-menu\">\n";
    }
    function end_lvl(&$output, $depth = 0, $args = array())
    {
        if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }

        $item = $this->curItem;
        $title = get_field('title', $item);
        $img = get_field('img', $item);
        $content = get_field('content', $item);
        $link = get_field('link', $item);

        $indent = str_repeat($t, $depth);
        $output .= "$indent</ul>{$n}";

        /* if($title) {
    $output .= '<div class="sub-menu-featured">';
        if($title) {
        $output .= '<span class="title">' . $title . '</span>';
        }
        if($img) {
        $output .= '<img src="' . $img['sizes']['medium_large'] . '" />';
        }
        if($content) {
        $output .= '<span class="content">' . $content . '</span>';
        }
        if($link) {
        $output .= '<a href="' . $link['url'] . '">' . $link['title'] . '</a>';
        }
        $output .= '</div>';
    } */
        $output .= "\n$indent</span>{$n}";
    }
    function start_el(&$output, $item, $depth = 0, $args = array(), $id = 0)
    {
        $indent = ($depth) ? str_repeat("\t", $depth) : '';

        /**
         * Dividers, Headers or Disabled
         * =============================
         * Determine whether the item is a Divider, Header, Disabled or regular
         * menu item. To prevent errors we use the strcasecmp() function to so a
         * comparison that is not case sensitive. The strcasecmp() function returns
         * a 0 if the strings are equal.
         */
        if (strcasecmp($item->attr_title, 'divider') === 0 && 1 === $depth) {
            $output .= $indent . '<li class="divider">';
        } else if (strcasecmp($item->title, 'divider') === 0 && 1 === $depth) {
            $output .= $indent . '
<li class="divider">';
        } else if (strcasecmp($item->attr_title, 'dropdown-header') === 0 && 1 === $depth) {
            $output .= $indent . '
<li class="dropdown-header">' . esc_attr($item->title);
        } else if (strcasecmp($item->attr_title, 'disabled') === 0) {
            $output .= $indent . '
<li class="disabled"><a href="#">' . esc_attr($item->title) . '</a>';
        } else {
            $class_names = $value = '';
            $classes = empty($item->classes) ? array() : (array) $item->classes;
            $classes[] = 'air-light-menu-item menu-item-' . $item->ID;
            $class_names = join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args));
            if ($args->has_children) {
                $class_names .= ' dropdown';
            }
            if (in_array('current-menu-item', $classes)) {
                $class_names .= ' active';
            }
            $class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';
            $id = apply_filters('nav_menu_item_id', 'menu-item-' . $item->ID, $item, $args);
            $id = $id ? ' id="' . esc_attr($id) . '"' : '';
            $output .= $indent . '<li' . $id . $value . $class_names . '>';
            $atts = array();
            $atts['title'] = ! empty($item->attr_title) ? $item->attr_title : '';
            $atts['target'] = ! empty($item->target) ? $item->target : '';
            $atts['rel'] = ! empty($item->xfn) ? $item->xfn : '';
            // If item has_children add atts to a.
            if ($args->has_children && 0 === $depth) {
                $this->curItem = $item;
                $atts['href'] = ! empty($item->url) ? $item->url : '';
                $atts['data-toggle'] = 'dropdown';
                $atts['class'] = 'dropdown';
            } else {
                $atts['href'] = ! empty($item->url) ? $item->url : '';
            }
            $atts = apply_filters('nav_menu_link_attributes', $atts, $item, $args);
            $attributes = '';
            foreach ($atts as $attr => $value) {
                if (! empty($value)) {
                    $value = ('href' === $attr) ? esc_url($value) : esc_attr($value);
                    $attributes .= ' ' . $attr . '="' . $value . '"';
                }
            }
            $item_output = $args->before;
            $item_output .= '<a' . $attributes . '>';
            $item_output .= $args->link_before . apply_filters('the_title', $item->title, $item->ID) . $args->link_after;
            $item_output .= ($args->has_children && 0 === $depth) ? ' </a>' : '</a>';
            $item_output .= $args->after;
            $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
        }
    }
    /**
     * Traverse elements to create list from elements.
     *
     * Display one element if the element doesn't have any children otherwise,
     * display the element and its children. Will only traverse up to the max
     * depth and no ignore elements under that depth.
     *
     * This method shouldn't be called directly, use the walk() method instead.
     *
     * @see Walker::start_el()
     * @since 2.5.0
     *
     * @param object $element Data object.
     * @param array $children_elements List of elements to continue traversing.
     * @param int $max_depth Max depth to traverse.
     * @param int $depth Depth of current element.
     * @param array $args Args.
     * @param string $output Passed by reference. Used to append additional content.
     * @return null Null on failure with no changes to parameters.
     */
    public function display_element($element, &$children_elements, $max_depth, $depth, $args, &$output)
    {
        if (! $element) {
            return;
        }
        $id_field = $this->db_fields['id'];
        // Display this element.
        if (is_object($args[0])) {
            $args[0]->has_children = ! empty($children_elements[$element->$id_field]);
        }
        parent::display_element($element, $children_elements, $max_depth, $depth, $args, $output);
    }
    /**
     * Menu Fallback
     * =============
     * If this function is assigned to the wp_nav_menu's fallback_cb variable
     * and a manu has not been assigned to the theme location in the WordPress
     * menu manager the function with display nothing to a non-logged in user,
     * and will add a link to the WordPress menu manager if logged in as an admin.
     *
     * @param array $args passed from the wp_nav_menu function.
     */
    public static function fallback($args)
    {
        if (current_user_can('manage_options')) {
            extract($args);
            $fb_output = null;
            if ($container) {
                $fb_output = '<' . $container;
                if ($container_id) {
                    $fb_output .= ' id="' . $container_id . '"';
                }
                if ($container_class) {
                    $fb_output .= ' class="' . $container_class . '"';
                }
                $fb_output .= '>';
            }
            $fb_output .= '<ul';
            if ($menu_id) {
                $fb_output .= ' id="' . $menu_id . '"';
            }
            if ($menu_class) {
                $fb_output .= ' class="' . $menu_class . '"';
            }
            $fb_output .= '>';
            $fb_output .= '<li><a href="' . admin_url('nav-menus.php') . '">Add a menu</a></li>';
            $fb_output .= '</ul>';
            if ($container) {
                $fb_output .= '</' . $container . '>';
            }
            echo $fb_output;
        }
    }
}
