function handleMouseDown() {
  document.body.classList.remove("user-is-tabbing");
  window.addEventListener("keydown", handleFirstTab);
  window.removeEventListener("mousedown", handleMouseDown);
}

function handleFirstTab(e) {
  if (e.key === "Tab") {
    document.body.classList.add("user-is-tabbing");
    window.removeEventListener("keydown", handleFirstTab);
    window.addEventListener("mousedown", handleMouseDown);
  }
}

window.addEventListener("keydown", handleFirstTab);
