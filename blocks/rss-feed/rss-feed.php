<?php
/**
 * Plugin Name:       <PERSON><PERSON>KK RSS Feed
 * Description:       A RSS feed block for HSKK content.
 * Version:           0.1.0
 * Requires at least: 6.7
 * Requires PHP:      7.4
 * Author:            Alfons Digital
 * License:           GPL-2.0-or-later
 * License URI:       https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain:       rss-feed
 *
 * @package CreateBlock
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly
}

error_log('rss-feed.php loaded');

require_once __DIR__ . '/build/rss-feed/render.php';

add_action( 'init', function () {
	register_block_type( __DIR__ . '/build/rss-feed', [
		'render_callback' => 'rss_feed_render_callback',
	] );
} );
