/*!*********************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./src/news-lift-wrapper/style.scss ***!
  \*********************************************************************************************************************************************************************************************************************************************************/
.news-lift-slider {
  display: flex;
  overflow-x: scroll;
  scroll-snap-type: x mandatory;
  gap: 2.5rem;
  padding-bottom: 50px;
  margin-bottom: 20px;
}

.news-lift-slider::-webkit-scrollbar {
  -webkit-appearance: none;
  height: 8px !important;
  border-radius: 8px;
  background-color: rgba(29, 57, 127, 0.1);
}

.news-lift-slider::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background-color: rgba(29, 57, 127, 0.4);
}

.news-lift-slider-wrapper.alignfull {
  width: 100vw;
  margin-right: calc(50% - 50vw);
}

.page-template-default .news-lift-slider-wrapper.alignfull,
.page-template-template-NYT .news-lift-slider-wrapper.alignfull {
  width: 100%;
  margin-right: 0;
}

.news-lift-slider-wrapper {
  width: 100%;
  position: relative;
}

.submit-btn {
  position: absolute;
  bottom: 1.5rem;
  right: 50%;
  z-index: 9999;
}

.alignwide {
  max-width: 98rem;
  margin: auto;
}

.page-template-default .alignwide,
.page-template-template-NYT .alignwide {
  max-width: 100%;
  margin: 0 2rem;
}

.news-lift-heading {
  display: flex;
  gap: 150px;
  align-items: baseline;
  margin: 0 20px 20px 20px;
}

.news-lift-heading h2 {
  font-size: 38px;
  color: #002662;
  font-weight: 600;
}

.news-lift-heading a {
  font-size: 18px;
  font-weight: 600;
  color: #002662;
  text-decoration: none;
  position: relative;
}

.news-lift-heading a:hover {
  text-decoration: underline;
}

.news-lift-heading a::after {
  content: "";
  position: absolute;
  top: 5px;
  width: 8px;
  height: 13px;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiID8+CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgd2lkdGg9IjgiIGhlaWdodD0iMTMiPgoJPHBhdGggZmlsbD0iIzAwMjY2MyIgZD0iTTcuOTEzMzg2MyA2LjVMNy4xOTk3MjkgNy4yMTU1MzhMMi4xNDcyODc2IDEyLjI4MTI5NkwxLjQzMDQ3MjUgMTNMMCAxMS41NjU3NThMMC43MTM2NTczMiAxMC44NTAyMTlMNS4wNTI0NDExIDYuNUwwLjcxNjgxNTExIDIuMTQ5NzgwOEwwIDEuNDM0MjQyNkwxLjQzMDQ3MjUgMEwyLjE0NDEyOTggMC43MTU1MzgyNkw3LjE5NjU3MDkgNS43ODEyOTU4TDcuOTEzMzg2MyA2LjVaIi8+Cjwvc3ZnPgo=);
  background-size: contain;
  background-repeat: no-repeat;
  margin-right: 12px;
}

@media (max-width: 1520px) {
  .news-lift-slider {
    margin: 0 20px 20px 20px;
  }
  .page-template-default .news-lift-slider,
  .page-template-template-NYT .news-lift-slider {
    margin: 0 0 20px 0;
  }
  .news-lift-heading {
    margin: 0 40px 20px 40px;
  }
  .page-template-default .news-lift-heading,
  .page-template-template-NYT .news-lift-heading {
    margin: 0 20px 20px 20px;
  }
}
@media (max-width: 1190px) and (min-width: 1120px) {
  .page-template-default .news-lift-heading {
    display: block;
  }
}
@media (max-width: 1119px) {
  .page-template-default .news-lift-slider-wrapper.alignfull,
  .page-template-template-NYT .news-lift-slider-wrapper.alignfull {
    width: 100vw;
    margin-right: -3rem;
  }
}
@media (max-width: 788px) {
  .news-lift-heading {
    display: block;
  }
}
@media (max-width: 640px) {
  .alignwide {
    max-width: 100%;
    margin: auto;
  }
}
