<?php
// This file is generated. Do not modify it manually.
return array(
	'news-lift-item' => array(
		'$schema' => 'https://schemas.wp.org/trunk/block.json',
		'apiVersion' => 3,
		'name' => 'hskk/news-lift-item',
		'version' => '0.1.0',
		'title' => 'News Lift Item',
		'category' => 'kauppakamari-blocks',
		'icon' => 'megaphone',
		'description' => 'Yksittäinen nosto uutisslideriin.',
		'parent' => array(
			'hskk/news-lift-wrapper'
		),
		'example' => array(
			
		),
		'supports' => array(
			'html' => false
		),
		'attributes' => array(
			'mode' => array(
				'type' => 'string',
				'default' => 'manual'
			),
			'selectedPost' => array(
				'type' => 'number'
			),
			'selectedPostType' => array(
				'type' => 'string',
				'default' => ''
			),
			'manualTitle' => array(
				'type' => 'string'
			),
			'manualText' => array(
				'type' => 'string'
			),
			'manualDate' => array(
				'type' => 'string'
			),
			'manualCategory' => array(
				'type' => 'string'
			),
			'manualImageUrl' => array(
				'type' => 'string'
			),
			'manualImageId' => array(
				'type' => 'number'
			),
			'manualLinkText' => array(
				'type' => 'string',
				'default' => ''
			),
			'manualLinkUrl' => array(
				'type' => 'string',
				'default' => ''
			),
			'postTitle' => array(
				'type' => 'string',
				'default' => ''
			),
			'postText' => array(
				'type' => 'string',
				'default' => ''
			),
			'postDate' => array(
				'type' => 'string',
				'default' => ''
			),
			'postCategory' => array(
				'type' => 'string',
				'default' => ''
			),
			'postUrl' => array(
				'type' => 'string',
				'default' => ''
			),
			'postImageUrl' => array(
				'type' => 'string',
				'default' => ''
			),
			'overrideImageUrl' => array(
				'type' => 'string',
				'default' => ''
			)
		),
		'textdomain' => 'news-lift-item',
		'editorScript' => 'file:./index.js',
		'editorStyle' => 'file:./index.css',
		'style' => 'file:./style-index.css',
		'viewScript' => 'file:./view.js'
	)
);
