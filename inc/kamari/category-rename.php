<?php

function kauppakamari_change_cat_label() {
  global $submenu;
  $submenu['edit.php'][15][0] = 'Teemat'; // Rename categories to Authors
}
add_action( 'admin_menu', 'kauppakamari_change_cat_label' );

function kauppakamari_change_cat_object() {
  global $wp_taxonomies;
  $labels = &$wp_taxonomies['category']->labels;
  $labels->name = 'Teema';
  $labels->singular_name = 'Teema';
  $labels->add_new = 'Lisää Teema';
  $labels->add_new_item = 'Lisää Teema';
  $labels->edit_item = 'Muokkaa teemaa';
  $labels->new_item = 'Teema';
  $labels->all_items = 'Kaik<PERSON> Te<PERSON>t';
  $labels->menu_name = 'Teema';
  $labels->name_admin_bar = 'Teema';
}
add_action( 'init', 'kauppakamari_change_cat_object' );


function admin_posts_taxonomy_filter() {
  global $typenow, $post_type; // this variable stores the current custom post type

  //bug fix, admin filter post_type = Array
  if(is_array($post_type)) {
    $post_type = $typenow;
  }


  //if( $typenow == 'post' ){ // choose one or more post types to apply taxonomy filter for them if( in_array( $typenow  array('post','games') )
  if( in_array( $typenow,  array('post','release') )){ // choose one or more post types to apply taxonomy filter for them if( in_array( $typenow  array('post','games') )
    $taxonomy_names = array('article_type');
    foreach ($taxonomy_names as $single_taxonomy) {
      $current_taxonomy = isset( $_GET[$single_taxonomy] ) ? $_GET[$single_taxonomy] : '';
      $taxonomy_object = get_taxonomy( $single_taxonomy );
      $taxonomy_name = strtolower( $taxonomy_object->labels->name );
      $taxonomy_terms = get_terms( $single_taxonomy );
      if(count($taxonomy_terms) > 0) {
        echo "<select name='$single_taxonomy' id='$single_taxonomy' class='postform'>";
        echo "<option value=''>Kaikki $taxonomy_name</option>";
        foreach ($taxonomy_terms as $single_term) {
          echo '<option value='. $single_term->slug, $current_taxonomy == $single_term->slug ? ' selected="selected"' : '','>Sisältötyyppi: ' . $single_term->name .' (' . $single_term->count .')</option>';
        }
        echo "</select>";
      }
    }

    //tags:
    $taxonomies = array('post_tag');

    foreach ( $taxonomies as $tax_slug ) {
      $current_tax_slug = isset( $_GET['tag'] ) ? $_GET['tag'] : false;
      $tax_obj = get_taxonomy( $tax_slug );
      $tax_name = $tax_obj->labels->name;
      $terms = get_terms($tax_slug);
      if ( count( $terms ) > 0) {
        // The select name must be 'tag' ONLY.
        echo "<select name='tag' id='$tax_slug' class='postform'>";
        echo "<option value=''>Kaikki $tax_name</option>";
        foreach ( $terms as $term ) {
          echo '<option value=' . $term->slug, $current_tax_slug == $term->slug ? ' selected="selected"' : '','>' . $term->name .' (' . $term->count .')</option>';
        }
        echo "</select>";
      }
    }

  }
  //if
}

add_action( 'restrict_manage_posts', 'admin_posts_taxonomy_filter' );


//hide yoast / SEO filter dropdown

function bb_disable_yoast_seo_metabox( $post_types ) {
  global $wpseo_meta_columns, $typenow;
  if($typenow == 'post' || $typenow == 'release') {
    remove_action( 'restrict_manage_posts', array( $wpseo_meta_columns , 'posts_filter_dropdown' ) );
    remove_action( 'restrict_manage_posts', array( $wpseo_meta_columns , 'posts_filter_dropdown_readability' ) );
    unset( $post_types['custom_post_type'] );
    return $post_types;
  }
 }
 //add_filter( 'wpseo_accessible_post_types', 'bb_disable_yoast_seo_metabox' );
