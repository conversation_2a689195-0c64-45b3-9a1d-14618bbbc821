.block-content-background {
  // background-color: $grey;
  background-color: #F7F5F0;
  &--wrap {
    @include wrap();
    padding-top: $gap;
    padding-bottom: $gap;
  }
  &--title {
    border-bottom: 1px solid $grey-dark;
    padding-bottom: $gap;
    color: $primary;
  }
  &--content {
    p {
      margin-top: $gap;
      margin-bottom: $gap;
      font-size: 1.25rem;

      @include breakpoint($m) {
        padding-right: 25%;
      }
    }
  }
  &--list {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }
  &--listitem {
    width: 100%;
    @include breakpoint($m) {
      width: calc((100% / 2) - 1rem);
    }
    @include breakpoint($l) {
      width: calc((100% / 3) - 1rem);
    }
    h3 {
      margin-top: 0px;
      margin-bottom: $gap;
      font-size: 1.2rem;
      span {
        color: $brand;
        font-weight: bold;
      }
    }
  }
}
