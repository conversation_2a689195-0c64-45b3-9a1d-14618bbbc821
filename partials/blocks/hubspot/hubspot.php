<?php

/**
 *  hubspot
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-hubspot-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-hubspot';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$items = get_field('block_rows');

?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">
<?php

  if( get_field('hubstop-option') === 'form') :
    $hubspot = get_field('form');
    $id = $hubspot['formid'];
    $portal_id = $hubspot['portalid'];
    echo '
      <' . 'script charset="utf-8" type="text/javascript" src="//js.hsforms.net/forms/v2-legacy.js"></script>
      <' . 'script charset="utf-8" type="text/javascript" src="//js.hsforms.net/forms/v2.js"></script>
      <script>
        hbspt.forms.create({
          portalId: "' . $portal_id . '",
          formId: "' . $id . '"
        });
      </script>
    ';
  elseif ( get_field('hubstop-option') === 'cta') :
    $hubspot = get_field('cta');
    $id = $hubspot['cta_id'];
    $portal_id = $hubspot['portalid'];
    echo '
      <!--HubSpot Call-to-Action Code -->
      <span class="hs-cta-wrapper" id="hs-cta-wrapper-' . $id . '">
          <span class="hs-cta-node hs-cta-' . $id . '" id="' . $id . '">
              <!--[if lte IE 8]>
              <div id="hs-cta-ie-element"></div>
              <![endif]-->
              <a href="https://cta-redirect.hubspot.com/cta/redirect/' . $portal_id . '/' . $id . '" >
                  <img class="hs-cta-img" id="hs-cta-img-' . $id . '" style="border-width:0px;" src="https://no-cache.hubspot.com/cta/default/' . $portal_id . '/' . $id . '.png"  alt="New call-to-action"/>
              </a>
          </span>
          <' . 'script charset="utf-8" src="//js.hubspot.com/cta/current.js"></script>
          <script type="text/javascript">
              hbspt.cta.load(' . $portal_id . ', \'' . $id . '\', {});
          </script>
      </span>
      <!-- end HubSpot Call-to-Action Code -->
    ';
  endif;
?>

</div>
