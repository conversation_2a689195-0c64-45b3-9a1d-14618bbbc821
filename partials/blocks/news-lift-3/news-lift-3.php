<?php

/**
 * Column news-lift-3 - 2022 3 columns for selected type+category
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
if(isset($block['id'])) {
    $id = 'block-news-lift-3' . $block['id'];
}
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
if(!isset($id)) {
    $id = 'block-news-lift-3' . '100000';
}
$block_background_html='';

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-news-lift-3';

// Load values and assing defaults.
//$content = get_field('news-lift-3');
$relevanssi_posts = false;
$related_posts_ids = array();

//this is inserted acf/read-more-3 / Lue lis<PERSON> 3 -block into page
if(isset($block) && isset($block['name']) && $block['name'] == 'acf/read-more-3') {
    $relevanssi_posts = true;
    $parent_post_id = get_the_ID();
    $block_selected_post_type = 'post';
    $block_link = false;
    $block_background_color = '#EFF4FA';

    //$block_title = 'Lue myös';
    //$block_selected_post_taxonomy = 'article_type'; //DEBUG

    $block_title = get_field('title');
    $block_background_color = get_field('background_color');

    $className .= ' read-more-3'; //add to class

}

/* this file is also used in signle-kamari as include fo Read more block */
else if(isset($read_more_block) && $read_more_block) {
    $relevanssi_posts = true;
    $parent_post_id = get_the_ID();
    $block_selected_post_type = 'post';
    $block_link = false;
    $block_selected_post_taxonomy = 'article_type'; //DEBUG

    $block_title = 'Lue myös';
    $block_background_color = ''; #EFF4FA - no bg in article

    //$block_selected_post_taxonomy_term = get_the_category();
    //print_r($block_selected_post_taxonomy_term);
    /*
    [term_id] => 2736
    [name] => Uutiset Alateema1
    [slug] => uutiset-alateema1
    [term_group] => 0
    [term_taxonomy_id] => 2736
    [taxonomy] => category
    [description] =>
    [parent] => 2734
    [count] => 2
    [filter] => raw
    [term_order] => 3
    [cat_ID] => 2736
    [category_count] => 2
    [category_description] =>
    [cat_name] => Uutiset Alateema1
    [category_nicename] => uutiset-alateema1
    [category_parent] => 2734
    */
}
else {
    $block_selected_post_type = get_field('selected_post_type');
    $block_title = get_field('title');
    $block_link = get_field('link');
    $block_background_color = get_field('background_color'); //if set, use wide
    $block_selected_post_taxonomy = get_field('selected_post_taxonomy'); //artikkelityyppi ! ei siis teema
    $title_link_possible = true; //when this is as read also -block, cannot link the title
}

if($relevanssi_posts) {
    if(function_exists('relevanssi_get_related_post_objects')) {
        $related_posts_obj = relevanssi_get_related_post_objects($parent_post_id); /* ID - returns post objects */
        echo "\n";
        echo '<!-- relevanssi parent post id: '. $parent_post_id .'-->';
        echo "\n";
        foreach ($related_posts_obj as $rp) {
            if($parent_post_id !== $rp->ID) {
              $related_posts_ids[] = $rp->ID;
            }
        }

        if($post->post_type == 'post') {
            $posts_per_page = 3;
        }
        else {
            $posts_per_page = 3; //not affecting to post amount, need more because relevanssi can return wrong post types also
        }

        $rp_args = array(
            'post_type' => array( 'post', 'release' ),
            'posts_per_page' => $posts_per_page,
            'orderby' => 'date_published',
            'post__in' => $related_posts_ids,
            //'category__not_in' =>
        );

        //wp_reset_query();
        //wp_reset_postdata();
        $relevanssi_found = true;




    }
    //function_exists
    else {
        echo '<br>relevanssi is disabled';
    }
}



if(empty($block_selected_post_type)) {
    $block_selected_post_type = 'post'; //default
}


if($block_selected_post_taxonomy) {
   $tax_query = array(
        array(
          'taxonomy' => 'article_type',
          'field' => 'term_id',
          'terms' => $block_selected_post_taxonomy
        )
    );
}
else {
    $tax_query=array(); //allow releases without taxonomy
}



//if( !empty($block['className']) ) {
//    $className .= ' ' . $block['className'];
//}
/*
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}
*/

if( !empty($block_background_color) ) {
    $className .= ' alignfull'; //full wide column if bg chosen
    $block_background_html = 'style="background-color: ' . $block_background_color.';"';
}
else {
    $className .=  ' alignwide'; //full wide column if bg chosen
}



?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>" <?php echo $block_background_html;?> >

  <div class="block-news-lift-3__wrap">

    <?php
    if(!empty($block_title)) {
      echo '<h2 class="block-news-lift-3__title">';
      if(isset($title_link_possible) && $title_link_possible) {
        if($block_selected_post_taxonomy > 0 && $block_selected_post_type != 'release') {
            $selected_term = get_term($block_selected_post_taxonomy, 'article_type' );
            $selected_slug = $selected_term->slug .'/';
            $block_title_url = '/artikkelityyppi/' . $selected_slug;
        }
        else {
            if($block_selected_post_type == 'release') {
                $block_title_url = '/artikkelityyppi/tiedotteet/';
            }
            else {
                $block_title_url = '/artikkelityyppi/uutiset/';
            }
        }


        echo '<a class="title" href="'.$block_title_url.'">';
        echo $block_title;
        echo '</a>';
      }
      else {
        echo "<span class='title' style='font-size:28px;'>$block_title</span>";
      }
      echo '</h2>';
    }
    else echo '<div class="block-news-lift-3__title-margin"></div>';


    $args = array(
      'posts_per_page'   => 3,
      'post_status'      => 'publish',
      'post_type'       => $block_selected_post_type,
      'orderby'         => 'date',
      'order'           => 'desc',
      'tax_query'       => $tax_query,
    );

    if(isset($relevanssi_found)) {
        //relevanssi has posts already
        $lift3_query = new WP_Query( $rp_args );
        echo "\n";
        echo '<!-- relevanssi posts -->';
        echo "\n";
    }
    else {
        echo "\n";
        echo '<!-- normal posts by args - in article  -->';
        echo "\n";
        $lift3_query = new WP_Query( $args );
    }

    ?>


    <div class="block-news-lift-3__content_wrap">
      <?php
      $lift_n = 0;
      if ( $lift3_query->have_posts() ) {
            wp_reset_query();
            wp_reset_postdata();
            while ( $lift3_query->have_posts() && $lift_n < 3) {
              //$this_ID = get_the_ID();
              global $post;
              $lift3_query->the_post();

              //scope error workaround:
              if(isset($parent_post_id) && $parent_post_id > 1 && $post->ID == $parent_post_id) {
                continue;
              }

              $lift_n++;

              //setup_postdata( $post );
              get_template_part('partials/content/teaser-3-cols', 'teaser-3-cols');
            }
            wp_reset_postdata();
        }
        else {
            ?>
            <p><?php _e( 'Sorry, no posts matched your criteria.' ); ?></p>
            <?php
        }
        ?>
    </div>

  </div>

  <?php
  if(!empty($block_link)) {
    echo '<p class="block-news-lift-3-bottom"><a href="'.$block_link['url'].'" '.($block_link['target'] ? 'target="_blank"' : '' ).'>'. $block_link['title'] .'</a></p>';
  }
  ?>


</div>
