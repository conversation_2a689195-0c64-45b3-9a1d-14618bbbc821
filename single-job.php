<?php

/**
 * The template for displaying all single JOB posts.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package kauppakamari
 */

// Acf fields
if (function_exists('get_field')) {
  $jobStarts = get_field('job_starts');
  $jobDuration = get_field('job_duration');
  $jobTime = get_field('job_time');
  $jobApply = get_field('job_apply');
  $jobPostalInfo = get_field('job_post_info');
  $jobContact = get_field('job_contact');

  $jobBody = get_field('job_body');
}

get_header(); ?>

<?php get_template_part('partials/content/hero-page'); ?>

<div id="primary" class="primary primary--single">

  <main id="main" class="main">

    <?php while (have_posts()) : the_post(); ?>

      <article id="post-<?php the_ID(); ?>" <?php post_class('entry entry--post'); ?>>

        <header class="entry__header">
          <h1><?php the_title(); ?></h1>
        </header>

        <div class="entry__content wysiwyg">
          <?php if (!empty($jobBody)) echo $jobBody; ?> <!-- post main body -->

          <!-- box with job info after contents -->
          <div class="job__content-acf"><?php if (!empty($jobStarts)) : ?>
              <span>
                <strong>Työ alkaa: </strong><?php echo $jobStarts; ?>
              </span>
            <?php endif; ?>
            <?php if (!empty($jobDuration)) : ?>
              <span>
                <strong>Työn kesto: </strong><?php echo $jobDuration; ?>
              </span>
            <?php endif; ?>
            <?php if (!empty($jobTime)) : ?>
              <span>
                <strong>Työaika: </strong><?php echo $jobTime; ?>
              </span>
            <?php endif; ?>
            <?php if (!empty($jobApply)) : ?>
              <span>
                <strong>Hakemus: </strong><?php echo $jobApply; ?>
              </span>
            <?php endif; ?>
            <?php if (!empty($jobPostalInfo)) : ?>
              <span>
                <strong>Postitse: </strong><?php echo $jobPostalInfo; ?>
              </span>
            <?php endif; ?>
            <?php if (!empty($jobContact)) : ?>
              <span>
                <strong>Yhteystiedot: </strong><?php echo $jobContact; ?>
              </span>
            <?php endif; ?>
          </div>

          <div><a href="<?php echo $post->origin_link; ?>" class="apply-btn">Hae työpaikkaa</a></div>

        </div>

        <footer class="entry__footer">
          <?php kauppakamari_social_share_buttons_simple(); ?>
        </footer>

      </article>

    <?php endwhile; ?>

  </main><!-- #main -->

  <?php get_sidebar('jobs'); ?>

</div><!-- #primary -->

<div id="secondary" class="secondary secondary-items wysiwyg">

  <?php the_content(); ?> <!-- actual post_content after main -->

</div> <!-- #secondary -->

<?php
get_footer();
