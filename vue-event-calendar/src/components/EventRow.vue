<template>
  <a :href="externalLink">
    <div class="event-item">
      <div class="item-timebox">
        <div class="item-time-days">
          {{ startDate.day }} {{ startDate.dateStr }}
        </div>
        <div
          v-if="startDate.dateStr !== endDate.dateStr"
          class="item-time-dates"
        >
          {{ endDate.day }} {{ endDate.dateStr }}
        </div>
      </div>
      <div class="item-content-wrapper">
        <div class="item-top-row">
          <span class="item-method">
            {{ event.method }}
          </span>
          <span
            v-for="category in event.categories"
            class="item-categories-pill"
          >
            {{ category }}
          </span>
        </div>
        <div class="item-title" v-html="event.title.rendered">
          <!-- <span class="icon-external-link"></span> -->
        </div>
        <!-- {{event.month}} -->
        <!-- <a :href="event.link">Avaa</a> -->
      </div>
    </div>
  </a>
</template>
<script>
import "../assets/custom-icons.css";
import { dayIndex } from "../helpers";
export default {
  props: ["event", "isWorkshop"],
  computed: {
    startDate() {
      if (!this.event.start_date) return "";
      return this.processDate(this.event.start_date);
    },
    endDate() {
      if (!this.event.end_date) return "";
      return this.processDate(this.event.end_date);
    },
    externalLink() {
      const eventTypeInUrl = this.isWorkshop
        ? "koulutukset"
        : "jasentapahtumat";
      const baseUrl =
        "https://www.kauppakamarikauppa.fi/collections/" +
        eventTypeInUrl +
        "/products/";
      return baseUrl + this.event.handle;
    },
  },
  methods: {
    processDate(rawDate) {
      const date = new Date(rawDate);
      const day = dayIndex[date.getDay()].substr(0, 2);
      const dateStr =
        date.getDate() + "." + (date.getMonth() + 1) + "." + date.getFullYear(); //.toString().slice(-2)
      return { day, dateStr };
    },
  },
};
</script>
<style lang="scss">
$darkBlue: #002663;
$liteBlue: #d8e3f2;
$separatorblue: #e4eff8;
$hotpink: #f9a4c4;
$coral: #feada0;
$grey: #ebebeb;
$tablet: 768px;
.event-item {
  color: #222;
  padding: 0.5rem 0;
  border-bottom: 2px solid $separatorblue;
  .item-timebox {
    background: $coral;
    padding: 20px;
    color: $darkBlue;
    // font-size: 18px;
    > div {
      font-weight: bold;
    }
  }
  .item-top-row {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem;
  }

  .item-content-wrapper {
    padding: 10px 0;
  }
  .item-method {
  }
  .item-categories-pill {
    display: inline-block;
    background: $liteBlue;
    color: $darkBlue;
    font-size: 12px;
    line-height: 1.5;
    font-weight: 600;
    padding: 0.26rem 1rem;
    border-radius: 14px;
  }
  .item-title {
    margin: 10px 0;
    font-size: 18px;
    color: $darkBlue;
    font-weight: bold;
    &:hover {
      text-decoration: underline;
    }
    a:focus > & {
      border: 1px dotted $darkBlue;
      width: fit-content;
    }
    &:after {
      // position: absolute;
      background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23002663' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
      background-position: center;
      background-size: 84%;
      width: 1.1rem;
      height: 1rem;
      display: inline-block;
      content: " ";
      margin-left: 8px;
      background-repeat: no-repeat;
    }
  }
  @media (min-width: $tablet) {
    display: grid;
    gap: 1rem;
    grid-template-columns: 148px 1fr;
    .item-title {
      margin: 10px 0 30px;
      font-size: 21px;
    }
  }
}
.icon-external-link {
  font-size: 14px;
  margin-left: 1rem;
}
</style>
