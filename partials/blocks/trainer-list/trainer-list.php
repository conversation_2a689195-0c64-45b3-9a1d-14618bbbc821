<?php

/**
 * Trainer-list
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-trainer-list' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-trainer-list';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$content = get_field('trainers-list');

?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">

  <?php
  $i = 1;
  foreach ($content as $post) {
    setup_postdata($post); ?>
    <div class="loop_item item_<?php echo $i; ?>" style="background-image:url(<?php echo get_the_post_thumbnail_url($post->ID, 'large') ?>);" >
      <div class="loop_item__content">
        <?php
          if(get_field('title-company', $post->ID)) {
            echo '<span class="name">' . get_the_title($post->ID) . " &bull; " . get_field('title-company', $post->ID) . '</span>';
          } else {
            echo '<span class="name">' . get_the_title($post->ID) . '</span>';
          }
          if($terms = get_the_terms( $post->ID, 'training_type' )) :
            echo '<span class="term">' . $terms[0]->name . '</span>';
          endif;
          echo '<span class="excerpt">' . get_field('description', $post->ID) . '</span>';
        ?>
      </div>
      <div class="dimming dimming__bottom"></div>
      <a href="<?php echo get_permalink($post->ID); ?>"></a>
    </div>
    <?php $i++;
    if($i === 10) {
      break;
    }
  } ?>

</div>
