<?php
/**
 * The template for displaying all single posts.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package kauppakamari
 */

get_header(); ?>

  <?php get_template_part('partials/content/hero'); ?>

  <div id="primary" class="primary primary--single">

    <main id="main" class="main">

      <?php while (have_posts()) : the_post(); ?>

        <article id="post-<?php the_ID(); ?>" <?php post_class('entry entry--post'); ?>>

          <header class="entry__header">
            <h1><?php the_title();?></h1>
          </header>

          <div class="entry__content wysiwyg">
            <?php the_content(); ?>
          </div>

          <footer class="entry__footer">
            <?php kauppakamari_entry_footer(); ?>
            <?php kauppakamari_social_share_buttons(); ?>
          </footer>

        </article>

      <?php endwhile; ?>

    </main><!-- #main -->

  </div><!-- #primary -->

<?php
get_footer();
