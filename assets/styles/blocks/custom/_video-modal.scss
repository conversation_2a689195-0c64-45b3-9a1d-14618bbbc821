.block-video-modal {
  min-height: 100vh;
  background-size: cover;
  position: relative;
  &__wrap {
    @include wrap();
    display: flex;
    align-items: center;
    height: calc(100vh - 112px);
  }
  &__content {
    width: 50%;
    color: white;
    padding-right: 20%;
    z-index: 10;
    .title {
      font-size: 1rem;
      text-transform: uppercase;
    }
    h5 {
      font-size: 2.53rem;
      margin-top: 1rem;
      line-height: 2.75rem;
      font-weight: 700;
      color: white;
    }
  }
  .close-video-modal {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    svg {
      fill: white;
      width: 2rem;
      height: 2rem;
    }
  }
  .js-trigger-video-modal {
    z-index: 10;

  }
  .video-banner-icon-play {
    width: 125px;
    transition:
		all 0.2s ease-out 0.05s;
  }
  .video-banner-icon-play:hover {
    transform: scale(1.2);
  }
  &__video {
    width: 50%;
    display: flex;
    justify-content: center;
  }
  .video-modal,
  .video-modal .overlay {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 3000;
  }
  .video-modal {
    overflow: hidden;
    position: fixed;
    opacity: 0.0;

    -webkit-transform: translate(500%,0%);
    transform: translate(500%,0%);

    -webkit-transition: -webkit-transform 0s linear 0s;
    transition: transform 0s linear 0s;


    /* using flexbox for vertical centering */

    /* Flexbox display */
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;

    /* Vertical alignment */
    -webkit-box-align: center;
    -moz-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;

    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
  .video-modal .overlay {
    z-index: 0;
    background: hsla(217, 100%, 19%, 0.82);
    opacity: 0.0;

    -webkit-transition: opacity 0.2s ease-out 0.05s;
    transition: opacity 0.2s ease-out 0.05s;
  }
  .video-modal-content {
    position: relative;
    top: auto;
    right: auto;
    bottom: auto;
    left: auto;
    z-index: 1;

    margin: 0 auto;

    overflow-y: visible;

    background: #000;

    width: calc(85%);
    height: 0;
    padding-top: calc((85% - 5em) * .5625);
    @include breakpoint($sub-m) {
      height: 300px;
    }
  }
  iframe#youtube {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;

    background: #000;
    box-shadow: 0px 2px 16px rgba(0,0,0,0.5);
  }

  .fluid-width-video-wrapper {
    position: initial;
  }

  .link {
    @include button;
    margin-top: 1.5rem;
    display: block;
    width: auto;
    min-width: 150px;
    text-align: center;
    border: 1px solid $brand;
    color: white;
    background: $brand;
    width: fit-content;
    &:hover {
      color: white;
    }
    &.external {
      background-color: #002663;
      border: 1px solid #002663;
      color: white;
      padding: 0.75rem 4.5rem 0.75rem 3rem;
      &:after {
        position: absolute;
        background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23ffffff' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-position: center;
        background-size: 84%;
        width: 1.1rem;
        height: 1rem;
        display: inline-block;
        content: " ";
        margin-left: 8px;
        color: transparent;
        background-repeat: no-repeat;
      }
      &:hover {
        background-color: white;
        color: #002663;
        &:after {
          background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='16px' height='16px' viewBox='0 0 16 16' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Elink out / black copy 13%3C/title%3E%3Cg id='Symbols' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='link-out-/-black-copy-13' fill='%23002663' fill-rule='nonzero'%3E%3Cpath d='M12.4444444,14.4444444 L1.55555556,14.4444444 L1.55555556,3.55555556 L6.22222222,3.55555556 L6.22222222,2 L1.55555556,2 C0.692222222,2 0,2.7 0,3.55555556 L0,14.4444444 C0,15.3 0.692222222,16 1.55555556,16 L12.4444444,16 C13.3,16 14,15.3 14,14.4444444 L14,9.77777778 L12.4444444,9.77777778 L12.4444444,14.4444444 Z M10.5555556,0 L10.5555556,1.55555556 L13.3477778,1.55555556 L5.70222222,9.20111111 L6.79888889,10.2977778 L14.4444444,2.65222222 L14.4444444,5.44444444 L16,5.44444444 L16,0 L10.5555556,0 Z' id='Shape'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
      }
    }
    &:hover {
      background-color: $white;
      border-color: $brand;
      color: $brand;
    }
  }

}

/* show the modal:
    add class to the body to reveal */
    .show-video-modal .video-modal {
    opacity: 1.0;

    transform: translate(0%,0%);
    -webkit-transform: translate(0%,0%);
  }
  .show-video-modal .video-modal .overlay {
    opacity: 1.0;
  }
  .show-video-modal .video-modal-content {
    transform: translate(0%,0%);
    -webkit-transform: translate(0%,0%);
  }
