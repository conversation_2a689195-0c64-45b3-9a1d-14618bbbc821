.wp-block-cover-image,
.wp-block-cover {
	position: relative;
	background-color: $black;
	background-size: cover;
	background-position: center center;
	min-height: 430px;
	height: 100%;
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	overflow: hidden;

	&.has-parallax {
		background-attachment: fixed;

		// Mobile Safari does not support fixed background attachment properly.
		// See also https://stackoverflow.com/questions/24154666/background-size-cover-not-working-on-ios
		// Chrome on Android does not appear to support the attachment at all: https://issuetracker.google.com/issues/36908439
		@supports (-webkit-overflow-scrolling: touch) {
			background-attachment: scroll;
		}

		// Remove the appearance of scrolling based on OS-level animation preferences.
		@media (prefers-reduced-motion: reduce) {
			background-attachment: scroll;
		}
	}

	&.has-background-dim::before {
		content: "";
		background-color: inherit;
	}

	&.has-background-dim:not(.has-background-gradient)::before,
	.wp-block-cover__gradient-background {
		position: absolute;
		top: 0;
		left: 0;
		bottom: 0;
		right: 0;
		z-index: z-index(".wp-block-cover.has-background-dim::before");
	}

	&.has-background-dim:not(.has-background-gradient)::before,
	& .wp-block-cover__gradient-background {
		opacity: 0.5;
	}


	@for $i from 1 through 10 {
		&.has-background-dim.has-background-dim-#{ $i * 10 } {
			&:not(.has-background-gradient)::before {
				opacity: $i * 0.1;
			}
			.wp-block-cover__gradient-background {
				opacity: $i * 0.1;
			}
		}
	}

	// Apply max-width to floated items that have no intrinsic width
	&.alignleft,
	&.alignright {
		max-width: $content-default-width-half;
		width: 100%;
	}

	// Using flexbox without an assigned height property breaks vertical center alignment in IE11.
	// Appending an empty ::after element tricks IE11 into giving the cover image an implicit height, which sidesteps this issue.
	&::after {
		display: block;
		content: "";
		font-size: 0;
		min-height: inherit;

		// IE doesn't support flex so omit that.
		@supports (position: sticky) {
			content: none;
		}
	}

	// Aligned cover blocks should not use our global alignment rules
	&.aligncenter,
	&.alignleft,
	&.alignright {
		display: flex;
	}

	.wp-block-cover__inner-container {
		width: calc(100% - 70px);
    //z-index: z-index(".wp-block-cover__inner-container");
    z-index: 1;
		color: $light-gray-100;
	}

	p,
	h1,
	h2,
	h3,
	h4,
	h5,
	h6,
	.wp-block-subhead {
		&:not(.has-text-color) {
			color: inherit;
		}
	}
}

.wp-block-cover__video-background {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translateX(-50%) translateY(-50%);
	width: 100%;
	height: 100%;
	z-index: z-index(".wp-block-cover__video-background");
	object-fit: cover;
}

// Styles bellow only exist to support older versions of the block.
// Versions that not had inner blocks and used an h2 heading had a section (and not a div) with a class wp-block-cover-image (and not a wp-block-cover).
// We are using the previous referred differences to target old versions.

section.wp-block-cover-image h2,
.wp-block-cover-image-text,
.wp-block-cover-text {
	color: $white;
	a,
	a:hover,
	a:focus,
	a:active {
		color: $white;
	}
}

.wp-block-cover-image
.wp-block-cover {
	&.has-left-content {
		justify-content: flex-start;
	}

	&.has-right-content {
		justify-content: flex-end;
	}
}

section.wp-block-cover-image.has-left-content > h2,
.wp-block-cover-image.has-left-content .wp-block-cover-image-text,
.wp-block-cover.has-left-content .wp-block-cover-text {
	margin-left: 0;
	text-align: left;
}

section.wp-block-cover-image.has-right-content > h2,
.wp-block-cover-image.has-right-content .wp-block-cover-image-text,
.wp-block-cover.has-right-content .wp-block-cover-text {
	margin-right: 0;
	text-align: right;
}

section.wp-block-cover-image > h2,
.wp-block-cover-image .wp-block-cover-image-text,
.wp-block-cover .wp-block-cover-text {
	font-size: 2em;
	line-height: 1.25;
	z-index: 1;
	margin-bottom: 0;
	max-width: $content-width;
	padding: $block-padding;
	text-align: center;
}
