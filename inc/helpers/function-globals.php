<?php

/**
 * Prerendered vardump for debugging
 */
if (!function_exists("vd")) {
  function vd($data) {
    echo "<pre>";
    print_r($data); // or var_dump($data);
    echo "</pre>";
  }
}

/**
 * Get sites info
 */
function kaup<PERSON>kamari_get_sites() {
  if(!empty(get_field('global_fi', 'options'))) {
    return get_field('global_fi', 'options')['sites'];
  }
}

/* function add_cors_http_header(){
  header("Access-Control-Allow-Origin: " . get_site_url());
}
add_action('init','add_cors_http_header');
 */
