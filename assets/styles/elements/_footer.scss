/* ==========================================================================
 # Footer
========================================================================== */

// Absolute bottom element
.site-footer {
  background: $primary;
  padding: 1rem;
  color: white;
  font-weight: $ws-regular;

  &__container {
    @include wrap;
    margin-left: 2rem;
    display: flex;
    justify-content: space-between;
  }

  &__links {
    .footer-navigation__items {
      @include breakpoint($sub-m) {
        flex-direction: column;
        li {
          padding: .75rem 0;
        }
      }
      list-style: none;
      display: flex;
      justify-content: flex-start;
      padding-left: 0;
      margin-bottom: 0;

      &.external {
        &::after {
          content: "\f35d";
          display: inline-block;
          margin-left: 5px;
          font-family: 'Font Awesome 6 Sharp';
          font-size: 12px;
          text-decoration: none;
        }
      }
      li {
        margin-right: 1.5rem;
        a {
          color: white;
          text-decoration: none;
          &:hover {
            @include elementhover;
          }
        }
      }
    }
  }
  // scroll up button
  .top-link {
    transition: all 0.25s ease-in;
    position: fixed;
    bottom: 70px;
    right: 80px;
    z-index: 2;
    border: none;
    outline: none;
    background-color: white;
    color: #002663;
    cursor: pointer;
    padding: 12px;
    border-radius: 50%;
    border: 1px solid $light-blue-50;
    &.btn {
      display: none;
      span {
        display: none;
      }
    }
    &:hover {
      transform: translateY(-6px);
    }
    &.hide {
      display: none;
      visibility: hidden;
      opacity: 0;
    }
    &.show {
      visibility: visible;
      opacity: 1;
    }
  }
  @include breakpoint($sub-m) {
    .top-link {
      display: none;
    }
  }
}



// Information -footer
.footer-content {

  //kaikki footer linkit
  a {
    font-size: 16px;
    &:hover {
      @include elementhover;
      // text-decoration: underline;
    }
  }
  .hide-on-desktop {
    display: none;
  }
  .branding {
    // margin: 2rem 0 4rem 0;
    padding-top: 2.3rem;
    padding-bottom: 3.75rem;
    img {
      height: 3rem;
      width: auto;
      margin: 0;
      @media screen and (min-width: $menu-visible) {
        height: 44px;
      }
    }
  }
  // Footer ratas -svg
  .svg-wrapper {
    margin-bottom: 2.5rem;
  }

  background: #1d397f;
  padding: 3rem;

  &__column-primary {
    justify-content: center !important;
    margin-bottom: 2rem;
  }
  &__column-second {
    justify-content: space-evenly !important;
    >div {
      margin: 1rem 0;
    }
  }
  &__link, &__site {
    color: $white;
    text-decoration: none;
    width: 100%;
    display: block;
    position: relative;
    font-weight: $ws-medium;
    margin-bottom: 0.7rem;
    padding-bottom: 0.35rem;
    &:hover {
      @include elementhover;
    }
  }
  &__site::after{
    content: "\f35d";
    display: inline-block;
    font-family: 'Font Awesome 6 Sharp';
    font-size: inherit;
    text-decoration: none;
    position: absolute;
    margin-left: 6px;
  }

  &__column {
    color: $white;
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    justify-content: space-between;
    p {
      font-size: 16px !important;
      font-weight: $ws-medium;
    }
  }

  .contacts-link {
    color: $white;
    text-decoration: none;
    font-weight: $ws-semibold;
    font-size: 18px;
    &:hover {
      @include elementhover;
    }
  }

  @include breakpoint(1370px) {
    display: flex;
    padding: 1rem 0;
    justify-content: space-between;
    div:first-of-type() {
      padding-right: 10%;
    }

    &__column {
      padding-top: 40px;
      padding-bottom: 20px;
      display: flex;
      flex-wrap: wrap;
      width: 33%;
    }

    /* new links + social columns */
    &__column-second {
      width: 67%;
      padding: 40px 0;
      padding-right: 80px;
      justify-content: space-between;
      &>div {
        width: max-content;
        margin: 0;
      }
      &__sites {
      //  margin-right: 2rem !important;
      }
    }
  }
  .footer-column-header {
    font-weight: $ws-semibold;
    text-transform: uppercase;
    font-size: 18px;
    color: $coral;
    padding-bottom: 0.2rem;
  }
}


@media (max-width: 760px) {
  .footer-content {
    &__column-primary {
      justify-content: space-between !important;
    }
    padding: 2rem;
    .hide-on-mobile {
      display: none;
    }
    .hide-on-desktop {
      display: block !important;
      margin-top: 2rem;
      .mobile-footer-svg {
        height: 64px;
        &:not(:last-of-type) {
          margin-right: $gap;
        }
      }
    }

    &__column-second {
      &__links, &__sites, &__social {
        width: 100%;
      }
      &__social, &__sites {
        margin-top: 40px;
      }
      &__links {
        padding-right: 0px !important
      }
    }
  }
}

@media (min-width: 720px) {
  .footer-content .footer-content__column-second__links a:last-child {
    border-bottom: none;
  }
}
