/* ==========================================================================
 # Main (front-end)
========================================================================== */

/* Font Awesome
----------------------------------------------- */
@import "fontawesome";
@import "fontawesome-sharp-solid";

/* Vendors
----------------------------------------------- */

@import "../../node_modules/breakpoint-sass/stylesheets/_breakpoint.scss";

/* Variables
----------------------------------------------- */

@import "base/variables/width";
@import "base/variables/spacing";
@import "base/variables/breakpoints";
@import "base/variables/colors";
@import "base/variables/fonts";

/* Mixins
----------------------------------------------- */

@import "base/mixins/buttons";
@import "base/mixins/resets";
@import "base/mixins/tools";
@import "base/mixins/wrap";
@import "base/mixins/inputmixins";

/* Generic
----------------------------------------------- */

@import "base/generic/normalize";
@import "base/generic/all";
@import "base/generic/typography";
@import "base/generic/animation";

/* Blocks
----------------------------------------------- */

@import "blocks/core/core-button";
@import "blocks/core/core-columns";
@import "blocks/core/core-embed";
@import "blocks/core/core-file";
@import "blocks/core/core-freeform";
@import "blocks/core/core-gallery";
@import "blocks/core/core-heading";
@import "blocks/core/core-image";
@import "blocks/core/core-list";
@import "blocks/core/core-media-text";
@import "blocks/core/core-paragraph";
@import "blocks/core/core-quote";
@import "blocks/core/core-separator";
@import "blocks/core/core-table";
@import "blocks/core/core-cover";

@import "blocks/settings/blocks-alignment";
@import "blocks/settings/blocks-colors";

@import "blocks/custom/icon-text";
@import "blocks/custom/trainer";
@import "blocks/custom/tabs";
@import "blocks/custom/text-image";
@import "blocks/custom/teaser";
@import "blocks/custom/clubs";
@import "blocks/custom/product-teasers";
@import "blocks/custom/gravityforms_form";
@import "blocks/custom/trainer-list";
@import "blocks/custom/people";
@import "blocks/custom/people-select";
@import "blocks/custom/color-teasers";
@import "blocks/custom/news-list";
@import "blocks/custom/post-columns";
@import "blocks/custom/video-modal";
@import "blocks/custom/eventsexternal";
@import "blocks/custom/column-features";
@import "blocks/custom/memberstories";
@import "blocks/custom/article-teaser";
@import "blocks/custom/content-background";
@import "blocks/custom/price-text";
@import "blocks/custom/presentations";

/* 2022 blocks for news */
@import "blocks/custom/news-lift-1-2";
@import "blocks/custom/news-lift-3";

/* 2023 new blocks */
@import "blocks/custom/highlight-1";
@import "blocks/custom/small-lift-2";
@import "blocks/custom/membership-charts";
@import "blocks/custom/events-frontpage";
@import "blocks/custom/slider";
@import "blocks/custom/accordion";
@import "blocks/custom/animated-numbers";
@import "blocks/custom/job-listing";

/* Elements
----------------------------------------------- */

/* 2023 elements */
@import "elements/introduction-frontpage";

@import "elements/buttons";
@import "elements/entry";
@import "elements/footer";
@import "elements/forms";
@import "elements/header";
@import "elements/hero";
@import "elements/media";
@import "elements/menu-primary";
@import "elements/menu-social";
@import "elements/menu-toggle";
@import "elements/numeric-posts-nav";
@import "elements/social-share";
@import "elements/teaser";
@import "elements/featured-loop";
@import "elements/dimming";
@import "elements/products";
@import "elements/slick";
@import "elements/slick-theme";
@import "elements/newsletter";
@import "elements/national";
@import "elements/breadcrumb";
@import "elements/article-filter";
@import "elements/people-search-results";
@import "elements/teaser-search-result";

/* 2025 elements */
@import "elements/sidebar-news";
@import "elements/sk-linkedin";

/* Layout
----------------------------------------------- */

@import "views/layout/layout";

/* Templates
----------------------------------------------- */

@import "views/templates/404";
@import "views/templates/front-page";
@import "views/templates/index";
@import "views/templates/page";
@import "views/templates/search";
@import "views/templates/search-top";
@import "views/templates/search-new";
@import "views/templates/single";
@import "views/templates/nyt";
@import "views/templates/single-content-page";
@import "views/templates/single-job-page";
@import "views/templates/contact";
@import "views/templates/verkostoidu";

/* Print
----------------------------------------------- */

@import "base/generic/print";

/* general fixes and small things not suitable for other files */
@import "base/generic/fixes";
