<?php
/**
 * Function for post meta
 *
 * @package kauppakamari
 */

/**
 * get article type link
 *
 */

function get_article_type_link($category) {
  $child_category_slug = $category[0]->slug;

  $category_term = get_term_by('slug', $child_category_slug, 'category');
  $parent_category_term = get_term_by('id', $category_term->parent, 'category');
  $parent_category_term_slug = $parent_category_term->slug;
  $article_type_term = get_term_by('slug', $parent_category_term_slug, 'article_type');

  if(is_object($article_type_term)) {
    $name = $article_type_term->name;
    $url = get_term_link($article_type_term->term_id);
    return sprintf('<a href="%s">%s</a>', $url, $name);
  }
  else {
    return '';
  }
}
/**
 * get article type link
 *
 */

function get_article_type_link_from_term($post_id) {
  $term = get_the_terms($post_id, 'article_type');
  if($term) {
    $term = $term[0];
    $name = $term->name;
    $url = get_term_link($term->term_id);
    return sprintf('<a href="%s">%s</a>', $url, $name);
  }
  else {
    return '';
  }
}

/**
 * get article_type term, error safe
 *
 */
function get_article_type_term_by_post_id($post_id) {
  $term = get_the_terms($post_id, 'article_type');
  //error_log(print_r($terms, true));
  if($term && $term[0]) {
    return $term[0];
  }
  return null;
}

/**
 * Posted on
 */
function kauppakamari_get_post_meta() { ?>
  <div class="entry-meta">
    <?php
    /* logic for separator */
    $posted_on_date = kauppakamari_get_posted_on();
    $separator='';
    if(!strstr($posted_on_date, 'hide')) {
      $separator = '<span class="entry-meta__separator">I</span>';
    }
    ?>
    <span class="entry-meta__date"><?php echo $posted_on_date; ?></span>
    <?php echo $separator; ?>
    <span class="entry-meta__term">
      <?php
      $category = get_the_category();
      if ( $category[0] ) {
          //echo $category[0]->cat_name;
          //TEST 2022:
          echo '<a href="' . esc_url( get_category_link( $category[0]->term_id ) ) . '">' . esc_html( $category[0]->name ) . '</a>';
      }
      ?>
    </span>
  </div>
<?php
}

function kauppakamari_get_post_meta_new($limit_to_one=true, $show_type=false) { ?>
  <div class="entry-meta entry-meta-new">
    <?php
    $posted_on_date = kauppakamari_get_posted_on();
    ?>
    <span class="entry-meta__date entry-meta__date-new"><?php echo $posted_on_date; ?></span>
    <?php
    if($show_type) {
      echo '<span class="entry-meta__article_type">';
      //echo get_article_type_link(get_the_category());
      echo get_article_type_link_from_term(get_the_ID());
      echo '</span>';
    }

    ?>
    <span class="entry-meta__term entry-meta__term-new">
      <?php
      $category = get_the_category();
      if(sizeof($category) > 1) {
        foreach($category as $i => $term) {
            if( get_post_meta(get_the_ID(), '_yoast_wpseo_primary_category',true) == $term->term_id ) {
              unset($category[$i]);
              array_unshift($category, $term); //to first
              break;
           }
        }
      }
      
      if ( $category[0] ) {
          //echo $category[0]->cat_name;
          echo '<a href="' . esc_url( get_category_link( $category[0]->term_id ) ) . '">' . esc_html( $category[0]->name ) . '</a>';
      }
      //2 & 3:
      if($limit_to_one !== true) {
          if ( isset($category[1]) && $category[1]) {
            echo '<a href="' . esc_url( get_category_link( $category[1]->term_id ) ) . '">' . esc_html( $category[1]->name ) . '</a>';
          }
          if ( isset($category[2]) && $category[2]) {
            echo '<a href="' . esc_url( get_category_link( $category[2]->term_id ) ) . '">' . esc_html( $category[2]->name ) . '</a>';
          }
      }
      ?>
    </span>
  </div>
<?php
}

function kauppakamari_get_post_meta_frontpage() { ?> <!-- Made for 'news-list' block on frontpage -->
  <div class="entry-meta">
    <?php
    $posted_on_date = kauppakamari_get_posted_on();
    ?>
    <span class="entry-meta__date"><?php echo $posted_on_date; ?></span>
    <?php
      echo '<span class="entry-meta__article_type">';
      //echo get_article_type_link(get_the_category());
      echo get_article_type_link_from_term(get_the_ID());
      echo '</span>';
    ?>
  </div>
<?php
}
