{"key": "group_5e13587a68f3f", "title": "Artikkelit", "fields": [{"key": "field_5e13589ca4867", "label": "Ingressi", "name": "ingress", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": "", "new_lines": ""}, {"key": "field_5e1358b4a4868", "label": "Kainalo", "name": "side_content", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "tabs": "all", "toolbar": "full", "media_upload": 1, "default_value": "", "delay": 0}, {"key": "field_5e1358c7a4869", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "writer", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5e2829d5ec5cf", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON> valinta", "name": "writer_object", "aria-label": "", "type": "post_object", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["people"], "taxonomy": "", "allow_null": 1, "multiple": 0, "return_format": "id", "translations": "copy_once", "ui": 1, "bidirectional_target": []}, {"key": "field_5e2aa6cb222e1", "label": "<PERSON><PERSON><PERSON>", "name": "photographer", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5e29a77d5ddea", "label": "<PERSON><PERSON> l<PERSON>", "name": "", "aria-label": "", "type": "message", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "<PERSON><PERSON> lisää ohjeet: \r\n<PERSON><PERSON> on valittu, mutta asiasanaa ei = näytetään artikkeli\r\nJo<PERSON> art<PERSON> on valittu, ja asiasana on valittu = näytetään artikkeli\r\nJos artikkelia ei ole valittu, mutta asiasana on = näytetään asiasanan mukaan\r\nJos arikkel<PERSON> ei ole valittu, eik<PERSON> asiasanaa ole valittu = näytetään tämän artikkelin teeman mukaan.", "new_lines": "wpautop", "esc_html": 0}, {"key": "field_5e29a2fed2e4a", "label": "<PERSON>e lisää (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> tai <PERSON>) (Tämä ei enää jatkossa käytössä, k<PERSON><PERSON><PERSON> blokkia _Lue lisää 3_)", "name": "featured_article", "aria-label": "", "type": "post_object", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["post", "release", "page"], "taxonomy": "", "allow_null": 1, "multiple": 0, "return_format": "object", "translations": "copy_once", "ui": 1, "bidirectional_target": []}, {"key": "field_5e28468bec5d0", "label": "<PERSON><PERSON> lisää (asiasana) (tämäkään ei enää jatkossa käytössä)", "name": "featured_tag", "aria-label": "", "type": "taxonomy", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "taxonomy": "post_tag", "field_type": "checkbox", "add_term": 1, "save_terms": 0, "load_terms": 0, "return_format": "id", "multiple": 0, "allow_null": 0, "bidirectional_target": []}, {"key": "field_611cff9aff6ed", "label": "Pi<PERSON>ta j<PERSON>kaisupäivä", "name": "hide_date", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 0, "ui": 0, "translations": "copy_once", "ui_on_text": "", "ui_off_text": ""}], "location": [[{"param": "post_type", "operator": "==", "value": "post"}], [{"param": "post_type", "operator": "==", "value": "release"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": ["discussion", "comments"], "active": true, "description": "", "show_in_rest": 1, "modified": 1746186852}