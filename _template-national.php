<?php
/**
 * Poistettu template name <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package kauppakamari
 */

get_header();

?>

  <?php //get_template_part('partials/content/hero'); ?>

  <div id="primary" class="primary primary--page">

    <main id="main" class="main">

      <?php while (have_posts()) : the_post(); ?>

        <article id="post-<?php the_ID(); ?>" <?php post_class('entry entry--page'); ?>>

          <div class="entry__content wysiwyg">
            <?php the_content(); ?>
          </div>

        </article>

        <div class="national_wrap">
          <h5><?php echo get_field('list_title'); ?></h5>
          <?php if($links = get_field('links')) {
            foreach($links as $link) {
              echo '<a class="national_link" href="' . $link['link']['url'] . '" target="_blank">' . $link['link']['title'] . kauppakamari_get_svg('caret-right') . '</a>';
            }
          } ?>
        </div>

      <?php endwhile; ?>

    </main><!-- #main -->

  </div><!-- #primary -->

<?php
get_footer();
