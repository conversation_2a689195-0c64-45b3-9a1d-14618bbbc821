{"version": 3, "file": "news-lift-wrapper/view.js", "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G", "sources": ["webpack://news-lift-wrapper/./src/news-lift-wrapper/view.js"], "sourcesContent": ["/**\n * Use this file for JavaScript code that you want to run in the front-end\n * on posts/pages that contain this block.\n *\n * When this file is defined as the value of the `viewScript` property\n * in `block.json` it will be enqueued on the front end of the site.\n *\n * Example:\n *\n * ```js\n * {\n *   \"viewScript\": \"file:./view.js\"\n * }\n * ```\n *\n * If you're not making any changes to this file because your project doesn't need any\n * JavaScript running in the front-end, then you should delete this file and remove\n * the `viewScript` property from `block.json`.\n *\n * @see https://developer.wordpress.org/block-editor/reference-guides/block-api/block-metadata/#view-script\n */\n"], "names": [], "sourceRoot": ""}