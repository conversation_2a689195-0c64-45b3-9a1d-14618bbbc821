/* ==========================================================================
 # Block colors
========================================================================== */

// white
.has-white-background-color {
  background: $white;
}
.has-white-color {
  &, a {
    color: $white;
  }
}

// black
.has-black-background-color {
  background: $base-font-color;
}
.has-black-color {
  &, a {
    color: $base-font-color;
  }
}

// primary
.has-primary-background-color {
  background-color: $primary;
}
.has-primary-color {
  &, a {
    color: $primary;
  }
}

// primary
.has-main-background-color {
  background-color: #002663;
}
.has-main-color {
  &, a {
    color: #002663;
  }
}

// primary
.has-brand-background-color {
  background-color: #3A75C4;
}
.has-brand-color {
  &, a {
    color: #3A75C4;
  }
}

// primary
.has-blue-bg-background-color {
  background-color: #EFF4FA;
}
.has-blue-bg-gray-color {
  &, a {
    color: #EFF4FA;
  }
}

// primary
.has-very-light-gray-background-color {
  background-color: #F8F8F8;
}
.has-very-light-gray-color {
  &, a {
    color: #F8F8F8;
  }
}

// primary
.has-very-dark-gray-background-color {
  background-color: #808080;
}
.has-very-dark-gray-color {
  &, a {
    color: #808080;
  }
}


/* external text link icon padding */

.main {
  p:not(.item-title):not(.shopify-scroller-link-out) > a.external,
  strong > a.external {
    padding-right: 20px;
  }


  p.has-background.has-brand-background-color,
  p.has-background.has-main-background-color,
  p.has-background.has-very-dark-gray-background-color {
    a.external {
     @include external-link-white;
     padding-right: 24px;
   }
 }
}
