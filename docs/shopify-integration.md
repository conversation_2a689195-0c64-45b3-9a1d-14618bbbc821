# Shopify Integration Documentation

This document explains how the HSKK site integrates with Shopify to fetch and display product and event data.

## Overview

The system consists of two main components:

1. `chamber-shopify-updater` plugin - fetches raw data from Shopify API
2. `shopify-events.php` - processes the data and creates WordPress posts

## Data Flow

```
Shopify API → chamber-shopify-updater → JSON files → shopify-events.php → WordPress CPTs
```

## Key Components

### 1. Shopify Data Fetching (chamber-shopify-updater)

The plugin fetches collection data from Shopify and stores it as JSON files in the `uploads/shopify/` directory. It handles:

- Initial data fetching from collections
- Rate limiting (2 queries/second)
- Pagination for large collections
- Error handling and logging

### 2. Metadata Fetching

Metadata for products is fetched separately to get additional information like:

- Event dates
- Pricing
- Location
- Custom fields

This data is merged with the product data and saved to the same JSON files.

### 3. WordPress Integration (shopify-events.php)

The system creates WordPress custom post types from the JSON data:

- Events are created as 'event' post type
- Products are created with appropriate taxonomies
- Images are imported to the WordPress media library
- Custom fields are populated with Shopify metadata

## Execution Methods

The system can be triggered in several ways:

1. **WP-CLI Commands**:

   ```
   wp update_shopify_metadata [collection_id]
   wp update_shopify_events
   ```

2. **REST API Endpoints**:

   - `/wp-json/api/v1/cron/shopify_metadata/`
   - `/wp-json/api/v1/cron/shopify_eventdata/`
   - `/wp-json/api/v1/cron/shopify_eventdata_json/`

3. **Admin Interface**:
   - Settings page in WordPress admin at "Settings > Shopify Updater"

## Update Process

The recommended update sequence is:

1. Fetch product data from Shopify:

   ```
   wp update_shopify_metadata
   ```

2. Update WordPress posts with the fetched data:
   ```
   wp update_shopify_events
   ```
   It only processes workshops and events (collections 265924051093 and 265924247701)

This process typically takes about 15 minutes for a complete update of all collections.

## Troubleshooting

- If updates fail, check the WordPress error log
- Rate limiting issues may occur if too many requests are made to Shopify
- Large collections may timeout - consider updating one collection at a time

## Frontend Integration

Event data is displayed using custom templates and blocks. The data can be queried using standard WordPress functions or through the REST API.
