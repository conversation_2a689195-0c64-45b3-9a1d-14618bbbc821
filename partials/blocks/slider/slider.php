<?php
/**
 * Slider
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'slider-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-slider';
if( !empty($block['className']) ) {
  $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
  $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$slider_items = get_field("items") ? get_field("items") : [];

?>

<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">
  <div class="block-slider__container">
    <div class="block-slider__texts">
      <?php if(get_field('title')) { ?>
        <h2 class="block-slider__title"><?php echo get_field('title'); ?></h2>
      <?php } ?>
    </div>
      <div class="block-slider__items block-slides-container">
        <?php foreach($slider_items as $item) { ?>
          <div class="slide-base">
            <div class="slide-container">
              <div class="slide-image-wrapper" style="background-image:url(<?php if($item['image']) echo $item['image']['url']; ?>)">
                <?php // if($item['image']) echo '<img src="' . $item['image']['url'] . '" />'; ?>
              </div>
              <div class="slide-content">
                <div class="slide-quote">
                  <?php if($item['content']) echo '"' . $item['content'] . '"'; ?>
                </div>
                <div class="slide-author">
                  <?php if($item['name']) echo $item['name']; ?>
                </div>
                <div class="slide-status">
                  <?php foreach($item['status'] as $status) {
                    echo $status['row'];
                    echo '<br />';
                  } ?>
                </div>
                <?php if($item['link']) echo '<a href="' . $item['link']['url'] .
                  '" class="slide-link"><span>' . $item['link']['title'] . '</span></a>';
                ?>
              </div>
              </div>
            </div>
          <?php } ?>
        </div>
      <div class="block-slider-arrows"></div>
    </div>
</div>