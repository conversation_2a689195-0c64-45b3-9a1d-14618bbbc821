<template>
  <div class="calendar-paging">
    <!-- Previous page -->
    <button v-if="currentPage > 1" @click="prevPage" class="btn-prevnext">
      <PERSON><PERSON><PERSON>
    </button>
    <div class="page-balls">
      <!-- First -->
      <button @click="gotoPage(1)" v-if="currentPage > 1" class="btn-ball">
        1
      </button>
      <span class="btn-ball current">
        {{ currentPage }}
      </span>
      <!-- Button for next pages -->
      <button
        @click="gotoPage(pageNum)"
        v-for="pageNum in paging.nextButtons"
        :key="'pbtn-' + pageNum"
        class="btn-ball"
      >
        {{ pageNum }}
      </button>
      <!-- Dots indicating more pages in between -->
      <span v-if="paging.dots">...</span>
      <!-- Last -->
      <button
        @click="gotoPage(paging.pageCount)"
        v-if="currentPage < paging.pageCount"
        class="btn-ball"
      >
        {{ paging.pageCount }}
      </button>
    </div>
    <!-- Next page -->
    <button v-if="!isLastPage" @click="nextPage" class="btn-prevnext">
      Seuraava
    </button>
  </div>
</template>
<script>
export default {
  props: ["totalPages", "currentPage", "gotoPage"],
  data() {
    return {
      prop: true,
    };
  },

  computed: {
    paging() {
      const nextButtons = [];
      if (this.currentPage + 1 < this.totalPages) {
        const nextBtnCount = Math.min(
          this.totalPages - this.currentPage - 1,
          3,
        );
        for (let i = 0; i < nextBtnCount; i++) {
          nextButtons.push(this.currentPage + 1 + i);
        }
      }
      return {
        pageCount: this.totalPages,
        nextButtons: nextButtons,
        dots: this.totalPages - this.currentPage > 3,
      };
    },

    isLastPage() {
      return this.currentPage >= this.paging.pageCount;
    },
  },

  methods: {
    prevPage() {
      if (this.currentPage > 1) this.gotoPage(this.currentPage - 1);
    },
    nextPage() {
      if (!this.isLastPage) this.gotoPage(this.currentPage + 1);
    },
  },
};
</script>
<style lang="scss">
$darkBlue: #002663;
$liteBlue: #d8e3f2;
$grey: #ebebeb;
$tablet: 768px;
.calendar-paging {
  padding: 2rem;
  // width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 9px;
  @media (max-width: $tablet) {
    flex-direction: column;
  }
  .page-balls {
    display: flex;
    gap: 9px;
    align-items: center;
  }
  span,
  button {
    border: none;
    background: transparent;
    font-family: sans-serif;
    line-height: 1;
    font-size: 1rem;
    display: inline-block;
    cursor: pointer;
  }
  .btn-prevnext {
    color: $darkBlue;
    &:hover {
      color: lighten($darkBlue, 20%);
    }
  }
  .btn-ball {
    background: $liteBlue;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50px;
    &.current {
      background: $darkBlue;
      color: white;
    }
  }
  @media (min-width: $tablet) {
    .btn-ball {
      width: 32px;
      height: 32px;
      &:hover {
        background: darken($liteBlue, 10%);
      }
    }
  }
}
</style>
