<?php
/**
 * Setup <PERSON>nberg
 *
 * @package kauppakamari
 */

/**
 * <PERSON>utenberg setup
 */
add_action('after_setup_theme', function() {

  // support wide width
  add_theme_support('align-wide');

  // support responsive embeds
  add_theme_support('responsive-embeds');

  // disable custom color picker
  add_theme_support('disable-custom-colors');

  // disable font size selection
  add_theme_support('disable-custom-font-sizes');

  // remove font size options
  add_theme_support('editor-font-sizes', []);

  /**
   * Remove colors and prefer using colors through custom styles
   * as long as Gutenberg won't support scoping specific colors
   * to specific blocks. As for now, the same color options will
   * show up to every block that supports colors and fot both
   * text color and background color options.
   */
  add_theme_support('editor-color-palette', []);

});

function kauppakamari_setup_theme_supported_features() {
  add_theme_support( 'editor-color-palette', array(
      array(
          'name' => __( 'main', 'themeLangDomain' ),
          'slug' => 'main',
          'color' => '#002663',
      ),
      array(
          'name' => __( 'brand', 'themeLangDomain' ),
          'slug' => 'brand',
          'color' => '#3A75C4',
      ),
      array(
          'name' => __( 'blue-bg', 'themeLangDomain' ),
          'slug' => 'blue-bg',
          'color' => '#EFF4FA',
      ),
      array(
          'name' => __( 'very light gray', 'themeLangDomain' ),
          'slug' => 'very-light-gray',
          'color' => '#F8F8F8',
      ),
      array(
          'name' => __( 'very dark gray', 'themeLangDomain' ),
          'slug' => 'very-dark-gray',
          'color' => '#808080',
      ),
  ) );
}

add_action( 'after_setup_theme', 'kauppakamari_setup_theme_supported_features' );
