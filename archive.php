<?php

/**
 * The main template file.
 *
 * This is the most generic template file in a WordPress theme
 * and one of the two required files for a theme (the other being style.css).
 * It is used to display a page when nothing more specific matches a query.
 * E.g., it puts together the home page when no home.php file exists.
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package kauppakamari
 */

global $wp_query;
$is404 = is_404();

get_header(); ?>


<?php
//@@TODO move to include
// TODO look inc/kamari/cpt-releases.php
// it's forcing releases also to be in
$pre_cats = sanitize_text_field($_GET['category']);
//$pre_cats_array = explode(',', $pre_cats);
// https://helsinkichb.local/artikkelityyppi/blogit/?category=kansainvalisyys



?>
<?php get_template_part('partials/content/hero-only-breadcrumb'); ?>


<div id="primary" class="primary primary--index">
    <main id="main" class="main">

        <?php if ($is404) : ?>
            <h1 class="has-text-align-center"><?php pll_e("Valitettavasti emme löytäneet etsimääsi sisältöä. <br>Tässä kuitenkin ajankohtaisia artikkeleitamme.") ?></h1>
            <hr style="margin-bottom:3rem">
        <?php endif; ?>

        <?php
        require_once(dirname(__FILE__) . '/partials/content/article-filter.php');
        ?>


        <div class="teaser-container--archive">
            <p class="teaser-container--archive--path"><?php pll_e("Tulokset") ?>: <strong><?php echo $filter_title; ?></strong></p>
            <?php
            $i = 0;
            $show_three_meta = true;
            $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
            while (have_posts()) {
                the_post();
                // You wish to make $my_var available to the template part at `content-part.php`
                //set_query_var( 'my_var', $my_var );
                //get_template_part( 'content', 'part' );

                // Show articles this way if "all" category -filter is selected
                if ($article_type_parameter == "kaikki") {
                    echo '<div class="clearfix"></div>';
                    get_template_part('partials/content/teaser-new-wide');
                } else {
                    if ($i === 0 && $paged <= 1) {
                        echo '<div class="teaser-new-2cols">';
                        get_template_part('partials/content/teaser-1-subcol');
                    } elseif ($i === 1 && $paged <= 1) {
                        get_template_part('partials/content/teaser-1-subcol');
                        echo '</div> <!-- teaser-new-2cols end -->';
                    }
                    /* if page is 2+ use this loop only */ elseif ($i >= 2 || $paged > 1) {
                        echo '<div class="clearfix"></div>';
                        get_template_part('partials/content/teaser-new-wide');
                    }
                }

                $i++;
            }


            if ($i == 0) {
                if (date('Ymd') < '20220310') {
                    echo '<p class="no-posts">Löydät täältä sisältöä pian. Uudistamme tätä sivua tiistain 8.3.2022 aikana.</p>';
                } else {
                    echo '<p class="no-posts"> Yhtään artikkelia ei löytynyt</p>';
                }
            }
            ?>
        </div>

        <?php kauppakamari_numeric_posts_nav(); ?>

    </main><!-- #main -->


</div><!-- #primary -->

<?php
wp_reset_query(); //resets modified queries so that all footer menus etc work
get_footer();
