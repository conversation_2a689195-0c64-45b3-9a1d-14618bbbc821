<?php
/**
 * Template part: Teaser
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package kauppakamari
 */

?>

<article id="teaser-<?php the_ID(); ?>" <?php post_class('teaser teaser--medium teaser--' . get_post_type()); ?>>

  <?php if (has_post_thumbnail()) : ?>
    <div class="teaser__thumbnail">
      <a href="<?php the_permalink(); ?>" tabindex="-1">
        <?php echo kauppakamari_get_image(get_post_thumbnail_id(), 'article'); ?>
      </a>
    </div>
  <?php else : ?>
    <div class="teaser__thumbnail">
      <a href="<?php the_permalink(); ?>" tabindex="-1">
        <?php echo kauppakamari_get_image(kauppakamari_get_hardcoded_id('img_placeholder'), 'article'); ?>
      </a>
    </div>
  <?php endif; ?>

  <div class="teaser__content">

    <header class="teaser__header">
      <?php echo kauppakamari_get_post_meta(); ?>
      <?php the_title(sprintf('<h2 class="teaser__header__title"><a href="%s" rel="bookmark">', esc_url(get_permalink())), '</a></h2>'); ?>
    </header>

    <div class="teaser__summary">
      <a href="<?php the_permalink(); ?>" tabindex="-1">
      <?php
        if (get_field('ingress')) {
          echo get_field('ingress');
        } else {
          echo excerpt(25);
        }
      ?>
      </a>
    </div>

  </div>

</article>
