"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}!function(e,t){"object"==("undefined"==typeof module?"undefined":_typeof(module))&&"object"==_typeof(module.exports)?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:void 0,function(te,e){function g(e){return null!=e&&e===e.window}var ne=[],r=Object.getPrototypeOf,re=ne.slice,v=ne.flat?function(e){return ne.flat.call(e)}:function(e){return ne.concat.apply([],e)},s=ne.push,ie=ne.indexOf,n={},i=n.toString,oe=n.hasOwnProperty,o=oe.toString,a=o.call(Object),ae={},y=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item},C=te.document,u={type:!0,src:!0,nonce:!0,noModule:!0};function m(e,t,n){var r,i,o=(n=n||C).createElement("script");if(o.text=e,t)for(r in u)(i=t[r]||t.getAttribute&&t.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function x(e){return null==e?e+"":"object"==_typeof(e)||"function"==typeof e?n[i.call(e)]||"object":_typeof(e)}var t="3.7.1",l=/HTML$/i,se=function(e,t){return new se.fn.init(e,t)};function c(e){var t=!!e&&"length"in e&&e.length,n=x(e);return!y(e)&&!g(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}function ue(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}se.fn=se.prototype={jquery:t,constructor:se,length:0,toArray:function(){return re.call(this)},get:function(e){return null==e?re.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=se.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return se.each(this,e)},map:function(n){return this.pushStack(se.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(re.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(se.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(se.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(0<=n&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:s,sort:ne.sort,splice:ne.splice},se.extend=se.fn.extend=function(){var e,t,n,r,i,o,a=arguments[0]||{},s=1,u=arguments.length,l=!1;for("boolean"==typeof a&&(l=a,a=arguments[s]||{},s++),"object"==_typeof(a)||y(a)||(a={}),s===u&&(a=this,s--);s<u;s++)if(null!=(e=arguments[s]))for(t in e)r=e[t],"__proto__"!==t&&a!==r&&(l&&r&&(se.isPlainObject(r)||(i=Array.isArray(r)))?(n=a[t],o=i&&!Array.isArray(n)?[]:i||se.isPlainObject(n)?n:{},i=!1,a[t]=se.extend(l,o,r)):void 0!==r&&(a[t]=r));return a},se.extend({expando:"jQuery"+(t+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==i.call(e)||(t=r(e))&&("function"!=typeof(n=oe.call(t,"constructor")&&t.constructor)||o.call(n)!==a))},isEmptyObject:function(e){for(var t in e)return!1;return!0},globalEval:function(e,t,n){m(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,r=0;if(c(e))for(n=e.length;r<n&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},text:function(e){var t,n="",r=0,i=e.nodeType;if(!i)for(;t=e[r++];)n+=se.text(t);return 1===i||11===i?e.textContent:9===i?e.documentElement.textContent:3===i||4===i?e.nodeValue:n},makeArray:function(e,t){var n=t||[];return null!=e&&(c(Object(e))?se.merge(n,"string"==typeof e?[e]:e):s.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:ie.call(t,e,n)},isXMLDoc:function(e){var t=e&&e.namespaceURI,n=e&&(e.ownerDocument||e).documentElement;return!l.test(t||n&&n.nodeName||"HTML")},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,a=!n;i<o;i++)!t(e[i],i)!=a&&r.push(e[i]);return r},map:function(e,t,n){var r,i,o=0,a=[];if(c(e))for(r=e.length;o<r;o++)null!=(i=t(e[o],o,n))&&a.push(i);else for(o in e)null!=(i=t(e[o],o,n))&&a.push(i);return v(a)},guid:1,support:ae}),"function"==typeof Symbol&&(se.fn[Symbol.iterator]=ne[Symbol.iterator]),se.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){n["[object "+t+"]"]=t.toLowerCase()});var le=ne.pop,ce=ne.sort,fe=ne.splice,de="[\\x20\\t\\r\\n\\f]",pe=new RegExp("^"+de+"+|((?:^|[^\\\\])(?:\\\\.)*)"+de+"+$","g");se.contains=function(e,t){var n=t&&t.parentNode;return e===n||!(!n||1!==n.nodeType||!(e.contains?e.contains(n):e.compareDocumentPosition&&16&e.compareDocumentPosition(n)))};var f=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function d(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}se.escapeSelector=function(e){return(e+"").replace(f,d)};var he=C,ge=s;!function(){var e,b,w,o,a,T,r,C,p,i,E=ge,S=se.expando,A=0,n=0,s=_(),c=_(),u=_(),h=_(),l=function(e,t){return e===t&&(a=!0),0},f="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",t="(?:\\\\[\\da-fA-F]{1,6}"+de+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",d="\\["+de+"*("+t+")(?:"+de+"*([*^$|!~]?=)"+de+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+t+"))|)"+de+"*\\]",g=":("+t+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+d+")*)|.*)\\)|)",v=new RegExp(de+"+","g"),y=new RegExp("^"+de+"*,"+de+"*"),m=new RegExp("^"+de+"*([>+~]|"+de+")"+de+"*"),x=new RegExp(de+"|>"),k=new RegExp(g),j=new RegExp("^"+t+"$"),D={ID:new RegExp("^#("+t+")"),CLASS:new RegExp("^\\.("+t+")"),TAG:new RegExp("^("+t+"|[*])"),ATTR:new RegExp("^"+d),PSEUDO:new RegExp("^"+g),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+de+"*(even|odd|(([+-]|)(\\d*)n|)"+de+"*(?:([+-]|)"+de+"*(\\d+)|))"+de+"*\\)|)","i"),bool:new RegExp("^(?:"+f+")$","i"),needsContext:new RegExp("^"+de+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+de+"*((?:-\\d)?\\d*)"+de+"*\\)|)(?=[^-]|$)","i")},N=/^(?:input|select|textarea|button)$/i,L=/^h\d$/i,q=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,H=/[+~]/,O=new RegExp("\\\\[\\da-fA-F]{1,6}"+de+"?|\\\\([^\\r\\n\\f])","g"),M=function(e,t){var n="0x"+e.slice(1)-65536;return t||(n<0?String.fromCharCode(65536+n):String.fromCharCode(n>>10|55296,1023&n|56320))},z=function(){X()},P=Y(function(e){return!0===e.disabled&&ue(e,"fieldset")},{dir:"parentNode",next:"legend"});try{E.apply(ne=re.call(he.childNodes),he.childNodes),ne[he.childNodes.length].nodeType}catch(e){E={apply:function(e,t){ge.apply(e,re.call(t))},call:function(e){ge.apply(e,re.call(arguments,1))}}}function R(e,t,n,r){var i,o,a,s,u,l,c,f=t&&t.ownerDocument,d=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==d&&9!==d&&11!==d)return n;if(!r&&(X(t),t=t||T,C)){if(11!==d&&(u=q.exec(e)))if(i=u[1]){if(9===d){if(!(a=t.getElementById(i)))return n;if(a.id===i)return E.call(n,a),n}else if(f&&(a=f.getElementById(i))&&R.contains(t,a)&&a.id===i)return E.call(n,a),n}else{if(u[2])return E.apply(n,t.getElementsByTagName(e)),n;if((i=u[3])&&t.getElementsByClassName)return E.apply(n,t.getElementsByClassName(i)),n}if(!(h[e+" "]||p&&p.test(e))){if(c=e,f=t,1===d&&(x.test(e)||m.test(e))){for((f=H.test(e)&&B(t.parentNode)||t)==t&&ae.scope||((s=t.getAttribute("id"))?s=se.escapeSelector(s):t.setAttribute("id",s=S)),o=(l=V(e)).length;o--;)l[o]=(s?"#"+s:":scope")+" "+G(l[o]);c=l.join(",")}try{return E.apply(n,f.querySelectorAll(c)),n}catch(t){h(e,!0)}finally{s===S&&t.removeAttribute("id")}}}return ee(e.replace(pe,"$1"),t,n,r)}function _(){var r=[];return function e(t,n){return r.push(t+" ")>b.cacheLength&&delete e[r.shift()],e[t+" "]=n}}function W(e){return e[S]=!0,e}function F(e){var t=T.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function I(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&P(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function $(a){return W(function(o){return o=+o,W(function(e,t){for(var n,r=a([],e.length,o),i=r.length;i--;)e[n=r[i]]&&(e[n]=!(t[n]=e[n]))})})}function B(e){return e&&void 0!==e.getElementsByTagName&&e}function X(e){var t,n=e?e.ownerDocument||e:he;return n!=T&&9===n.nodeType&&n.documentElement&&(r=(T=n).documentElement,C=!se.isXMLDoc(T),i=r.matches||r.webkitMatchesSelector||r.msMatchesSelector,r.msMatchesSelector&&he!=T&&(t=T.defaultView)&&t.top!==t&&t.addEventListener("unload",z),ae.getById=F(function(e){return r.appendChild(e).id=se.expando,!T.getElementsByName||!T.getElementsByName(se.expando).length}),ae.disconnectedMatch=F(function(e){return i.call(e,"*")}),ae.scope=F(function(){return T.querySelectorAll(":scope")}),ae.cssHas=F(function(){try{return T.querySelector(":has(*,:jqfake)"),0}catch(e){return 1}}),ae.getById?(b.filter.ID=function(e){var t=e.replace(O,M);return function(e){return e.getAttribute("id")===t}},b.find.ID=function(e,t){if(void 0!==t.getElementById&&C){var n=t.getElementById(e);return n?[n]:[]}}):(b.filter.ID=function(e){var n=e.replace(O,M);return function(e){var t=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return t&&t.value===n}},b.find.ID=function(e,t){if(void 0!==t.getElementById&&C){var n,r,i,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(i=t.getElementsByName(e),r=0;o=i[r++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),b.find.TAG=function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):t.querySelectorAll(e)},b.find.CLASS=function(e,t){if(void 0!==t.getElementsByClassName&&C)return t.getElementsByClassName(e)},p=[],F(function(e){var t;r.appendChild(e).innerHTML="<a id='"+S+"' href='' disabled='disabled'></a><select id='"+S+"-\r\\' disabled='disabled'><option selected=''></option></select>",e.querySelectorAll("[selected]").length||p.push("\\["+de+"*(?:value|"+f+")"),e.querySelectorAll("[id~="+S+"-]").length||p.push("~="),e.querySelectorAll("a#"+S+"+*").length||p.push(".#.+[+~]"),e.querySelectorAll(":checked").length||p.push(":checked"),(t=T.createElement("input")).setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),r.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&p.push(":enabled",":disabled"),(t=T.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||p.push("\\["+de+"*name"+de+"*="+de+"*(?:''|\"\")")}),ae.cssHas||p.push(":has"),p=p.length&&new RegExp(p.join("|")),l=function(e,t){if(e===t)return a=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!ae.sortDetached&&t.compareDocumentPosition(e)===n?e===T||e.ownerDocument==he&&R.contains(he,e)?-1:t===T||t.ownerDocument==he&&R.contains(he,t)?1:o?ie.call(o,e)-ie.call(o,t):0:4&n?-1:1)}),T}for(e in R.matches=function(e,t){return R(e,null,null,t)},R.matchesSelector=function(e,t){if(X(e),C&&!h[t+" "]&&(!p||!p.test(t)))try{var n=i.call(e,t);if(n||ae.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){h(t,!0)}return 0<R(t,T,null,[e]).length},R.contains=function(e,t){return(e.ownerDocument||e)!=T&&X(e),se.contains(e,t)},R.attr=function(e,t){(e.ownerDocument||e)!=T&&X(e);var n=b.attrHandle[t.toLowerCase()],r=n&&oe.call(b.attrHandle,t.toLowerCase())?n(e,t,!C):void 0;return void 0!==r?r:e.getAttribute(t)},R.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},se.uniqueSort=function(e){var t,n=[],r=0,i=0;if(a=!ae.sortStable,o=!ae.sortStable&&re.call(e,0),ce.call(e,l),a){for(;t=e[i++];)t===e[i]&&(r=n.push(i));for(;r--;)fe.call(e,n[r],1)}return o=null,e},se.fn.uniqueSort=function(){return this.pushStack(se.uniqueSort(re.apply(this)))},(b=se.expr={cacheLength:50,createPseudo:W,match:D,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(O,M),e[3]=(e[3]||e[4]||e[5]||"").replace(O,M),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||R.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&R.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return D.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&k.test(n)&&(t=V(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(O,M).toLowerCase();return"*"===e?function(){return!0}:function(e){return ue(e,t)}},CLASS:function(e){var t=s[e+" "];return t||(t=new RegExp("(^|"+de+")"+e+"("+de+"|$)"))&&s(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(n,r,i){return function(e){var t=R.attr(e,n);return null==t?"!="===r:!r||(t+="","="===r?t===i:"!="===r?t!==i:"^="===r?i&&0===t.indexOf(i):"*="===r?i&&-1<t.indexOf(i):"$="===r?i&&t.slice(-i.length)===i:"~="===r?-1<(" "+t.replace(v," ")+" ").indexOf(i):"|="===r&&(t===i||t.slice(0,i.length+1)===i+"-"))}},CHILD:function(p,e,t,h,g){var v="nth"!==p.slice(0,3),y="last"!==p.slice(-4),m="of-type"===e;return 1===h&&0===g?function(e){return!!e.parentNode}:function(e,t,n){var r,i,o,a,s,u=v!=y?"nextSibling":"previousSibling",l=e.parentNode,c=m&&e.nodeName.toLowerCase(),f=!n&&!m,d=!1;if(l){if(v){for(;u;){for(o=e;o=o[u];)if(m?ue(o,c):1===o.nodeType)return!1;s=u="only"===p&&!s&&"nextSibling"}return!0}if(s=[y?l.firstChild:l.lastChild],y&&f){for(d=(a=(r=(i=l[S]||(l[S]={}))[p]||[])[0]===A&&r[1])&&r[2],o=a&&l.childNodes[a];o=++a&&o&&o[u]||(d=a=0)||s.pop();)if(1===o.nodeType&&++d&&o===e){i[p]=[A,a,d];break}}else if(f&&(d=a=(r=(i=e[S]||(e[S]={}))[p]||[])[0]===A&&r[1]),!1===d)for(;(o=++a&&o&&o[u]||(d=a=0)||s.pop())&&((m?!ue(o,c):1!==o.nodeType)||!++d||(f&&((i=o[S]||(o[S]={}))[p]=[A,d]),o!==e)););return(d-=g)===h||d%h==0&&0<=d/h}}},PSEUDO:function(e,o){var t,a=b.pseudos[e]||b.setFilters[e.toLowerCase()]||R.error("unsupported pseudo: "+e);return a[S]?a(o):1<a.length?(t=[e,e,"",o],b.setFilters.hasOwnProperty(e.toLowerCase())?W(function(e,t){for(var n,r=a(e,o),i=r.length;i--;)e[n=ie.call(e,r[i])]=!(t[n]=r[i])}):function(e){return a(e,0,t)}):a}},pseudos:{not:W(function(e){var r=[],i=[],s=Z(e.replace(pe,"$1"));return s[S]?W(function(e,t,n,r){for(var i,o=s(e,null,r,[]),a=e.length;a--;)(i=o[a])&&(e[a]=!(t[a]=i))}):function(e,t,n){return r[0]=e,s(r,null,n,i),r[0]=null,!i.pop()}}),has:W(function(t){return function(e){return 0<R(t,e).length}}),contains:W(function(t){return t=t.replace(O,M),function(e){return-1<(e.textContent||se.text(e)).indexOf(t)}}),lang:W(function(n){return j.test(n||"")||R.error("unsupported lang: "+n),n=n.replace(O,M).toLowerCase(),function(e){var t;do{if(t=C?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=te.location&&te.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===r},focus:function(e){return e===function(){try{return T.activeElement}catch(e){}}()&&T.hasFocus()&&!!(e.type||e.href||~e.tabIndex)},enabled:I(!1),disabled:I(!0),checked:function(e){return ue(e,"input")&&!!e.checked||ue(e,"option")&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!b.pseudos.empty(e)},header:function(e){return L.test(e.nodeName)},input:function(e){return N.test(e.nodeName)},button:function(e){return ue(e,"input")&&"button"===e.type||ue(e,"button")},text:function(e){var t;return ue(e,"input")&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:$(function(){return[0]}),last:$(function(e,t){return[t-1]}),eq:$(function(e,t,n){return[n<0?n+t:n]}),even:$(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:$(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:$(function(e,t,n){for(var r=n<0?n+t:t<n?t:n;0<=--r;)e.push(r);return e}),gt:$(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=b.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})b.pseudos[e]=function(t){return function(e){return ue(e,"input")&&e.type===t}}(e);for(e in{submit:!0,reset:!0})b.pseudos[e]=function(t){return function(e){return(ue(e,"input")||ue(e,"button"))&&e.type===t}}(e);function U(){}function V(e,t){var n,r,i,o,a,s,u,l=c[e+" "];if(l)return t?0:l.slice(0);for(a=e,s=[],u=b.preFilter;a;){for(o in n&&!(r=y.exec(a))||(r&&(a=a.slice(r[0].length)||a),s.push(i=[])),n=!1,(r=m.exec(a))&&(n=r.shift(),i.push({value:n,type:r[0].replace(pe," ")}),a=a.slice(n.length)),b.filter)!(r=D[o].exec(a))||u[o]&&!(r=u[o](r))||(n=r.shift(),i.push({value:n,type:o,matches:r}),a=a.slice(n.length));if(!n)break}return t?a.length:a?R.error(e):c(e,s).slice(0)}function G(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function Y(a,e,t){var s=e.dir,u=e.next,l=u||s,c=t&&"parentNode"===l,f=n++;return e.first?function(e,t,n){for(;e=e[s];)if(1===e.nodeType||c)return a(e,t,n);return!1}:function(e,t,n){var r,i,o=[A,f];if(n){for(;e=e[s];)if((1===e.nodeType||c)&&a(e,t,n))return!0}else for(;e=e[s];)if(1===e.nodeType||c)if(i=e[S]||(e[S]={}),u&&ue(e,u))e=e[s]||e;else{if((r=i[l])&&r[0]===A&&r[1]===f)return o[2]=r[2];if((i[l]=o)[2]=a(e,t,n))return!0}return!1}}function Q(i){return 1<i.length?function(e,t,n){for(var r=i.length;r--;)if(!i[r](e,t,n))return!1;return!0}:i[0]}function J(e,t,n,r,i){for(var o,a=[],s=0,u=e.length,l=null!=t;s<u;s++)(o=e[s])&&(n&&!n(o,r,i)||(a.push(o),l&&t.push(s)));return a}function K(e){for(var i,t,n,r=e.length,o=b.relative[e[0].type],a=o||b.relative[" "],s=o?1:0,u=Y(function(e){return e===i},a,!0),l=Y(function(e){return-1<ie.call(i,e)},a,!0),c=[function(e,t,n){var r=!o&&(n||t!=w)||((i=t).nodeType?u:l)(e,t,n);return i=null,r}];s<r;s++)if(t=b.relative[e[s].type])c=[Y(Q(c),t)];else{if((t=b.filter[e[s].type].apply(null,e[s].matches))[S]){for(n=++s;n<r&&!b.relative[e[n].type];n++);return function e(p,h,g,v,y,t){return v&&!v[S]&&(v=e(v)),y&&!y[S]&&(y=e(y,t)),W(function(e,t,n,r){var i,o,a,s,u=[],l=[],c=t.length,f=e||function(e,t,n){for(var r=0,i=t.length;r<i;r++)R(e,t[r],n);return n}(h||"*",n.nodeType?[n]:n,[]),d=!p||!e&&h?f:J(f,u,p,n,r);if(g?g(d,s=y||(e?p:c||v)?[]:t,n,r):s=d,v)for(i=J(s,l),v(i,[],n,r),o=i.length;o--;)(a=i[o])&&(s[l[o]]=!(d[l[o]]=a));if(e){if(y||p){if(y){for(i=[],o=s.length;o--;)(a=s[o])&&i.push(d[o]=a);y(null,s=[],i,r)}for(o=s.length;o--;)(a=s[o])&&-1<(i=y?ie.call(e,a):u[o])&&(e[i]=!(t[i]=a))}}else s=J(s===t?s.splice(c,s.length):s),y?y(null,t,s,r):E.apply(t,s)})}(1<s&&Q(c),1<s&&G(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(pe,"$1"),t,s<n&&K(e.slice(s,n)),n<r&&K(e=e.slice(n)),n<r&&G(e))}c.push(t)}return Q(c)}function Z(e,t){var n,v,y,m,x,r,i=[],o=[],a=u[e+" "];if(!a){for(n=(t=t||V(e)).length;n--;)(a=K(t[n]))[S]?i.push(a):o.push(a);(a=u(e,(v=o,m=0<(y=i).length,x=0<v.length,r=function(e,t,n,r,i){var o,a,s,u=0,l="0",c=e&&[],f=[],d=w,p=e||x&&b.find.TAG("*",i),h=A+=null==d?1:Math.random()||.1,g=p.length;for(i&&(w=t==T||t||i);l!==g&&null!=(o=p[l]);l++){if(x&&o){for(a=0,t||o.ownerDocument==T||(X(o),n=!C);s=v[a++];)if(s(o,t||T,n)){E.call(r,o);break}i&&(A=h)}m&&((o=!s&&o)&&u--,e&&c.push(o))}if(u+=l,m&&l!==u){for(a=0;s=y[a++];)s(c,f,t,n);if(e){if(0<u)for(;l--;)c[l]||f[l]||(f[l]=le.call(r));f=J(f)}E.apply(r,f),i&&!e&&0<f.length&&1<u+y.length&&se.uniqueSort(r)}return i&&(A=h,w=d),c},m?W(r):r))).selector=e}return a}function ee(e,t,n,r){var i,o,a,s,u,l="function"==typeof e&&e,c=!r&&V(e=l.selector||e);if(n=n||[],1===c.length){if(2<(o=c[0]=c[0].slice(0)).length&&"ID"===(a=o[0]).type&&9===t.nodeType&&C&&b.relative[o[1].type]){if(!(t=(b.find.ID(a.matches[0].replace(O,M),t)||[])[0]))return n;l&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(i=D.needsContext.test(e)?0:o.length;i--&&(a=o[i],!b.relative[s=a.type]);)if((u=b.find[s])&&(r=u(a.matches[0].replace(O,M),H.test(o[0].type)&&B(t.parentNode)||t))){if(o.splice(i,1),!(e=r.length&&G(o)))return E.apply(n,r),n;break}}return(l||Z(e,c))(r,t,!C,n,!t||H.test(e)&&B(t.parentNode)||t),n}U.prototype=b.filters=b.pseudos,b.setFilters=new U,ae.sortStable=S.split("").sort(l).join("")===S,X(),ae.sortDetached=F(function(e){return 1&e.compareDocumentPosition(T.createElement("fieldset"))}),se.find=R,se.expr[":"]=se.expr.pseudos,se.unique=se.uniqueSort,R.compile=Z,R.select=ee,R.setDocument=X,R.tokenize=V,R.escape=se.escapeSelector,R.getText=se.text,R.isXML=se.isXMLDoc,R.selectors=se.expr,R.support=se.support,R.uniqueSort=se.uniqueSort}();function p(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(i&&se(e).is(n))break;r.push(e)}return r}function h(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}var b=se.expr.match.needsContext,w=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function T(e,n,r){return y(n)?se.grep(e,function(e,t){return!!n.call(e,t,e)!==r}):n.nodeType?se.grep(e,function(e){return e===n!==r}):"string"!=typeof n?se.grep(e,function(e){return-1<ie.call(n,e)!==r}):se.filter(n,e,r)}se.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?se.find.matchesSelector(r,e)?[r]:[]:se.find.matches(e,se.grep(t,function(e){return 1===e.nodeType}))},se.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(se(e).filter(function(){for(t=0;t<r;t++)if(se.contains(i[t],this))return!0}));for(n=this.pushStack([]),t=0;t<r;t++)se.find(e,i[t],n);return 1<r?se.uniqueSort(n):n},filter:function(e){return this.pushStack(T(this,e||[],!1))},not:function(e){return this.pushStack(T(this,e||[],!0))},is:function(e){return!!T(this,"string"==typeof e&&b.test(e)?se(e):e||[],!1).length}});var E,S=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(se.fn.init=function(e,t,n){var r,i;if(!e)return this;if(n=n||E,"string"!=typeof e)return e.nodeType?(this[0]=e,this.length=1,this):y(e)?void 0!==n.ready?n.ready(e):e(se):se.makeArray(e,this);if(!(r="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:S.exec(e))||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof se?t[0]:t,se.merge(this,se.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:C,!0)),w.test(r[1])&&se.isPlainObject(t))for(r in t)y(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return(i=C.getElementById(r[2]))&&(this[0]=i,this.length=1),this}).prototype=se.fn,E=se(C);var A=/^(?:parents|prev(?:Until|All))/,k={children:!0,contents:!0,next:!0,prev:!0};function j(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}se.fn.extend({has:function(e){var t=se(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(se.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,i=this.length,o=[],a="string"!=typeof e&&se(e);if(!b.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?-1<a.index(n):1===n.nodeType&&se.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(1<o.length?se.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?ie.call(se(e),this[0]):ie.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(se.uniqueSort(se.merge(this.get(),se(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),se.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return p(e,"parentNode")},parentsUntil:function(e,t,n){return p(e,"parentNode",n)},next:function(e){return j(e,"nextSibling")},prev:function(e){return j(e,"previousSibling")},nextAll:function(e){return p(e,"nextSibling")},prevAll:function(e){return p(e,"previousSibling")},nextUntil:function(e,t,n){return p(e,"nextSibling",n)},prevUntil:function(e,t,n){return p(e,"previousSibling",n)},siblings:function(e){return h((e.parentNode||{}).firstChild,e)},children:function(e){return h(e.firstChild)},contents:function(e){return null!=e.contentDocument&&r(e.contentDocument)?e.contentDocument:(ue(e,"template")&&(e=e.content||e),se.merge([],e.childNodes))}},function(r,i){se.fn[r]=function(e,t){var n=se.map(this,i,e);return"Until"!==r.slice(-5)&&(t=e),t&&"string"==typeof t&&(n=se.filter(t,n)),1<this.length&&(k[r]||se.uniqueSort(n),A.test(r)&&n.reverse()),this.pushStack(n)}});var D=/[^\x20\t\r\n\f]+/g;function N(e){return e}function L(e){throw e}function q(e,t,n,r){var i;try{e&&y(i=e.promise)?i.call(e).done(t).fail(n):e&&y(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}se.Callbacks=function(r){var n;r="string"==typeof r?(n={},se.each(r.match(D)||[],function(e,t){n[t]=!0}),n):se.extend({},r);function i(){for(a=a||r.once,t=o=!0;u.length;l=-1)for(e=u.shift();++l<s.length;)!1===s[l].apply(e[0],e[1])&&r.stopOnFalse&&(l=s.length,e=!1);r.memory||(e=!1),o=!1,a&&(s=e?[]:"")}var o,e,t,a,s=[],u=[],l=-1,c={add:function(){return s&&(e&&!o&&(l=s.length-1,u.push(e)),function n(e){se.each(e,function(e,t){y(t)?r.unique&&c.has(t)||s.push(t):t&&t.length&&"string"!==x(t)&&n(t)})}(arguments),e&&!o&&i()),this},remove:function(){return se.each(arguments,function(e,t){for(var n;-1<(n=se.inArray(t,s,n));)s.splice(n,1),n<=l&&l--}),this},has:function(e){return e?-1<se.inArray(e,s):0<s.length},empty:function(){return s=s&&[],this},disable:function(){return a=u=[],s=e="",this},disabled:function(){return!s},lock:function(){return a=u=[],e||o||(s=e=""),this},locked:function(){return!!a},fireWith:function(e,t){return a||(t=[e,(t=t||[]).slice?t.slice():t],u.push(t),o||i()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!t}};return c},se.extend({Deferred:function(e){var o=[["notify","progress",se.Callbacks("memory"),se.Callbacks("memory"),2],["resolve","done",se.Callbacks("once memory"),se.Callbacks("once memory"),0,"resolved"],["reject","fail",se.Callbacks("once memory"),se.Callbacks("once memory"),1,"rejected"]],i="pending",a={state:function(){return i},always:function(){return s.done(arguments).fail(arguments),this},catch:function(e){return a.then(null,e)},pipe:function(){var i=arguments;return se.Deferred(function(r){se.each(o,function(e,t){var n=y(i[t[4]])&&i[t[4]];s[t[1]](function(){var e=n&&n.apply(this,arguments);e&&y(e.promise)?e.promise().progress(r.notify).done(r.resolve).fail(r.reject):r[t[0]+"With"](this,n?[e]:arguments)})}),i=null}).promise()},then:function(t,n,r){var u=0;function l(i,o,a,s){return function(){function e(){var e,t;if(!(i<u)){if((e=a.apply(n,r))===o.promise())throw new TypeError("Thenable self-resolution");t=e&&("object"==_typeof(e)||"function"==typeof e)&&e.then,y(t)?s?t.call(e,l(u,o,N,s),l(u,o,L,s)):(u++,t.call(e,l(u,o,N,s),l(u,o,L,s),l(u,o,N,o.notifyWith))):(a!==N&&(n=void 0,r=[e]),(s||o.resolveWith)(n,r))}}var n=this,r=arguments,t=s?e:function(){try{e()}catch(e){se.Deferred.exceptionHook&&se.Deferred.exceptionHook(e,t.error),u<=i+1&&(a!==L&&(n=void 0,r=[e]),o.rejectWith(n,r))}};i?t():(se.Deferred.getErrorHook?t.error=se.Deferred.getErrorHook():se.Deferred.getStackHook&&(t.error=se.Deferred.getStackHook()),te.setTimeout(t))}}return se.Deferred(function(e){o[0][3].add(l(0,e,y(r)?r:N,e.notifyWith)),o[1][3].add(l(0,e,y(t)?t:N)),o[2][3].add(l(0,e,y(n)?n:L))}).promise()},promise:function(e){return null!=e?se.extend(e,a):a}},s={};return se.each(o,function(e,t){var n=t[2],r=t[5];a[t[1]]=n.add,r&&n.add(function(){i=r},o[3-e][2].disable,o[3-e][3].disable,o[0][2].lock,o[0][3].lock),n.add(t[3].fire),s[t[0]]=function(){return s[t[0]+"With"](this===s?void 0:this,arguments),this},s[t[0]+"With"]=n.fireWith}),a.promise(s),e&&e.call(s,s),s},when:function(e){function t(t){return function(e){i[t]=this,o[t]=1<arguments.length?re.call(arguments):e,--n||a.resolveWith(i,o)}}var n=arguments.length,r=n,i=Array(r),o=re.call(arguments),a=se.Deferred();if(n<=1&&(q(e,a.done(t(r)).resolve,a.reject,!n),"pending"===a.state()||y(o[r]&&o[r].then)))return a.then();for(;r--;)q(o[r],t(r),a.reject);return a.promise()}});var H=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;se.Deferred.exceptionHook=function(e,t){te.console&&te.console.warn&&e&&H.test(e.name)&&te.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},se.readyException=function(e){te.setTimeout(function(){throw e})};var O=se.Deferred();function M(){C.removeEventListener("DOMContentLoaded",M),te.removeEventListener("load",M),se.ready()}se.fn.ready=function(e){return O.then(e).catch(function(e){se.readyException(e)}),this},se.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--se.readyWait:se.isReady)||(se.isReady=!0)!==e&&0<--se.readyWait||O.resolveWith(C,[se])}}),se.ready.then=O.then,"complete"===C.readyState||"loading"!==C.readyState&&!C.documentElement.doScroll?te.setTimeout(se.ready):(C.addEventListener("DOMContentLoaded",M),te.addEventListener("load",M));var z=function(e,t,n,r,i,o,a){var s=0,u=e.length,l=null==n;if("object"===x(n))for(s in i=!0,n)z(e,t,s,n[s],!0,o,a);else if(void 0!==r&&(i=!0,y(r)||(a=!0),l&&(t=a?(t.call(e,r),null):(l=t,function(e,t,n){return l.call(se(e),n)})),t))for(;s<u;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:l?t.call(e):u?t(e[0],n):o},P=/^-ms-/,R=/-([a-z])/g;function _(e,t){return t.toUpperCase()}function W(e){return e.replace(P,"ms-").replace(R,_)}function F(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType}function I(){this.expando=se.expando+I.uid++}I.uid=1,I.prototype={cache:function(e){var t=e[this.expando];return t||(t={},F(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[W(t)]=n;else for(r in t)i[W(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][W(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){n=(t=Array.isArray(t)?t.map(W):(t=W(t))in r?[t]:t.match(D)||[]).length;for(;n--;)delete r[t[n]]}void 0!==t&&!se.isEmptyObject(r)||(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!se.isEmptyObject(t)}};var $=new I,B=new I,X=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,U=/[A-Z]/g;function V(e,t,n){var r,i;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(U,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n="true"===(i=n)||"false"!==i&&("null"===i?null:i===+i+""?+i:X.test(i)?JSON.parse(i):i)}catch(e){}B.set(e,t,n)}else n=void 0;return n}se.extend({hasData:function(e){return B.hasData(e)||$.hasData(e)},data:function(e,t,n){return B.access(e,t,n)},removeData:function(e,t){B.remove(e,t)},_data:function(e,t,n){return $.access(e,t,n)},_removeData:function(e,t){$.remove(e,t)}}),se.fn.extend({data:function(n,e){var t,r,i,o=this[0],a=o&&o.attributes;if(void 0!==n)return"object"==_typeof(n)?this.each(function(){B.set(this,n)}):z(this,function(e){var t;return o&&void 0===e?void 0!==(t=B.get(o,n))||void 0!==(t=V(o,n))?t:void 0:void this.each(function(){B.set(this,n,e)})},null,e,1<arguments.length,null,!0);if(this.length&&(i=B.get(o),1===o.nodeType&&!$.get(o,"hasDataAttrs"))){for(t=a.length;t--;)a[t]&&0===(r=a[t].name).indexOf("data-")&&(r=W(r.slice(5)),V(o,r,i[r]));$.set(o,"hasDataAttrs",!0)}return i},removeData:function(e){return this.each(function(){B.remove(this,e)})}}),se.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=$.get(e,t),n&&(!r||Array.isArray(n)?r=$.access(e,t,se.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=se.queue(e,t),r=n.length,i=n.shift(),o=se._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,function(){se.dequeue(e,t)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return $.get(e,n)||$.access(e,n,{empty:se.Callbacks("once memory").add(function(){$.remove(e,[t+"queue",n])})})}}),se.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?se.queue(this[0],t):void 0===n?this:this.each(function(){var e=se.queue(this,t,n);se._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&se.dequeue(this,t)})},dequeue:function(e){return this.each(function(){se.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){function n(){--i||o.resolveWith(a,[a])}var r,i=1,o=se.Deferred(),a=this,s=this.length;for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(r=$.get(a[s],e+"queueHooks"))&&r.empty&&(i++,r.empty.add(n));return n(),o.promise(t)}});var G=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,Y=new RegExp("^(?:([+-])=|)("+G+")([a-z%]*)$","i"),Q=["Top","Right","Bottom","Left"],J=C.documentElement,K=function(e){return se.contains(e.ownerDocument,e)},Z={composed:!0};J.getRootNode&&(K=function(e){return se.contains(e.ownerDocument,e)||e.getRootNode(Z)===e.ownerDocument});function ee(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&K(e)&&"none"===se.css(e,"display")}function ve(e,t,n,r){var i,o,a=20,s=r?function(){return r.cur()}:function(){return se.css(e,t,"")},u=s(),l=n&&n[3]||(se.cssNumber[t]?"":"px"),c=e.nodeType&&(se.cssNumber[t]||"px"!==l&&+u)&&Y.exec(se.css(e,t));if(c&&c[3]!==l){for(u/=2,l=l||c[3],c=+u||1;a--;)se.style(e,t,c+l),(1-o)*(1-(o=s()/u||.5))<=0&&(a=0),c/=o;c*=2,se.style(e,t,c+l),n=n||[]}return n&&(c=+c||+u||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=l,r.start=c,r.end=i)),i}var ye={};function me(e,t){for(var n,r,i,o,a,s,u=[],l=0,c=e.length;l<c;l++)(r=e[l]).style&&(n=r.style.display,t?("none"===n&&(u[l]=$.get(r,"display")||null,u[l]||(r.style.display="")),""===r.style.display&&ee(r)&&(u[l]=(s=o=i=void 0,o=r.ownerDocument,a=r.nodeName,(s=ye[a])||(i=o.body.appendChild(o.createElement(a)),s=se.css(i,"display"),i.parentNode.removeChild(i),"none"===s&&(s="block"),ye[a]=s)))):"none"!==n&&(u[l]="none",$.set(r,"display",n)));for(l=0;l<c;l++)null!=u[l]&&(e[l].style.display=u[l]);return e}se.fn.extend({show:function(){return me(this,!0)},hide:function(){return me(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){ee(this)?se(this).show():se(this).hide()})}});var xe,be=/^(?:checkbox|radio)$/i,we=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,Te=/^$|^module$|\/(?:java|ecma)script/i,Ce=C.createDocumentFragment().appendChild(C.createElement("div"));(xe=C.createElement("input")).setAttribute("type","radio"),xe.setAttribute("checked","checked"),xe.setAttribute("name","t"),Ce.appendChild(xe),ae.checkClone=Ce.cloneNode(!0).cloneNode(!0).lastChild.checked,Ce.innerHTML="<textarea>x</textarea>",ae.noCloneChecked=!!Ce.cloneNode(!0).lastChild.defaultValue,Ce.innerHTML="<option></option>",ae.option=!!Ce.lastChild;var Ee={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function Se(e,t){var n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[];return void 0===t||t&&ue(e,t)?se.merge([e],n):n}function Ae(e,t){for(var n=0,r=e.length;n<r;n++)$.set(e[n],"globalEval",!t||$.get(t[n],"globalEval"))}Ee.tbody=Ee.tfoot=Ee.colgroup=Ee.caption=Ee.thead,Ee.th=Ee.td,ae.option||(Ee.optgroup=Ee.option=[1,"<select multiple='multiple'>","</select>"]);var ke=/<|&#?\w+;/;function je(e,t,n,r,i){for(var o,a,s,u,l,c,f=t.createDocumentFragment(),d=[],p=0,h=e.length;p<h;p++)if((o=e[p])||0===o)if("object"===x(o))se.merge(d,o.nodeType?[o]:o);else if(ke.test(o)){for(a=a||f.appendChild(t.createElement("div")),s=(we.exec(o)||["",""])[1].toLowerCase(),u=Ee[s]||Ee._default,a.innerHTML=u[1]+se.htmlPrefilter(o)+u[2],c=u[0];c--;)a=a.lastChild;se.merge(d,a.childNodes),(a=f.firstChild).textContent=""}else d.push(t.createTextNode(o));for(f.textContent="",p=0;o=d[p++];)if(r&&-1<se.inArray(o,r))i&&i.push(o);else if(l=K(o),a=Se(f.appendChild(o),"script"),l&&Ae(a),n)for(c=0;o=a[c++];)Te.test(o.type||"")&&n.push(o);return f}var De=/^([^.]*)(?:\.(.+)|)/;function Ne(){return!0}function Le(){return!1}function qe(e,t,n,r,i,o){var a,s;if("object"==_typeof(t)){for(s in"string"!=typeof n&&(r=r||n,n=void 0),t)qe(e,s,n,r,t[s],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=Le;else if(!i)return e;return 1===o&&(a=i,(i=function(e){return se().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=se.guid++)),e.each(function(){se.event.add(this,t,i,r,n)})}function He(e,r,t){t?($.set(e,r,!1),se.event.add(e,r,{namespace:!1,handler:function(e){var t,n=$.get(this,r);if(1&e.isTrigger&&this[r]){if(n)(se.event.special[r]||{}).delegateType&&e.stopPropagation();else if(n=re.call(arguments),$.set(this,r,n),this[r](),t=$.get(this,r),$.set(this,r,!1),n!==t)return e.stopImmediatePropagation(),e.preventDefault(),t}else n&&($.set(this,r,se.event.trigger(n[0],n.slice(1),this)),e.stopPropagation(),e.isImmediatePropagationStopped=Ne)}})):void 0===$.get(e,r)&&se.event.add(e,r,Ne)}se.event={global:{},add:function(t,e,n,r,i){var o,a,s,u,l,c,f,d,p,h,g,v=$.get(t);if(F(t))for(n.handler&&(n=(o=n).handler,i=o.selector),i&&se.find.matchesSelector(J,i),n.guid||(n.guid=se.guid++),(u=v.events)||(u=v.events=Object.create(null)),(a=v.handle)||(a=v.handle=function(e){return void 0!==se&&se.event.triggered!==e.type?se.event.dispatch.apply(t,arguments):void 0}),l=(e=(e||"").match(D)||[""]).length;l--;)p=g=(s=De.exec(e[l])||[])[1],h=(s[2]||"").split(".").sort(),p&&(f=se.event.special[p]||{},p=(i?f.delegateType:f.bindType)||p,f=se.event.special[p]||{},c=se.extend({type:p,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&se.expr.match.needsContext.test(i),namespace:h.join(".")},o),(d=u[p])||((d=u[p]=[]).delegateCount=0,f.setup&&!1!==f.setup.call(t,r,h,a)||t.addEventListener&&t.addEventListener(p,a)),f.add&&(f.add.call(t,c),c.handler.guid||(c.handler.guid=n.guid)),i?d.splice(d.delegateCount++,0,c):d.push(c),se.event.global[p]=!0)},remove:function(e,t,n,r,i){var o,a,s,u,l,c,f,d,p,h,g,v=$.hasData(e)&&$.get(e);if(v&&(u=v.events)){for(l=(t=(t||"").match(D)||[""]).length;l--;)if(p=g=(s=De.exec(t[l])||[])[1],h=(s[2]||"").split(".").sort(),p){for(f=se.event.special[p]||{},d=u[p=(r?f.delegateType:f.bindType)||p]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=d.length;o--;)c=d[o],!i&&g!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(d.splice(o,1),c.selector&&d.delegateCount--,f.remove&&f.remove.call(e,c));a&&!d.length&&(f.teardown&&!1!==f.teardown.call(e,h,v.handle)||se.removeEvent(e,p,v.handle),delete u[p])}else for(p in u)se.event.remove(e,p+t[l],n,r,!0);se.isEmptyObject(u)&&$.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,a,s=new Array(arguments.length),u=se.event.fix(e),l=($.get(this,"events")||Object.create(null))[u.type]||[],c=se.event.special[u.type]||{};for(s[0]=u,t=1;t<arguments.length;t++)s[t]=arguments[t];if(u.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,u)){for(a=se.event.handlers.call(this,u,l),t=0;(i=a[t++])&&!u.isPropagationStopped();)for(u.currentTarget=i.elem,n=0;(o=i.handlers[n++])&&!u.isImmediatePropagationStopped();)u.rnamespace&&!1!==o.namespace&&!u.rnamespace.test(o.namespace)||(u.handleObj=o,u.data=o.data,void 0!==(r=((se.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,s))&&!1===(u.result=r)&&(u.preventDefault(),u.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,u),u.result}},handlers:function(e,t){var n,r,i,o,a,s=[],u=t.delegateCount,l=e.target;if(u&&l.nodeType&&!("click"===e.type&&1<=e.button))for(;l!==this;l=l.parentNode||this)if(1===l.nodeType&&("click"!==e.type||!0!==l.disabled)){for(o=[],a={},n=0;n<u;n++)void 0===a[i=(r=t[n]).selector+" "]&&(a[i]=r.needsContext?-1<se(i,this).index(l):se.find(i,this,null,[l]).length),a[i]&&o.push(r);o.length&&s.push({elem:l,handlers:o})}return l=this,u<t.length&&s.push({elem:l,handlers:t.slice(u)}),s},addProp:function(t,e){Object.defineProperty(se.Event.prototype,t,{enumerable:!0,configurable:!0,get:y(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(e){return e[se.expando]?e:new se.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return be.test(t.type)&&t.click&&ue(t,"input")&&He(t,"click",!0),!1},trigger:function(e){var t=this||e;return be.test(t.type)&&t.click&&ue(t,"input")&&He(t,"click"),!0},_default:function(e){var t=e.target;return be.test(t.type)&&t.click&&ue(t,"input")&&$.get(t,"click")||ue(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},se.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},se.Event=function(e,t){if(!(this instanceof se.Event))return new se.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?Ne:Le,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&se.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[se.expando]=!0},se.Event.prototype={constructor:se.Event,isDefaultPrevented:Le,isPropagationStopped:Le,isImmediatePropagationStopped:Le,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=Ne,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=Ne,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=Ne,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},se.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},se.event.addProp),se.each({focus:"focusin",blur:"focusout"},function(r,i){function o(e){var t,n;C.documentMode?(t=$.get(this,"handle"),(n=se.event.fix(e)).type="focusin"===e.type?"focus":"blur",n.isSimulated=!0,t(e),n.target===n.currentTarget&&t(n)):se.event.simulate(i,e.target,se.event.fix(e))}se.event.special[r]={setup:function(){var e;if(He(this,r,!0),!C.documentMode)return!1;(e=$.get(this,i))||this.addEventListener(i,o),$.set(this,i,(e||0)+1)},trigger:function(){return He(this,r),!0},teardown:function(){var e;if(!C.documentMode)return!1;(e=$.get(this,i)-1)?$.set(this,i,e):(this.removeEventListener(i,o),$.remove(this,i))},_default:function(e){return $.get(e.target,r)},delegateType:i},se.event.special[i]={setup:function(){var e=this.ownerDocument||this.document||this,t=C.documentMode?this:e,n=$.get(t,i);n||(C.documentMode?this.addEventListener(i,o):e.addEventListener(r,o,!0)),$.set(t,i,(n||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,t=C.documentMode?this:e,n=$.get(t,i)-1;n?$.set(t,i,n):(C.documentMode?this.removeEventListener(i,o):e.removeEventListener(r,o,!0),$.remove(t,i))}}}),se.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,i){se.event.special[e]={delegateType:i,bindType:i,handle:function(e){var t,n=e.relatedTarget,r=e.handleObj;return n&&(n===this||se.contains(this,n))||(e.type=r.origType,t=r.handler.apply(this,arguments),e.type=i),t}}}),se.fn.extend({on:function(e,t,n,r){return qe(this,e,t,n,r)},one:function(e,t,n,r){return qe(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,se(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"!=_typeof(e))return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=Le),this.each(function(){se.event.remove(this,e,n,t)});for(i in e)this.off(i,t,e[i]);return this}});var Oe=/<script|<style|<link/i,Me=/checked\s*(?:[^=]|=\s*.checked.)/i,ze=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function Pe(e,t){return ue(e,"table")&&ue(11!==t.nodeType?t:t.firstChild,"tr")&&se(e).children("tbody")[0]||e}function Re(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function _e(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function We(e,t){var n,r,i,o,a,s;if(1===t.nodeType){if($.hasData(e)&&(s=$.get(e).events))for(i in $.remove(t,"handle events"),s)for(n=0,r=s[i].length;n<r;n++)se.event.add(t,i,s[i][n]);B.hasData(e)&&(o=B.access(e),a=se.extend({},o),B.set(t,a))}}function Fe(n,r,i,o){r=v(r);var e,t,a,s,u,l,c=0,f=n.length,d=f-1,p=r[0],h=y(p);if(h||1<f&&"string"==typeof p&&!ae.checkClone&&Me.test(p))return n.each(function(e){var t=n.eq(e);h&&(r[0]=p.call(this,e,t.html())),Fe(t,r,i,o)});if(f&&(t=(e=je(r,n[0].ownerDocument,!1,n,o)).firstChild,1===e.childNodes.length&&(e=t),t||o)){for(s=(a=se.map(Se(e,"script"),Re)).length;c<f;c++)u=e,c!==d&&(u=se.clone(u,!0,!0),s&&se.merge(a,Se(u,"script"))),i.call(n[c],u,c);if(s)for(l=a[a.length-1].ownerDocument,se.map(a,_e),c=0;c<s;c++)u=a[c],Te.test(u.type||"")&&!$.access(u,"globalEval")&&se.contains(l,u)&&(u.src&&"module"!==(u.type||"").toLowerCase()?se._evalUrl&&!u.noModule&&se._evalUrl(u.src,{nonce:u.nonce||u.getAttribute("nonce")},l):m(u.textContent.replace(ze,""),u,l))}return n}function Ie(e,t,n){for(var r,i=t?se.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||se.cleanData(Se(r)),r.parentNode&&(n&&K(r)&&Ae(Se(r,"script")),r.parentNode.removeChild(r));return e}se.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,a,s,u,l,c=e.cloneNode(!0),f=K(e);if(!(ae.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||se.isXMLDoc(e)))for(a=Se(c),r=0,i=(o=Se(e)).length;r<i;r++)s=o[r],"input"===(l=(u=a[r]).nodeName.toLowerCase())&&be.test(s.type)?u.checked=s.checked:"input"!==l&&"textarea"!==l||(u.defaultValue=s.defaultValue);if(t)if(n)for(o=o||Se(e),a=a||Se(c),r=0,i=o.length;r<i;r++)We(o[r],a[r]);else We(e,c);return 0<(a=Se(c,"script")).length&&Ae(a,!f&&Se(e,"script")),c},cleanData:function(e){for(var t,n,r,i=se.event.special,o=0;void 0!==(n=e[o]);o++)if(F(n)){if(t=n[$.expando]){if(t.events)for(r in t.events)i[r]?se.event.remove(n,r):se.removeEvent(n,r,t.handle);n[$.expando]=void 0}n[B.expando]&&(n[B.expando]=void 0)}}}),se.fn.extend({detach:function(e){return Ie(this,e,!0)},remove:function(e){return Ie(this,e)},text:function(e){return z(this,function(e){return void 0===e?se.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return Fe(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Pe(this,e).appendChild(e)})},prepend:function(){return Fe(this,arguments,function(e){var t;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(t=Pe(this,e)).insertBefore(e,t.firstChild)})},before:function(){return Fe(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return Fe(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(se.cleanData(Se(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return se.clone(this,e,t)})},html:function(e){return z(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Oe.test(e)&&!Ee[(we.exec(e)||["",""])[1].toLowerCase()]){e=se.htmlPrefilter(e);try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(se.cleanData(Se(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];return Fe(this,arguments,function(e){var t=this.parentNode;se.inArray(this,n)<0&&(se.cleanData(Se(this)),t&&t.replaceChild(e,this))},n)}}),se.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,a){se.fn[e]=function(e){for(var t,n=[],r=se(e),i=r.length-1,o=0;o<=i;o++)t=o===i?this:this.clone(!0),se(r[o])[a](t),s.apply(n,t.get());return this.pushStack(n)}});function $e(e,t,n){var r,i,o={};for(i in t)o[i]=e.style[i],e.style[i]=t[i];for(i in r=n.call(e),t)e.style[i]=o[i];return r}var Be,Xe,Ue,Ve,Ge,Ye,Qe,Je,Ke=new RegExp("^("+G+")(?!px)[a-z%]+$","i"),Ze=/^--/,et=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=te),t.getComputedStyle(e)},tt=new RegExp(Q.join("|"),"i");function nt(e,t,n){var r,i,o,a,s=Ze.test(t),u=e.style;return(n=n||et(e))&&(a=n.getPropertyValue(t)||n[t],s&&a&&(a=a.replace(pe,"$1")||void 0),""!==a||K(e)||(a=se.style(e,t)),!ae.pixelBoxStyles()&&Ke.test(a)&&tt.test(t)&&(r=u.width,i=u.minWidth,o=u.maxWidth,u.minWidth=u.maxWidth=u.width=a,a=n.width,u.width=r,u.minWidth=i,u.maxWidth=o)),void 0!==a?a+"":a}function rt(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}function it(){var e;Je&&(Qe.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",Je.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",J.appendChild(Qe).appendChild(Je),e=te.getComputedStyle(Je),Be="1%"!==e.top,Ye=12===ot(e.marginLeft),Je.style.right="60%",Ve=36===ot(e.right),Xe=36===ot(e.width),Je.style.position="absolute",Ue=12===ot(Je.offsetWidth/3),J.removeChild(Qe),Je=null)}function ot(e){return Math.round(parseFloat(e))}Qe=C.createElement("div"),(Je=C.createElement("div")).style&&(Je.style.backgroundClip="content-box",Je.cloneNode(!0).style.backgroundClip="",ae.clearCloneStyle="content-box"===Je.style.backgroundClip,se.extend(ae,{boxSizingReliable:function(){return it(),Xe},pixelBoxStyles:function(){return it(),Ve},pixelPosition:function(){return it(),Be},reliableMarginLeft:function(){return it(),Ye},scrollboxSize:function(){return it(),Ue},reliableTrDimensions:function(){var e,t,n,r;return null==Ge&&(e=C.createElement("table"),t=C.createElement("tr"),n=C.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",t.style.cssText="box-sizing:content-box;border:1px solid",t.style.height="1px",n.style.height="9px",n.style.display="block",J.appendChild(e).appendChild(t).appendChild(n),r=te.getComputedStyle(t),Ge=parseInt(r.height,10)+parseInt(r.borderTopWidth,10)+parseInt(r.borderBottomWidth,10)===t.offsetHeight,J.removeChild(e)),Ge}}));var at=["Webkit","Moz","ms"],st=C.createElement("div").style,ut={};function lt(e){return se.cssProps[e]||ut[e]||(e in st?e:ut[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=at.length;n--;)if((e=at[n]+t)in st)return e}(e)||e)}var ct=/^(none|table(?!-c[ea]).+)/,ft={position:"absolute",visibility:"hidden",display:"block"},dt={letterSpacing:"0",fontWeight:"400"};function pt(e,t,n){var r=Y.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function ht(e,t,n,r,i,o){var a="width"===t?1:0,s=0,u=0,l=0;if(n===(r?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(l+=se.css(e,n+Q[a],!0,i)),r?("content"===n&&(u-=se.css(e,"padding"+Q[a],!0,i)),"margin"!==n&&(u-=se.css(e,"border"+Q[a]+"Width",!0,i))):(u+=se.css(e,"padding"+Q[a],!0,i),"padding"!==n?u+=se.css(e,"border"+Q[a]+"Width",!0,i):s+=se.css(e,"border"+Q[a]+"Width",!0,i));return!r&&0<=o&&(u+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-u-s-.5))||0),u+l}function gt(e,t,n){var r=et(e),i=(!ae.boxSizingReliable()||n)&&"border-box"===se.css(e,"boxSizing",!1,r),o=i,a=nt(e,t,r),s="offset"+t[0].toUpperCase()+t.slice(1);if(Ke.test(a)){if(!n)return a;a="auto"}return(!ae.boxSizingReliable()&&i||!ae.reliableTrDimensions()&&ue(e,"tr")||"auto"===a||!parseFloat(a)&&"inline"===se.css(e,"display",!1,r))&&e.getClientRects().length&&(i="border-box"===se.css(e,"boxSizing",!1,r),(o=s in e)&&(a=e[s])),(a=parseFloat(a)||0)+ht(e,t,n||(i?"border":"content"),o,r,a)+"px"}function vt(e,t,n,r,i){return new vt.prototype.init(e,t,n,r,i)}se.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=nt(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=W(t),u=Ze.test(t),l=e.style;if(u||(t=lt(s)),a=se.cssHooks[t]||se.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(i=a.get(e,!1,r))?i:l[t];"string"===(o=_typeof(n))&&(i=Y.exec(n))&&i[1]&&(n=ve(e,t,i),o="number"),null!=n&&n==n&&("number"!==o||u||(n+=i&&i[3]||(se.cssNumber[s]?"":"px")),ae.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,r))||(u?l.setProperty(t,n):l[t]=n))}},css:function(e,t,n,r){var i,o,a,s=W(t);return Ze.test(t)||(t=lt(s)),(a=se.cssHooks[t]||se.cssHooks[s])&&"get"in a&&(i=a.get(e,!0,n)),void 0===i&&(i=nt(e,t,r)),"normal"===i&&t in dt&&(i=dt[t]),""===n||n?(o=parseFloat(i),!0===n||isFinite(o)?o||0:i):i}}),se.each(["height","width"],function(e,u){se.cssHooks[u]={get:function(e,t,n){if(t)return!ct.test(se.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?gt(e,u,n):$e(e,ft,function(){return gt(e,u,n)})},set:function(e,t,n){var r,i=et(e),o=!ae.scrollboxSize()&&"absolute"===i.position,a=(o||n)&&"border-box"===se.css(e,"boxSizing",!1,i),s=n?ht(e,u,n,a,i):0;return a&&o&&(s-=Math.ceil(e["offset"+u[0].toUpperCase()+u.slice(1)]-parseFloat(i[u])-ht(e,u,"border",!1,i)-.5)),s&&(r=Y.exec(t))&&"px"!==(r[3]||"px")&&(e.style[u]=t,t=se.css(e,u)),pt(0,t,s)}}}),se.cssHooks.marginLeft=rt(ae.reliableMarginLeft,function(e,t){if(t)return(parseFloat(nt(e,"marginLeft"))||e.getBoundingClientRect().left-$e(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),se.each({margin:"",padding:"",border:"Width"},function(i,o){se.cssHooks[i+o]={expand:function(e){for(var t=0,n={},r="string"==typeof e?e.split(" "):[e];t<4;t++)n[i+Q[t]+o]=r[t]||r[t-2]||r[0];return n}},"margin"!==i&&(se.cssHooks[i+o].set=pt)}),se.fn.extend({css:function(e,t){return z(this,function(e,t,n){var r,i,o={},a=0;if(Array.isArray(t)){for(r=et(e),i=t.length;a<i;a++)o[t[a]]=se.css(e,t[a],!1,r);return o}return void 0!==n?se.style(e,t,n):se.css(e,t)},e,t,1<arguments.length)}}),((se.Tween=vt).prototype={constructor:vt,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||se.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(se.cssNumber[n]?"":"px")},cur:function(){var e=vt.propHooks[this.prop];return e&&e.get?e.get(this):vt.propHooks._default.get(this)},run:function(e){var t,n=vt.propHooks[this.prop];return this.options.duration?this.pos=t=se.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):vt.propHooks._default.set(this),this}}).init.prototype=vt.prototype,(vt.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=se.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){se.fx.step[e.prop]?se.fx.step[e.prop](e):1!==e.elem.nodeType||!se.cssHooks[e.prop]&&null==e.elem.style[lt(e.prop)]?e.elem[e.prop]=e.now:se.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=vt.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},se.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},se.fx=vt.prototype.init,se.fx.step={};var yt,mt,xt,bt,wt=/^(?:toggle|show|hide)$/,Tt=/queueHooks$/;function Ct(){mt&&(!1===C.hidden&&te.requestAnimationFrame?te.requestAnimationFrame(Ct):te.setTimeout(Ct,se.fx.interval),se.fx.tick())}function Et(){return te.setTimeout(function(){yt=void 0}),yt=Date.now()}function St(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)i["margin"+(n=Q[r])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function At(e,t,n){for(var r,i=(kt.tweeners[t]||[]).concat(kt.tweeners["*"]),o=0,a=i.length;o<a;o++)if(r=i[o].call(n,t,e))return r}function kt(o,e,t){var n,a,r=0,i=kt.prefilters.length,s=se.Deferred().always(function(){delete u.elem}),u=function(){if(a)return!1;for(var e=yt||Et(),t=Math.max(0,l.startTime+l.duration-e),n=1-(t/l.duration||0),r=0,i=l.tweens.length;r<i;r++)l.tweens[r].run(n);return s.notifyWith(o,[l,n,t]),n<1&&i?t:(i||s.notifyWith(o,[l,1,0]),s.resolveWith(o,[l]),!1)},l=s.promise({elem:o,props:se.extend({},e),opts:se.extend(!0,{specialEasing:{},easing:se.easing._default},t),originalProperties:e,originalOptions:t,startTime:yt||Et(),duration:t.duration,tweens:[],createTween:function(e,t){var n=se.Tween(o,l.opts,e,t,l.opts.specialEasing[e]||l.opts.easing);return l.tweens.push(n),n},stop:function(e){var t=0,n=e?l.tweens.length:0;if(a)return this;for(a=!0;t<n;t++)l.tweens[t].run(1);return e?(s.notifyWith(o,[l,1,0]),s.resolveWith(o,[l,e])):s.rejectWith(o,[l,e]),this}}),c=l.props;for(function(e,t){var n,r,i,o,a;for(n in e)if(i=t[r=W(n)],o=e[n],Array.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),(a=se.cssHooks[r])&&"expand"in a)for(n in o=a.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}(c,l.opts.specialEasing);r<i;r++)if(n=kt.prefilters[r].call(l,o,c,l.opts))return y(n.stop)&&(se._queueHooks(l.elem,l.opts.queue).stop=n.stop.bind(n)),n;return se.map(c,At,l),y(l.opts.start)&&l.opts.start.call(o,l),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always),se.fx.timer(se.extend(u,{elem:o,anim:l,queue:l.opts.queue})),l}se.Animation=se.extend(kt,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return ve(n.elem,e,Y.exec(t),n),n}]},tweener:function(e,t){for(var n,r=0,i=(e=y(e)?(t=e,["*"]):e.match(D)).length;r<i;r++)n=e[r],kt.tweeners[n]=kt.tweeners[n]||[],kt.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var r,i,o,a,s,u,l,c,f="width"in t||"height"in t,d=this,p={},h=e.style,g=e.nodeType&&ee(e),v=$.get(e,"fxshow");for(r in n.queue||(null==(a=se._queueHooks(e,"fx")).unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,d.always(function(){d.always(function(){a.unqueued--,se.queue(e,"fx").length||a.empty.fire()})})),t)if(i=t[r],wt.test(i)){if(delete t[r],o=o||"toggle"===i,i===(g?"hide":"show")){if("show"!==i||!v||void 0===v[r])continue;g=!0}p[r]=v&&v[r]||se.style(e,r)}if((u=!se.isEmptyObject(t))||!se.isEmptyObject(p))for(r in f&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],null==(l=v&&v.display)&&(l=$.get(e,"display")),"none"===(c=se.css(e,"display"))&&(l?c=l:(me([e],!0),l=e.style.display||l,c=se.css(e,"display"),me([e]))),("inline"===c||"inline-block"===c&&null!=l)&&"none"===se.css(e,"float")&&(u||(d.done(function(){h.display=l}),null==l&&(c=h.display,l="none"===c?"":c)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",d.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),u=!1,p)u||(v?"hidden"in v&&(g=v.hidden):v=$.access(e,"fxshow",{display:l}),o&&(v.hidden=!g),g&&me([e],!0),d.done(function(){for(r in g||me([e]),$.remove(e,"fxshow"),p)se.style(e,r,p[r])})),u=At(g?v[r]:0,r,d),r in v||(v[r]=u.start,g&&(u.end=u.start,u.start=0))}],prefilter:function(e,t){t?kt.prefilters.unshift(e):kt.prefilters.push(e)}}),se.speed=function(e,t,n){var r=e&&"object"==_typeof(e)?se.extend({},e):{complete:n||!n&&t||y(e)&&e,duration:e,easing:n&&t||t&&!y(t)&&t};return se.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in se.fx.speeds?r.duration=se.fx.speeds[r.duration]:r.duration=se.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){y(r.old)&&r.old.call(this),r.queue&&se.dequeue(this,r.queue)},r},se.fn.extend({fadeTo:function(e,t,n,r){return this.filter(ee).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(t,e,n,r){function i(){var e=kt(this,se.extend({},t),a);(o||$.get(this,"finish"))&&e.stop(!0)}var o=se.isEmptyObject(t),a=se.speed(e,n,r);return i.finish=i,o||!1===a.queue?this.each(i):this.queue(a.queue,i)},stop:function(i,e,o){function a(e){var t=e.stop;delete e.stop,t(o)}return"string"!=typeof i&&(o=e,e=i,i=void 0),e&&this.queue(i||"fx",[]),this.each(function(){var e=!0,t=null!=i&&i+"queueHooks",n=se.timers,r=$.get(this);if(t)r[t]&&r[t].stop&&a(r[t]);else for(t in r)r[t]&&r[t].stop&&Tt.test(t)&&a(r[t]);for(t=n.length;t--;)n[t].elem!==this||null!=i&&n[t].queue!==i||(n[t].anim.stop(o),e=!1,n.splice(t,1));!e&&o||se.dequeue(this,i)})},finish:function(a){return!1!==a&&(a=a||"fx"),this.each(function(){var e,t=$.get(this),n=t[a+"queue"],r=t[a+"queueHooks"],i=se.timers,o=n?n.length:0;for(t.finish=!0,se.queue(this,a,[]),r&&r.stop&&r.stop.call(this,!0),e=i.length;e--;)i[e].elem===this&&i[e].queue===a&&(i[e].anim.stop(!0),i.splice(e,1));for(e=0;e<o;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),se.each(["toggle","show","hide"],function(e,r){var i=se.fn[r];se.fn[r]=function(e,t,n){return null==e||"boolean"==typeof e?i.apply(this,arguments):this.animate(St(r,!0),e,t,n)}}),se.each({slideDown:St("show"),slideUp:St("hide"),slideToggle:St("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,r){se.fn[e]=function(e,t,n){return this.animate(r,e,t,n)}}),se.timers=[],se.fx.tick=function(){var e,t=0,n=se.timers;for(yt=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||se.fx.stop(),yt=void 0},se.fx.timer=function(e){se.timers.push(e),se.fx.start()},se.fx.interval=13,se.fx.start=function(){mt||(mt=!0,Ct())},se.fx.stop=function(){mt=null},se.fx.speeds={slow:600,fast:200,_default:400},se.fn.delay=function(r,e){return r=se.fx&&se.fx.speeds[r]||r,e=e||"fx",this.queue(e,function(e,t){var n=te.setTimeout(e,r);t.stop=function(){te.clearTimeout(n)}})},xt=C.createElement("input"),bt=C.createElement("select").appendChild(C.createElement("option")),xt.type="checkbox",ae.checkOn=""!==xt.value,ae.optSelected=bt.selected,(xt=C.createElement("input")).value="t",xt.type="radio",ae.radioValue="t"===xt.value;var jt,Dt=se.expr.attrHandle;se.fn.extend({attr:function(e,t){return z(this,se.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){se.removeAttr(this,e)})}}),se.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?se.prop(e,t,n):(1===o&&se.isXMLDoc(e)||(i=se.attrHooks[t.toLowerCase()]||(se.expr.match.bool.test(t)?jt:void 0)),void 0!==n?null===n?void se.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):!(i&&"get"in i&&null!==(r=i.get(e,t)))&&null==(r=se.find.attr(e,t))?void 0:r)},attrHooks:{type:{set:function(e,t){if(!ae.radioValue&&"radio"===t&&ue(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(D);if(i&&1===e.nodeType)for(;n=i[r++];)e.removeAttribute(n)}}),jt={set:function(e,t,n){return!1===t?se.removeAttr(e,n):e.setAttribute(n,n),n}},se.each(se.expr.match.bool.source.match(/\w+/g),function(e,t){var a=Dt[t]||se.find.attr;Dt[t]=function(e,t,n){var r,i,o=t.toLowerCase();return n||(i=Dt[o],Dt[o]=r,r=null!=a(e,t,n)?o:null,Dt[o]=i),r}});var Nt=/^(?:input|select|textarea|button)$/i,Lt=/^(?:a|area)$/i;function qt(e){return(e.match(D)||[]).join(" ")}function Ht(e){return e.getAttribute&&e.getAttribute("class")||""}function Ot(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(D)||[]}se.fn.extend({prop:function(e,t){return z(this,se.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[se.propFix[e]||e]})}}),se.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&se.isXMLDoc(e)||(t=se.propFix[t]||t,i=se.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=se.find.attr(e,"tabindex");return t?parseInt(t,10):Nt.test(e.nodeName)||Lt.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),ae.optSelected||(se.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),se.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){se.propFix[this.toLowerCase()]=this}),se.fn.extend({addClass:function(t){var e,n,r,i,o,a;return y(t)?this.each(function(e){se(this).addClass(t.call(this,e,Ht(this)))}):(e=Ot(t)).length?this.each(function(){if(r=Ht(this),n=1===this.nodeType&&" "+qt(r)+" "){for(o=0;o<e.length;o++)i=e[o],n.indexOf(" "+i+" ")<0&&(n+=i+" ");a=qt(n),r!==a&&this.setAttribute("class",a)}}):this},removeClass:function(t){var e,n,r,i,o,a;return y(t)?this.each(function(e){se(this).removeClass(t.call(this,e,Ht(this)))}):arguments.length?(e=Ot(t)).length?this.each(function(){if(r=Ht(this),n=1===this.nodeType&&" "+qt(r)+" "){for(o=0;o<e.length;o++)for(i=e[o];-1<n.indexOf(" "+i+" ");)n=n.replace(" "+i+" "," ");a=qt(n),r!==a&&this.setAttribute("class",a)}}):this:this.attr("class","")},toggleClass:function(t,n){var e,r,i,o,a=_typeof(t),s="string"===a||Array.isArray(t);return y(t)?this.each(function(e){se(this).toggleClass(t.call(this,e,Ht(this),n),n)}):"boolean"==typeof n&&s?n?this.addClass(t):this.removeClass(t):(e=Ot(t),this.each(function(){if(s)for(o=se(this),i=0;i<e.length;i++)r=e[i],o.hasClass(r)?o.removeClass(r):o.addClass(r);else void 0!==t&&"boolean"!==a||((r=Ht(this))&&$.set(this,"__className__",r),this.setAttribute&&this.setAttribute("class",!r&&!1!==t&&$.get(this,"__className__")||""))}))},hasClass:function(e){for(var t,n=0,r=" "+e+" ";t=this[n++];)if(1===t.nodeType&&-1<(" "+qt(Ht(t))+" ").indexOf(r))return!0;return!1}});var Mt=/\r/g;se.fn.extend({val:function(n){var r,e,i,t=this[0];return arguments.length?(i=y(n),this.each(function(e){var t;1===this.nodeType&&(null==(t=i?n.call(this,e,se(this).val()):n)?t="":"number"==typeof t?t+="":Array.isArray(t)&&(t=se.map(t,function(e){return null==e?"":e+""})),(r=se.valHooks[this.type]||se.valHooks[this.nodeName.toLowerCase()])&&"set"in r&&void 0!==r.set(this,t,"value")||(this.value=t))})):t?(r=se.valHooks[t.type]||se.valHooks[t.nodeName.toLowerCase()])&&"get"in r&&void 0!==(e=r.get(t,"value"))?e:"string"==typeof(e=t.value)?e.replace(Mt,""):null==e?"":e:void 0}}),se.extend({valHooks:{option:{get:function(e){var t=se.find.attr(e,"value");return null!=t?t:qt(se.text(e))}},select:{get:function(e){for(var t,n,r=e.options,i=e.selectedIndex,o="select-one"===e.type,a=o?null:[],s=o?i+1:r.length,u=i<0?s:o?i:0;u<s;u++)if(((n=r[u]).selected||u===i)&&!n.disabled&&(!n.parentNode.disabled||!ue(n.parentNode,"optgroup"))){if(t=se(n).val(),o)return t;a.push(t)}return a},set:function(e,t){for(var n,r,i=e.options,o=se.makeArray(t),a=i.length;a--;)((r=i[a]).selected=-1<se.inArray(se.valHooks.option.get(r),o))&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),se.each(["radio","checkbox"],function(){se.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<se.inArray(se(e).val(),t)}},ae.checkOn||(se.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var zt=te.location,Pt={guid:Date.now()},Rt=/\?/;se.parseXML=function(e){var t,n;if(!e||"string"!=typeof e)return null;try{t=(new te.DOMParser).parseFromString(e,"text/xml")}catch(e){}return n=t&&t.getElementsByTagName("parsererror")[0],t&&!n||se.error("Invalid XML: "+(n?se.map(n.childNodes,function(e){return e.textContent}).join("\n"):e)),t};function _t(e){e.stopPropagation()}var Wt=/^(?:focusinfocus|focusoutblur)$/;se.extend(se.event,{trigger:function(e,t,n,r){var i,o,a,s,u,l,c,f=[n||C],d=oe.call(e,"type")?e.type:e,p=oe.call(e,"namespace")?e.namespace.split("."):[],h=c=o=n=n||C;if(3!==n.nodeType&&8!==n.nodeType&&!Wt.test(d+se.event.triggered)&&(-1<d.indexOf(".")&&(d=(p=d.split(".")).shift(),p.sort()),s=d.indexOf(":")<0&&"on"+d,(e=e[se.expando]?e:new se.Event(d,"object"==_typeof(e)&&e)).isTrigger=r?2:3,e.namespace=p.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:se.makeArray(t,[e]),l=se.event.special[d]||{},r||!l.trigger||!1!==l.trigger.apply(n,t))){if(!r&&!l.noBubble&&!g(n)){for(a=l.delegateType||d,Wt.test(a+d)||(h=h.parentNode);h;h=h.parentNode)f.push(h),o=h;o===(n.ownerDocument||C)&&f.push(o.defaultView||o.parentWindow||te)}for(i=0;(h=f[i++])&&!e.isPropagationStopped();)c=h,e.type=1<i?a:l.bindType||d,(u=($.get(h,"events")||Object.create(null))[e.type]&&$.get(h,"handle"))&&u.apply(h,t),(u=s&&h[s])&&u.apply&&F(h)&&(e.result=u.apply(h,t),!1===e.result&&e.preventDefault());return e.type=d,r||e.isDefaultPrevented()||l._default&&!1!==l._default.apply(f.pop(),t)||!F(n)||s&&y(n[d])&&!g(n)&&((o=n[s])&&(n[s]=null),se.event.triggered=d,e.isPropagationStopped()&&c.addEventListener(d,_t),n[d](),e.isPropagationStopped()&&c.removeEventListener(d,_t),se.event.triggered=void 0,o&&(n[s]=o)),e.result}},simulate:function(e,t,n){var r=se.extend(new se.Event,n,{type:e,isSimulated:!0});se.event.trigger(r,null,t)}}),se.fn.extend({trigger:function(e,t){return this.each(function(){se.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return se.event.trigger(e,t,n,!0)}});var Ft=/\[\]$/,It=/\r?\n/g,$t=/^(?:submit|button|image|reset|file)$/i,Bt=/^(?:input|select|textarea|keygen)/i;se.param=function(e,t){function n(e,t){var n=y(t)?t():t;i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)}var r,i=[];if(null==e)return"";if(Array.isArray(e)||e.jquery&&!se.isPlainObject(e))se.each(e,function(){n(this.name,this.value)});else for(r in e)!function n(r,e,i,o){if(Array.isArray(e))se.each(e,function(e,t){i||Ft.test(r)?o(r,t):n(r+"["+("object"==_typeof(t)&&null!=t?e:"")+"]",t,i,o)});else if(i||"object"!==x(e))o(r,e);else for(var t in e)n(r+"["+t+"]",e[t],i,o)}(r,e[r],t,n);return i.join("&")},se.fn.extend({serialize:function(){return se.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=se.prop(this,"elements");return e?se.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!se(this).is(":disabled")&&Bt.test(this.nodeName)&&!$t.test(e)&&(this.checked||!be.test(e))}).map(function(e,t){var n=se(this).val();return null==n?null:Array.isArray(n)?se.map(n,function(e){return{name:t.name,value:e.replace(It,"\r\n")}}):{name:t.name,value:n.replace(It,"\r\n")}}).get()}});var Xt=/%20/g,Ut=/#.*$/,Vt=/([?&])_=[^&]*/,Gt=/^(.*?):[ \t]*([^\r\n]*)$/gm,Yt=/^(?:GET|HEAD)$/,Qt=/^\/\//,Jt={},Kt={},Zt="*/".concat("*"),en=C.createElement("a");function tn(o){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,r=0,i=e.toLowerCase().match(D)||[];if(y(t))for(;n=i[r++];)"+"===n[0]?(n=n.slice(1)||"*",(o[n]=o[n]||[]).unshift(t)):(o[n]=o[n]||[]).push(t)}}function nn(t,i,o,a){var s={},u=t===Kt;function l(e){var r;return s[e]=!0,se.each(t[e]||[],function(e,t){var n=t(i,o,a);return"string"!=typeof n||u||s[n]?u?!(r=n):void 0:(i.dataTypes.unshift(n),l(n),!1)}),r}return l(i.dataTypes[0])||!s["*"]&&l("*")}function rn(e,t){var n,r,i=se.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r=r||{})[n]=t[n]);return r&&se.extend(!0,e,r),e}en.href=zt.href,se.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:zt.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(zt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Zt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":se.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?rn(rn(e,se.ajaxSettings),t):rn(se.ajaxSettings,e)},ajaxPrefilter:tn(Jt),ajaxTransport:tn(Kt),ajax:function(e,t){"object"==_typeof(e)&&(t=e,e=void 0),t=t||{};var c,f,d,n,p,r,h,g,i,o,v=se.ajaxSetup({},t),y=v.context||v,m=v.context&&(y.nodeType||y.jquery)?se(y):se.event,x=se.Deferred(),b=se.Callbacks("once memory"),w=v.statusCode||{},a={},s={},u="canceled",T={readyState:0,getResponseHeader:function(e){var t;if(h){if(!n)for(n={};t=Gt.exec(d);)n[t[1].toLowerCase()+" "]=(n[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=n[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return h?d:null},setRequestHeader:function(e,t){return null==h&&(e=s[e.toLowerCase()]=s[e.toLowerCase()]||e,a[e]=t),this},overrideMimeType:function(e){return null==h&&(v.mimeType=e),this},statusCode:function(e){if(e)if(h)T.always(e[T.status]);else for(var t in e)w[t]=[w[t],e[t]];return this},abort:function(e){var t=e||u;return c&&c.abort(t),l(0,t),this}};if(x.promise(T),v.url=((e||v.url||zt.href)+"").replace(Qt,zt.protocol+"//"),v.type=t.method||t.type||v.method||v.type,v.dataTypes=(v.dataType||"*").toLowerCase().match(D)||[""],null==v.crossDomain){r=C.createElement("a");try{r.href=v.url,r.href=r.href,v.crossDomain=en.protocol+"//"+en.host!=r.protocol+"//"+r.host}catch(e){v.crossDomain=!0}}if(v.data&&v.processData&&"string"!=typeof v.data&&(v.data=se.param(v.data,v.traditional)),nn(Jt,v,t,T),h)return T;for(i in(g=se.event&&v.global)&&0==se.active++&&se.event.trigger("ajaxStart"),v.type=v.type.toUpperCase(),v.hasContent=!Yt.test(v.type),f=v.url.replace(Ut,""),v.hasContent?v.data&&v.processData&&0===(v.contentType||"").indexOf("application/x-www-form-urlencoded")&&(v.data=v.data.replace(Xt,"+")):(o=v.url.slice(f.length),v.data&&(v.processData||"string"==typeof v.data)&&(f+=(Rt.test(f)?"&":"?")+v.data,delete v.data),!1===v.cache&&(f=f.replace(Vt,"$1"),o=(Rt.test(f)?"&":"?")+"_="+Pt.guid+++o),v.url=f+o),v.ifModified&&(se.lastModified[f]&&T.setRequestHeader("If-Modified-Since",se.lastModified[f]),se.etag[f]&&T.setRequestHeader("If-None-Match",se.etag[f])),(v.data&&v.hasContent&&!1!==v.contentType||t.contentType)&&T.setRequestHeader("Content-Type",v.contentType),T.setRequestHeader("Accept",v.dataTypes[0]&&v.accepts[v.dataTypes[0]]?v.accepts[v.dataTypes[0]]+("*"!==v.dataTypes[0]?", "+Zt+"; q=0.01":""):v.accepts["*"]),v.headers)T.setRequestHeader(i,v.headers[i]);if(v.beforeSend&&(!1===v.beforeSend.call(y,T,v)||h))return T.abort();if(u="abort",b.add(v.complete),T.done(v.success),T.fail(v.error),c=nn(Kt,v,t,T)){if(T.readyState=1,g&&m.trigger("ajaxSend",[T,v]),h)return T;v.async&&0<v.timeout&&(p=te.setTimeout(function(){T.abort("timeout")},v.timeout));try{h=!1,c.send(a,l)}catch(e){if(h)throw e;l(-1,e)}}else l(-1,"No Transport");function l(e,t,n,r){var i,o,a,s,u,l=t;h||(h=!0,p&&te.clearTimeout(p),c=void 0,d=r||"",T.readyState=0<e?4:0,i=200<=e&&e<300||304===e,n&&(s=function(e,t,n){for(var r,i,o,a,s=e.contents,u=e.dataTypes;"*"===u[0];)u.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in s)if(s[i]&&s[i].test(r)){u.unshift(i);break}if(u[0]in n)o=u[0];else{for(i in n){if(!u[0]||e.converters[i+" "+u[0]]){o=i;break}a=a||i}o=o||a}if(o)return o!==u[0]&&u.unshift(o),n[o]}(v,T,n)),!i&&-1<se.inArray("script",v.dataTypes)&&se.inArray("json",v.dataTypes)<0&&(v.converters["text script"]=function(){}),s=function(e,t,n,r){var i,o,a,s,u,l={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)l[a.toLowerCase()]=e.converters[a];for(o=c.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=c.shift())if("*"===o)o=u;else if("*"!==u&&u!==o){if(!(a=l[u+" "+o]||l["* "+o]))for(i in l)if((s=i.split(" "))[1]===o&&(a=l[u+" "+s[0]]||l["* "+s[0]])){!0===a?a=l[i]:!0!==l[i]&&(o=s[0],c.unshift(s[1]));break}if(!0!==a)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+u+" to "+o}}}return{state:"success",data:t}}(v,s,T,i),i?(v.ifModified&&((u=T.getResponseHeader("Last-Modified"))&&(se.lastModified[f]=u),(u=T.getResponseHeader("etag"))&&(se.etag[f]=u)),204===e||"HEAD"===v.type?l="nocontent":304===e?l="notmodified":(l=s.state,o=s.data,i=!(a=s.error))):(a=l,!e&&l||(l="error",e<0&&(e=0))),T.status=e,T.statusText=(t||l)+"",i?x.resolveWith(y,[o,l,T]):x.rejectWith(y,[T,l,a]),T.statusCode(w),w=void 0,g&&m.trigger(i?"ajaxSuccess":"ajaxError",[T,v,i?o:a]),b.fireWith(y,[T,l]),g&&(m.trigger("ajaxComplete",[T,v]),--se.active||se.event.trigger("ajaxStop")))}return T},getJSON:function(e,t,n){return se.get(e,t,n,"json")},getScript:function(e,t){return se.get(e,void 0,t,"script")}}),se.each(["get","post"],function(e,i){se[i]=function(e,t,n,r){return y(t)&&(r=r||n,n=t,t=void 0),se.ajax(se.extend({url:e,type:i,dataType:r,data:t,success:n},se.isPlainObject(e)&&e))}}),se.ajaxPrefilter(function(e){for(var t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),se._evalUrl=function(e,t,n){return se.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){se.globalEval(e,t,n)}})},se.fn.extend({wrapAll:function(e){var t;return this[0]&&(y(e)&&(e=e.call(this[0])),t=se(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(n){return y(n)?this.each(function(e){se(this).wrapInner(n.call(this,e))}):this.each(function(){var e=se(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=y(t);return this.each(function(e){se(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(e){return this.parent(e).not("body").each(function(){se(this).replaceWith(this.childNodes)}),this}}),se.expr.pseudos.hidden=function(e){return!se.expr.pseudos.visible(e)},se.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},se.ajaxSettings.xhr=function(){try{return new te.XMLHttpRequest}catch(e){}};var on={0:200,1223:204},an=se.ajaxSettings.xhr();ae.cors=!!an&&"withCredentials"in an,ae.ajax=an=!!an,se.ajaxTransport(function(i){var o,a;if(ae.cors||an&&!i.crossDomain)return{send:function(e,t){var n,r=i.xhr();if(r.open(i.type,i.url,i.async,i.username,i.password),i.xhrFields)for(n in i.xhrFields)r[n]=i.xhrFields[n];for(n in i.mimeType&&r.overrideMimeType&&r.overrideMimeType(i.mimeType),i.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)r.setRequestHeader(n,e[n]);o=function(e){return function(){o&&(o=a=r.onload=r.onerror=r.onabort=r.ontimeout=r.onreadystatechange=null,"abort"===e?r.abort():"error"===e?"number"!=typeof r.status?t(0,"error"):t(r.status,r.statusText):t(on[r.status]||r.status,r.statusText,"text"!==(r.responseType||"text")||"string"!=typeof r.responseText?{binary:r.response}:{text:r.responseText},r.getAllResponseHeaders()))}},r.onload=o(),a=r.onerror=r.ontimeout=o("error"),void 0!==r.onabort?r.onabort=a:r.onreadystatechange=function(){4===r.readyState&&te.setTimeout(function(){o&&a()})},o=o("abort");try{r.send(i.hasContent&&i.data||null)}catch(e){if(o)throw e}},abort:function(){o&&o()}}}),se.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),se.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return se.globalEval(e),e}}}),se.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),se.ajaxTransport("script",function(n){var r,i;if(n.crossDomain||n.scriptAttrs)return{send:function(e,t){r=se("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",i=function(e){r.remove(),i=null,e&&t("error"===e.type?404:200,e.type)}),C.head.appendChild(r[0])},abort:function(){i&&i()}}});var sn,un=[],ln=/(=)\?(?=&|$)|\?\?/;se.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=un.pop()||se.expando+"_"+Pt.guid++;return this[e]=!0,e}}),se.ajaxPrefilter("json jsonp",function(e,t,n){var r,i,o,a=!1!==e.jsonp&&(ln.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&ln.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return r=e.jsonpCallback=y(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(ln,"$1"+r):!1!==e.jsonp&&(e.url+=(Rt.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return o||se.error(r+" was not called"),o[0]},e.dataTypes[0]="json",i=te[r],te[r]=function(){o=arguments},n.always(function(){void 0===i?se(te).removeProp(r):te[r]=i,e[r]&&(e.jsonpCallback=t.jsonpCallback,un.push(r)),o&&y(i)&&i(o[0]),o=i=void 0}),"script"}),ae.createHTMLDocument=((sn=C.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===sn.childNodes.length),se.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(ae.createHTMLDocument?((r=(t=C.implementation.createHTMLDocument("")).createElement("base")).href=C.location.href,t.head.appendChild(r)):t=C),o=!n&&[],(i=w.exec(e))?[t.createElement(i[1])]:(i=je([e],t,o),o&&o.length&&se(o).remove(),se.merge([],i.childNodes)));var r,i,o},se.fn.load=function(e,t,n){var r,i,o,a=this,s=e.indexOf(" ");return-1<s&&(r=qt(e.slice(s)),e=e.slice(0,s)),y(t)?(n=t,t=void 0):t&&"object"==_typeof(t)&&(i="POST"),0<a.length&&se.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done(function(e){o=arguments,a.html(r?se("<div>").append(se.parseHTML(e)).find(r):e)}).always(n&&function(e,t){a.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},se.expr.pseudos.animated=function(t){return se.grep(se.timers,function(e){return t===e.elem}).length},se.offset={setOffset:function(e,t,n){var r,i,o,a,s,u,l=se.css(e,"position"),c=se(e),f={};"static"===l&&(e.style.position="relative"),s=c.offset(),o=se.css(e,"top"),u=se.css(e,"left"),i=("absolute"===l||"fixed"===l)&&-1<(o+u).indexOf("auto")?(a=(r=c.position()).top,r.left):(a=parseFloat(o)||0,parseFloat(u)||0),y(t)&&(t=t.call(e,n,se.extend({},s))),null!=t.top&&(f.top=t.top-s.top+a),null!=t.left&&(f.left=t.left-s.left+i),"using"in t?t.using.call(e,f):c.css(f)}},se.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){se.offset.setOffset(this,t,e)});var e,n,r=this[0];return r?r.getClientRects().length?(e=r.getBoundingClientRect(),n=r.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if("fixed"===se.css(r,"position"))t=r.getBoundingClientRect();else{for(t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===se.css(e,"position");)e=e.parentNode;e&&e!==r&&1===e.nodeType&&((i=se(e).offset()).top+=se.css(e,"borderTopWidth",!0),i.left+=se.css(e,"borderLeftWidth",!0))}return{top:t.top-i.top-se.css(r,"marginTop",!0),left:t.left-i.left-se.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===se.css(e,"position");)e=e.offsetParent;return e||J})}}),se.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,i){var o="pageYOffset"===i;se.fn[t]=function(e){return z(this,function(e,t,n){var r;return g(e)?r=e:9===e.nodeType&&(r=e.defaultView),void 0===n?r?r[i]:e[t]:void(r?r.scrollTo(o?r.pageXOffset:n,o?n:r.pageYOffset):e[t]=n)},t,e,arguments.length)}}),se.each(["top","left"],function(e,n){se.cssHooks[n]=rt(ae.pixelPosition,function(e,t){if(t)return t=nt(e,n),Ke.test(t)?se(e).position()[n]+"px":t})}),se.each({Height:"height",Width:"width"},function(a,s){se.each({padding:"inner"+a,content:s,"":"outer"+a},function(r,o){se.fn[o]=function(e,t){var n=arguments.length&&(r||"boolean"!=typeof e),i=r||(!0===e||!0===t?"margin":"border");return z(this,function(e,t,n){var r;return g(e)?0===o.indexOf("outer")?e["inner"+a]:e.document.documentElement["client"+a]:9===e.nodeType?(r=e.documentElement,Math.max(e.body["scroll"+a],r["scroll"+a],e.body["offset"+a],r["offset"+a],r["client"+a])):void 0===n?se.css(e,t,i):se.style(e,t,n,i)},s,n?e:void 0,n)}})}),se.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){se.fn[t]=function(e){return this.on(t,e)}}),se.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.on("mouseenter",e).on("mouseleave",t||e)}}),se.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){se.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}});var cn=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;se.proxy=function(e,t){var n,r,i;if("string"==typeof t&&(n=e[t],t=e,e=n),y(e))return r=re.call(arguments,2),(i=function(){return e.apply(t||this,r.concat(re.call(arguments)))}).guid=e.guid=e.guid||se.guid++,i},se.holdReady=function(e){e?se.readyWait++:se.ready(!0)},se.isArray=Array.isArray,se.parseJSON=JSON.parse,se.nodeName=ue,se.isFunction=y,se.isWindow=g,se.camelCase=W,se.type=x,se.now=Date.now,se.isNumeric=function(e){var t=se.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},se.trim=function(e){return null==e?"":(e+"").replace(cn,"$1")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return se});var fn=te.jQuery,dn=te.$;return se.noConflict=function(e){return te.$===se&&(te.$=dn),e&&te.jQuery===se&&(te.jQuery=fn),se},void 0===e&&(te.jQuery=te.$=se),se}),function(e){var t=function(u,z){"use strict";if(!z.getElementsByClassName){return}var P,R,_=z.documentElement,l=u.Date,i=u.HTMLPictureElement,c="addEventListener",W="getAttribute",F=u[c],I=u.setTimeout,f=u.requestAnimationFrame||I,d=u.requestIdleCallback,$=/^picture$/i,o=["load","error","lazyincluded","_lazyloaded"],r={},B=Array.prototype.forEach,X=function e(t,n){if(!r[n]){r[n]=new RegExp("(\\s|^)"+n+"(\\s|$)")}return r[n].test(t[W]("class")||"")&&r[n]},U=function e(t,n){if(!X(t,n)){t.setAttribute("class",(t[W]("class")||"").trim()+" "+n)}},V=function e(t,n){var r;if(r=X(t,n)){t.setAttribute("class",(t[W]("class")||"").replace(r," "))}},G=function e(t,n,r){var i=r?c:"removeEventListener";if(r){G(t,n)}o.forEach(function(e){t[i](e,n)})},Y=function e(t,n,r,i,o){var a=z.createEvent("Event");if(!r){r={}}r.instance=P;a.initEvent(n,!i,!o);a.detail=r;t.dispatchEvent(a);return a},Q=function e(t,n){var r;if(!i&&(r=u.picturefill||R.pf)){if(n&&n.src&&!t[W]("srcset")){t.setAttribute("srcset",n.src)}r({reevaluate:true,elements:[t]})}else if(n&&n.src){t.src=n.src}},J=function e(t,n){return(getComputedStyle(t,null)||{})[n]},s=function e(t,n,r){r=r||t.offsetWidth;while(r<R.minSize&&n&&!t._lazysizesWidth){r=n.offsetWidth;n=n.parentNode}return r},K=function(){var r,i;var n=[];var o=[];var a=n;var s=function e(){var t=a;a=n.length?o:n;r=true;i=false;while(t.length){t.shift()()}r=false};var e=function e(t,n){if(r&&!n){t.apply(this,arguments)}else{a.push(t);if(!i){i=true;(z.hidden?I:f)(s)}}};e._lsFlush=s;return e}(),Z=function e(n,t){return t?function(){K(n)}:function(){var e=this;var t=arguments;K(function(){n.apply(e,t)})}},e=function e(t){var n;var r=0;var i=R.throttleDelay;var o=R.ricTimeout;var a=function e(){n=false;r=l.now();t()};var s=d&&o>49?function(){d(a,{timeout:o});if(o!==R.ricTimeout){o=R.ricTimeout}}:Z(function(){I(a)},true);return function(e){var t;if(e=e===true){o=33}if(n){return}n=true;t=i-(l.now()-r);if(t<0){t=0}if(e||t<9){s()}else{I(s,t)}}},ee=function e(t){var n,r;var i=99;var o=function e(){n=null;t()};var a=function e(){var t=l.now()-r;if(t<i){I(a,i-t)}else{(d||o)(o)}};return function(){r=l.now();if(!n){n=I(a,i)}}};(function(){var e;var t={lazyClass:"lazyload",loadedClass:"lazyloaded",loadingClass:"lazyloading",preloadClass:"lazypreload",errorClass:"lazyerror",autosizesClass:"lazyautosizes",srcAttr:"data-src",srcsetAttr:"data-srcset",sizesAttr:"data-sizes",minSize:40,customMedia:{},init:true,expFactor:1.5,hFac:.8,loadMode:2,loadHidden:true,ricTimeout:0,throttleDelay:125};R=u.lazySizesConfig||u.lazysizesConfig||{};for(e in t){if(!(e in R)){R[e]=t[e]}}u.lazySizesConfig=R;I(function(){if(R.init){n()}})})();var t=function(){var h,g,f,v,n;var y,m,x,b,w,T,C;var a=/^img$/i;var d=/^iframe$/i;var E="onscroll"in u&&!/(gle|ing)bot/.test(navigator.userAgent);var S=0;var A=0;var k=0;var j=-1;var p=function e(t){k--;if(!t||k<0||!t.target){k=0}};var D=function e(t){if(C==null){C=J(z.body,"visibility")=="hidden"}return C||J(t.parentNode,"visibility")!="hidden"&&J(t,"visibility")!="hidden"};var N=function e(t,n){var r;var i=t;var o=D(t);x-=n;T+=n;b-=n;w+=n;while(o&&(i=i.offsetParent)&&i!=z.body&&i!=_){o=(J(i,"opacity")||1)>0;if(o&&J(i,"overflow")!="visible"){r=i.getBoundingClientRect();o=w>r.left&&b<r.right&&T>r.top-1&&x<r.bottom+1}}return o};var t=function e(){var t,n,r,i,o,a,s,u,l,c,f,d;var p=P.elements;if((v=R.loadMode)&&k<8&&(t=p.length)){n=0;j++;c=!R.expand||R.expand<1?_.clientHeight>500&&_.clientWidth>500?500:370:R.expand;P._defEx=c;f=c*R.expFactor;d=R.hFac;C=null;if(A<f&&k<1&&j>2&&v>2&&!z.hidden){A=f;j=0}else if(v>1&&j>1&&k<6){A=c}else{A=S}for(;n<t;n++){if(!p[n]||p[n]._lazyRace){continue}if(!E){M(p[n]);continue}if(!(u=p[n][W]("data-expand"))||!(a=u*1)){a=A}if(l!==a){y=innerWidth+a*d;m=innerHeight+a;s=a*-1;l=a}r=p[n].getBoundingClientRect();if((T=r.bottom)>=s&&(x=r.top)<=m&&(w=r.right)>=s*d&&(b=r.left)<=y&&(T||w||b||x)&&(R.loadHidden||D(p[n]))&&(g&&k<3&&!u&&(v<3||j<4)||N(p[n],a))){M(p[n]);o=true;if(k>9){break}}else if(!o&&g&&!i&&k<4&&j<4&&v>2&&(h[0]||R.preloadAfterLoad)&&(h[0]||!u&&(T||w||b||x||p[n][W](R.sizesAttr)!="auto"))){i=h[0]||p[n]}}if(i&&!o){M(i)}}};var r=e(t);var L=function e(t){var n=t.target;if(n._lazyCache){delete n._lazyCache;return}p(t);U(n,R.loadedClass);V(n,R.loadingClass);G(n,q);Y(n,"lazyloaded")};var i=Z(L);var q=function e(t){i({target:t.target})};var H=function e(t,n){try{t.contentWindow.location.replace(n)}catch(e){t.src=n}};var O=function e(t){var n;var r=t[W](R.srcsetAttr);if(n=R.customMedia[t[W]("data-media")||t[W]("media")]){t.setAttribute("media",n)}if(r){t.setAttribute("srcset",r)}};var s=Z(function(t,e,n,r,i){var o,a,s,u,l,c;if(!(l=Y(t,"lazybeforeunveil",e)).defaultPrevented){if(r){if(n){U(t,R.autosizesClass)}else{t.setAttribute("sizes",r)}}a=t[W](R.srcsetAttr);o=t[W](R.srcAttr);if(i){s=t.parentNode;u=s&&$.test(s.nodeName||"")}c=e.firesLoad||"src"in t&&(a||o||u);l={target:t};U(t,R.loadingClass);if(c){clearTimeout(f);f=I(p,2500);G(t,q,true)}if(u){B.call(s.getElementsByTagName("source"),O)}if(a){t.setAttribute("srcset",a)}else if(o&&!u){if(d.test(t.nodeName)){H(t,o)}else{t.src=o}}if(i&&(a||u)){Q(t,{src:o})}}if(t._lazyRace){delete t._lazyRace}V(t,R.lazyClass);K(function(){var e=t.complete&&t.naturalWidth>1;if(!c||e){if(e){U(t,"ls-is-cached")}L(l);t._lazyCache=true;I(function(){if("_lazyCache"in t){delete t._lazyCache}},9)}},true)});var M=function e(t){var n;var r=a.test(t.nodeName);var i=r&&(t[W](R.sizesAttr)||t[W]("sizes"));var o=i=="auto";if((o||!g)&&r&&(t[W]("src")||t.srcset)&&!t.complete&&!X(t,R.errorClass)&&X(t,R.lazyClass)){return}n=Y(t,"lazyunveilread").detail;if(o){te.updateElem(t,true,t.offsetWidth)}t._lazyRace=true;k++;s(t,n,o,i,r)};var o=function e(){if(g){return}if(l.now()-n<999){I(o,999);return}var t=ee(function(){R.loadMode=3;r()});g=true;R.loadMode=3;r();F("scroll",function(){if(R.loadMode==3){R.loadMode=2}t()},true)};return{_:function e(){n=l.now();P.elements=z.getElementsByClassName(R.lazyClass);h=z.getElementsByClassName(R.lazyClass+" "+R.preloadClass);F("scroll",r,true);F("resize",r,true);if(u.MutationObserver){new MutationObserver(r).observe(_,{childList:true,subtree:true,attributes:true})}else{_[c]("DOMNodeInserted",r,true);_[c]("DOMAttrModified",r,true);setInterval(r,999)}F("hashchange",r,true);["focus","mouseover","click","load","transitionend","animationend","webkitAnimationEnd"].forEach(function(e){z[c](e,r,true)});if(/d$|^c/.test(z.readyState)){o()}else{F("load",o);z[c]("DOMContentLoaded",r);I(o,2e4)}if(P.elements.length){t();K._lsFlush()}else{r()}},checkElems:r,unveil:M}}(),te=function(){var r;var a=Z(function(e,t,n,r){var i,o,a;e._lazysizesWidth=r;r+="px";e.setAttribute("sizes",r);if($.test(t.nodeName||"")){i=t.getElementsByTagName("source");for(o=0,a=i.length;o<a;o++){i[o].setAttribute("sizes",r)}}if(!n.detail.dataAttr){Q(e,n.detail)}});var i=function e(t,n,r){var i;var o=t.parentNode;if(o){r=s(t,o,r);i=Y(t,"lazybeforesizes",{width:r,dataAttr:!!n});if(!i.defaultPrevented){r=i.detail.width;if(r&&r!==t._lazysizesWidth){a(t,o,i,r)}}}};var e=function e(){var t;var n=r.length;if(n){t=0;for(;t<n;t++){i(r[t])}}};var t=ee(e);return{_:function e(){r=z.getElementsByClassName(R.autosizesClass);F("resize",t)},checkElems:t,updateElem:i}}(),n=function e(){if(!n.i){n.i=true;te._();t._()}};return P={cfg:R,autoSizer:te,loader:t,init:n,uP:Q,aC:U,rC:V,hC:X,fire:Y,gW:s,rAF:K}}(e,e.document);e.lazySizes=t,"object"==("undefined"==typeof module?"undefined":_typeof(module))&&module.exports&&(module.exports=t)}(window),document.addEventListener("lazybeforeunveil",function(e){var t=e.target;t.addEventListener("load",function(){var e=t.nextSibling;e&&e!==t&&null!=e&&1===e.nodeType&&(void 0===e.classList.contains||e.classList.contains("lazyload-preload"))&&e.classList.add("lazyload-preload--ready")})});
//# sourceMappingURL=critical.js.map
