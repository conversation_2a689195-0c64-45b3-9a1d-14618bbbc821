.block-article-teaser {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  background-color: $light-blue-25;
  &__content {
    width: 50%;
    display: flex;
    padding: 3.73rem 6.67rem;
    order: 1;
    flex-direction: column;
    justify-content: flex-start;
    img {
      width: fit-content;
      margin-bottom: 1rem;
    }
    @include breakpoint($sub-l) {
      min-width: 100%;
      order: 2;
      padding: 3.73rem 2.67rem;
    }
  }
  &__image {
    width: 50%;
    order: 2;
    background-size: cover;
    background-position: center center;
    min-height: 200px;
    @include breakpoint($sub-l) {
      width: 100%;
      min-height: 450px;
      order: 1;
    }
    @include breakpoint($sub-s) {
      min-height: 260px;
    }
  }
  .teaser__header__title {
    font-weight: 500;
    margin-top: .75rem;
    font-size: 1.83rem;
  }
  img + &--title {
    font-size: 1.5rem;
    font-weight: 400;
  }
  &--content {
    margin-bottom: 1.5rem;
  }
  .btn {
    align-self: flex-end;
    @include button;
    width: 100%;
    text-align: center;
    color: #fff !important;
  }
  .teaser__links {
    margin-top: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    a {
      width: 45%;
      text-decoration: none;
      color: #1A57A8;
      &:hover {
        @include elementhover;
      }
    }
  }
}
.page-template-default .block-article-teaser {
  display: flex;
  flex-direction: column;
  &__content {
    width: 100%;
    order: 1;
  }
  &__image {
    width: 100%;
    order: 0;
    // min-height: 375px;
    padding-bottom:56.25%;
    @include breakpoint($sub-l) {
      // min-height: 375px;
    }
  }
}
