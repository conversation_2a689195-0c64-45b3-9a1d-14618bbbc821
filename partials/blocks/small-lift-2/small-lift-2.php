<?php
/**
 * small lift 2 items
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'small-lift-2-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'small-lift-2';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$items = get_field("block_rows");

?>

<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>"> 
    <?php
    foreach($items as $item) { ?>
        <?php if(!empty($item['group']['title']) && !empty($item['group']['body'])) { ?>
            <?php get_template_part('partials/blocks/small-lift-2/small-lift-single/single', null, ['item'=>$item['group']]); ?>
        <?php } ?>
        <?php if(!empty($item['group_2']['title']) && !empty($item['group_2']['body'])) { ?>
            <?php get_template_part('partials/blocks/small-lift-2/small-lift-single/single', null, ['item'=>$item['group_2']]); ?>
        <?php } ?>
    <?php } ?>
</div>


