/* ==========================================================================
 # Columns block
========================================================================== */

/* Front-end and back-end
----------------------------------------------- */

.editor-styles-wrapper .wp-block-columns,
.wysiwyg .wp-block-columns {

  .wp-block-column {
    //margin: 0 0 $gap;
    @include child-margin-reset;

  }

  .gform_wrapper {
    padding: 0 1.5rem;

    .top_label input.medium, .top_label select.medium {
      width: 100% !important;
    }
  }


  &.alignwide,
  &.alignfull {
    margin-top: $gap-big;
    margin-bottom: $gap-big;
  }

  &.alignfull {
    margin-left: auto;
    margin-right: auto;
  }

}

/* Only front-end
----------------------------------------------- */

.wysiwyg .wp-block-columns {

  @include breakpoint($sub-s) {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    & > * {
      flex-shrink: 0;
      width: calc(100% / 2 - $gap-small);
      .page & {
        width: 100%;
      }
    }
  }

  &.has-2-columns,
  &.columns-2 {
    @include breakpoint($s) {
      margin-bottom: $gap;
      & > * {
        margin-bottom: 0;
      }
    }
  }

  &.has-3-columns,
  &.columns-3 {
    @include breakpoint($s) {
      margin-bottom: $gap;
      & > * {
        width: calc(100% / 3 - $gap-medium);
        margin-bottom: 0;
      }
    }
  }

  &.has-4-columns,
  &.columns-4 {
    @include breakpoint($m) {
      margin-bottom: $gap;
      & > * {
        width: calc(100% / 4 - $gap-medium-large);
        margin-bottom: 0;
      }
    }
  }

  &.has-5-columns,
  &.columns-5 {
    @include breakpoint($m) {
      width: calc(100% / 3 - $gap-medium);
    }
    @include breakpoint($l) {
      margin-bottom: $gap;
      & > * {
        width: calc(100% / 5 - $gap-medium-larger);
        margin-bottom: 0;
      }
    }
  }

  &.alignwide,
  &.alignfull {
    margin-bottom: $gap-big;
  }

  /* fix responsive wp 3 column images not to show 2+1 but 3  */
  @media (min-width: 600px) and (max-width: 781px) {
    flex-wrap: nowrap!important;

    .wp-block-column:nth-child(3n) {
      // margin-left: 2em;
    }
  }
  @media (max-width: 600px) {
    flex-direction: column;
    .wp-block-column,
    .wp-block-column:nth-child(2n),
    .wp-block-column:nth-child(3n) {
      margin-left: auto;
      margin-right: auto;
      margin-bottom: 2rem;
    }
  }

}

/* Only back-end
----------------------------------------------- */

.editor-styles-wrapper .wp-block-columns {

}
