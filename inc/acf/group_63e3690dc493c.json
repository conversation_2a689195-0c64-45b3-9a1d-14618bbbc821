{"key": "group_63e3690dc493c", "title": "Block: Jäsenyys diagrammit", "fields": [{"key": "field_63ea2ecf90da6", "label": "<PERSON><PERSON><PERSON><PERSON>", "name": "title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}, {"key": "field_63ea2eda90da7", "label": "Alaotsik<PERSON>", "name": "subtitle", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}, {"key": "field_63ea2ee090da8", "label": "<PERSON><PERSON>", "name": "link", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "translations": "copy_once"}, {"key": "field_63ea3f7344895", "label": "<PERSON><PERSON><PERSON><PERSON>ot", "name": "chart_data", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "", "sub_fields": [{"key": "field_63ea402742c8b", "label": "Kaavio 1 (donits<PERSON>)", "name": "group", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_63ea3f8a44896", "label": "Yläteksti", "name": "upper-text", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}, {"key": "field_63ea3f9944897", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "percentage", "type": "number", "instructions": "Määrittelee kaaviossa näkyvän prosenttimäär<PERSON>n arvon. <PERSON><PERSON><PERSON> on donitsi, joten luvun täytyy olla prosenttiluku 0-100.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": 1, "max": 100, "step": "", "translations": "copy_once"}, {"key": "field_63ea3fd144898", "label": "<PERSON><PERSON><PERSON><PERSON>", "name": "lower-text", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}]}, {"key": "field_63ea404a42c8c", "label": "Kaavio 2 (<PERSON><PERSON><PERSON> ka<PERSON>)", "name": "group_2", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_63ea404a42c8f", "label": "Yläteksti", "name": "upper-text", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}, {"key": "field_63ea404a42c8e", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> / prosentti", "name": "percentage", "type": "text", "instructions": "Määrittää kartan vieressä näkyvän arvon. Muista lisätä prosenttimerk<PERSON>, jos luku halutaan olevan prosenttimäärä.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}, {"key": "field_63ea404a42c8d", "label": "<PERSON><PERSON><PERSON><PERSON>", "name": "lower-text", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}, {"key": "field_63fddbc06b4a0", "label": "Kartan täyttö", "name": "map_fill", "type": "radio", "instructions": "Kartan täyttö on porrastettu muutamaan osaan. Valitse tästä kuinka paljon kartta täyttyy", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"less-20": "alle 20%", "less-40": "40% (20-40%)", "less-60": "60% (40-60%)", "less-80": "80% (60-80%)", "less-100": "80-100%", "is-100": "<PERSON><PERSON><PERSON><PERSON> (100%)", "fill-helsinki": "<PERSON><PERSON> se<PERSON>u"}, "allow_null": 0, "other_choice": 0, "default_value": "", "layout": "vertical", "return_format": "value", "translations": "copy_once", "save_other_choice": 0}]}, {"key": "field_63ea405342c90", "label": "Kaavio 3 (don<PERSON><PERSON>)", "name": "group_3", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_63ea405342c93", "label": "Yläteksti", "name": "upper-text", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}, {"key": "field_63ea405342c92", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "percentage", "type": "number", "instructions": "Määrittelee kaaviossa näkyvän prosenttimäär<PERSON>n arvon. <PERSON><PERSON><PERSON> on donitsi, joten luvun täytyy olla prosenttiluku 0-100.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": 1, "max": 100, "step": "", "translations": "copy_once"}, {"key": "field_63ea405342c91", "label": "<PERSON><PERSON><PERSON><PERSON>", "name": "lower-text", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "translations": "translate"}]}]}], "location": [[{"param": "block", "operator": "==", "value": "acf/membership-charts"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "modified": 1678702064}