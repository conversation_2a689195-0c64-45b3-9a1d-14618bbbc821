{"version": 3, "file": "rss-feed/frontend.js", "mappings": ";;;;;;;;;;;;;;;;;;AAAyD;;AAEzD;AAAA;AACA,SAASM,OAAOA,CAAA,EAAG;EAClB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,4DAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,4DAAQ,CAAC,IAAI,CAAC;EAE5CC,6DAAS,CAAC,MAAM;IACf;IACA,MAAMU,aAAa,GAAG,MAAAA,CAAA,KAAY;MACjCD,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACH;QACA,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAC3B,qFACD,CAAC;QACD,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACE,IAAI,CAAC,CAAC;QAClC,MAAMC,MAAM,GAAG,IAAIC,SAAS,CAAC,CAAC;QAC9B,MAAMC,MAAM,GAAGF,MAAM,CAACG,eAAe,CAACJ,IAAI,EAAE,iBAAiB,CAAC;;QAE9D;QACA,MAAMK,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACzD,MAAMC,MAAM,GAAGJ,KAAK,CAClBK,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;UACtBC,EAAE,EAAED,KAAK;UACTE,KAAK,EAAEH,IAAI,CAACI,aAAa,CAAC,OAAO,CAAC,EAAEC,WAAW,IAAI,aAAa;UAChEC,IAAI,EAAE,IAAIC,IAAI,CACbP,IAAI,CAACI,aAAa,CAAC,SAAS,CAAC,EAAEC,WAChC,CAAC,CAACG,kBAAkB,CAAC,OAAO,CAAC;UAC7BC,OAAO,EACNT,IAAI,CAACU,oBAAoB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAEL,WAAW,IACvD,YAAY;UACbM,IAAI,EAAEX,IAAI,CAACI,aAAa,CAAC,MAAM,CAAC,EAAEC;QACnC,CAAC,CAAC;QACF;QAAA,CACCO,MAAM,CACLC,OAAO,IACP,CAAC,CAAC,UAAU,EAAE,oBAAoB,CAAC,CAACC,QAAQ,CAACD,OAAO,CAACJ,OAAO,CAC9D,CAAC;QAEF1B,WAAW,CAACe,MAAM,CAAC;MACpB,CAAC,CAAC,OAAOiB,GAAG,EAAE;QACb;QACAC,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAAEF,GAAG,CAAC;MACrD,CAAC,SAAS;QACT9B,UAAU,CAAC,KAAK,CAAC;MAClB;IACD,CAAC;IAEDC,aAAa,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACCR,sDAAA;IAAAwC,QAAA,EACElC,OAAO;IAAA;IACP;IACAN,sDAAA;MAAAwC,QAAA,EAAG;IAAwB,CAAG,CAAC;IAAA;IAE/B;IACAxC,sDAAA;MAAKyC,KAAK,EAAC,aAAa;MAAAD,QAAA,EACtBpC,QAAQ,CAACsC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACrB,GAAG,CAAEc,OAAO,iBACjCjC,uDAAA;QAAsBuC,KAAK,EAAC,iBAAiB;QAAAD,QAAA,gBAC5CxC,sDAAA;UAAIyC,KAAK,EAAC;QAA2B,CAAK,CAAC,eAC3CvC,uDAAA;UAAGuC,KAAK,EAAC,MAAM;UAAAD,QAAA,GACbL,OAAO,CAACP,IAAI,EAAC,GAAC,eAAA5B,sDAAA;YAAMyC,KAAK,EAAC,WAAW;YAAAD,QAAA,EAAC;UAAC,CAAM,CAAC,EAAC,GAAG,EAClDL,OAAO,CAACJ,OAAO;QAAA,CACd,CAAC,eACJ/B,sDAAA;UAAAwC,QAAA,eACCxC,sDAAA;YACC2C,IAAI,EAAER,OAAO,CAACF,IAAK;YACnBW,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YAAAL,QAAA,EAExBL,OAAO,CAACV;UAAK,CACZ;QAAC,CACD,CAAC;MAAA,GAdIU,OAAO,CAACX,EAeb,CACL;IAAC,CACE;EACL,CACG,CAAC;AAER;AAEA,iEAAerB,OAAO;;;;;;;;;;ACpFtB;;;;;;;;;;ACAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;ACN4C;AACT;;AAEnC;AAAA;AACA,SAAS4C,eAAeA,CAAA,EAAG;EAC1B,oBAAO/C,sDAAA,CAACG,mDAAO,IAAE,CAAC;AACnB;;AAEA;AACA6C,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;EACnDD,QAAQ,CAAC7B,gBAAgB,CAAC,gBAAgB,CAAC,CAAC+B,OAAO,CAAEC,EAAE,IAAK;IAC3D,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,EAAE,CAACI,OAAO,CAACC,UAAU,IAAI,IAAI,CAAC;IACvDV,0DAAM,cAAC9C,sDAAA,CAAC+C,eAAe;MAACS,UAAU,EAAEJ;IAAM,CAAE,CAAC,EAAED,EAAE,CAAC;EACnD,CAAC,CAAC;AACH,CAAC,CAAC,C", "sources": ["webpack://rss-feed/./src/rss-feed/useRSSFeed.js", "webpack://rss-feed/external window [\"wp\",\"element\"]", "webpack://rss-feed/external window \"ReactJSXRuntime\"", "webpack://rss-feed/webpack/bootstrap", "webpack://rss-feed/webpack/runtime/compat get default export", "webpack://rss-feed/webpack/runtime/define property getters", "webpack://rss-feed/webpack/runtime/hasOwnProperty shorthand", "webpack://rss-feed/webpack/runtime/make namespace object", "webpack://rss-feed/./src/rss-feed/frontend.js"], "sourcesContent": ["import { useState, useEffect } from \"@wordpress/element\";\n\n// Front-end component for rendering RSS feed\nfunction RSSFeed() {\n\tconst [articles, setArticles] = useState([]);\n\tconst [loading, setLoading] = useState(true);\n\n\tuseEffect(() => {\n\t\t// Fetch RSS articles on initial component mount\n\t\tconst fetchArticles = async () => {\n\t\t\tsetLoading(true);\n\t\t\ttry {\n\t\t\t\t// Retrieve and parse the RSS feed XML\n\t\t\t\tconst response = await fetch(\n\t\t\t\t\t\"https://www.retriever-info.com/feed/2011171/helsingin-seudun-kauppakamari/index.xml\",\n\t\t\t\t);\n\t\t\t\tconst text = await response.text();\n\t\t\t\tconst parser = new DOMParser();\n\t\t\t\tconst xmlDoc = parser.parseFromString(text, \"application/xml\");\n\n\t\t\t\t// Extract article data from XML items\n\t\t\t\tconst items = Array.from(xmlDoc.querySelectorAll(\"item\"));\n\t\t\t\tconst parsed = items\n\t\t\t\t\t.map((item, index) => ({\n\t\t\t\t\t\tid: index,\n\t\t\t\t\t\ttitle: item.querySelector(\"title\")?.textContent || \"Ei otsikkoa\",\n\t\t\t\t\t\tdate: new Date(\n\t\t\t\t\t\t\titem.querySelector(\"pubDate\")?.textContent,\n\t\t\t\t\t\t).toLocaleDateString(\"fi-FI\"),\n\t\t\t\t\t\tcreator:\n\t\t\t\t\t\t\titem.getElementsByTagName(\"dc:creator\")[0]?.textContent ||\n\t\t\t\t\t\t\t\"Tuntematon\",\n\t\t\t\t\t\tlink: item.querySelector(\"link\")?.textContent,\n\t\t\t\t\t}))\n\t\t\t\t\t// Filter out items with creator as \"STT Info\" or \"Kauppalehti KL Nyt\"\n\t\t\t\t\t.filter(\n\t\t\t\t\t\t(article) =>\n\t\t\t\t\t\t\t![\"STT Info\", \"Kauppalehti KL Nyt\"].includes(article.creator),\n\t\t\t\t\t);\n\n\t\t\t\tsetArticles(parsed);\n\t\t\t} catch (err) {\n\t\t\t\t// Log any errors during fetch/parsing\n\t\t\t\tconsole.error(\"Virhe haettaessa RSS-syötettä:\", err);\n\t\t\t} finally {\n\t\t\t\tsetLoading(false);\n\t\t\t}\n\t\t};\n\n\t\tfetchArticles();\n\t}, []);\n\n\treturn (\n\t\t<div>\n\t\t\t{loading ? (\n\t\t\t\t// Show loading message while fetching articles\n\t\t\t\t<p>Ladataan RSS-syötettä...</p>\n\t\t\t) : (\n\t\t\t\t// Render the parsed articles\n\t\t\t\t<div class=\"rss-wrapper\">\n\t\t\t\t\t{articles.slice(0, 6).map((article) => (\n\t\t\t\t\t\t<div key={article.id} class=\"wp-block-column\">\n\t\t\t\t\t\t\t<hr class=\"wp-block-separator rss-hr\"></hr>\n\t\t\t\t\t\t\t<p class=\"meta\">\n\t\t\t\t\t\t\t\t{article.date} <span class=\"separator\">|</span>{\" \"}\n\t\t\t\t\t\t\t\t{article.creator}\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t<h3>\n\t\t\t\t\t\t\t\t<a\n\t\t\t\t\t\t\t\t\thref={article.link}\n\t\t\t\t\t\t\t\t\ttarget=\"_blank\"\n\t\t\t\t\t\t\t\t\trel=\"noopener noreferrer\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{article.title}\n\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t</h3>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t))}\n\t\t\t\t</div>\n\t\t\t)}\n\t\t</div>\n\t);\n}\n\nexport default RSSFeed;\n", "module.exports = window[\"wp\"][\"element\"];", "module.exports = window[\"ReactJSXRuntime\"];", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import { render } from \"@wordpress/element\";\nimport RSSFeed from \"./useRSSFeed\";\n\n// Front-end component for rendering RSS feed\nfunction RSSFeedFrontend() {\n\treturn <RSSFeed />;\n}\n\n// Mount the React component to all RSS feed block roots on the page\ndocument.addEventListener(\"DOMContentLoaded\", () => {\n\tdocument.querySelectorAll(\"#rss-feed-root\").forEach((el) => {\n\t\tconst attrs = JSON.parse(el.dataset.attributes || \"{}\");\n\t\trender(<RSSFeedFrontend attributes={attrs} />, el);\n\t});\n});\n"], "names": ["useState", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "RSSFeed", "articles", "setArticles", "loading", "setLoading", "fetchArticles", "response", "fetch", "text", "parser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xmlDoc", "parseFromString", "items", "Array", "from", "querySelectorAll", "parsed", "map", "item", "index", "id", "title", "querySelector", "textContent", "date", "Date", "toLocaleDateString", "creator", "getElementsByTagName", "link", "filter", "article", "includes", "err", "console", "error", "children", "class", "slice", "href", "target", "rel", "render", "RSSFeedFrontend", "document", "addEventListener", "for<PERSON>ach", "el", "attrs", "JSON", "parse", "dataset", "attributes"], "sourceRoot": ""}