<?php
/**
 * The template for displaying all single posts.
 * Used for posts and releases
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package kauppakamari
 */

get_header(); ?>

  <?php get_template_part('partials/content/breadcrumb'); ?>

  <div id="primary" class="primary primary--single">

    <main id="main" class="main">

      <?php while (have_posts()) : the_post(); ?>

        <article id="post-<?php the_ID(); ?>" <?php post_class('entry entry--post'); ?>>

          <div class="entry__image">
            <?php echo kauppakamari_get_image(get_post_thumbnail_id(), 'article'); ?>
          </div>

          <div class="entry__meta">
            <span class="entry__meta__cats">
                <?php
                /*
                $category = get_the_category();
                if ( $category[0] ) {
                    echo $category[0]->cat_name;
                }
                */
                echo kauppakamari_get_post_meta_new(false, true);

                ?>
            </span>
            <span class="entry__meta__tags">
              <?php
              /*
              //no tags 2022
              $tags = get_the_tags();
              if (!empty($tags)) :
              foreach ( $tags as $tag ) {
                if ($tag->name != 'A_Näytä karusellissa') {
                    echo '<a href="' . get_tag_link( $tag ) . '" class="tag">' . $tag->name . '</a>';
                } else {}
              }
              endif;
              */
              ?>
            </span>
          </div>

          <header class="entry__header">
            <h1><?php the_title(); ?></h1>
          </header>

          <?php $authorname = ''; ?>
          <?php if (!empty(get_field('writer_object'))
                  || !empty(get_field('photographer'))
                  || !empty(get_field('writer'))) : ?>

          <div class="entry__author">
            <?php
              $author = get_field('writer_object');
              if (!empty($author)) :
                $author = get_post($author);
            ?>
            <div class="entry__author__inner">
              <div class="entry__author__inner__img" style="background-image: url(<?php echo get_the_post_thumbnail_url($author->ID, 'medium'); ?>)"></div>
                <?php if(!empty(get_field('writer_object'))) : ?>
                  <div class="entry__author__inner__writer">
                    <div class="entry__author__inner__title">
                      Teksti
                    </div>
                    <div class="entry__author__inner__content">
                      <div class="entry__author__inner__name">
                        <?php echo get_the_title($author->ID); ?>
                        <?php $authorname = get_the_title($author->ID) ?: ''; ?>
                      </div>
                      <?php if(!empty(get_field('title', $author->ID))) : ?>
                      <div class="entry__author__inner__info">
                        <?php echo get_field('title', $author->ID); ?>
                      </div>
                      <?php endif; ?>
                    </div>
                  </div>
                <?php endif; ?>
                <?php if (!empty(get_field('photographer'))) : ?>
                <div class="entry__author__inner__photographer">
                  <div class="entry__author__inner__title">
                    Kuva
                  </div>
                  <div class="entry__author__inner__content">
                    <?php echo get_field('photographer'); ?>
                  </div>
                </div>
                <?php endif; ?>
              </div>
            <?php else : ?>
              <div class="entry__author__inner">
                <?php if(!empty(get_field('writer'))) : ?>
                  <div class="entry__author__inner__writer">
                    <div class="entry__author__inner__title">
                      Teksti
                    </div>
                    <div class="entry__author__inner__content">
                      <?php echo get_field('writer'); ?>
                      <?php $authorname = get_field('writer') ?: ''; ?>
                    </div>
                  </div>
                <?php endif; ?>
                <?php if(!empty(get_field('photographer'))) : ?>
                  <div class="entry__author__inner__photographer">
                    <div class="entry__author__inner__title">
                      Kuva
                    </div>
                    <div class="entry__author__inner__content">
                      <?php echo get_field('photographer'); ?>
                    </div>
                  </div>
                <?php endif; ?>
              </div>
            <?php endif; ?>
          </div>

          <?php endif; ?>

          <div class="entry_published">
            <div class="entry_published__inner">
              <span class="entry_published__dateheader">
                <?php if (!get_field('hide_date')) : ?>
                Julkaistu: <?php echo kauppakamari_get_posted_on(); ?>
                <?php endif; ?>
              </span>
              <span class="entry_published__some"><?php kauppakamari_social_share_buttons_simple(); ?></span>
            </div>
          </div>

          <div class="entry__ingress">
            <?php echo get_field('ingress'); ?>
          </div>

          <div class="entry__content wysiwyg">
            <?php the_content(); ?>
          </div>

          <?php if(!empty(get_field('side_content'))) : ?>
            <div class="entry__side_content">
              <div class="entry__side_content__inner">
                <?php echo get_field('side_content'); ?>
              </div>
            </div>
          <?php endif; ?>





          <div class="entry_featured__new">
            <?php
            $read_more_block = true;
            require_once('partials/blocks/news-lift-3/news-lift-3.php')
            ?>
          </div>
        </article>

      <?php endwhile; ?>

    </main><!-- #main -->

  </div><!-- #primary -->

<?php
get_footer();
