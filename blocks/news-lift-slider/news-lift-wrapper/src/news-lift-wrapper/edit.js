import { __ } from "@wordpress/i18n";
import {
	useBlockProps,
	useInnerBlocksProps,
	InspectorControls,
	ColorPalette,
} from "@wordpress/block-editor";
import { PanelBody, TextControl, Button } from "@wordpress/components";
import { useSelect, useDispatch } from "@wordpress/data";
import { createBlock } from "@wordpress/blocks";

const ALLOWED_BLOCKS = ["hskk/news-lift-item"];
const MAX_ITEMS = 10;
const DEFAULT_BACKGROUND = "#fff";

export default function Edit({ attributes, setAttributes, clientId }) {
	const { backgroundColor, title, linkText, linkUrl } = attributes;

	const innerBlocks = useSelect(
		(select) => select("core/block-editor").getBlocks(clientId),
		[clientId],
	);

	const blockProps = useBlockProps({
		style: {
			backgroundColor: backgroundColor || DEFAULT_BACKGROUND,
			paddingTop: "var(--wp--preset--spacing--50)",
			paddingBottom: "var(--wp--preset--spacing--50)",
		},
		className: "news-lift-slider-wrapper alignwide",
	});

	const CustomAppender = ({ isDisabled }) => {
		const { insertBlock } = useDispatch("core/block-editor");

		return (
			<Button
				variant="primary"
				className="submit-btn"
				disabled={isDisabled}
				onClick={() => {
					if (!isDisabled) {
						const newBlock = createBlock("hskk/news-lift-item");
						insertBlock(newBlock, undefined, clientId);
					}
				}}
			>
				{isDisabled ? "Maksimimäärä saavutettu" : "Lisää nosto"}
			</Button>
		);
	};

	const innerBlocksProps = useInnerBlocksProps(
		{
			className: "news-lift-slider",
		},
		{
			allowedBlocks: ALLOWED_BLOCKS,
			orientation: "horizontal",
			templateLock: false,
			renderAppender: false,
		},
	);

	return (
		<>
			<InspectorControls>
				<PanelBody title={__("Asetukset", "news-lift-wrapper")}>
					<TextControl
						label={__("Otsikko", "news-lift-wrapper")}
						value={title}
						onChange={(val) => setAttributes({ title: val })}
					/>
					<TextControl
						label={__("Linkkiteksti", "news-lift-wrapper")}
						value={linkText}
						onChange={(val) => setAttributes({ linkText: val })}
					/>
					<TextControl
						label={__("Linkin URL", "news-lift-wrapper")}
						value={linkUrl}
						onChange={(val) => setAttributes({ linkUrl: val })}
					/>
					<p>{__("Taustaväri", "news-lift-wrapper")}</p>
					<ColorPalette
						colors={[
							{ name: "Koralli Light", color: "#FFE4E0" },
							{ name: "Beige Medium", color: "#F3F0EA" },
							{ name: "Vaaleansininen Light", color: "#E8F1F9" },
							{ name: "Sininen Light", color: "#D5DAE7" },
							{ name: "Vaaleansininen", color: "#A5C9E7" },
							{ name: "Koralli", color: "#FD9180" },
						]}
						value={backgroundColor}
						onChange={(color) =>
							setAttributes({ backgroundColor: color || DEFAULT_BACKGROUND })
						}
					/>
				</PanelBody>
			</InspectorControls>

			<div {...blockProps}>
				<div className="alignwide">
					<div className="news-lift-heading">
						{title && <h2 className="news-lift-title">{title}</h2>}
						{linkText && linkUrl && (
							<p className="news-lift-link">
								<a href={linkUrl}>{linkText}</a>
							</p>
						)}
					</div>
					<div {...innerBlocksProps} />
				</div>
				<CustomAppender isDisabled={innerBlocks.length >= MAX_ITEMS} />
			</div>
		</>
	);
}
