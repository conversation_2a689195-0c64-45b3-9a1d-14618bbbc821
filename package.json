{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.2.0", "author": "Aucor Oy", "homepage": "https://www.aucor.fi", "private": true, "licenses": [{"type": "MIT", "url": "http://opensource.org/licenses/MIT"}], "scripts": {"build": "gulp", "jshint": "gulp jshint", "jscs": "jscs gulpfile.js assets/scripts/*.js"}, "engines": {"node": ">=20", "npm": ">=10"}, "dependencies": {"@material/select": "^4.0.0", "axios": "^1.6.8", "breakpoint-sass": "^3.0.0", "bulma": "^0.9.4", "chart.js": "^4.4.2", "fitvids": "^2.1.1", "jquery": "^3.7.1", "lazysizes": "^4.0.4", "objectFitPolyfill": "^2.3.5", "sass": "^1.77.0", "slick-slider": "^1.8.2", "svgxuse": "^1.2.6", "waypoints": "^4.0.1"}, "devDependencies": {"@babel/core": "^7.24.5", "@babel/polyfill": "^7.6.0", "@babel/preset-env": "^7.24.5", "beeper": "^1.1.1", "browser-sync": "^2.24.5", "del": "^3.0.0", "gulp": "^5.0.0", "gulp-autoprefixer": "^5.0.0", "gulp-babel": "^8.0.0", "gulp-concat": "^2.6.1", "gulp-cssnano": "^2.1.3", "gulp-file": "^0.4.0", "gulp-flatten": "0.4.0", "gulp-if": "^2.0.2", "gulp-imagemin": "^6.2.0", "gulp-jshint": "^2.1.0", "gulp-plumber": "^1.2.1", "gulp-rename": "^1.3.0", "gulp-sass": "^5.1.0", "gulp-sourcemaps": "^2.6.4", "gulp-svgmin": "^1.2.4", "gulp-svgstore": "^9.0.0", "gulp-uglify": "^3.0.2", "imagemin-pngcrush": "^7.0.0", "jshint": "^2.13.6", "jshint-stylish": "^2.2.1", "lazypipe": "^1.0.2", "merge-stream": "^1.0.1", "minimist": "^1.2.8", "run-sequence": "^2.2.1", "traverse": "^0.6.9"}, "resolutions": {"graceful-fs": "^4.2.4"}, "packageManager": "yarn@1.22.21+sha1.1959a18351b811cdeedbd484a8f86c3cc3bbaf72"}