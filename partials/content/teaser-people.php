<?php
/**
 * Template part: People
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package kauppakamari
 */


$simple_class = ' teaser--people-full';
$simple = false;
if($args === 'simple') {
  $simple_class = ' teaser--people-simple'; /* no image*/
  $simple = true;
}
?>
<div id="people-<?php the_ID(); ?>" class="search-result--people--item teaser--people<?php echo $simple_class;?>">

<?php
if(!$simple):
  if (get_the_post_thumbnail_url(get_the_ID(),'medium')) : ?>
  <div class="block-people-select__img" style="background-image: url(<?php echo get_the_post_thumbnail_url(get_the_ID(),'medium'); ?>)"></div>
  <?php else : ?>
  <div class="block-people-select__img" style="background-image: url(<?php echo get_template_directory_uri() . '/dist/images/person-placeholder.png'; ?>)"></div>
  <?php
  endif;
endif;
?>


<div class="contact__item__cat">
<?php
$peoplecat = get_the_terms(get_the_ID(), 'people-cat');
$i = 0;
foreach ($peoplecat as $people) {
  if ($i > 0) {
    echo ', ';
  }
  echo $people->name;
  $i++;
}
echo '</div>';
?>
<div class="contact__item__name">
<?php
the_title();
echo '</div>';
echo '<div class="contact__item__info person-title">'.get_field('title').'</div>';
echo '<div class="contact__item__info person-puh">'.get_field('puh').'</div>';
if(!$simple) {
  echo '<div class="contact__item__info person-info">'.get_field('info').'</div>';
}
//echo '<a href="#" data-id="'.get_the_ID().'" class="search_by_writer">Hae henkilön kirjoittamia sisältöjä</a>';
?>
</div>
