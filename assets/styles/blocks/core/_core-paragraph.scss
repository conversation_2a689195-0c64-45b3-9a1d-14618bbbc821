/* ==========================================================================
 # Paragraph block
=============================================================================

Notice: Paragraphs without custom styles won't have identifiable classes on front-end.
Be careful with paragraph styles as they will be applied by other blocks as well.

*/

/* Front-end and back-end
----------------------------------------------- */

.editor-styles-wrapper .wp-block-paragraph,
.wysiwyg p {

  &.has-background {
    display: block;
    padding: $gap;
    margin-top: $gap;
    margin-bottom: $gap;
  }

}

.page-template-template-landingpage {
  .editor-styles-wrapper > .wp-block-paragraph,
  .wysiwyg > p, .wysiwyg > h2, .wysiwyg >h3, .wysiwyg >h4, 
  .wysiwyg >h5, .wysiwyg >h6, .wysiwyg >button, .wysiwyg >a,
  .wysiwyg > .wp-block-button, .wysiwyg > .wp-block-buttons, 
  .wysiwyg > .block-shopify-events-workshops, .wysiwyg>.wp-block-columns  {

    margin-top: $gap;
    margin-bottom: $gap;
    // font-size: 19px;

    @include breakpoint($m) {
      padding-right: 10% !important;
      padding-left: 10% !important;
    }

    &.has-background {
      display: block;
      padding: $gap;
      margin-top: $gap;
      margin-bottom: $gap;
    }
  }
}



/* Only front-end
----------------------------------------------- */

.wysiwyg p {

}

/* Only back-end
----------------------------------------------- */

.editor-styles-wrapper .wp-block-paragraph {
  line-height: $line-height;
}

