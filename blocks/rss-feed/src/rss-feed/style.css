/**
 * The following styles get applied both on the front of your site
 * and in the editor.
 *
 * Replace them with your own styles or remove the file completely.
 */

.news-feed {
	color: var(--wp--preset--color--tumma-sininen) !important;
	font-family: var(--wp--preset--font-family--opensans) !important;
	font-size: 18px;
}

@media (min-width: 901px) {
	.newspage-article-wrapper .wp-block-column {
		flex: 1 1 calc(33.333% - var(--wp--preset--spacing--40));
	}
}

@media (max-width: 900px) {
	.article-wrapper .wp-block-column:last-child {
		display: none !important;
	}
}

@media (min-width: 601px) and (max-width: 900px) {
	.newspage-article-wrapper .wp-block-column {
		flex: 1 1 calc(50% - var(--wp--preset--spacing--40));
	}
}

@media (max-width: 600px) {
	.article-wrapper,
	.newspage-article-wrapper {
		display: block !important;
	}

	.article-wrapper .wp-block-column {
		margin-bottom: var(--wp--preset--spacing--80) !important;
	}

	.newspage-article-wrapper .wp-block-column {
		margin-bottom: var(--wp--preset--spacing--60);
	}

	.article-group {
		position: relative;
		padding-bottom: 45px;
	}

	.article-group .wp-block-buttons {
		position: absolute;
		bottom: 0;
		text-align: center;
	}
}
