<?php

/**
 * text image
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'block-text-image-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'block-text-image';
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// Load values and assing defaults.
$items = get_field('block_rows');

?>
<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">

  <?php
    foreach($items as $item) { ?>
    
    <?php if(!empty($item['group']['title']) && !empty($item['group']['content'])) { ?>
      <?php get_template_part('partials/blocks/text-image/text-image-single/single', null, ['item'=>$item['group']]); ?>
    <?php } ?>
    <?php if(!empty($item['group_2']['title']) && !empty($item['group_2']['content'])) { ?>
      <?php get_template_part('partials/blocks/text-image/text-image-single/single', null, ['item'=>$item['group_2']]); ?>
    <?php } ?>

    <?php } ?>

</div>
