<?php
// This file is generated. Do not modify it manually.
return array(
	'news-lift-wrapper' => array(
		'$schema' => 'https://schemas.wp.org/trunk/block.json',
		'apiVersion' => 3,
		'name' => 'hskk/news-lift-wrapper',
		'version' => '0.1.0',
		'title' => 'News Lift Slider',
		'category' => 'kauppakamari-blocks',
		'icon' => 'slides',
		'description' => 'Uutis/sivunostojen slider.',
		'example' => array(
			
		),
		'supports' => array(
			'inserter' => true,
			'html' => false
		),
		'attributes' => array(
			'backgroundColor' => array(
				'type' => 'string',
				'default' => '#f5f5f5'
			),
			'title' => array(
				'type' => 'string',
				'default' => ''
			),
			'linkText' => array(
				'type' => 'string',
				'default' => ''
			),
			'linkUrl' => array(
				'type' => 'string',
				'default' => ''
			)
		),
		'textdomain' => 'news-lift-wrapper',
		'editorScript' => 'file:./index.js',
		'editorStyle' => 'file:./index.css',
		'style' => 'file:./style-index.css',
		'viewScript' => 'file:./view.js'
	)
);
