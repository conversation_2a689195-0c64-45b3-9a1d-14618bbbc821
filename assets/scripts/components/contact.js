var checkboxes = document.querySelectorAll(".contact__checkboxes--box");

if (typeof checkboxes[0] != "undefined" && checkboxes[0] != null) {
  const boxes = document.querySelectorAll(".contact__checkboxes--box");

  function setGetParam(key, value) {
    if (history.pushState) {
      var params = new URLSearchParams(window.location.search);
      params.set(key, value);
      var newUrl =
        window.location.protocol +
        "//" +
        window.location.host +
        window.location.pathname +
        "?" +
        params.toString();
      window.history.pushState({ path: newUrl }, "", newUrl);
    }
  }

  var url = new URL(window.location.href);
  let cats = [];

  for (let i = 0; i < boxes.length; i++) {
    boxes[i].addEventListener("change", (e) => {
      if (e.target.checked) {
        cats.push(boxes[i].value);
      } else if (!e.target.checked) {
        let catindex = cats.indexOf(e.target.value);
        cats.splice(catindex, 1);
      } else {
      }
      nyt_filter(cats);
    });
  }

  function nyt_filter(cats = "") {
    let data = {
      action: "kauppakamari_filter_contact",
      peoplecat: cats,
    };

    console.log(data);

    setGetParam("kategoria", data.peoplecat);

    jQuery.ajaxSetup({ cache: false });

    jQuery.ajax({
      type: "POST", // *GET, POST, PUT, DELETE, etc.
      cache: false,
      headers: { "cache-control": "no-cache" },
      credentials: "same-origin", // include, *same-origin, omit
      url:
        frontendajax.ajaxurl +
        '?lang=""' /* disables polylang lang filter, some are fi, some empty */,
      data: data,
      beforeSend: function (xhr) {
        let posts_list = document.querySelector(".contact__list");
        posts_list.innerHTML = "Loading";
      },
      success: function (response) {
        console.log(response);
        if (response) {
          let posts_list = document.querySelector(".contact__list");
          posts_list.innerHTML = response.data;
        } else {
          console.log(response);
        }
      },
    });
  }
}
